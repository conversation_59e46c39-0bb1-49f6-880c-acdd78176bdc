export interface SubscriptionPreviewType {
    success: boolean
    data: Data
    detail?: string
  }
  
  export interface Data {
    success: boolean
    workspace_id: string
    current_states: string[]
    new_states: string[]
    current_quantity: number
    new_quantity: number
    pricing: Pricing
  }
  
  export interface Pricing {
    base_price_per_state: number
    current_monthly_cost: number
    new_monthly_cost: number
    proration: Proration
    immediate_charge: number
    next_period_charge: number
    billing_note: string
  }
  
  export interface Proration {
    refund_amount: number
    charge_amount: number
    net_adjustment: number
  }
  