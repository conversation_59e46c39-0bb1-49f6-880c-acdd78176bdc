export interface PropertyDetailsType {
    timestamp: string
    address: Address
    data: Data
}
  
export interface Address {
    display_address: string
    street_address: string
    city: string
    state: string
    zip: string
    latitude: number
    longitude: number
}

export interface Data {
    bed_count: number
    bath_count: any
    gross_sq_ft: any
    lot_size_acre: number
    year_built: any
    property_use_code_mapped: number
    hvacc_cooling_code: any
    hvacc_heating_code: any
    parking_garage_code: any
    parking_space_count: number
    basement_sq_ft: any
    basement_finished_sq_ft: any
    basement_unfinished_sq_ft: any
    flooring_material_code: any
    has_laundry_room: boolean
    fireplace_count: any
    structure_style_code: any
    roof_material_code: any
    driveway_material_code: any
    construction_code: any
    roof_construction_code: any
    exterior_code: any
    sewer_usage_code: any
    water_source_code: any
    last_sale_date: string
    last_sale_amount: number
}
  