export interface UnitDataType {
    status: string
    token: any
    data: Data
  }
  
  export interface Data {
    timestamp: string
    units: Unit[]
    market_data: MarketDaum[]
  }
  
  export interface Unit {
    address: string
    tax_assessor_id: number
    bed_count: number
    bath_count?: number
    hoa_fee: any
    document_recorded_date?: string
    mortgage_amount?: number
    id: string
    unit: string
  }
  
  export interface MarketDaum {
    address: string
    unit: string
    data: Daum[]
    id: string
    unit_id?: string
  }
  
  export interface Daum {
    pending_date?: string
    property_id: string
    list_date: string
    status: string
    last_sold_price?: number
    last_sold_date?: string
    list_price: number
    list_price_max: any
    list_price_min: any
    price_per_sqft?: number
    tags: string[]
    flags: Flags
    description: Description
    source: Source
    hoa: Hoa
    advertisers?: Advertiser[]
    location: Location
    tax_record?: TaxRecord
    primary_photo?: PrimaryPhoto
    photos?: Photo2[]
    listing_type: string
  }
  
  export interface Flags {
    is_contingent: any
    is_pending?: boolean
    is_for_rent?: boolean
  }
  
  export interface Description {
    type: string
    sqft?: number
    beds: number
    baths_full: number
    baths_half?: number
    lot_sqft: any
    sold_price?: number
    year_built: any
    garage?: number
    name: any
    stories?: number
    text: string
  }
  
  export interface Source {
    agents: Agent[]
    id: string
    listing_id: string
    disclaimer: Disclaimer
  }
  
  export interface Agent {
    agent_id?: string
    agent_name?: string
    agent_phone: any
    agent_email: any
    office_id: string
    office_name?: string
    office_phone: any
    type: string
  }
  
  export interface Disclaimer {
    logo: any
    text: string
    href: any
  }
  
  export interface Hoa {
    fee?: number
  }
  
  export interface Advertiser {
    type: string
    name?: string
    email?: string
    state_license: any
    nrds_id?: string
    broker?: Broker
    photo?: Photo
    phones?: Phone[]
  }
  
  export interface Broker {
    name?: string
    accent_color?: string
    logo: any
    designations: any
  }
  
  export interface Photo {
    href: string
  }
  
  export interface Phone {
    number: string
    type?: string
    primary: boolean
    trackable: any
    ext?: string
  }
  
  export interface Location {
    address: Address
    county: County
    neighborhoods: any
  }
  
  export interface Address {
    street_direction: any
    street_number: string
    street_name: string
    street_suffix: any
    line: string
    unit?: string
    city: string
    state_code: string
    postal_code: string
    coordinate: Coordinate
  }
  
  export interface Coordinate {
    lon: number
    lat: number
  }
  
  export interface County {
    name: string
    fips_code: string
  }
  
  export interface TaxRecord {
    public_record_id: string
  }
  
  export interface PrimaryPhoto {
    href: string
  }
  
  export interface Photo2 {
    href: string
  }
  