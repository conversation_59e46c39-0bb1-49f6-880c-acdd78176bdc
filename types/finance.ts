export interface FinanceCalculationParams {
    prop_address_id: string;
    portfolio_id: string;
    return_projections: boolean;
    override_user_input: boolean;
    year: number;
    params: {
        default_source: string;
        skip_validation: boolean;
        force_completion: boolean;
    };
}

export interface FinanceSource {
    data: {
        url: string;
        source: string;
        description: string;
    };
    date: string;
}

export interface FinanceMetadataItem {
    year?: number;
    sources: FinanceSource[];
    task_type: string;
}

export interface FinanceMetadata {
    year: number;
    repairs: FinanceMetadataItem[];
    sources: FinanceSource[];
    reserves: FinanceMetadataItem[];
    services: FinanceMetadataItem[];
}

export interface FinanceYearData {
    id: number | null;
    prop_id: string | null;
    year: number | null;
    rental_income: number | null;
    long_term_rental: number | null;
    short_term_rental: number | null;
    other_income: number | null;
    vacancy_loss: number | null;
    credit_loss: number | null;
    effective_gross_income: number | null;
    property_tax: number | null;
    insurance: number | null;
    repairs: number | null;
    maintenance: number | null;
    professional_fees: number | null;
    management_fees: number | null;
    leasing_fees: number | null;
    legal_fees: number | null;
    accounting_fees: number | null;
    engineering_fees: number | null;
    marketing_fees: number | null;
    consulting_fees: number | null;
    utilities: number | null;
    services: number | null;
    reserves: number | null;
    total_operating_expenses: number | null;
    net_operating_income: number | null;
    annual_debt_service: number | null;
    dscr: number | null;
    cash_flow_before_taxes: number | null;
    cash_flow_after_taxes: number | null;
    cumulative_cash_flow: number | null;
    cap_rate: number | null;
    gross_rent_multiplier: number | null;
    equity_multiple: number | null;
    cash_on_cash_return: number | null;
    total_acquisition_cost: number | null;
    aggregated_noi: number | null;
    blended_cap_rate: number | null;
    portfolio_irr: number | null;
    created_at: string | null;
    updated_at: string | null;
    metadata: FinanceMetadata | null;
}

export interface FinanceCalculationResponse {
    success: boolean;
    task?: string;
    task_token?: string;
    property_id: string;
    portfolio_id: string;
    year: number;
    data?: {
        [key: string]: FinanceYearData;
    } | {
        // Handle nested response structure from polling API
        success: boolean;
        task?: string;
        property_id: string;
        portfolio_id: string;
        year: number;
        data: {
            [key: string]: FinanceYearData;
        };
    };
    sources?: FinanceSource[];
    message?: string;
    status?: 'pending' | 'processing' | 'completed' | 'failed';
}