export interface SubscriptionConfirmType {
    success: boolean
    data: Data
  }
  
  export interface Data {
    success: boolean
    workspace_id: string
    subscription_id: string
    active_states: string[]
    added_states: string[]
    removed_states: any[]
    pending_states: PendingStates
    quantity: number
    proration_amount: number
    invoice_id: string
    invoice_status: string
    payment_status: string
    payment_error: string
    payment_action_required: boolean
    update_payment_url: string
    message: string
    billing_note: string
  }
  
  export interface PendingStates {
    add: string[]
    remove: any[]
  }
  