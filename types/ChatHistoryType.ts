export interface ChatHistoryType {
    id: string
    portfolio_id: string
    created_by: string
    created_at: string
    updated_at: string
    deleted_at: any
    is_deleted: boolean
    messages: Message[]
    members: Member[]
  }
  
  export interface Message {
    id: string
    chat_id: string
    user_id: string
    message: string
    role: string
    created_at: string
    updated_at: string
    deleted_at: any
  }
  
  export interface Member {
    id: string
    user_id: string
    chat_id: string
    role: string
    added_at: string
  }
  