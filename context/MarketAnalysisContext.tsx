'use client';
import { fetchSchoolData } from "@/actions/marketAnalysisActions/cherreActions";
import { fetchNearbyPlacesWithGoogleMaps } from "@/actions/marketAnalysisActions/googleMapsActions";
import { getTransitData } from "@/actions/marketAnalysisActions/unitDataActions";
import { fetchDemographics, fetchTaxHistory, fetchUnitData, getAiSummary } from "@/actions/propertyActions";
import { getPropDataById } from "@/actions/portfolioActions";
import { getUnitDataDB } from "@/actions/propetyUnitsActions";
import React, {createContext, useContext, useEffect, useState} from "react";
import { useSearchParams } from "next/navigation";
import { getPropertyMarketDataDB } from "@/actions/propertyMarketDataActions";
import { getPropertyPoisDB } from "@/actions/propertyPoisActions";
import { getPropertyDemographicsDB } from "@/actions/propertyDemographicsActions";
import { getPropertySolarDataDB } from "@/actions/propertySolarActions";
import { fetchPropertyDetails } from "@/actions/propertyActions";
import { PropertyDetailsType } from "@/types/PropertyDetailsType";
import { TaxHistoryType } from "@/types/TaxHistoryType";
import { Unit, MarketDaum } from "@/types/UnitDataType";
import { DemographicsType } from "@/types/DemographicsType";
import { getPropertyTaxHistoryDB } from "@/actions/propertyTaxHistoryActions";
import { getPropertyDetailsDB } from "@/actions/propertyDetailsActions";

interface MarketAnalysisContextType {
    selectedStates: {name: string, abbreviation: string}[]
    searchPlace: { [key: string]: any } | string
    errorState: {name: string, abbreviation: string} | null
    propertyDetails: PropertyDetailsType | null
    isLoadingPropertyDetails: boolean
    aiSummary: { [key: string]: any } | null
    isLoadingAiSummary: boolean
    unitData: Unit[] | null
    isLoadingUnitData: boolean
    marketData: MarketDaum[] | null
    isLoadingMarketData: boolean
    nearbyLocations: { [key: string]: any } | null
    isLoadingNearbyLocations: boolean
    schoolData: { [key: string]: any } | null
    isLoadingSchoolData: boolean
    myLocations: { [key: string]: any }[] | null
    selectedLocation: { [key: string]: any }[]
    demographicsData: DemographicsType | null
    isLoadingDemographics: boolean
    mainImage: string | null
    propertyImage: string | null
    allImageUrls: string[] | null
    transitData: { [key: string]: any } | null
    isLoadingTransitData: boolean
    solarData: { [key: string]: any } | null
    isLoadingSolarData: boolean
    taxHistory: TaxHistoryType | null
    isLoadingTaxHistory: boolean
    updateMarketAnalysisState: (data: Partial<MarketAnalysisContextType>) => void
    fetchData: (searchPlace: { [key: string]: any }) => void
    handleSelectedLocation: (selectedLocation: { [key: string]: any }[], address: string, latitude: number, longitude: number) => void
}


export const MarketAnalysisContext = createContext<MarketAnalysisContextType | undefined>(undefined);


export const useMarketAnalysis = () => {
	const context = useContext(MarketAnalysisContext);
	if (!context) {
		throw new Error('useMarketAnalysis must be used within an MarketAnalysisProvider');
	}
	return context;
};

export const MarketAnalysisProvider = ({ children }: { children: React.ReactNode }) => {
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const marketAddress = searchParams.get('marketAddress')
    
	const [marketAnalysisState, setMarketAnalysisState] = useState<MarketAnalysisContextType>({
        selectedStates: [],
        searchPlace: marketAddress ? marketAddress : '',
        errorState: null,
        propertyDetails: null,
        isLoadingPropertyDetails: false,
        aiSummary: null,
        isLoadingAiSummary: false,
        unitData: null,
        isLoadingUnitData: false,
        marketData: null,
        isLoadingMarketData: false,
        nearbyLocations: null,
        isLoadingNearbyLocations: false,
        schoolData: null,
        myLocations: null,
        isLoadingSchoolData: false,
        selectedLocation: [],
        demographicsData: null,
        isLoadingDemographics: false,
        mainImage: null,
        propertyImage: null,
        allImageUrls: null,
        transitData: null,
        isLoadingTransitData: false,
        solarData: null,
        isLoadingSolarData: false,
        taxHistory: null,
        isLoadingTaxHistory: false,
        updateMarketAnalysisState: (data: Partial<MarketAnalysisContextType>) => {
			setMarketAnalysisState(prevState => ({ ...prevState, ...data }))
		},
        fetchData: async (searchPlace: { [key: string]: any }) => {
            setMarketAnalysisState(prevState => ({ 
                ...prevState,
                isLoadingPropertyDetails: true,
                isLoadingUnitData: true,
                isLoadingMarketData: true,
                isLoadingNearbyLocations: true,
                isLoadingSchoolData: true,
                isLoadingDemographics: true,
                isLoadingAiSummary: true,
                isLoadingTransitData: true,
                isLoadingTaxHistory: true,
                isLoadingSolarData: true,
                propertyDetails: null
            }))

            const addressSearch = searchPlace?.address_components?.find((component: any) => component.types.includes('street_number'))?.long_name + ' ' + searchPlace?.address_components?.find((component: any) => component.types.includes('route'))?.long_name
            const displayAddressSearch = searchPlace?.formatted_address
            const citySearch = searchPlace?.address_components?.find((component: any) => component.types.includes('locality'))?.short_name
            const stateSearch = searchPlace?.address_components?.find((component: any) => component.types.includes('administrative_area_level_1'))?.short_name
            const zipcodeSearch = searchPlace?.address_components?.find((component: any) => component.types.includes('postal_code'))?.short_name
            const latSearch = searchPlace?.geometry?.location?.lat()
            const lonSearch = searchPlace?.geometry?.location?.lng()
            

            if(addressId){
                //pois
                getPropertyPoisDB(addressId, 'transit').then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, transitData: data, isLoadingTransitData: false }))
                })
                
                getPropertyPoisDB(addressId, 'nearby_locations').then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, nearbyLocations: data, isLoadingNearbyLocations: false }))
                })

                getPropertyPoisDB(addressId, 'schools').then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, schoolData: data, isLoadingSchoolData: false }))
                })

                getPropertyPoisDB(addressId, 'my_locations').then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, myLocations: data, isLoadingMyLocations: false }))
                })
                ///

                getPropertySolarDataDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, solarData: data, isLoadingSolarData: false }))
                })
                
                getPropDataById(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, aiSummary: data?.ai_summary, isLoadingAiSummary: false, propertyImage: data?.main_img_url }))
                })

                getUnitDataDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, unitData: data, isLoadingUnitData: false }))
                })

                getPropertyMarketDataDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, marketData: data, isLoadingMarketData: false }))
                })

                getPropertyTaxHistoryDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, taxHistory: data, isLoadingTaxHistory: false }))
                })

                getPropertyDemographicsDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, demographicsData: data, isLoadingDemographics: false }))
                })

                getPropertyDetailsDB(addressId).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, propertyDetails: data as PropertyDetailsType, isLoadingPropertyDetails: false }))
                })

            }else{
                
                const propertyDetails = await fetchPropertyDetails(addressSearch, citySearch, stateSearch, zipcodeSearch)
                setMarketAnalysisState(prevState => ({ ...prevState, propertyDetails: propertyDetails, isLoadingPropertyDetails: false }))

                const displayAddress = propertyDetails?.address?.display_address || displayAddressSearch
                const address = propertyDetails?.address?.street_address || addressSearch
                const city = propertyDetails?.address?.city || citySearch
                const state = propertyDetails?.address?.state || stateSearch
                const zipcode = propertyDetails?.address?.zip || zipcodeSearch
                const lat = propertyDetails?.address?.latitude || latSearch
                const lon = propertyDetails?.address?.longitude || lonSearch
               

                fetchTaxHistory(address, city, state, zipcode).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, taxHistory: data, isLoadingTaxHistory: false }))
                })


                fetchUnitData(address, city, state, zipcode).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, unitData: data?.data?.units, isLoadingUnitData: false, marketData: data?.data?.market_data, isLoadingMarketData: false }))
                })

                fetchDemographics(address, city, state, zipcode).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, demographicsData: data, isLoadingDemographics: false }))
                })

                /*getPropertySolarData(lat, lon).then((data) => {
                    console.log(data, 11111)
                    setMarketAnalysisState(prevState => ({ ...prevState, solarData: data?.parsedData, isLoadingSolarData: false }))
                })*/

                // pois
                getTransitData(lat, lon).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, transitData: data, isLoadingTransitData: false }))
                })

                fetchNearbyPlacesWithGoogleMaps(displayAddress, lat, lon).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, nearbyLocations: data, isLoadingNearbyLocations: false }))
                })
    
                fetchSchoolData(`${address}, ${zipcode}`).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, schoolData: data, isLoadingSchoolData: false }))
                })
                ///

                getAiSummary(displayAddress).then((data) => {
                    setMarketAnalysisState(prevState => ({ ...prevState, aiSummary: data, isLoadingAiSummary: false }))
                })
            }
    
        },
        handleSelectedLocation: (selectedLocation: { [key: string]: any }[], address: string, latitude: number, longitude: number) => {
            const exists = selectedLocation.some(loc => loc.address === address);
            if(exists){
                setMarketAnalysisState(prevState => ({ ...prevState, selectedLocation: selectedLocation.filter(loc => loc.address !== address) }))
            }else{
                setMarketAnalysisState(prevState => ({ ...prevState, selectedLocation: [...selectedLocation, {address, latitude, longitude}] }))
            }
        }
    })


    useEffect(() => {
        const { searchPlace, fetchData } = marketAnalysisState
        if(typeof searchPlace !== 'string'){
            fetchData(searchPlace)
        }
    }, [marketAnalysisState.searchPlace])
    

	return <MarketAnalysisContext.Provider value={{ ...marketAnalysisState }}>{children}</MarketAnalysisContext.Provider>;
};