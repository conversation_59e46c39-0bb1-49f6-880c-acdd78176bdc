'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface StatusItem {
    id: string;
    title: string;
    message: string;
    type: 'loading' | 'success' | 'error' | 'info';
    progress?: number; // 0-100 for progress bar
    autoClose?: boolean; // Auto close after duration
    duration?: number; // Duration in ms (default 5000)
    createdAt: Date;
}

interface StatusContextType {
    statusItems: StatusItem[];
    addStatus: (status: Omit<StatusItem, 'id' | 'createdAt'>) => string;
    updateStatus: (id: string, updates: Partial<StatusItem>) => void;
    removeStatus: (id: string) => void;
    clearAllStatus: () => void;
}

const StatusContext = createContext<StatusContextType | undefined>(undefined);

export function StatusProvider({ children }: { children: ReactNode }) {
    const [statusItems, setStatusItems] = useState<StatusItem[]>([]);

    const addStatus = (status: Omit<StatusItem, 'id' | 'createdAt'>): string => {
        const id = `status_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newStatus: StatusItem = {
            ...status,
            id,
            createdAt: new Date(),
            autoClose: status.autoClose ?? (status.type === 'success' || status.type === 'error'),
            duration: status.duration ?? 5000
        };

        setStatusItems(prev => {
            // Keep only the last 5 items
            const updated = [...prev, newStatus].slice(-5);
            return updated;
        });

        // Auto close if specified
        if (newStatus.autoClose) {
            setTimeout(() => {
                removeStatus(id);
            }, newStatus.duration);
        }

        return id;
    };

    const updateStatus = (id: string, updates: Partial<StatusItem>) => {
        setStatusItems(prev => 
            prev.map(item => 
                item.id === id ? { ...item, ...updates } : item
            )
        );
    };

    const removeStatus = (id: string) => {
        setStatusItems(prev => prev.filter(item => item.id !== id));
    };

    const clearAllStatus = () => {
        setStatusItems([]);
    };

    return (
        <StatusContext.Provider value={{
            statusItems,
            addStatus,
            updateStatus,
            removeStatus,
            clearAllStatus
        }}>
            {children}
        </StatusContext.Provider>
    );
}

export function useStatus() {
    const context = useContext(StatusContext);
    if (context === undefined) {
        throw new Error('useStatus must be used within a StatusProvider');
    }
    return context;
} 