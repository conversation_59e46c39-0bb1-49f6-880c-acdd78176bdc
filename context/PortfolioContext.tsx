'use client'
import { deletePortfolio, getPortfolioData, getPortfolioFinancials, getPortfolios, getPortfoliosWithProperties, getPortfolioPropertiesWithImages } from "@/actions/portfolioActions";
import { useParams, usePathname } from "next/navigation";
import React, {createContext, useContext, useEffect, useState, Suspense, useRef} from "react";
import { useModal } from "./ModalContext";
import modalTriggerType from "@/constants/modalTriggerType";
import { createChat, getChatActive, getChatList } from "@/actions/chatActions";
import { ChatActiveType } from "@/types/ChatActiveType";
import { useSearchParams } from "next/navigation";
import { useAuth } from "./AuthContext";
import { useRouter } from "next/navigation";
import pathName from "@/constants/pathName";
import { ChatListType } from "@/types/ChatListType";
import { getDocumentProcessStatus } from "@/actions/documentUploadActions";
import { DocumentProcessStatusType } from "@/types/DocumentProcessStatusType";
import { PortfolioStatsService } from "@/services/portfolioStatsService";


interface PortfolioContextType {
	selectedPortfolio: { [key: string]: any } | null
	chatActive: ChatActiveType | null
	chatList: ChatListType | null
	portfolios: { [key: string]: any }[] | null
	isLoadingPortfolios: boolean
	properties: { [key: string]: any }[] | null
	isLoadingProperties: boolean
	portfolioFinancials: { [key: string]: any }[] | null
	updatePortfolioState: (data: Partial<PortfolioContextType>) => void
	documentProcessStatus: DocumentProcessStatusType | null
	propertyStats: { [key: string]: any }[] | null
}

export const PortfolioContext = createContext<PortfolioContextType | undefined>(undefined);

export const usePortfolio = () => {
	const context = useContext(PortfolioContext);
	if (!context) {
		throw new Error('usePortfolio must be used within a PortfolioProvider');
	}
	return context;
};

export const PortfolioProvider = ({ children }: { children: React.ReactNode }) => {
    const params = useParams();
	const searchParams = useSearchParams()
	const portfolioIdParam = searchParams.get('portfolioId')
	const { workspaceSubscriptionDetails, userDataDB } = useAuth()
	const { push } = useRouter()
	const path = usePathname()
	
	// Add ref for timer
	const documentStatusTimerRef = useRef<NodeJS.Timeout | null>(null)

	
    const { modalTrigger, updateModalTrigger, modalData } = useModal()
	const [portfolioState, setPortfolioState] = useState<PortfolioContextType>({
        selectedPortfolio: null,
		chatActive: null,
		chatList: null,
        portfolios: null,
        isLoadingPortfolios: true,
        properties: null,
        isLoadingProperties: true,
		portfolioFinancials: null,
		documentProcessStatus: null,
		propertyStats: null,
        updatePortfolioState: (data: Partial<PortfolioContextType>) => {
			setPortfolioState(prevState => ({ ...prevState, ...data }))
		},
        
    });

	// Function to check document processing status and handle timer
	const checkDocumentStatus = async (portfolioId: string) => {
		try {
			const data = await getDocumentProcessStatus(localStorage.getItem('task_token') as string, portfolioId);
			portfolioState.updatePortfolioState({ documentProcessStatus: data });

			if(!data?.documents || data?.documents?.length === 0) {
				portfolioState.updatePortfolioState({ documentProcessStatus: null })
				return
			}
			
			// Check if there are any documents with 'processing' status
			const hasProcessingDocuments = data?.documents?.find(item => item.status === 'processing');
			
			if (hasProcessingDocuments) {
				// If there are processing documents and no timer is running, start one
				if (!documentStatusTimerRef.current) {
					documentStatusTimerRef.current = setInterval(() => {
						console.log('checkDocumentStatusTime')
						checkDocumentStatus(portfolioId);
					}, 10000); 
				}
			} else {
				// If no processing documents, clear the timer
				if (documentStatusTimerRef.current) {
					clearInterval(documentStatusTimerRef.current);
					documentStatusTimerRef.current = null;
				}
			}
		} catch (error) {
			console.error('Error checking document status:', error);
		}
	};

	// Cleanup timer on unmount
	useEffect(() => {
		return () => {
			if (documentStatusTimerRef.current) {
				clearInterval(documentStatusTimerRef.current);
			}
		};
	}, []);

    useEffect(() => {
        const { updatePortfolioState } = portfolioState
        updatePortfolioState({ isLoadingPortfolios: true })
		
		getPortfoliosWithProperties(params?.id as string || '').then(data => {
			updatePortfolioState({ portfolios: data, isLoadingPortfolios: false })
		});
		
        
    }, [params?.id, workspaceSubscriptionDetails]);

    useEffect(() => {
        const { updatePortfolioState, selectedPortfolio, portfolios } = portfolioState
		if(modalTrigger === modalTriggerType.createPortfolio) { 
			getPortfoliosWithProperties(params?.id as string || '').then(data => {
				updatePortfolioState({ portfolios: data, isLoadingPortfolios: false })
				updateModalTrigger(null)
			});
		}

		if(modalTrigger === modalTriggerType.updatePortfolio) { 
			getPortfoliosWithProperties(params?.id as string || '').then(data => {
				updatePortfolioState({ portfolios: data, isLoadingPortfolios: false })
				updateModalTrigger(null)
			});
		}

        if(modalTrigger === modalTriggerType.deleteAddress) {
            getPortfolioPropertiesWithImages(selectedPortfolio?.id).then((data) => {
				updatePortfolioState({ properties: data, isLoadingProperties: false })
			})
            updateModalTrigger(null)
        }

		if(modalTrigger === modalTriggerType.deletePortfolio) {
			deletePortfolio(modalData?.id)
			const newPortfolios = portfolios ? portfolios.filter(portfolio => portfolio.id !== modalData?.id) : null
			updatePortfolioState({ portfolios: newPortfolios, selectedPortfolio: selectedPortfolio?.id !== modalData?.id ? selectedPortfolio : newPortfolios?.[0] as { [key: string]: any } })
			updateModalTrigger(null)
		}

		if(modalTrigger === modalTriggerType.documentProcess) {
			getDocumentProcessStatus(localStorage.getItem('task_token') as string, portfolioState.selectedPortfolio?.id).then(data => {
				if(data?.documents?.length > 0) {
					updatePortfolioState({ documentProcessStatus: data })
					checkDocumentStatus(portfolioState.selectedPortfolio?.id);
					updateModalTrigger(null)
				} else {
					updatePortfolioState({ documentProcessStatus: null })
				}
			})
		}
	}, [modalTrigger])


    useEffect(() => {
        const { isLoadingPortfolios, portfolios, selectedPortfolio, updatePortfolioState } = portfolioState
		if(!isLoadingPortfolios && portfolios &&  portfolios?.length > 0 && !selectedPortfolio) {
			const portfolio = portfolioIdParam ? portfolios.filter((portfolio) => portfolio.id === portfolioIdParam)[0] : portfolios[0]
			if(portfolioIdParam){
				updatePortfolioState({ selectedPortfolio: portfolio })
			}
			getChatList(portfolio?.id).then((data) => {
				updatePortfolioState({chatList: data, chatActive: data?.chats[0]})
			})

			PortfolioStatsService.calculatePropertiesStats(portfolio.id)
				.then(stats => {
					console.log(stats, 222)
					updatePortfolioState({ propertyStats: stats })
				})
				
		}
	}, [portfolioState?.isLoadingPortfolios])

	useEffect(() => {
        const { selectedPortfolio, updatePortfolioState } = portfolioState
		if(selectedPortfolio) {
			updatePortfolioState({ isLoadingProperties: true })
			getPortfolioPropertiesWithImages(selectedPortfolio?.id).then((data) => {
				updatePortfolioState({ properties: data, isLoadingProperties: false })
			})
			getChatList(selectedPortfolio?.id).then((data) => {
				updatePortfolioState({chatList: data, chatActive: data?.chats[0]})
			})
			if(localStorage.getItem('task_token')){
				getDocumentProcessStatus(localStorage.getItem('task_token') as string, selectedPortfolio?.id).then(data => {
					if(data?.documents?.length > 0) {
						updatePortfolioState({ documentProcessStatus: data })
						checkDocumentStatus(portfolioState.selectedPortfolio?.id);
					} else {
						updatePortfolioState({ documentProcessStatus: null })
					}
				})
			}
		}
	}, [portfolioState?.selectedPortfolio])

	return (
		<PortfolioContext.Provider value={{ ...portfolioState }}>
			{children}
		</PortfolioContext.Provider>
	);
}