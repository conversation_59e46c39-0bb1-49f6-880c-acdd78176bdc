'use client'
import React, { createContext, useContext, useState } from 'react';

interface ModalContextType {
	modal: string | boolean
	showModal: (modalType: string, modalSmall?: boolean) => void
	closeModal: () => void
	modalTrigger: string | null
	updateModalTrigger: (val: string | null) => void
	modalData: Record<string, any> | null
	updateModalData: (val: Record<string, any> | null) => void
}


export const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const useModal = () => {
	const context = useContext(ModalContext)
	if (!context) {
		throw new Error('useModal must be used within a ModalProvider')
	}
	return context
}

export function ModalProvider({ children }: { children: React.ReactNode }) {
	const [modal, setModal] = useState<string | boolean>(false)
	const [modalTrigger, setModalTrigger] = useState<string | null>(null)
	const [modalData, setModalData] = useState<Record<string, any> | null>(null)

	const showModal = (modalType: string) => {
		setModal(modalType);
	}

	const closeModal = () => {
		setModal(false)
	}

	const updateModalTrigger = (val: string | null) => {
		setModalTrigger(val)
	}

	const updateModalData = (val: any) => {
		setModalData(val)
	}

	return (
		<ModalContext.Provider value={{ modal, showModal, closeModal, modalTrigger, updateModalTrigger, modalData, updateModalData }}>
			{children}
		</ModalContext.Provider>
	)
}