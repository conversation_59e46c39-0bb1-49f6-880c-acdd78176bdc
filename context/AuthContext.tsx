import React, { createContext, useContext, useRef, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useEffect } from 'react';
import { Session } from '@supabase/supabase-js';
import {createWorkspace, getUserWorkspaces} from "@/actions/workspaceActions";
import { subscriptionDetails } from '@/actions/subscriptionActions';
import { WorkspaceSubscriptionDetails } from '@/types/SubscriptionDetailsType';
import { getSharedList, getUserData, insertDataRoomGuest } from '@/actions/userActions';
import { getCookie, getCookies, setCookie, deleteCookie, hasCookie } from 'cookies-next';
import { useSearchParams } from 'next/navigation';
import cookieName from '@/constants/cookieName';

interface AuthContextType {
    user: Session | null
    userDataDB: { [key: string]: any } | null
    isLoadingUserData: boolean
	workspaces: { [key: string]: any }[] | null
    workspaceSubscriptionDetails: WorkspaceSubscriptionDetails[] | null
	handleGetUserWorkspaces: () => Promise<any[]>
    sharedList: { [key: string]: any }[] | null
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [isLoadingUserData, setIsLoadingUserData] = useState(true)
    const [user, setUser] = useState<Session | null>(null)
    const [userDataDB, setUserDataDB] = useState<{ [key: string]: any } | null>(null)
	const [workspaces, setWorkspaces] = useState<any[] | null>(null)
    const [workspaceSubscriptionDetails, setWorkspaceSubscriptionDetails] = useState<WorkspaceSubscriptionDetails[] | null>(null)
    const params = useSearchParams()
    const token = params.get('token')
    const [sharedList, setSharedList] = useState<{ [key: string]: any }[] | null>(null)

	const handleGetUserWorkspaces = async () => {
		const workspaces = await getUserWorkspaces()
        if(workspaces.length > 0) {
            setWorkspaces(workspaces)
        }else{
            await createWorkspace('First Workspace 🏡')
            const workspaces = await getUserWorkspaces()
            setWorkspaces(workspaces)
        }
		return workspaces
	}

    const handleGetSubscriptionDetails = async (isSuperuser: boolean) => {
        const subscriptionDetailsData = await subscriptionDetails()
        setWorkspaceSubscriptionDetails(isSuperuser ? subscriptionDetailsData.data?.map(item => ({...item, has_active_subscription: true})) : subscriptionDetailsData.data)
        return subscriptionDetailsData
    }

    const handleGetSharedList = async () => {
        const sharedList = await getSharedList(user?.user?.id as string)
        setSharedList(sharedList)
    }

    const handleGetUserData = async () => {
        const userData = await getUserData(user?.user?.id as string)
        setUserDataDB(userData)
    }

    const handleGetInvitePortfolioToken = async (userId: string) => {
        if(hasCookie(cookieName.invitePortfolioToken)) {
            const token = getCookie(cookieName.invitePortfolioToken)
            const res = await fetch('/api/jwt-decode-token', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: token }),
            })
            const tokenData = await res.json()
          
            insertDataRoomGuest(tokenData?.payload, userId).then(() => {
                deleteCookie(cookieName.invitePortfolioToken)
                handleGetSharedList()
            })
        }
    }

    useEffect(() => {
        setIsLoadingUserData(true)
        const { data: authListener } = createClient().auth.onAuthStateChange(async (event, session) => {
            setIsLoadingUserData(false);
            console.log('Auth event:', event);
            console.log('Session:', session);
        
            if (event === 'SIGNED_IN' || event === 'INITIAL_SESSION' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED') {
                if(session) {
                    setUser(session)
                }
            } else if (event === 'SIGNED_OUT') {
                setUser(null);
            }
        });
        
        return () => {
            authListener?.subscription.unsubscribe();
        };
    }, []);

    const isEffectDevFetchWorkspaces = useRef(false);
    useEffect(() => {
        if(user && !isLoadingUserData && !workspaces && !isEffectDevFetchWorkspaces.current) {
            isEffectDevFetchWorkspaces.current = true;
            handleGetUserWorkspaces()
            handleGetUserData()
            handleGetSharedList()
        }
    }, [isLoadingUserData, user, workspaces])

    useEffect(() => {
        if(token) {
            setCookie(cookieName.invitePortfolioToken, token, { maxAge: 60 * 60 * 24 * 180,})
        }
    }, [token])


    useEffect(() => {
        if(userDataDB) {
            handleGetSubscriptionDetails(userDataDB?.is_superuser)
            handleGetInvitePortfolioToken(userDataDB?.id)
        }
    }, [userDataDB, workspaces])

    return <AuthContext.Provider value={{ user, userDataDB, isLoadingUserData, workspaces, workspaceSubscriptionDetails, handleGetUserWorkspaces, sharedList }}>{children}</AuthContext.Provider>;
};