import React, {createContext, useContext, useEffect, useState} from "react";
import {getUserWorkspaces} from "@/actions/workspaceActions";

interface WorkspaceContextType {
	workSpaces: any[] | null
	isLoadingWorkspaces: boolean
}

export const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const useWorkspace = () => {
	const context = useContext(WorkspaceContext);
	if (!context) {
		throw new Error('useAuth must be used within an WorkspaceProvider');
	}
	return context;
};

export const WorkspaceProvider = ({ children }: { children: React.ReactNode }) => {
	const [workSpaces, setWorkSpaces] = useState<any[] | null>(null)
	const [isLoadingWorkspaces, setIsLoadingWorkSpaces] = useState(true)

	useEffect(() => {
		setIsLoadingWorkSpaces(true)
		getUserWorkspaces().then((workspaces) => {
			console.log(workspaces)
			setWorkSpaces(workspaces)
			setIsLoadingWorkSpaces(false)
		})
	}, [])

	return <WorkspaceContext.Provider value={{ workSpaces, isLoadingWorkspaces }}>{children}</WorkspaceContext.Provider>;
};
