import { startCronJobs } from './cron-scheduler';

let initialized = false;

export function initializeApplication() {
    if (initialized) {
        console.log('🔄 Application already initialized, skipping...');
        return;
    }

    console.log('🚀 Initializing application...');

    try {
        // Start cron jobs
        startCronJobs();
        
        initialized = true;
        console.log('✅ Application initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing application:', error);
    }
}

// Auto-initialize when this module is imported (only in production)
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_CRON === 'true') {
    // Use setTimeout to ensure this runs after the application is fully loaded
    setTimeout(() => {
        initializeApplication();
    }, 5000); // 5 second delay to ensure app is ready
} 