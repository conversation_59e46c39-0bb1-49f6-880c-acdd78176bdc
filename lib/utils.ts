export function getBaseUrl() {
    // Check if we're running on the client
    if (typeof window === 'undefined') {
        // Server-side: use environment variables
        if (process.env.VERCEL_URL) {
            return `https://${process.env.VERCEL_URL}`;
        }
        return `http://localhost:${process.env.PORT || 3000}`;
    }
    
    // Client-side: use window.location
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}`;
} 