import cron from 'node-cron';

let cronJobStarted = false;

export function startCronJobs() {
    if (cronJobStarted) {
        console.log('⏰ Cron jobs already started, skipping...');
        return;
    }

    console.log('🚀 Starting cron job scheduler...');

    // Schedule daily news analysis at 2:00 AM EST (7:00 AM UTC)
    // Cron format: second minute hour day month dayOfWeek
    const cronExpression = '0 0 6 * * *'; // 7:00 AM UTC = 2:00 AM EST
    
    cron.schedule(cronExpression, async () => {
        console.log('⏰ Triggering daily news analysis cron job at:', new Date().toISOString());
        
        try {
            const cronSecret = 'cwn87rh3934rjn38nfg90c438n938gn34';
            const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
            
            const response = await fetch(`${baseUrl}/api/cron/daily-news-analysis`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${cronSecret}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Daily news analysis cron job completed successfully:', result.summary);
            } else {
                const error = await response.text();
                console.error('❌ Daily news analysis cron job failed:', error);
            }
        } catch (error) {
            console.error('💥 Error triggering daily news analysis cron job:', error);
        }
    }, {
        timezone: "America/New_York" // EST timezone
    });

    cronJobStarted = true;
    console.log('✅ Cron job scheduled: Daily news analysis will run at 2:00 AM EST every day');
}

/*export function stopCronJobs() {
    // Get all scheduled tasks and stop them
    const tasks = cron?.getTasks();
    if (tasks) {
        Object.values(tasks).forEach((task: cron.ScheduledTask) => {
            task.stop();
        });
    }
    cronJobStarted = false;
    console.log('🛑 All cron jobs stopped');
} */