#!/usr/bin/env node

/**
 * Manual trigger script for the daily news preload cron job
 * Usage: node scripts/trigger-news-preload.js [environment]
 * 
 * Environment can be:
 * - local (default): http://localhost:3000
 * - production: your production URL
 */

const https = require('https');
const http = require('http');

const environment = process.argv[2] || 'local';

const config = {
  local: {
    protocol: 'http:',
    hostname: 'localhost',
    port: 3001,
    path: '/api/cron/daily-news-preload'
  },
  production: {
    protocol: 'https:',
    hostname: 'your-production-domain.vercel.app', // Update this with your actual domain
    port: 443,
    path: '/api/cron/daily-news-preload'
  }
};

const selectedConfig = config[environment];
if (!selectedConfig) {
  console.error(`❌ Unknown environment: ${environment}`);
  console.error('Available environments: local, production');
  process.exit(1);
}

const cronSecret = process.env.CRON_SECRET || 'relm-intel-cron-2024';

const options = {
  ...selectedConfig,
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${cronSecret}`,
    'Content-Type': 'application/json'
  }
};

console.log(`🚀 Triggering news preload cron job on ${environment}...`);
console.log(`📍 URL: ${selectedConfig.protocol}//${selectedConfig.hostname}:${selectedConfig.port}${selectedConfig.path}`);

const client = selectedConfig.protocol === 'https:' ? https : http;

const req = client.request(options, (res) => {
  console.log(`📊 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('📄 Response:');
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('✅ News preload completed successfully!');
        console.log(`⏱️  Duration: ${response.duration}`);
        console.log(`📈 Success: ${response.successCount}, Errors: ${response.errorCount}`);
      } else {
        console.log('❌ News preload failed!');
        console.log(`💥 Error: ${response.error}`);
      }
    } catch (error) {
      console.log('📄 Raw Response:', data);
      console.error('❌ Failed to parse response as JSON:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.error('💥 Request failed:', error.message);
});

req.end();

console.log('⏳ Request sent, waiting for response...'); 