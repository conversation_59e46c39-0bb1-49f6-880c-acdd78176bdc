-- Add unique constraints for financial tables to support upsert operations
-- This ensures that each property/portfolio can only have one financial record per year

-- Add unique constraint for prop_financials (prop_id, year)
ALTER TABLE public.prop_financials 
ADD CONSTRAINT prop_financials_prop_id_year_key UNIQUE (prop_id, year);

-- Add unique constraint for portfolio_financials (portfolio_id, year)
ALTER TABLE public.portfolio_financials 
ADD CONSTRAINT portfolio_financials_portfolio_id_year_key UNIQUE (portfolio_id, year);
