-- Add storage policies for workspacelogos bucket

-- Add logo_url column to workspaces table if it doesn't exist
ALTER TABLE public.workspaces 
ADD COLUMN IF NOT EXISTS logo_url TEXT;

-- Create the workspacelogos bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'workspacelogos',
  'workspacelogos',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- Policy to allow authenticated users to upload workspace logos
CREATE POLICY "Authenticated users can upload workspace logos" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'workspacelogos' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = 'workspacelogos'
);

-- Policy to allow public read access to workspace logos
CREATE POLICY "Public read access for workspace logos" ON storage.objects
FOR SELECT USING (
  bucket_id = 'workspacelogos'
);

-- Policy to allow authenticated users to update their workspace logos
CREATE POLICY "Authenticated users can update workspace logos" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'workspacelogos' 
  AND auth.role() = 'authenticated'
) WITH CHECK (
  bucket_id = 'workspacelogos' 
  AND auth.role() = 'authenticated'
);

-- Policy to allow authenticated users to delete workspace logos
CREATE POLICY "Authenticated users can delete workspace logos" ON storage.objects
FOR DELETE USING (
  bucket_id = 'workspacelogos' 
  AND auth.role() = 'authenticated'
);

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
