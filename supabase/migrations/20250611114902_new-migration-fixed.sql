-- Fixed migration with correct table creation order
-- Dependencies created before references

-- Create the update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 1. FOUNDATIONAL TABLES (no dependencies)
create table public.users (
  id uuid not null,
  full_name text null,
  email text null,
  stripe_customer_id text null,
  photo text null,
  organization text null,
  phone text null,
  address text null,
  is_superuser boolean null default false,
  constraint users_pkey primary key (id),
  constraint users_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create table public.roles (
  id uuid not null default extensions.uuid_generate_v4 (),
  name character varying(50) not null,
  allowance jsonb not null default '{}'::jsonb,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint roles_pkey primary key (id),
  constraint roles_name_key unique (name)
) TABLESPACE pg_default;

create index IF not exists idx_roles_name on public.roles using btree (name) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on roles for EACH row
execute FUNCTION update_updated_at_column ();

-- 2. WORKSPACE TABLES
create table public.workspaces (
  id uuid not null default extensions.uuid_generate_v4 (),
  name character varying(100) not null,
  owner_id uuid not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  is_personal boolean null,
  is_deleted boolean not null default false,
  billing_admin_id uuid null,
  constraint workspaces_pkey primary key (id),
  constraint workspaces_billing_admin_id_fkey foreign KEY (billing_admin_id) references users (id),
  constraint workspaces_owner_id_fkey foreign KEY (owner_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_workspaces_owner_id on public.workspaces using btree (owner_id) TABLESPACE pg_default;

create index IF not exists idx_workspaces_name on public.workspaces using btree (name) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on workspaces for EACH row
execute FUNCTION update_updated_at_column ();

create table public.workspace_members (
  id uuid not null default extensions.uuid_generate_v4 (),
  workspace_id uuid not null,
  user_id uuid not null,
  role_id uuid not null,
  is_personal boolean null default false,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint workspace_members_pkey primary key (id),
  constraint workspace_members_workspace_id_user_id_key unique (workspace_id, user_id),
  constraint workspace_members_role_id_fkey foreign KEY (role_id) references roles (id),
  constraint workspace_members_user_id_fkey foreign KEY (user_id) references auth.users (id),
  constraint workspace_members_workspace_id_fkey foreign KEY (workspace_id) references workspaces (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists workspace_members_created_at_idx on public.workspace_members using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_workspace_members_workspace_id on public.workspace_members using btree (workspace_id) TABLESPACE pg_default;

create index IF not exists idx_workspace_members_user_id on public.workspace_members using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_workspace_members_role_id on public.workspace_members using btree (role_id) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on workspace_members for EACH row
execute FUNCTION update_updated_at_column ();

create table public.workspace_subscriptions (
  id uuid not null default extensions.uuid_generate_v4 (),
  workspace_id uuid not null,
  state_code character varying(2) not null,
  status text not null default 'active'::text,
  stripe_subscription_id text not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint workspace_subscriptions_pkey primary key (id),
  constraint workspace_subscriptions_workspace_id_state_code_key unique (workspace_id, state_code),
  constraint workspace_subscriptions_workspace_id_fkey foreign KEY (workspace_id) references workspaces (id) on delete CASCADE
) TABLESPACE pg_default;

-- 3. PORTFOLIO TABLES
create table public.portfolios (
  id uuid not null default extensions.uuid_generate_v4 (),
  name character varying(100) not null,
  workspace_id uuid not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  is_deleted boolean null default false,
  constraint portfolios_pkey primary key (id),
  constraint portfolios_workspace_id_fkey foreign KEY (workspace_id) references workspaces (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_portfolios_workspace_id on public.portfolios using btree (workspace_id) TABLESPACE pg_default;

create index IF not exists idx_portfolios_name on public.portfolios using btree (name) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on portfolios for EACH row
execute FUNCTION update_updated_at_column ();

-- 4. PROPERTY ADDRESS TABLES
create table public.prop_addresses (
  id uuid not null default extensions.uuid_generate_v4 (),
  address character varying(255) not null,
  city character varying(100) not null,
  state character varying(50) not null,
  zip character varying(20) not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  lat text null,
  lon text null,
  constraint prop_addresses_pkey primary key (id)
) TABLESPACE pg_default;

create index IF not exists idx_prop_addresses_city_state on public.prop_addresses using btree (city, state) TABLESPACE pg_default;

create index IF not exists idx_prop_addresses_zip on public.prop_addresses using btree (zip) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_addresses for EACH row
execute FUNCTION update_updated_at_column ();

-- 5. PROPERTY TABLES
create table public.prop (
  id uuid not null default extensions.uuid_generate_v4 (),
  address_id uuid not null,
  portfolio_id uuid not null,
  main_img_url text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  is_deleted boolean null default false,
  ai_summary jsonb null,
  year_built smallint null,
  img_urls jsonb null,
  constraint prop_pkey primary key (id),
  constraint prop_address_id_fkey foreign KEY (address_id) references prop_addresses (id),
  constraint prop_portfolio_id_fkey foreign KEY (portfolio_id) references portfolios (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_prop_address_id on public.prop using btree (address_id) TABLESPACE pg_default;

create index IF not exists idx_prop_portfolio_id on public.prop using btree (portfolio_id) TABLESPACE pg_default;

create index IF not exists idx_prop_is_deleted on public.prop using btree (is_deleted) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop for EACH row
execute FUNCTION update_updated_at_column ();

-- 6. CHAT TABLES (now that portfolios exists)
create table public.chat (
  id uuid not null default extensions.uuid_generate_v4 (),
  portfolio_id uuid not null,
  created_by uuid not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  deleted_at timestamp with time zone null,
  is_deleted boolean null default false,
  name text null default 'Portfolio Chat'::text,
  constraint chat_pkey primary key (id),
  constraint chat_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint chat_portfolio_id_fkey foreign KEY (portfolio_id) references portfolios (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_chat_portfolio_id on public.chat using btree (portfolio_id) TABLESPACE pg_default;

create index IF not exists idx_chat_created_by on public.chat using btree (created_by) TABLESPACE pg_default;

create index IF not exists idx_chat_is_deleted on public.chat using btree (is_deleted) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on chat for EACH row
execute FUNCTION update_updated_at_column ();

create table public.chat_members (
  id uuid not null default extensions.uuid_generate_v4 (),
  user_id uuid not null,
  chat_id uuid not null,
  role character varying(50) not null,
  added_at timestamp with time zone null default now(),
  constraint chat_members_pkey primary key (id),
  constraint chat_members_chat_id_user_id_key unique (chat_id, user_id),
  constraint chat_members_chat_id_fkey foreign KEY (chat_id) references chat (id) on delete CASCADE,
  constraint chat_members_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_chat_members_user_id on public.chat_members using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_chat_members_chat_id on public.chat_members using btree (chat_id) TABLESPACE pg_default;

create table public.chat_messages (
  id uuid not null default extensions.uuid_generate_v4 (),
  chat_id uuid not null,
  user_id uuid not null,
  message text not null,
  role character varying(50) not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  deleted_at timestamp with time zone null,
  is_deleted boolean not null default false,
  constraint chat_message_pkey primary key (id),
  constraint chat_message_chat_id_fkey foreign KEY (chat_id) references chat (id) on delete CASCADE,
  constraint chat_message_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_chat_message_chat_id on public.chat_messages using btree (chat_id) TABLESPACE pg_default;

create index IF not exists idx_chat_message_user_id on public.chat_messages using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_chat_message_created_at on public.chat_messages using btree (created_at) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on chat_messages for EACH row
execute FUNCTION update_updated_at_column ();

-- 7. CHANGE LOG (now that portfolios and prop exist)
create table public.change_log (
  id uuid not null default extensions.uuid_generate_v4 (),
  user_id uuid not null,
  portfolio_id uuid not null,
  prop_id uuid null,
  data jsonb not null default '{}'::jsonb,
  updated_table text null,
  created_at timestamp without time zone null default now(),
  updated_at timestamp without time zone null default now(),
  constraint change_log_pkey primary key (id),
  constraint change_log_portfolio_id_fkey foreign KEY (portfolio_id) references portfolios (id) on delete CASCADE,
  constraint change_log_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete set null,
  constraint change_log_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_change_log_user_id on public.change_log using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_change_log_portfolio_id on public.change_log using btree (portfolio_id) TABLESPACE pg_default;

create index IF not exists idx_change_log_prop_id on public.change_log using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_change_log_gin on public.change_log using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on change_log for EACH row
execute FUNCTION update_updated_at_column ();

-- 8. PROPERTY-RELATED TABLES (now that prop exists)
create table public.demographics (
  id uuid not null default gen_random_uuid (),
  prop_id uuid null,
  data jsonb null,
  created_at timestamp with time zone not null default now(),
  constraint demographics_pkey primary key (id),
  constraint demographics_prop_id_fkey foreign KEY (prop_id) references prop (id)
) TABLESPACE pg_default;

create table public.pois (
  id uuid not null default extensions.uuid_generate_v4 (),
  prop_id uuid not null,
  data jsonb not null default '{}'::jsonb,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  type text null,
  constraint pois_pkey primary key (id),
  constraint pois_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists pois_type_idx on public.pois using btree (type) TABLESPACE pg_default;

create index IF not exists idx_pois_prop_id on public.pois using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_pois_gin on public.pois using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on pois for EACH row
execute FUNCTION update_updated_at_column ();

create table public.prop_air_quality (
  id uuid not null default extensions.uuid_generate_v4 (),
  prop_id uuid not null,
  data jsonb not null default '{}'::jsonb,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint prop_air_quality_pkey primary key (id),
  constraint prop_air_quality_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_prop_air_quality_prop_id on public.prop_air_quality using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_prop_air_quality_gin on public.prop_air_quality using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_air_quality for EACH row
execute FUNCTION update_updated_at_column ();

create table public.prop_demographics (
  id uuid not null default gen_random_uuid (),
  prop_id uuid null,
  created_at timestamp without time zone null default now(),
  modified_at timestamp without time zone null default now(),
  vintage text null,
  population_2020_count numeric null,
  population_median_age numeric null,
  median_household_income numeric null,
  average_household_income numeric null,
  crime_total_risk numeric null,
  constraint prop_demographics_pkey primary key (id)
) TABLESPACE pg_default;

create table public.prop_details (
  id uuid not null default gen_random_uuid (),
  prop_id uuid null,
  created_at timestamp without time zone null default now(),
  modified_at timestamp without time zone null default now(),
  bed_count integer null,
  bath_count numeric null,
  gross_sq_ft integer null,
  lot_size_acre numeric null,
  year_built integer null,
  property_use_code_mapped text null,
  hvacc_cooling_code text null,
  hvacc_heating_code text null,
  parking_garage_code text null,
  parking_space_count integer null,
  basement_sq_ft integer null,
  basement_finished_sq_ft integer null,
  basement_unfinished_sq_ft integer null,
  flooring_material_code text null,
  has_laundry_room boolean null,
  fireplace_count integer null,
  structure_style_code text null,
  roof_material_code text null,
  driveway_material_code text null,
  construction_code text null,
  roof_construction_code text null,
  exterior_code text null,
  sewer_usage_code text null,
  water_source_code text null,
  last_sale_date date null,
  last_sale_amount numeric null,
  longitude numeric null,
  latitude numeric null,
  constraint prop_details_pkey primary key (id)
) TABLESPACE pg_default;

create table public.prop_env_risk (
  id uuid not null default extensions.uuid_generate_v4 (),
  prop_id uuid not null,
  data jsonb not null default '{}'::jsonb,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint prop_env_risk_pkey primary key (id),
  constraint prop_env_risk_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_prop_env_risk_prop_id on public.prop_env_risk using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_prop_env_risk_gin on public.prop_env_risk using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_env_risk for EACH row
execute FUNCTION update_updated_at_column ();

create table public.prop_financials (
  id uuid not null,
  prop_id uuid not null,
  year integer not null,
  rental_income double precision null,
  long_term_rental double precision null,
  short_term_rental double precision null,
  other_income double precision null,
  vacancy_loss double precision null,
  credit_loss double precision null,
  effective_gross_income double precision null,
  property_tax double precision null,
  insurance double precision null,
  repairs double precision null,
  maintenance double precision null,
  professional_fees double precision null,
  management_fees double precision null,
  leasing_fees double precision null,
  legal_fees double precision null,
  accounting_fees double precision null,
  engineering_fees double precision null,
  marketing_fees double precision null,
  consulting_fees double precision null,
  utilities double precision null,
  services double precision null,
  reserves double precision null,
  total_operating_expenses double precision null,
  net_operating_income double precision null,
  annual_debt_service double precision null,
  dscr double precision null,
  cash_flow_before_taxes double precision null,
  cash_flow_after_taxes double precision null,
  cumulative_cash_flow double precision null,
  cap_rate double precision null,
  gross_rent_multiplier double precision null,
  equity_multiple double precision null,
  cash_on_cash_return double precision null,
  total_acquisition_cost double precision null,
  aggregated_noi double precision null,
  blended_cap_rate double precision null,
  portfolio_irr double precision null,
  created_at timestamp without time zone not null,
  updated_at timestamp without time zone not null,
  metadata jsonb null,
  constraint prop_financials_pkey primary key (id)
) TABLESPACE pg_default;

create index IF not exists prop_financials_prop_id_idx on public.prop_financials using btree (prop_id) TABLESPACE pg_default;

create index IF not exists prop_financials_year_idx on public.prop_financials using btree (year) TABLESPACE pg_default;

create table public.prop_market_data (
  id uuid not null default extensions.uuid_generate_v4 (),
  prop_id uuid not null,
  unit text not null,
  data jsonb not null default '{}'::jsonb,
  beds double precision null,
  baths double precision null,
  sqft double precision null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  unit_id uuid null,
  constraint prop_market_data_pkey primary key (id),
  constraint prop_market_data_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists prop_market_data_created_at_idx on public.prop_market_data using btree (created_at) TABLESPACE pg_default;

create index IF not exists prop_market_data_unit_id_idx on public.prop_market_data using btree (unit_id) TABLESPACE pg_default;

create index IF not exists idx_prop_market_data_prop_id on public.prop_market_data using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_prop_market_data_unit on public.prop_market_data using btree (unit) TABLESPACE pg_default;

create index IF not exists idx_prop_market_data_gin on public.prop_market_data using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_market_data for EACH row
execute FUNCTION update_updated_at_column ();

create table public.prop_solar_potential (
  id uuid not null default extensions.uuid_generate_v4 (),
  prop_id uuid not null,
  data jsonb not null default '{}'::jsonb,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint prop_solar_potential_pkey primary key (id),
  constraint prop_solar_potential_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_prop_solar_potential_prop_id on public.prop_solar_potential using btree (prop_id) TABLESPACE pg_default;

create index IF not exists idx_prop_solar_potential_gin on public.prop_solar_potential using gin (data) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_solar_potential for EACH row
execute FUNCTION update_updated_at_column ();

create table public.prop_tax_history (
  id uuid not null default gen_random_uuid (),
  prop_id uuid null,
  created_at timestamp without time zone null default now(),
  modified_at timestamp without time zone null default now(),
  assessed_tax_year numeric null,
  assessed_value_land numeric null,
  assessed_value_total numeric null,
  tax_bill_amount numeric null,
  constraint prop_tax_history_pkey primary key (id)
) TABLESPACE pg_default;

create table public.prop_units (
  id uuid not null,
  prop_id uuid not null,
  unit text not null,
  beds double precision null,
  baths double precision null,
  hoa_fee double precision null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  sqft double precision null,
  feature_tags jsonb[] null,
  amenities_tags jsonb[] null,
  img_urls jsonb null,
  metadata jsonb null,
  rent double precision null,
  availability_status text null default 'available'::text,
  price real null,
  tax_history jsonb null,
  tax_assessor_id smallint null,
  document_recorded_date text null,
  constraint prop_units_prop_id_fkey foreign KEY (prop_id) references prop (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists prop_units_created_at_idx on public.prop_units using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_prop_units_prop_id on public.prop_units using btree (prop_id) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on prop_units for EACH row
execute FUNCTION update_updated_at_column ();

-- 9. PORTFOLIO FINANCIALS (now that portfolios exists)
create table public.portfolio_financials (
  id uuid not null,
  portfolio_id uuid not null,
  year integer not null,
  rental_income double precision null,
  long_term_rental double precision null,
  short_term_rental double precision null,
  other_income double precision null,
  vacancy_loss double precision null,
  credit_loss double precision null,
  effective_gross_income double precision null,
  property_tax double precision null,
  insurance double precision null,
  repairs double precision null,
  maintenance double precision null,
  professional_fees double precision null,
  management_fees double precision null,
  leasing_fees double precision null,
  legal_fees double precision null,
  accounting_fees double precision null,
  engineering_fees double precision null,
  marketing_fees double precision null,
  consulting_fees double precision null,
  utilities double precision null,
  services double precision null,
  reserves double precision null,
  total_operating_expenses double precision null,
  net_operating_income double precision null,
  annual_debt_service double precision null,
  dscr double precision null,
  cash_flow_before_taxes double precision null,
  cash_flow_after_taxes double precision null,
  cumulative_cash_flow double precision null,
  cap_rate double precision null,
  gross_rent_multiplier double precision null,
  equity_multiple double precision null,
  cash_on_cash_return double precision null,
  total_acquisition_cost double precision null,
  aggregated_noi double precision null,
  blended_cap_rate double precision null,
  portfolio_irr double precision null,
  created_at timestamp without time zone not null,
  updated_at timestamp without time zone not null,
  metadata jsonb null,
  constraint portfolio_financials_pkey primary key (id)
) TABLESPACE pg_default;

-- 10. SLIDES DATA (now that portfolios exists)
create table public.slides_data (
  id uuid not null default gen_random_uuid (),
  portfolio_id uuid null default gen_random_uuid (),
  pdf_supabase_url text null,
  pdf_supabase_path text null,
  pdf_name text null,
  pdf_url text null,
  raw_html text null,
  created_at timestamp with time zone not null default now(),
  constraint slides_data_pkey primary key (id),
  constraint slides_data_portfolio_id_fkey foreign KEY (portfolio_id) references portfolios (id)
) TABLESPACE pg_default;

-- 11. DOCUMENTS (references exist, but missing explicit FK constraints - needs portfolios)
create table public.documents (
  id uuid not null default gen_random_uuid (),
  portfolio_id uuid not null,
  property_id uuid not null,
  user_id uuid not null,
  filename text not null,
  supabase_url text not null,
  uploaded_at timestamp with time zone null default now(),
  processed boolean null default false,
  metadata jsonb null,
  is_unit boolean null,
  unit_number text null,
  type text null,
  sub_type text null,
  is_deleted boolean not null default false,
  original_filename text null,
  constraint documents_pkey primary key (id),
  constraint documents_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

-- 12. DATAROOM TABLES
create table public.dataroom_events (
  id bigint generated by default as identity not null,
  user_id uuid null,
  event_type text null,
  timestamp timestamp with time zone not null default now(),
  portfolio_id uuid null,
  constraint dataroom_events_pkey primary key (id),
  constraint dataroom_events_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create table public.dataroom_guests (
  id bigint generated by default as identity not null,
  owner_id uuid null,
  sender_id uuid null,
  receiver_id uuid null,
  can_share boolean null,
  can_download boolean null,
  has_access boolean null,
  created_at timestamp with time zone not null default now(),
  portfolio_id uuid null,
  constraint dataroom_guests_pkey primary key (id),
  constraint dataroom_guests_owner_id_fkey foreign KEY (owner_id) references auth.users (id),
  constraint dataroom_guests_receiver_id_fkey foreign KEY (receiver_id) references auth.users (id),
  constraint dataroom_guests_sender_id_fkey foreign KEY (sender_id) references auth.users (id)
) TABLESPACE pg_default;

-- 13. SEARCH HISTORY
create table public.user_search_history (
  created_at timestamp with time zone not null default now(),
  address_string text null,
  is_deleted boolean null,
  updated_at timestamp with time zone null,
  user_id uuid null,
  id uuid not null,
  constraint user_search_history_pkey primary key (id),
  constraint user_search_history_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

-- 14. DEMOGRAPHIC/ZIP TABLES (no dependencies)
create table public.zip_demographics (
  id uuid not null default extensions.uuid_generate_v4 (),
  zipcode character varying(20) not null,
  city character varying(100) not null,
  snapshot_date date not null,
  population integer null,
  median_age double precision null,
  median_hh_income numeric(12, 2) null,
  avg_hh_income numeric(12, 2) null,
  crime_risk integer null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint zip_demographics_pkey primary key (id),
  constraint zip_demographics_zipcode_snapshot_date_key unique (zipcode, snapshot_date)
) TABLESPACE pg_default;

create index IF not exists idx_zip_demographics_zipcode on public.zip_demographics using btree (zipcode) TABLESPACE pg_default;

create index IF not exists idx_zip_demographics_city on public.zip_demographics using btree (city) TABLESPACE pg_default;

create index IF not exists idx_zip_demographics_snapshot_date on public.zip_demographics using btree (snapshot_date) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on zip_demographics for EACH row
execute FUNCTION update_updated_at_column ();

create table public.zipcode_mapping (
  id uuid not null default extensions.uuid_generate_v4 (),
  zipcode character varying(20) not null,
  city character varying(100) not null,
  state character varying(50) not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint zipcode_mapping_pkey primary key (id),
  constraint zipcode_mapping_zipcode_city_state_key unique (zipcode, city, state)
) TABLESPACE pg_default;

create index IF not exists idx_zipcode_mapping_zipcode on public.zipcode_mapping using btree (zipcode) TABLESPACE pg_default;

create index IF not exists idx_zipcode_mapping_city_state on public.zipcode_mapping using btree (city, state) TABLESPACE pg_default;

create trigger update_updated_at BEFORE
update on zipcode_mapping for EACH row
execute FUNCTION update_updated_at_column (); 