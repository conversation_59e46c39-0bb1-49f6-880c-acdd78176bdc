import { useAuth } from "@/context/AuthContext";
import { useEffect, useRef, useState } from "react"
import { createClient } from "@/utils/supabase/client";

export default function useWebsocketPortfolio(chat_id: string) {
    const [messages, setMessages] = useState<{ [key: string]: any }[]>([])
    const [messagesGenerated, setMessagesGenerated] = useState<{ [key: string]: any }[]>([])
    const [isLoadingResponse, setIsLoadingResponse] = useState(false)
    const { isLoadingUserData, user } = useAuth()
    const socketRef = useRef<WebSocket | null>(null)
    const reconnectIntervalRef = useRef<NodeJS.Timeout | null>(null)

    const connectWebSocket = (token: string, chat_id: string) => {
        if(!socketRef.current && token && chat_id){
            
            const wsUrl = `${process.env.NODE_ENV === 'development' ? 'ws' : 'wss'}://${process.env.NEXT_PUBLIC_WS_DOMAIN}${chat_id}?token=${token}`;

            const ws = new WebSocket(`${wsUrl}`);

            ws.onopen = function () {
                console.log('connected to websocket')
            }

            ws.onmessage = function (event) {
                console.log('message from websocket', event)

                const eventData = JSON.parse(event.data);
                
                if(eventData?.role === 'assistant' && eventData?.content){
                    setMessages((prevMessages) => [...prevMessages, eventData])
                    setMessagesGenerated([])
                    setIsLoadingResponse(false)
                }

                if(eventData?.role === 'update' && eventData?.content){
                    setMessagesGenerated((prevMessages) => [...prevMessages, eventData])
                }
            }

            ws.onclose = function (event) {
				if (event.wasClean) {
					console.log(`[close] connection close, code=${event.code} reason=${event.reason}`);
				} else {
					console.log('[close] connection close');
                    attemptReconnect()
				}
			};

			ws.onerror = function (error) {
				if (process.env.NODE_ENV === 'development') {
					console.log(`[error] ${error}`)
					console.log(error);
                    attemptReconnect()
				}
			};

            socketRef.current = ws;
        }
        
    }

    const attemptReconnect = async () => {
		if (reconnectIntervalRef.current) return;
		socketRef.current = null

		reconnectIntervalRef.current = setTimeout(() => {
			console.log("[reconnect] attempting to reconnect...");
			connectWebSocket(user?.access_token as string, chat_id);
			reconnectIntervalRef.current = null;
		}, 5000);
	};

    const handleReconnectOnClick = async () => {
		if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
			socketRef.current = null
			console.log("[click] reconnecting...");
			connectWebSocket(user?.access_token as string, chat_id);
		}
	};

    useEffect(() => {
        if(!isLoadingUserData && user?.access_token && chat_id) {
            connectWebSocket(user?.access_token, chat_id)
            document.addEventListener('click', handleReconnectOnClick);

            setIsLoadingResponse(false)
        }

        return () => {
			document.removeEventListener('click', handleReconnectOnClick);

			if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
				socketRef.current.close();
				socketRef.current = null;
			}
			if (reconnectIntervalRef.current) {
				clearTimeout(reconnectIntervalRef.current);
				reconnectIntervalRef.current = null;
			}
        }
    }, [isLoadingUserData, user?.access_token, chat_id])

    // Helper function to upload files to Supabase storage
    const uploadFiles = async (files: File[]): Promise<string[]> => {
        const supabase = createClient();
        const fileUrls: string[] = [];
        
        for (const file of files) {
            const fileExt = file.name.split('.').pop();
            const fileName = `${chat_id}/${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
            
            const { data, error } = await supabase.storage
                .from('chat_images')
                .upload(fileName, file);
                
            if (error) {
                console.error('Error uploading file:', error);
                continue;
            }
            
            const { data: urlData } = supabase.storage
                .from('chat_images')
                .getPublicUrl(fileName);
                
            if (urlData.publicUrl) {
                fileUrls.push(urlData.publicUrl);
            }
        }
        
        return fileUrls;
    };

    const handleSendMessageChat = async (message: string, files?: File[]) => {
        let attachmentUrls: string[] = [];
        
        // Upload files if any
        if (files && files.length > 0) {
            attachmentUrls = await uploadFiles(files);
        }
        
        // Add message to UI immediately
        setMessages((prevMessages) => [...prevMessages, {
            role: 'user',
            content: {
                text: message,
                image_urls: attachmentUrls
                /*attachments: attachmentUrls.map(url => ({
                    type: 'image', // For now assuming all are images, but could check file type
                    url: url
                }))*/
            }
        }]);

        setIsLoadingResponse(true);

        // Send message to websocket
        const jsonMessage = JSON.stringify({
            text: message,
            image_urls: attachmentUrls
            /*attachments: attachmentUrls.map(url => ({
                type: 'image', // Same assumption as above
                url: url
            }))*/
        });

        socketRef.current?.send(jsonMessage);
    };

    return {
        messages,
        setMessages,
        handleSendMessageChat,
        messagesGenerated,
        isLoadingResponse
    }
}