import { getChatHistory } from "@/actions/chatActions"
import { ChatHistoryType } from "@/types/ChatHistoryType"
import { useEffect, useState } from "react"

export default function useHistory(chat_id: string) {
    const [history, setHistory] = useState<ChatHistoryType | null>(null)

    useEffect(() => {
        if(chat_id) {
            getChatHistory(chat_id).then((data) => {
                setHistory(data)
            })
        }
    }, [chat_id])

    return {
        history,
        setHistory
    }
}