import { useRef, useEffect } from "react";
import { addToSearchHistory } from "@/utils/searchHistory";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";

export const useRecentSearch = () => {
    const { selectedStates, handleSelectedLocation, updateMarketAnalysisState, selectedLocation } = useMarketAnalysis();
    
    const handleRecentSearchSelect = (address: string) => {
        if (!geocoderRef.current || !window.google?.maps) {
            console.error("Google Maps API not loaded yet");
            return;
        }
        
        geocoderRef.current.geocode({ address }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
                const place = {
                    address_components: results[0].address_components,
                    formatted_address: results[0].formatted_address,
                    geometry: results[0].geometry,
                };
                
                // Extract state from address components
                const stateComponent = place.address_components?.find(
                    component => component.types.includes('administrative_area_level_1')
                );
                
                const stateCode = stateComponent?.short_name;
                const stateName = stateComponent?.long_name;
                
                // Check if the state is in the selectedStates list
                const stateIsSelected = selectedStates.some(
                    state => state.abbreviation === stateCode || state.name === stateName
                );
                
                if (stateIsSelected || selectedStates.length === 0) {
                    handleSelectedLocation(selectedLocation, place.formatted_address, place.geometry?.location?.lat() || 0, place.geometry?.location?.lng() || 0);
                    updateMarketAnalysisState({ searchPlace: place, errorState: null });
                    
                    // Store search history locally in cookies
                    try {
                        addToSearchHistory(place.formatted_address);
                    } catch (error) {
                        console.error('Error storing local search history:', error);
                    }
                } else {
                    updateMarketAnalysisState({ 
                        searchPlace: '', 
                        errorState: { name: stateName || '', abbreviation: stateCode || '' } 
                    });
                }
            } else {
                console.error("Geocoding failed:", status);
            }
        });
    };

    // Add geocoder ref for recent search functionality
    const geocoderRef = useRef<google.maps.Geocoder | null>(null);

    // Initialize geocoder when Google Maps loads
    useEffect(() => {
        const initGeocoder = () => {
            if (window.google?.maps) {
                geocoderRef.current = new google.maps.Geocoder();
            } else {
                setTimeout(initGeocoder, 1000);
            }
        };
        initGeocoder();
    }, []);

    return {
        handleRecentSearchSelect
    }
}