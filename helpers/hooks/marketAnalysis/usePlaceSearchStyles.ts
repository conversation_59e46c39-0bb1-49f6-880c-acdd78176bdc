import { useEffect } from 'react';

export const usePlaceSearchStyles = () => {
    useEffect(() => {
        const style = document.createElement('style');
        style.textContent = `
            .pac-container {
                border-radius: 0.5rem;
                margin-top: 4px;
                border: 1px solid #e2e8f0;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                font-family: inherit;
                z-index: 1100;
            }
            .pac-item {
                padding: 8px 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
            }
            .pac-item:hover, .pac-item-selected {
                background-color: #f3f4f6;
            }
            .pac-icon {
                display: none;
            }
            .pac-item::before {
                content: '';
                margin-right: 10px;
                font-size: 16px;
            }
            .pac-item-query {
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
            }
            .pac-matched {
                font-weight: 600;
            }
            .pac-secondary-text {
                font-size: 12px;
                color: #6b7280;
                margin-left: 4px;
            }
        `;
        document.head.appendChild(style);
        
        return () => {
            document.head.removeChild(style);
        };
    }, []);
}