import { downloadAllPortfolioDocuments } from "@/actions/dataRoomActions";
import { usePortfolio } from "@/context/PortfolioContext";
import { useState } from "react";

export const useDownloadAllDocuments = () => {
    const [isDownloading, setIsDownloading] = useState(false);
    const [downloadProgress, setDownloadProgress] = useState({ current: 0, total: 0, status: '', isComplete: false, isError: false });
    const { selectedPortfolio } = usePortfolio();

    
    const handleDownloadAll = async () => {
        if (!selectedPortfolio?.id || isDownloading) return;
    
        try {
          setIsDownloading(true);
          setDownloadProgress({ current: 0, total: 0, status: 'Preparing documents...', isComplete: false, isError: false });
    
          // Import JSZip dynamically
          const JSZip = (await import('jszip')).default;
          
          const result = await downloadAllPortfolioDocuments(selectedPortfolio.id);
          
          if (!result.success) {
            throw new Error('Failed to get document list');
          }
    
          if (result.totalFiles === 0) {
            throw new Error('No documents found in this portfolio');
          }
    
          setDownloadProgress({ current: 0, total: result.totalFiles, status: 'Starting download...', isComplete: false, isError: false });
    
          // Create zip file
          const zip = new JSZip();
          let downloadedCount = 0;
          let failedCount = 0;
    
          // Download and add files to zip
          for (const file of result.files) {
            try {
              setDownloadProgress({ 
                current: downloadedCount, 
                total: result.totalFiles, 
                status: `Downloading: ${file.name.length > 30 ? file.name.substring(0, 27) + '...' : file.name}`,
                isComplete: false,
                isError: false
              });
    
              const response = await fetch(file.url);
              if (response.ok) {
                const blob = await response.blob();
                zip.file(file.path, blob);
                downloadedCount++;
              } else {
                failedCount++;
                console.warn(`Failed to download ${file.name}: HTTP ${response.status}`);
              }
              
              setDownloadProgress({ 
                current: downloadedCount, 
                total: result.totalFiles, 
                status: `Downloaded ${downloadedCount}/${result.totalFiles} files${failedCount > 0 ? ` (${failedCount} failed)` : ''}`,
                isComplete: false,
                isError: false
              });
            } catch (error) {
              failedCount++;
              console.warn(`Failed to download ${file.name}:`, error);
            }
          }
    
          if (downloadedCount === 0) {
            throw new Error('No files could be downloaded. Please check your connection and try again.');
          }
    
          // Generate and download zip
          setDownloadProgress({ 
            current: downloadedCount, 
            total: result.totalFiles, 
            status: 'Creating zip file...',
            isComplete: false,
            isError: false
          });
    
          const zipBlob = await zip.generateAsync({ 
            type: 'blob',
            compression: 'DEFLATE',
            compressionOptions: { level: 6 }
          });
          
          const url = URL.createObjectURL(zipBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${result.portfolioName}_Documents.zip`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
    
          const successMessage = failedCount > 0 
            ? `Downloaded ${downloadedCount} files successfully! ${failedCount} files failed to download.`
            : `Successfully downloaded all ${downloadedCount} files!`;
          
          setDownloadProgress({ 
            current: downloadedCount, 
            total: result.totalFiles, 
            status: successMessage,
            isComplete: true,
            isError: false
          });
    
        } catch (error) {
          console.error('Error downloading documents:', error);
          const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
          setDownloadProgress({ 
            current: 0, 
            total: 0, 
            status: `Error: ${errorMessage}`,
            isComplete: false,
            isError: true
          });
          
          // Show user-friendly alert
          if (errorMessage.includes('No documents found')) {
            alert('This portfolio doesn\'t contain any documents to download.');
          } else if (errorMessage.includes('No files could be downloaded')) {
            alert('Unable to download files. Please check your internet connection and try again.');
          } else {
            alert('Failed to download documents. Please try again or contact support if the problem persists.');
          }
        } finally {
          setIsDownloading(false);
          setTimeout(() => {
            setDownloadProgress({ current: 0, total: 0, status: '', isComplete: false, isError: false });
          }, 4000); // Show final status for 4 seconds
        }
      };

    return {
        isDownloading,
        downloadProgress,
        setIsDownloading,
        setDownloadProgress,
        handleDownloadAll,
    }
}