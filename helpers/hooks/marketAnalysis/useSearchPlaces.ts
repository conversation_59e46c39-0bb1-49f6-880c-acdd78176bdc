import { useState } from "react";

const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 3958.8; // Radius of the Earth in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return parseFloat(distance.toFixed(2));
};

const capitalizePlaceName = (name: string): string => {
    if (!name) return '';
    
    // Special case for acronyms and common brand names that should be all caps
    const allCapsTerms = ['cvs', 'kfc', 'ikea', 'amc', 'bp', 'atm', 'h&m', 'tj', 'rbc', 'ups', 'usps'];
    
    // Words that should remain lowercase (unless at beginning of sentence)
    const lowercaseWords = ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'of'];
    
    // Split the name into words
    return name.split(' ').map((word, index) => {
      // Check if it's a word that should be all caps
      if (allCapsTerms.includes(word.toLowerCase())) {
        return word.toUpperCase();
      }
      
      // For words with apostrophes like "joe's"
      if (word.includes("'")) {
        const parts = word.split("'");
        // Capitalize first part, keep apostrophe, properly handle second part
        return parts[0].charAt(0).toUpperCase() + parts[0].slice(1) + "'" + 
          (parts[1] ? parts[1].toLowerCase() : '');
      }
      
      // Handle lowercase words (not at the beginning)
      if (index > 0 && lowercaseWords.includes(word.toLowerCase())) {
        return word.toLowerCase();
      }
      
      // Default: capitalize first letter
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    }).join(' ');
};

export default function useSearchPlaces(searchQuery: string, searchPlace: { [key: string]: any }) {
    const [searchResults, setSearchResults] = useState<{ [key: string]: any }[]>([])

    const searchPlaces = async () => {
        if(!searchQuery) return;
        if (typeof searchPlace === 'string' || !searchPlace?.geometry?.location) return;

        const latitude = searchPlace.geometry.location.lat()
        const longitude = searchPlace.geometry.location.lng()

        if(window.google && window.google.maps && latitude && longitude){
            const placesService = new google.maps.places.PlacesService(document.createElement('div'));
    
            // Create request for nearby search
            const request: google.maps.places.TextSearchRequest = {
                query: searchQuery,
                location: new google.maps.LatLng(latitude, longitude),
                radius: 5000 // 5km radius
            };

            const results = await new Promise<google.maps.places.PlaceResult[]>((resolve) => {
                placesService.textSearch(request, (results, status) => {
                  if (status === google.maps.places.PlacesServiceStatus.OK && results) {
                    resolve(results);
                  } else {
                    console.log(`No text search results found. Status: ${status}`);
                        resolve([]);
                    }
                });
            });

            const newResults = results.slice(0, 2).map(place => {
                const distance = place.geometry?.location 
                  ? calculateDistance(
                      latitude,
                      longitude,
                      place.geometry.location.lat(),
                      place.geometry.location.lng()
                    )
                  : 0;
                  
                return {
                  business_name: capitalizePlaceName(place.name || ''),
                  street: place.formatted_address || '',
                  latitude: place.geometry?.location?.lat(),
                  longitude: place.geometry?.location?.lng(),
                  distance: distance,
                  line_of_business: place.types?.[0].split('_').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)).join(' ') || 'Custom Location',
                  place_id: place.place_id
                };
            });

            console.log(newResults)

            setSearchResults(newResults);
        }

    };
    
    return {
        searchPlaces,
        searchResults,
        setSearchResults
    }
}