import { faBuilding, faClipboardList, faHome, faMapMarkerAlt, faUsers, IconDefinition } from "@fortawesome/free-solid-svg-icons";
import { useRef, useState, useEffect, RefObject, useCallback } from "react";

// Define types for section IDs and nav items
export type SectionId = 'building' | 'tax-history' | 'street-view' | 'units' | 'solar' | 'locations' | 'map' | 'ai-summary';

export interface NavItem {
    id: SectionId;
    label: string;
    icon: IconDefinition;
}

// Type for section refs that properly handles null
type SectionRefObject = RefObject<HTMLDivElement | null>;

export const useSidebarNavigation = (addressId: string | null, searchPlace: any) => {
    const [activeSection, setActiveSection] = useState<SectionId>('building');
    
    // Create refs for sections
    const buildingRef = useRef<HTMLDivElement>(null);
    const taxHistoryRef = useRef<HTMLDivElement>(null);
    const unitsRef = useRef<HTMLDivElement>(null);
    const solarRef = useRef<HTMLDivElement>(null);
    const locationsRef = useRef<HTMLDivElement>(null);
    const aiSummaryRef = useRef<HTMLDivElement>(null);
    
    // Group the refs in an object for convenience
    const sectionRefs = {
        building: buildingRef,
        aiSummary: aiSummaryRef,
        taxHistory: taxHistoryRef,
        units: unitsRef,
        solar: solarRef,
        locations: locationsRef,        
    } as const;

    // Function to scroll to a specific section
    const scrollToSection = useCallback((sectionId: SectionId) => {
        // Map section IDs to refs
        const refMap: { [key in SectionId]?: RefObject<HTMLDivElement | null> } = {
            'building': buildingRef,
            'ai-summary': aiSummaryRef,
            'tax-history': taxHistoryRef,
            'units': unitsRef,
            'solar': solarRef,
            'locations': locationsRef,
        };

        const targetRef = refMap[sectionId];
        if (targetRef?.current) {
            // Calculate the offset for fixed headers/navigation
            const offset = 100; // Adjust this value based on your header height
            const elementPosition = targetRef.current.offsetTop;
            const offsetPosition = elementPosition - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });

            // Update active section immediately
            setActiveSection(sectionId);
        }
    }, []);

    // Set up intersection observer to track active section
    useEffect(() => {
        // Only set up observer if we have sections to observe
        if (!addressId || typeof searchPlace === 'string') return;

        const observerOptions = {
            root: null,
            rootMargin: '-50px 0px -50px 0px', // Trigger when section is 50px from top/bottom
            threshold: 0.3 // At least 30% of the section should be visible
        };

        const observerCallback = (entries: IntersectionObserverEntry[]) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const sectionId = entry.target.id as SectionId;
                    if (sectionId) {
                        setActiveSection(sectionId);
                    }
                }
            });
        };

        const observer = new IntersectionObserver(observerCallback, observerOptions);

        // Observe all section elements
        const sectionsToObserve = [
            { ref: buildingRef, id: 'building' },
            { ref: aiSummaryRef, id: 'ai-summary' },
            { ref: taxHistoryRef, id: 'tax-history' },
            { ref: unitsRef, id: 'units' },
            { ref: solarRef, id: 'solar' },
            { ref: locationsRef, id: 'locations' }
        ];

        sectionsToObserve.forEach(({ ref, id }) => {
            if (ref.current) {
                // Ensure the element has the correct ID for identification
                ref.current.id = id;
                observer.observe(ref.current);
            }
        });

        // Cleanup
        return () => {
            observer.disconnect();
        };
    }, [addressId, searchPlace]);

    return {
        activeSection,
        sectionRefs,
        scrollToSection,
        setActiveSection
    };
};