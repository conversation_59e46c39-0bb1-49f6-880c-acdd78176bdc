import { useAuth } from "@/context/AuthContext"
import { useParams } from "next/navigation"

export const useCheckPay = () => {
    const {workspaceSubscriptionDetails, userDataDB} = useAuth()
    const {id} = useParams()
    
    const isPay = workspaceSubscriptionDetails?.find((item: any) => item?.id === id)?.has_active_subscription === true

    return {
        isPay: userDataDB?.is_superuser ? true : isPay
    }
}