import { useCallback, useState } from "react";

export default function useDragAndDrop() {
    const [isDragging, setIsDragging] = useState(false);
	const [files, setFiles] = useState<File[]>([]);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	}, []);

    const isPdfFile = (file: File): boolean => {
        return file.type === 'application/pdf';
    };

	const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
        setErrorMessage(null);
		
		if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
			const droppedFiles = Array.from(e.dataTransfer.files);
            const pdfFiles = droppedFiles.filter(isPdfFile);
            
            if (pdfFiles.length === 0) {
                setErrorMessage("Only PDF files are accepted");
                return;
            }
            
            if (pdfFiles.length !== droppedFiles.length) {
                setErrorMessage("Some files were ignored. Only PDF files are accepted");
            }
            
			setFiles([...files, ...pdfFiles]);
		}
	}, [files]);

	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setErrorMessage(null);
		if (e.target.files && e.target.files.length > 0) {
			const selectedFiles = Array.from(e.target.files);
            const pdfFiles = selectedFiles.filter(isPdfFile);
            
            if (pdfFiles.length === 0) {
                setErrorMessage("Only PDF files are accepted");
                return;
            }
            
            if (pdfFiles.length !== selectedFiles.length) {
                setErrorMessage("Some files were ignored. Only PDF files are accepted");
            }
            
			setFiles([...files, ...pdfFiles]);
		}
	};

	const removeFile = (index: number) => {
		setFiles(files.filter((_, i) => i !== index));
	};

	return {
		isDragging,
		files,
        setFiles,
		errorMessage,
		handleDragOver,
		handleDragLeave,
		handleDrop,
		handleFileChange,
		removeFile,
	};
}