import { deletePortfolio, getPortfolios } from "@/actions/portfolioActions";
import modalTriggerType from "@/constants/modalTriggerType";
import { useModal } from "@/context/ModalContext";
import { useEffect, useState } from "react";

export default function usePortfolio1(portfolioId: string) {
    const { modalTrigger, updateModalTrigger} = useModal()
    const [portfolios, setPortfolios] = useState<any[]>([]);
    const [isLoadingPortfolios, setIsLoadingPortfolios] = useState<boolean>(false);
    
    useEffect(() => {
        setIsLoadingPortfolios(true)
        getPortfolios(portfolioId as string || '').then(data => {
          setPortfolios(data)
          setIsLoadingPortfolios(false)
        });
        
    }, [portfolioId]);

    useEffect(() => {
		if(modalTrigger === modalTriggerType.createPortfolio) { 
			getPortfolios(portfolioId as string || '').then(data => {
				setPortfolios(data)
				updateModalTrigger(null)
			});
		}
	}, [modalTrigger]);

    const handleDeletePortfolio = (id: string) => {
		deletePortfolio(id)
		setPortfolios((prevState) => prevState.filter((item) => item.id !== id));
	}

    return {
        portfolios,
        handleDeletePortfolio,
        isLoadingPortfolios,
    }
}
