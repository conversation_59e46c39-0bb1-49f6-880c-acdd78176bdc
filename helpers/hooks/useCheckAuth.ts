import { useAuth } from "@/context/AuthContext"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function useCheckAuth() {
    const { isLoadingUserData, user } = useAuth()
    const {push} = useRouter()

    useEffect(() => {
        if(!isLoadingUserData && !user) {
            push('/login')
        }
    }, [isLoadingUserData, user])

    return { isLoadingUserData, user }
}