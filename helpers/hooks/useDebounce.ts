import { useCallback, useEffect, useRef } from 'react';

export function useDebounce(callback: (...args: any) => void, delay: number) {
	const timeoutRef = useRef<number | undefined>(undefined);        

	const debouncedCallback = useCallback(
		(...args: any) => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
			timeoutRef.current = window.setTimeout(() => {
				callback(...args);
			}, delay);
		},
		[callback, delay]
	);

	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	return debouncedCallback;
}