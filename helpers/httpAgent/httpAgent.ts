import { createClient } from "@/utils/supabase/server";


export default async function httpAgent(
	method: 'POST' | 'GET' | 'PUT' | 'PATCH' |'DELETE',
	url: string,
	options?: {
		body?: { [key: string]: string | number | object | boolean },
		next?: {
			revalidate?: false | number;
			tags?: string[];
		},
		headers?: { [key: string]: string }
	}
){

	const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
        throw new Error('User not authenticated');
    }

    const token = session.access_token;

	const requestOptions: RequestInit & { next?: { revalidate?: false | number, tags?: string[] } } = {
		method,
		headers: {
			'Authorization': `Bearer ${token}`,
			'Content-Type' : 'application/json',
		},
	};

	if(options)
	{
		if(options.headers)
		{
			requestOptions.headers = {...requestOptions.headers, ...options.headers}
		}

		if(options.body && method !== 'GET'){
			requestOptions.body = JSON.stringify(options.body)
		}

		if(options.next){
			requestOptions.next = {...options.next}
		}
	}


	const response = await fetch(`${url}`, requestOptions)
	const data = await response.json()

	return await data;
}