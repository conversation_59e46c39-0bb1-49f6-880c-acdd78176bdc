import { getCookie, set<PERSON><PERSON>ie, deleteCookie } from 'cookies-next';

export interface SearchHistoryItem {
    address: string;
    searchedAt: string;
    id: string;
}

const SEARCH_HISTORY_COOKIE_NAME = 'relm_search_history';
const MAX_HISTORY_ITEMS = 10;

/**
 * Get search history from cookies
 */
export const getSearchHistory = (): SearchHistoryItem[] => {
    try {
        const cookieValue = getCookie(SEARCH_HISTORY_COOKIE_NAME);
        if (!cookieValue) return [];
        
        const history = JSON.parse(cookieValue as string);
        return Array.isArray(history) ? history : [];
    } catch (error) {
        console.error('Error getting search history:', error);
        return [];
    }
};

/**
 * Add a new search to history
 */
export const addToSearchHistory = (address: string): SearchHistoryItem[] => {
    try {
        const currentHistory = getSearchHistory();
        
        // Remove duplicate addresses
        const filteredHistory = currentHistory.filter(item => item.address !== address);
        
        // Create new history item
        const newItem: SearchHistoryItem = {
            address,
            searchedAt: new Date().toISOString(),
            id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        
        // Add to beginning and limit to MAX_HISTORY_ITEMS
        const updatedHistory = [newItem, ...filteredHistory].slice(0, MAX_HISTORY_ITEMS);
        
        // Save to cookie with 30 days expiration
        setCookie(SEARCH_HISTORY_COOKIE_NAME, JSON.stringify(updatedHistory), {
            maxAge: 60 * 60 * 24 * 30, // 30 days
            sameSite: 'lax'
        });
        
        return updatedHistory;
    } catch (error) {
        console.error('Error adding to search history:', error);
        return getSearchHistory();
    }
};

/**
 * Remove a specific search from history
 */
export const removeFromSearchHistory = (id: string): SearchHistoryItem[] => {
    try {
        const currentHistory = getSearchHistory();
        const updatedHistory = currentHistory.filter(item => item.id !== id);
        
        setCookie(SEARCH_HISTORY_COOKIE_NAME, JSON.stringify(updatedHistory), {
            maxAge: 60 * 60 * 24 * 30, // 30 days
            sameSite: 'lax'
        });
        
        return updatedHistory;
    } catch (error) {
        console.error('Error removing from search history:', error);
        return getSearchHistory();
    }
};

/**
 * Clear all search history
 */
export const clearSearchHistory = (): void => {
    try {
        deleteCookie(SEARCH_HISTORY_COOKIE_NAME);
    } catch (error) {
        console.error('Error clearing search history:', error);
    }
};

/**
 * Format date for display
 */
export const formatSearchDate = (dateString: string): string => {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        
        if (diffMinutes < 60) {
            return diffMinutes <= 1 ? 'Just now' : `${diffMinutes}m ago`;
        } else if (diffHours < 24) {
            return `${diffHours}h ago`;
        } else if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays}d ago`;
        } else {
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
            });
        }
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Unknown';
    }
}; 