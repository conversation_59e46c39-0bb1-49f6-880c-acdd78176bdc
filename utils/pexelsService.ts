interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

interface PexelsResponse {
  total_results: number;
  page: number;
  per_page: number;
  photos: PexelsPhoto[];
  next_page?: string;
}

/**
 * Fetch real estate images from Pexels API
 */
export async function fetchPexelsImage(query: string = 'real estate'): Promise<string | null> {
  try {
    const apiKey = process.env.PEXELS_API_KEY;
    
    if (!apiKey) {
      console.warn('PEXELS_API_KEY not found in environment variables');
      return null;
    }

    // Real estate related search terms
    const realEstateQueries = [
      'real estate',
      'house exterior',
      'modern home',
      'apartment building',
      'residential building',
      'property investment',
      'home architecture',
      'building facade'
    ];

    // Use provided query or pick a random real estate term
    const searchQuery = query === 'real estate' 
      ? realEstateQueries[Math.floor(Math.random() * realEstateQueries.length)]
      : query;

    const response = await fetch(
      `https://api.pexels.com/v1/search?query=${encodeURIComponent(searchQuery)}&per_page=1&orientation=landscape`,
      {
        headers: {
          'Authorization': apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Pexels API error: ${response.status}`);
    }

    const data: PexelsResponse = await response.json();

    if (data.photos && data.photos.length > 0) {
      const photo = data.photos[0];
      // Return the large size for good quality
      return photo.src.large;
    }

    return null;
  } catch (error) {
    console.error('Error fetching Pexels image:', error);
    return null;
  }
}

/**
 * Get multiple Pexels images for variety
 */
export async function fetchMultiplePexelsImages(count: number = 5): Promise<string[]> {
  try {
    const apiKey = process.env.PEXELS_API_KEY;
    
    if (!apiKey) {
      console.warn('PEXELS_API_KEY not found in environment variables');
      return [];
    }

    const realEstateQueries = [
      'real estate',
      'house exterior',
      'modern home',
      'apartment building',
      'residential building'
    ];

    const randomQuery = realEstateQueries[Math.floor(Math.random() * realEstateQueries.length)];

    const response = await fetch(
      `https://api.pexels.com/v1/search?query=${encodeURIComponent(randomQuery)}&per_page=${count}&orientation=landscape`,
      {
        headers: {
          'Authorization': apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Pexels API error: ${response.status}`);
    }

    const data: PexelsResponse = await response.json();

    if (data.photos && data.photos.length > 0) {
      return data.photos.map(photo => photo.src.large);
    }

    return [];
  } catch (error) {
    console.error('Error fetching multiple Pexels images:', error);
    return [];
  }
} 