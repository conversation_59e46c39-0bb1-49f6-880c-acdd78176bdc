// FRED series IDs for real estate related data
const FRED_SERIES = {
  mortgage_rates: 'MORTGAGE30US',
  home_sales: 'EXHOSLUSM495S',
  home_prices: 'CSUSHPISA',
  housing_starts: 'HOUST',
  rental_vacancy: 'RRVRUSQ156N',
  construction_spending: 'TLRESCONS',
  real_estate_loans: 'REALLN',
  homeownership_rate: 'RHORUSQ156N',
  pending_home_sales: 'PHSUSQ',
  new_home_sales: 'HSN1F',
  median_home_price: 'MSPUS',
  housing_inventory: 'MSACSR'
};

/**
 * Get total number of available FRED chart pages
 */
export function getFredChartPages(chartsPerPage: number = 3): number {
  return Math.ceil(Object.keys(FRED_SERIES).length / chartsPerPage);
}

/**
 * Get total number of FRED series
 */
export function getTotalFredSeries(): number {
  return Object.keys(FRED_SERIES).length;
} 