/**
 * Format a number as currency (USD)
 * @param value Number to format
 * @param minimumFractionDigits Minimum number of decimal places (default 0)
 * @returns Formatted currency string
 */
export function formatCurrency(value: number, minimumFractionDigits = 0): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits,
    maximumFractionDigits: 2
  }).format(value);
}

export function formatCurrencyNoSymbol(value: number, minimumFractionDigits = 0): string {
  return new Intl.NumberFormat('en-US', {
    style: 'decimal',
    minimumFractionDigits,
    maximumFractionDigits: 2
  }).format(value);
}

/**
 * Format a number with commas for thousands
 * @param value Number to format
 * @returns Formatted number string
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US').format(value);
}

/**
 * Format a date string or Date object to "May 13th, 2025" format
 * @param dateInput Date string or Date object to format
 * @returns Formatted date string
 */
export function formatDate(dateInput: string | Date): string {
  if (!dateInput) return 'N/A';
  
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const year = date.getFullYear();
    
    // Add ordinal suffix to day
    const getOrdinalSuffix = (day: number): string => {
      if (day >= 11 && day <= 13) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };
    
    return `${month} ${day}${getOrdinalSuffix(day)}, ${year}`;
  } catch (error) {
    return String(dateInput);
  }
}

/**
 * Format a date string or Date object to "May 13th, 2025" format for market data
 * @param dateInput Date string or Date object to format
 * @returns Formatted date string with ordinal suffix
 */
export function formatMarketDate(dateInput: string | Date): string {
  if (!dateInput) return 'N/A';
  
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const year = date.getFullYear();
    
    // Add ordinal suffix to day
    const getOrdinalSuffix = (day: number): string => {
      if (day >= 11 && day <= 13) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };
    
    return `${month} ${day}${getOrdinalSuffix(day)}, ${year}`;
  } catch (error) {
    return String(dateInput);
  }
}

/**
 * Canonicalize listing types to standard values
 */
export function canonicalizeListingType(listingType: string): string {
    if (!listingType) return 'unknown';
    
    const type = listingType.toLowerCase().trim();
    
    // Define canonical mappings
    const canonicalMappings: { [key: string]: string } = {
        // For Rent variations
        'for_rent': 'for_rent',
        'for rent': 'for_rent',
        'forrent': 'for_rent',
        'rental': 'for_rent',
        'rental listing': 'for_rent',
        'rent': 'for_rent',
        'available for rent': 'for_rent',
        'apartment for rent': 'for_rent',
        'unit for rent': 'for_rent',
        
        // Rented variations
        'rented': 'rented',
        'leased': 'rented',
        'lease agreement': 'rented',
        'lease': 'rented',
        'occupied': 'rented',
        'tenant occupied': 'rented',
        'rental agreement': 'rented',
        
        // For Sale variations
        'for_sale': 'for_sale',
        'for sale': 'for_sale',
        'forsale': 'for_sale',
        'sale': 'for_sale',
        'listing': 'for_sale',
        'active listing': 'for_sale',
        'market listing': 'for_sale',
        
        // Sold variations
        'sold': 'sold',
        'closed': 'sold',
        'completed sale': 'sold',
        'transaction completed': 'sold',
        
        // Pending variations
        'pending': 'pending',
        'under contract': 'pending',
        'contingent': 'pending',
        'pending sale': 'pending',
        'offer accepted': 'pending',
        
        // Analysis/Research variations
        'market analysis': 'market_analysis',
        'market data': 'market_analysis',
        'research': 'market_analysis',
        'comparable': 'market_analysis',
        'comp': 'market_analysis',
        'market research': 'market_analysis',
        'analysis': 'market_analysis',
        
        // Off Market variations
        'off_market': 'off_market',
        'off market': 'off_market',
        'not listed': 'off_market',
        'private': 'off_market',
        'withdrawn': 'off_market',
        'expired': 'off_market'
    };
    
    // First try exact match
    if (canonicalMappings[type]) {
        return canonicalMappings[type];
    }
    
    // Then try partial matches
    for (const [key, value] of Object.entries(canonicalMappings)) {
        if (type.includes(key) || key.includes(type)) {
            return value;
        }
    }
    
    // Default fallback based on common patterns
    if (type.includes('rent')) {
        return 'for_rent';
    } else if (type.includes('sale') || type.includes('sell')) {
        return 'for_sale';
    } else if (type.includes('sold') || type.includes('closed')) {
        return 'sold';
    } else if (type.includes('pending') || type.includes('contract')) {
        return 'pending';
    } else if (type.includes('lease')) {
        return 'rented';
    } else if (type.includes('analysis') || type.includes('market') || type.includes('comp')) {
        return 'market_analysis';
    }
    
    return 'unknown';
}

/**
 * Get display name for canonical listing type
 */
export function getListingTypeDisplayName(canonicalType: string): string {
    const displayNames: { [key: string]: string } = {
        'for_rent': 'For Rent',
        'rented': 'Rented',
        'for_sale': 'For Sale',
        'sold': 'Sold',
        'pending': 'Pending',
        'market_analysis': 'Market Analysis',
        'off_market': 'Off Market',
        'unknown': 'Unknown'
    };
    
    return displayNames[canonicalType] || canonicalType;
}

/**
 * Get CSS classes for listing type badge
 */
export function getListingTypeBadgeClasses(canonicalType: string): string {
    const badgeClasses: { [key: string]: string } = {
        'for_rent': 'bg-green-100 text-green-700',
        'rented': 'bg-blue-100 text-blue-700', 
        'for_sale': 'bg-purple-100 text-purple-700',
        'sold': 'bg-indigo-100 text-indigo-700',
        'pending': 'bg-yellow-100 text-yellow-700',
        'market_analysis': 'bg-orange-100 text-orange-700',
        'off_market': 'bg-gray-100 text-gray-700',
        'unknown': 'bg-gray-100 text-gray-700'
    };
    
    return badgeClasses[canonicalType] || 'bg-gray-100 text-gray-700';
} 