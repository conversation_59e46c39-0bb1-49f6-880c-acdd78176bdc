import { createClient } from 'redis';

// Redis client setup
let redisClient: any = null;
let redisAvailable = false;

async function getRedisClient() {
  if (!redisClient && process.env.REDIS_URL) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL
      });

      redisClient.on('error', (err: any) => {
        console.error('Redis Client Error', err);
        redisAvailable = false;
      });

      await redisClient.connect();
      redisAvailable = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      redisAvailable = false;
      redisClient = null;
    }
  }
  return redisClient;
}

export interface RecentSearch {
  address: string;
  timestamp: number;
  placeId?: string;
  geometry?: {
    lat: number;
    lng: number;
  };
}

/**
 * Store a recent search for a specific user
 * @param userId - User ID
 * @param search - Search data to store
 */
export async function storeRecentSearch(userId: string, search: RecentSearch): Promise<void> {
  try {
    const redis = await getRedisClient();
    if (!redis || !redisAvailable) {
      console.log('Redis unavailable, skipping recent search storage');
      return;
    }

    const cacheKey = `recent_searches:${userId}`;
    
    // Get existing searches
    const existing = await redis.get(cacheKey);
    let searches: RecentSearch[] = existing ? JSON.parse(existing) : [];
    
    // Remove duplicate addresses
    searches = searches.filter(s => s.address !== search.address);
    
    // Add new search to the beginning
    searches.unshift(search);
    
    // Keep only last 6 searches
    searches = searches.slice(0, 6);
    
    // Store with 365 days expiration (365 * 24 * 60 * 60 = 31,536,000 seconds)
    await redis.setEx(cacheKey, 31536000, JSON.stringify(searches));
    
    console.log(`Stored recent search for user ${userId}:`, search.address);
  } catch (error) {
    console.error('Error storing recent search:', error);
  }
}

/**
 * Get recent searches for a specific user
 * @param userId - User ID
 * @returns Array of recent searches (max 6)
 */
export async function getRecentSearches(userId: string): Promise<RecentSearch[]> {
  try {
    const redis = await getRedisClient();
    if (!redis || !redisAvailable) {
      console.log('Redis unavailable, returning empty recent searches');
      return [];
    }

    const cacheKey = `recent_searches:${userId}`;
    const cached = await redis.get(cacheKey);
    
    if (cached) {
      const searches: RecentSearch[] = JSON.parse(cached);
      return searches.slice(0, 6); // Ensure we only return max 6
    }
    
    return [];
  } catch (error) {
    console.error('Error getting recent searches:', error);
    return [];
  }
}

/**
 * Clear all recent searches for a specific user
 * @param userId - User ID
 */
export async function clearRecentSearches(userId: string): Promise<void> {
  try {
    const redis = await getRedisClient();
    if (!redis || !redisAvailable) {
      console.log('Redis unavailable, cannot clear recent searches');
      return;
    }

    const cacheKey = `recent_searches:${userId}`;
    await redis.del(cacheKey);
    
    console.log(`Cleared recent searches for user ${userId}`);
  } catch (error) {
    console.error('Error clearing recent searches:', error);
  }
} 