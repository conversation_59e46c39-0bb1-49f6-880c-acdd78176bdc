'use server'

import apiEndpointsServer from "@/constants/apiEndpointsServer"
import httpAgent from "@/helpers/httpAgent/httpAgent"
import { SubscriptionDetailsType } from "@/types/SubscriptionDetailsType"
import { SubscriptionCheckoutType } from "@/types/SubscriptionCheckoutType"
import { SubscriptionPreviewType } from "@/types/SubscriptionPreviewType"
import { SubscriptionConfirmType } from "@/types/SubscriptionConfirmType"
export async function subscriptionDetails(workspace_id = ''): Promise<SubscriptionDetailsType> {
    return await httpAgent('GET', `${apiEndpointsServer.subscription.details}?workspace_id=${workspace_id}`)
}

export async function subscriptionCheckout(workspace_id = '', states: string[]): Promise<SubscriptionCheckoutType> {
    const body = {
        workspace_id,
        states
    }
    return await httpAgent('POST', `${apiEndpointsServer.subscription.checkout}`, {body})
}

export async function subscriptionPortal(): Promise<{success: string, portal_url: string}> {
    return await httpAgent('GET', apiEndpointsServer.subscription.portal)
}

export async function subscriptionPreview(workspace_id = '', added_states: string[], removed_states: string[]): Promise<SubscriptionPreviewType> {
    const body = {
        workspace_id,
        added_states,
        removed_states
    }
    return await httpAgent('POST', `${apiEndpointsServer.subscription.preview}`, {body})
}

export async function subscriptionConfirm(workspace_id = '', added_states: string[], removed_states: string[]): Promise<SubscriptionConfirmType> {
    const body = {
        workspace_id,
        added_states,
        removed_states
    }
    return await httpAgent('POST', `${apiEndpointsServer.subscription.confirm}`, {body})
}