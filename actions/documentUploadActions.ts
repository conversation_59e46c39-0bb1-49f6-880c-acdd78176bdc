'use server'
import { createClient } from "@/utils/supabase/server";
import { getSession } from "./userActions";
import { ChatActiveType } from "@/types/ChatActiveType";
import httpAgent from "@/helpers/httpAgent/httpAgent";
import apiEndpointsServer from "@/constants/apiEndpointsServer";
import { ProcessDocumentsType } from "@/types/DocumentProcessType";
import { DocumentProcessStatusType } from "@/types/DocumentProcessStatusType";


export async function getPropUnits(id: string) {
    const supabase = await createClient();
    const { data: prop, error: propError } = await supabase.from('prop').select('*').eq('address_id', id).single();
    const { data: units, error: unitsError } = await supabase.from('prop_units').select('*').eq('prop_id', prop?.id);
    
    if (propError || unitsError) {
        console.log('Error fetching getUnits:', propError || unitsError);
        throw new Error(`Error fetching getUnits: ${propError?.message || unitsError?.message}`);
    }

    return units;
}

export async function getPropsUnitsModal(portfolio_id: string, individual: boolean) {
    const supabase = await createClient();
    
    const { data: propsData, error: propsError } = individual ? await supabase.from('prop').select('*').eq('address_id', portfolio_id).eq('is_deleted', false) : await supabase.from('prop').select('*').eq('portfolio_id', portfolio_id).eq('is_deleted', false)
   
    if (propsError) {
        console.log('Error fetching propsData:', propsError);
        throw new Error(`Error fetching propsData: ${propsError.message}`);
    }

    const addressIds = propsData.map((prop: any) => prop.address_id);
    const propIds = propsData.map((prop: any) => prop.id);

    const { data: addressData, error: addressError } = await supabase.from('prop_addresses').select('*').in('id', addressIds);

    if (addressError) {
        console.log('Error fetching addressData:', addressError);
        throw new Error(`Error fetching addressData: ${addressError.message}`);
    }

    const { data: unitsData, error: unitsError } = await supabase.from('prop_units').select('*').in('prop_id', propIds);

    if (unitsError) {
        console.log('Error fetching unitsData:', unitsError);
        throw new Error(`Error fetching unitsData: ${unitsError.message}`);
    }

    const combinedData = propsData.map((prop: any) => {
        const address = addressData.find((addr: any) => addr.id === prop.address_id);
        const units = unitsData.filter((unit: any) => unit.prop_id === prop.id);
        
        return {
            ...prop,
            address,
            units
        };
    });

    return combinedData;
}

export async function getPropUnitsModal(id: string) {
    const supabase = await createClient();
    const { data: units, error: unitsError } = await supabase.from('prop_units').select('*').eq('prop_id', id);
    
    if (unitsError) {
        console.log('Error fetching getUnits:', unitsError);
        throw new Error(`Error fetching getUnits: ${unitsError?.message}`);
    }

    return units;
}

export async function documentsMetadata(portfolio_id: string, property_id: string, filename: string, supabase_url: string, is_unit: boolean, unit_number: string, type: string, sub_type: string, original_filename: string) {
    const supabase = await createClient();
    const userData = await getSession();
    const userId = userData?.id;

    
    const {data: documents, error: documentsError} = await supabase.from('documents').insert([
        {
            portfolio_id,
            property_id,
            user_id: userId,
            filename,
            supabase_url,
            is_unit,
            unit_number,
            type,
            sub_type,
            original_filename
        }
    ]).select('*').single();

    if (documentsError) {
        console.log('Error fetching documents:', documentsError);
        throw new Error(`Error fetching documents: ${documentsError.message}`);
    }

    return documents;

}

export async function processDocuments(supabase_urls: string[], portfolio_id: string): Promise<ProcessDocumentsType>{
    const userData = await getSession();
   
    const body = {
        supabase_urls,
        portfolio_id,
        user_id: userData?.id
    }
    return await httpAgent('POST', `${apiEndpointsServer.documents.process}`, {body})
}

export async function getDocumentProcessStatus(task_token: string, portfolio_id: string): Promise<DocumentProcessStatusType>{
    return await httpAgent('GET', `${apiEndpointsServer.documents.processStatus}?task_token=${task_token}&portfolio_id=${portfolio_id}`)
}