'use server'
import { createClient } from "@/utils/supabase/server";

export async function getSession() {
  const supabase = await createClient();
  const { data: {user}, error: sessionError } = await supabase.auth.getUser();

  if (sessionError){
    console.log('Error getting session:', sessionError);
    throw new Error(`Failed to get session: ${sessionError.message}`);
  }

  if (!user){
    console.log('User not found');
    throw new Error('User not found');
  }

  return user;
}

export async function getUserData (userId: string) {
  const supabase = await createClient();
 
  const { data: userData, error: userDataError } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();

  if (userDataError){
    console.log('Error getting user data:', userDataError);
    throw new Error(`Failed to get user data: ${userDataError.message}`);
  }

  return userData;
}

export async function updateUserData (userId: string, userData: any) {
  const supabase = await createClient();

  const { data: updatedData, error: updateError } = await supabase
    .from('users')
    .update({
      organization: userData.organization.value,
      address: userData.address.value,
      phone: userData.phone.value,
      photo: userData.photo?.value || null
    })
    .eq('id', userId)
    .select();

  if (updateError) {
    console.log('Error updating user data:', updateError);
    throw new Error(`Failed to update user data: ${updateError.message}`);
  }

  return updatedData;
}

export async function insertDataRoomGuest(data: any, userId: string) {
  const supabase = await createClient();
  console.log(data, userId)
  const {data: dataroomGuestData, error: dataroomGuestError} = await supabase
    .from('dataroom_guests')
    .select('*')
    .eq('owner_id', data.owner_id)
    .eq('portfolio_id', data.portfolio_id)
    .eq('receiver_id', userId)
    .single();

  if(!dataroomGuestData) {
    const { data: insertedData, error: insertError } = await supabase
    .from('dataroom_guests')
    .insert({
      owner_id: data.owner_id,
      sender_id: data.sender_id,
      receiver_id: userId,
      can_share: data.can_share,
      can_download: data.can_download,
      portfolio_id: data.portfolio_id,
      has_access: true,
    })
  }
}

export async function getSharedList(userId: string){
  const supabase = await createClient();
  
  const { data: sharedList, error: sharedListError } = await supabase
  .from('dataroom_guests')
  .select('*')
  .eq('receiver_id', userId)
  .eq('has_access', true)
  

  if (sharedListError) {
    throw new Error(`Error fetching sharedList: ${sharedListError.message}`);
  }

  const portfolioIds = sharedList.map(item => item.portfolio_id);

  const { data: portfolios, error: portfoliosError } = await supabase
    .from('portfolios')
    .select('*')
    .in('id', portfolioIds);

  if (portfoliosError) {
    throw new Error(`Error fetching portfolios: ${portfoliosError.message}`);
  }

  const senderIds = sharedList.map(item => item.sender_id);

  const { data: sendersData, error: sendersError } = await supabase
    .from('users')
    .select('*')
    .in('id', senderIds);

  if (sendersError) {
    throw new Error(`Error fetching senders: ${sendersError.message}`);
  }

  const result = sharedList.map(item => {
    const portfolio = portfolios.find(p => p.id === item.portfolio_id);
    const sender = sendersData.find(o => o.id === item.sender_id);
    return {
      ...item,
      portfolioData: portfolio, 
      senderData: sender
    };
  });

  return result;
}