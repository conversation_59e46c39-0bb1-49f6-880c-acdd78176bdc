import apiEndpointsServer from "@/constants/apiEndpointsServer";
import { createClient } from "@/utils/supabase/client";

export async function documentGenerateSlides(portfolio_id: string, user_id: string) {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
        throw new Error('User not authenticated');
    }

    const response = await fetch(apiEndpointsServer.documents.generateSlides, { 
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            portfolio_id,
            user_id
        })
    })

    const data = await response.json()

    return data
}