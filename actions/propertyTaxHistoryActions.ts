'use server'
import { createClient } from "@/utils/supabase/server";

export async function getPropertyTaxHistoryDB(address_id: string){
    const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: taxHistoryData, error: taxHistoryError} = await supabase.from('prop_tax_history').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).select();
    
    if(propError || taxHistoryError){
        console.log('Error fetching property tax history:', propError || taxHistoryError);
        throw new Error(`Error fetching property tax history: ${propError?.message || taxHistoryError?.message}`);
    }

    return {
        timestamp: propData?.updated_at,
        tax_records: taxHistoryData,
    }
}