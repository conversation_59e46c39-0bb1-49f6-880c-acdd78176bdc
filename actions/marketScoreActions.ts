'use server'

import { NewsArticle } from './newsActions';
import { <PERSON><PERSON><PERSON> } from './fredActions';
import { createClient } from 'redis';

export interface MarketScore {
  score: number; // 0-100
  sentiment: 'Bearish' | 'Neutral' | 'Bullish';
  confidence: number; // 0-100
  keyFactors: string[];
  summary: string;
  lastUpdated: string;
}

// Redis client setup
let redisClient: any = null;
let redisAvailable = false;

async function getRedisClient() {
  if (!redisClient && process.env.REDIS_URL) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL
      });
      
      redisClient.on('error', (err: any) => {
        console.error('Redis Client Error', err);
        redisAvailable = false;
      });
      
      await redisClient.connect();
      redisAvailable = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      redisAvailable = false;
      redisClient = null;
    }
  }
  return redisClient;
}

/**
 * Generate AI-powered rental market score with state-wide caching (12 hours for all users) - graceful fallback without Redis
 */
export async function generateMarketScore(
  newsArticles: NewsArticle[],
  fredCharts: Fred<PERSON>hart[]
): Promise<MarketScore> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable, proceeding without cache');
    }
    
    // Extract states from news articles for state-wide caching
    const states = [...new Set(newsArticles.map(article => (article as any).state).filter(Boolean))];
    const stateKey = states.sort().join(',').toLowerCase() || 'national';
    
    // Try to get cached score first (if Redis is available)
    if (redis && redisAvailable) {
      try {
        const newsSignature = newsArticles.slice(0, 10).map(article => 
          `${article.title.substring(0, 50)}:${article.date}`
        ).join('|');
        
        const fredSignature = fredCharts.map(chart => {
          const latestValue = chart.data[chart.data.length - 1];
          return `${chart.id}:${latestValue?.value || 0}`;
        }).join('|');
        
        const cacheKey = `state_market_score:${stateKey}:${Buffer.from(newsSignature + fredSignature).toString('base64')}`;
        const cachedScore = await redis.get(cacheKey);
        if (cachedScore) {
          console.log(`Returning cached market score for ${stateKey}`);
          return JSON.parse(cachedScore);
        }
      } catch (cacheError) {
        console.log('Cache read failed, generating fresh market score');
      }
    }

    // Import OpenAI dynamically to avoid module-level initialization
    const { default: OpenAI } = await import('openai');
    
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Prepare news data for analysis
    const newsData = newsArticles.slice(0, 10).map(article => ({
      title: article.title,
      summary: article.aiSummary || article.snippet,
      source: article.source,
      date: article.date,
      state: (article as any).state || 'Unknown'
    }));

    // Prepare FRED data for analysis
    const fredData = fredCharts.map(chart => {
      const latestValue = chart.data[chart.data.length - 1];
      const previousValue = chart.data[chart.data.length - 2];
      const trend = latestValue && previousValue ? 
        (latestValue.value > previousValue.value ? 'increasing' : 'decreasing') : 'stable';
      
      return {
        indicator: chart.title,
        description: chart.description,
        latestValue: latestValue?.value,
        trend,
        units: chart.units
      };
    });

    const prompt = `You are a real estate market analyst. Analyze the following data and provide a rental market "hotness" score from 0-100, where:
- 0-20: Very Cold (Poor rental market conditions)
- 21-40: Cold (Below average conditions)
- 41-60: Neutral (Average market conditions)
- 61-80: Hot (Above average, favorable conditions)
- 81-100: Very Hot (Excellent rental market conditions)

STATES ANALYZED: ${states.join(', ') || 'National'}

NEWS ARTICLES:
${JSON.stringify(newsData, null, 2)}

ECONOMIC INDICATORS (FRED DATA):
${JSON.stringify(fredData, null, 2)}

Provide your response in the following JSON format:
{
  "score": <number 0-100>,
  "sentiment": "<Bearish|Neutral|Bullish>",
  "confidence": <number 0-100>,
  "keyFactors": ["factor1", "factor2", "factor3"],
  "summary": "<2-3 sentence summary of market conditions>"
}

Consider factors like:
- Mortgage rates trends
- Home sales volume
- Price appreciation
- Housing inventory
- Construction activity
- News sentiment about rental markets
- Economic indicators affecting real estate demand`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert real estate market analyst. Provide accurate, data-driven market assessments in valid JSON format only.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 500,
      temperature: 0.3,
    });

    const responseContent = completion.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const aiResponse = JSON.parse(responseContent);
    
    // Validate and sanitize the data
    const validatedData: MarketScore = {
      score: Math.max(0, Math.min(100, aiResponse.score || 50)),
      sentiment: ['Bearish', 'Neutral', 'Bullish'].includes(aiResponse.sentiment) 
        ? aiResponse.sentiment as MarketScore['sentiment']
        : 'Neutral',
      confidence: Math.max(0, Math.min(100, aiResponse.confidence || 70)),
      keyFactors: Array.isArray(aiResponse.keyFactors) 
        ? aiResponse.keyFactors.slice(0, 5)
        : ['Insufficient data'],
      summary: aiResponse.summary || 'Market analysis unavailable',
      lastUpdated: new Date().toISOString(),
    };

    // Cache market score if Redis is available
    if (redis && redisAvailable) {
      try {
        const newsSignature = newsArticles.slice(0, 10).map(article => 
          `${article.title.substring(0, 50)}:${article.date}`
        ).join('|');
        
        const fredSignature = fredCharts.map(chart => {
          const latestValue = chart.data[chart.data.length - 1];
          return `${chart.id}:${latestValue?.value || 0}`;
        }).join('|');
        
        const cacheKey = `state_market_score:${stateKey}:${Buffer.from(newsSignature + fredSignature).toString('base64')}`;
        await redis.setEx(cacheKey, 43200, JSON.stringify(validatedData));
      } catch (cacheError) {
        console.log('Failed to cache market score, continuing without cache');
      }
    }

    return validatedData;
  } catch (error) {
    console.error('Error generating market score:', error);
    
    // Return a fallback score
    return {
      score: 50,
      sentiment: 'Neutral',
      confidence: 30,
      keyFactors: ['Analysis unavailable due to technical issues'],
      summary: 'Unable to generate market score at this time. Please try again later.',
      lastUpdated: new Date().toISOString(),
    };
  }
} 