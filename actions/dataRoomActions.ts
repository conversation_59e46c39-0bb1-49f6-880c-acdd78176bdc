'use server'
import httpAgent from "@/helpers/httpAgent/httpAgent";
import apiEndpointsServer from "@/constants/apiEndpointsServer";
import { createClient } from "@/utils/supabase/server";


export async function getPortfolioDocuments(portfolio_id: string) {
    return await httpAgent('GET', `${apiEndpointsServer.document.list}?portfolio_id=${portfolio_id}`);
}

export async function deletePortfolioDocument(portfolio_id: string, document_id: string) {
    return await httpAgent('DELETE', `${apiEndpointsServer.document.delete(portfolio_id, document_id)}`);
}

export async function getPortfolioOwner(portfolio_id: string) {
  const supabase = await createClient();

  const { data: propData, error: propError } = await supabase
    .from('portfolios')
    .select('*')
    .eq('id', portfolio_id)
    .single();

  const { data: workspaceMemberData, error: workspaceMemberError } = await supabase
    .from('workspace_members')
    .select('*')
    .eq('workspace_id', propData?.workspace_id)
    .single();

  
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', workspaceMemberData?.user_id)
    .single();
    
    if (propError || workspaceMemberError) {
        console.log('Error fetching getPortfolioOwner:', propError || workspaceMemberError);
        throw new Error(`Error fetching getPortfolioOwner: ${propError?.message || workspaceMemberError?.message}`);
    }
    

    return userData;
}

export async function checkEmailExists(email: string) {
  const supabase = await createClient();

  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('email', email)
    .single();

  return userData;
}

export async function getSharedList(senderId: string, portfolioId: string) {
  const supabase = await createClient();

  const { data: sharedList, error: sharedListError } = await supabase
    .from('dataroom_guests')
    .select('*')
    .eq('sender_id', senderId)
    .eq('portfolio_id', portfolioId)

    if (sharedListError) {
      throw new Error(`Error fetching portfolios: ${sharedListError.message}`);
    }
  
    const receiverIds = sharedList.map(item => item.receiver_id);

    const {data: users, error: usersError} = await supabase
      .from('users')
      .select('*')
      .in('id', receiverIds);

    if (usersError) {
      throw new Error(`Error fetching users: ${usersError.message}`);
    }

    const result = sharedList.map(item => {
      const user = users.find(u => u.id === item.receiver_id);
      return {
        ...item,
        userData: user,
      };
    });

  return result;
}

export async function updateDataRoomPermissions(can_share: boolean, can_download: boolean, receiver_id: string, portfolio_id: string){
  const supabase = await createClient();

  const { data: sharedList, error: sharedListError } = await supabase
    .from('dataroom_guests')
    .update({
      can_share,
      can_download
    })
    .eq('portfolio_id', portfolio_id)
    .eq('receiver_id', receiver_id)
}

export async function dataroomEvents(user_id: string, event_type: string, portfolio_id: string ) {
  const supabase = await createClient();
  console.log(user_id, event_type, 222)
  const {data: fistLogin, error: fistLoginError} = await supabase
    .from('dataroom_events')
    .select('*')
    .eq('user_id', user_id)
  


  const { data: dataroomEvents, error: dataroomEventsError } = await supabase
    .from('dataroom_events')
    .insert({
      user_id,
      event_type: fistLogin?.length === 0 ? 'login' : event_type,
      portfolio_id
    })

  if (dataroomEventsError) {
    throw new Error(`Error fetching dataroomEvents: ${dataroomEventsError.message}`);
  }
}

export async function getPortfolioDocumentsTest(portfolio_id: string) {

    return {
        "success": true,
        "type": "root",
        "portfolio_id": "093194d9-ec6a-422b-ba40-1cf60909aa60",
        "portfolio_name": "testing-fix",
        "children": [
          {
            "id": "offering_memorandum_093194d9-ec6a-422b-ba40-1cf60909aa60",
            "type": "folder",
            "name": "Offering Memorandum",
            "document_type": "offering_memorandum",
            "children": [
              {
                "id": "23fdbf4d-4a54-48f3-81ac-6bb1fb850c40",
                "type": "file",
                "document_type": "offering_memorandum",
                "name": "testing-fix_Slides_20250519174128.pdf",
                "url": "https://kqlohylngxxwqsoqpksm.supabase.co/storage/v1/object/public/documents/4ef98852-7831-4f99-a93e-43e511642fec/5ca75b01-7a36-41fd-9508-c4655b3ea937/construction_contract_property_location_2023.pdf",
                "created_at": "2025-05-19T17:41:28.959283+00:00"
              }
            ]
          },
          {
            "id": "17cd8236-ae35-42e5-9aef-b518a5d548ac",
            "type": "folder",
            "name": "9 AVENUE AT PORT IMPERIAL, WEST NEW YORK, NJ",
            "address": "9 AVENUE AT PORT IMPERIAL, WEST NEW YORK, NJ",
            "document_type": "property",
            "children": [
              {
                "id": "operating_financials_17cd8236-ae35-42e5-9aef-b518a5d548ac",
                "type": "folder",
                "name": "Operating and Financials",
                "document_type": "operating_financials",
                "children": [
                  {
                    "id": "leases_17cd8236-ae35-42e5-9aef-b518a5d548ac",
                    "type": "folder",
                    "name": "Leases",
                    "document_type": "leases",
                    "children": [
                      {
                        "id": "unit_leases_17cd8236-ae35-42e5-9aef-b518a5d548ac",
                        "type": "folder",
                        "name": "Unit Leases",
                        "document_type": "unit_leases",
                        "children": [
                          {
                            "id": "unit_1115X_17cd8236-ae35-42e5-9aef-b518a5d548ac",
                            "type": "folder",
                            "name": "Unit 1115X",
                            "document_type": "unit_1115X",
                            "children": [
                              {
                                "id": "5e7cecb3-7638-427e-a973-1522a9eb9874",
                                "type": "file",
                                "name": "residential_lease_agreement_20XX.pdf",
                                "document_type": "Operating & Financials",
                                "sub_type": "Leases - Unit Leases",
                                "unit_number": "1115X",
                                "is_unit": true,
                                "summary": "This Residential Lease Agreement outlines the terms and conditions between the Landlord and Tenant for leasing certain premises located in New York County. It specifies details such as the lease term, monthly rent, security deposit, and maintenance responsibilities. The document also addresses tenant obligations regarding property use, alterations, and compliance with laws. This is essential for effective property management and ensuring clear communication between both parties regarding their rights and responsibilities.",
                                "url": "https://kqlohylngxxwqsoqpksm.supabase.co/storage/v1/object/public/documents/074ddb2c-9af5-4d70-91b0-7408c30459b4/dcd3ba9c-1896-454a-8a9b-8ec9d79d1110/lease_agreement_premises_in_new_york_2023.pdf",
                                "uploaded_at": "2025-05-20T17:15:24.099957+00:00",
                                "property_id": "17cd8236-ae35-42e5-9aef-b518a5d548ac"
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "id": "expenses_17cd8236-ae35-42e5-9aef-b518a5d548ac",
                    "type": "folder",
                    "name": "Expenses",
                    "document_type": "expenses",
                    "children": [
                      {
                        "id": "contracts_17cd8236-ae35-42e5-9aef-b518a5d548ac_expenses",
                        "type": "folder",
                        "name": "Contracts",
                        "document_type": "contracts_expenses",
                        "children": [
                          {
                            "id": "c58b2b73-2936-449a-8ed4-4e58ffd24a05",
                            "type": "file",
                            "name": "construction_contract_property_2023.pdf",
                            "document_type": "Operating & Financials",
                            "sub_type": "Expenses - Contracts",
                            "unit_number": null,
                            "is_unit": false,
                            "summary": "This Construction Contract Agreement establishes the terms between a Constructor and a Client for a specific construction project on a defined property. The document outlines the scope of work, payment terms, permits, responsibilities, and conditions for termination. It is effective on the signing date and includes provisions for insurance, indemnification, and dispute resolution. The parties involved agree to abide by the terms set forth, including safety measures and financial commitments, ensuring proper management throughout the project.",
                            "url": "https://kqlohylngxxwqsoqpksm.supabase.co/storage/v1/object/public/documents/074ddb2c-9af5-4d70-91b0-7408c30459b4/dcd3ba9c-1896-454a-8a9b-8ec9d79d1110/lease_agreement_premises_in_new_york_2023.pdf",
                            "uploaded_at": "2025-05-20T17:06:13.520822+00:00",
                            "property_id": "17cd8236-ae35-42e5-9aef-b518a5d548ac"
                          },
                          {
                            "id": "8b312d64-d397-46eb-a3ff-ef71649a5c3f",
                            "type": "file",
                            "name": "construction_contract_agreement_2023.pdf",
                            "document_type": "Operating & Financials",
                            "sub_type": "Expenses - Contracts",
                            "unit_number": "1115X",
                            "is_unit": true,
                            "summary": "This Construction Contract Agreement is made between the Constructor and the Client, concerning a property located at an unspecified address. The document outlines the scope of work, payment terms, and the responsibilities of both parties, including the requirement for insurance and permit acquisition. It sets the conditions for termination and indemnification, as well as governing law and dispute resolution methods. This Agreement is a comprehensive document essential for establishing the legal terms of construction and protecting both parties involved.",
                            "url": "https://kqlohylngxxwqsoqpksm.supabase.co/storage/v1/object/public/documents/074ddb2c-9af5-4d70-91b0-7408c30459b4/dcd3ba9c-1896-454a-8a9b-8ec9d79d1110/lease_agreement_premises_in_new_york_2023.pdf",
                            "uploaded_at": "2025-05-20T17:14:05.90437+00:00",
                            "property_id": "17cd8236-ae35-42e5-9aef-b518a5d548ac"
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    //return await httpAgent('GET', `${apiEndpointsServer.dataRoom.getPortfolioDataRoom}/${id}`)
}

/**
 * Downloads all portfolio documents as a zip file
 * @param portfolio_id - Portfolio ID
 * @returns Base64 zip data and metadata
 */
export async function downloadAllPortfolioDocuments(portfolio_id: string) {
  try {
    // Get portfolio documents
    const documents = await getPortfolioDocuments(portfolio_id);
    
    if (!documents || !documents.children) {
      throw new Error('No documents found for this portfolio');
    }

    // Get portfolio name for the zip filename
    const supabase = await createClient();
    const { data: portfolio, error } = await supabase
      .from('portfolios')
      .select('name')
      .eq('id', portfolio_id)
      .single();

    if (error) {
      throw new Error(`Error fetching portfolio: ${error.message}`);
    }

    const portfolioName = portfolio?.name || 'Portfolio_Documents';
    const sanitizedName = portfolioName.replace(/[^a-zA-Z0-9_-]/g, '_');

    // Collect all file URLs with their paths
    const fileList: Array<{ url: string; path: string; name: string }> = [];
    
    const collectFiles = (node: any, currentPath: string = '') => {
      if (node.type === 'file' && node.url) {
        const filePath = currentPath ? `${currentPath}/${node.name}` : node.name;
        fileList.push({
          url: node.url,
          path: filePath,
          name: node.name
        });
      } else if (node.type === 'folder' && node.children) {
        const folderPath = currentPath ? `${currentPath}/${node.name}` : node.name;
        node.children.forEach((child: any) => collectFiles(child, folderPath));
      }
    };

    // Start collecting from root children
    documents.children.forEach((child: any) => collectFiles(child));

    if (fileList.length === 0) {
      throw new Error('No files found to download');
    }

    // Return file list for client-side zip creation
    return {
      success: true,
      portfolioName: sanitizedName,
      files: fileList,
      totalFiles: fileList.length
    };

  } catch (error) {
    console.error('Error in downloadAllPortfolioDocuments:', error);
    throw error;
  }
}