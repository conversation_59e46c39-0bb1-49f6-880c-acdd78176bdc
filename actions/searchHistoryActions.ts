'use server'

import { createClient } from '@/utils/supabase/server';

export interface SearchHistoryItem {
  id: string;
  address_string: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

/**
 * Store a new search in user's search history
 */
export async function storeSearchHistory(address: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    // Check if this address already exists in the user's search history
    const { data: existingSearch, error: checkError } = await supabase
      .from('user_search_history')
      .select('id')
      .eq('address_string', address)
      .eq('user_id', user.id)
      .eq('is_deleted', false)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing search:', checkError);
      return { success: false, error: 'Failed to check existing search' };
    }

    const now = new Date().toISOString();

    if (existingSearch) {
      // Update existing search with new timestamp
      const { error: updateError } = await supabase
        .from('user_search_history')
        .update({ updated_at: now })
        .eq('id', existingSearch.id);

      if (updateError) {
        console.error('Error updating search history:', updateError);
        return { success: false, error: 'Failed to update search history' };
      }
    } else {
      // Insert new search
      const { error: insertError } = await supabase
        .from('user_search_history')
        .insert({
          user_id: user.id,
          address_string: address,
          created_at: now,
          updated_at: now,
          is_deleted: false
        });

      if (insertError) {
        console.error('Error inserting search history:', insertError);
        return { success: false, error: 'Failed to insert search history' };
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in storeSearchHistory:', error);
    return { success: false, error: 'Failed to store search history' };
  }
}

/**
 * Get user's recent search history with pagination support
 */
export async function getSearchHistory(offset: number = 0, limit: number = 20): Promise<{ success: boolean; data?: SearchHistoryItem[]; error?: string }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data: searchHistory, error: fetchError } = await supabase
      .from('user_search_history')
      .select('id, address_string, created_at, updated_at, is_deleted')
      .eq('user_id', user.id)
      .eq('is_deleted', false)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (fetchError) {
      console.error('Error fetching search history:', fetchError);
      return { success: false, error: 'Failed to fetch search history' };
    }

    return { success: true, data: searchHistory || [] };
  } catch (error) {
    console.error('Error in getSearchHistory:', error);
    return { success: false, error: 'Failed to get search history' };
  }
}

/**
 * Delete a search from user's history (soft delete)
 */
export async function deleteSearchHistory(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error: deleteError } = await supabase
      .from('user_search_history')
      .update({ is_deleted: true })
      .eq('id', id)
      .eq('user_id', user.id); // Ensure user can only delete their own searches

    if (deleteError) {
      console.error('Error deleting search history:', deleteError);
      return { success: false, error: 'Failed to delete search history' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in deleteSearchHistory:', error);
    return { success: false, error: 'Failed to delete search history' };
  }
} 