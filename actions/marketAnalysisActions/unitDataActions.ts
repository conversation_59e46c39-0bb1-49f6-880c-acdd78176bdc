'use server'

export async function getUnitData(full_address: string) {
    const response = await fetch(`https://bmvir06g47.execute-api.us-east-1.amazonaws.com/v1/search-building-units/unit-data`, {
        method: 'POST',
        body: JSON.stringify({
            full_address
        })
    })
    const data = await response.json()
    return data
}

export async function getMarketData(location: string, postal_code: string) {
    const response = await fetch(`https://bmvir06g47.execute-api.us-east-1.amazonaws.com/v1/search-building-units/market-data`, {
        method: 'POST',
        body: JSON.stringify({
            location,
            postal_code
        })
    })
    const data = await response.json()
    return data
}

export async function getTransitData(lat: number, lon: number) {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/transit?lat=${lat}&lon=${lon}`)
    const data = await response.json()
    return data
}