/**
 * Service for interacting with <PERSON><PERSON><PERSON>'s GraphQL API
 */
import { GET_DEMOGRAPHICS_BY_ZIPCODE, GET_DEMOGRAPHICS_BY_CITY_STATE, GET_PROPERTY_DETAILS, GET_EXTENDED_PROPERTY_DETAILS, GET_PROPERTY_TAX_ASSESSMENT, GET_SCHOOL_DATA, GET_NEARBY_LOCATIONS, GET_ENVIRONMENTAL_RISK, GET_MULTIFAMILY_UNITS } from './graphql/queries';

const CHERRE_API_ENDPOINT = 'https://graphql.cherre.com/graphql';
const CHERRE_API_KEY = '********************************************************************************************************************************************************************';

/**
 * Demographics data interface
 */
export interface DemographicsData {
  vintage?: string;
  population_2020_count?: number;
  population_median_age?: number;
  median_household_income?: number;
  average_household_income?: number;
  crime_total_risk?: number;
}

/**
 * Tax history item interface
 */
export interface TaxHistoryItem {
  assessed_tax_year?: number;
  assessed_value_land?: number;
  assessed_value_total?: number;
  assessed_value_improvement?: number;
  market_value_land?: number;
  market_value_total?: number;
  market_value_improvement?: number;
  tax_bill_amount?: number;
  tax_code_area?: string;
  tax_amount_land?: number;
  tax_amount_improvement?: number;
}

/**
 * Unit information interface
 */
export interface UnitInfo {
  unit_number?: number;
  unit_type?: string;
  unit_style?: string;
  gross_sq_ft?: number;
  living_sq_ft?: number;
}

/**
 * Building information interface
 */
export interface BuildingInfo {
  building_count?: number;
  floors_count?: number;
  total_area?: number;
  residential_units_count?: number;
  commercial_units_count?: number;
  year_built?: number;
  zoning?: string;
}

/**
 * Extended property details interface
 */
export interface ExtendedPropertyDetails {
  // Building unit information
  unit_count?: number;
  parcel_number?: string;
  zoning_code?: string;
  
  // Property assessment values
  assessed_total_value?: number;
  assessed_land_value?: number;
  assessed_improvement_value?: number;
  market_value_total?: number;
  market_value_land?: number;
  market_value_improvement?: number;
  
  // Building size/area information
  gross_sq_ft?: number;
  living_sq_ft?: number;
  ground_floor_sq_ft?: number;
  adjusted_sq_ft?: number;
  first_floor_sq_ft?: number;
  basement_sq_ft?: number;
  
  // Building characteristics
  building_code?: string;
  building_class_code?: string;
  structure_style_code?: string;
  property_use_code?: string;
  property_use_code_mapped?: string;
  
  // Building information
  building_info?: BuildingInfo;
  
  // Units information
  units?: UnitInfo[];
}

/**
 * Property details interface
 */
export interface PropertyDetails {
  display_address?: string;
  street_address?: string;
  city?: string;
  state?: string;
  zip?: string;
  bed_count?: number;
  bath_count?: number;
  gross_sq_ft?: number;
  lot_size_acre?: number;
  year_built?: number;
  last_sale_date?: string;
  last_sale_amount?: number;
  longitude?: number;
  latitude?: number;
  property_use_code_mapped?: string;
  tax_history?: TaxHistoryItem[];
  demographics?: DemographicsData;
}

/**
 * School information interface
 */
export interface SchoolInfo {
  institution_id?: string;
  test_rating?: number;
  institution_name?: string;
  institution_address?: string;
  county_name?: string;
  student_count?: number;
  teacher_count?: number;
  enrollment_male?: number;
  enrollment_female?: number;
  grade_level_range_low?: number;
  grade_level_range_high?: number;
}

/**
 * POI (Point of Interest) information interface
 */
export interface POIInfo {
  street?: string;
  business_name?: string;
  line_of_business?: string;
  address_number?: string;
  city?: string;
  state?: string;
  latitude?: number;
  longitude?: number;
  distance?: number; // Calculated distance from property
}

/**
 * Neighborhood and POIs information interface
 */
export interface NeighborhoodInfo {
  geography_name?: string;
  reference_2?: string;
  pois?: POIInfo[];
}

/**
 * Risk level type
 */
export type RiskLevel = 'Minimal' | 'Minor' | 'Moderate' | 'Major' | 'Severe' | 'Extreme';

/**
 * Trend type
 */
export type TrendType = 'increasing' | 'not changing' | 'decreasing';

/**
 * Environmental risk factor interface
 */
export interface RiskFactor {
  risk_level: RiskLevel;
  trend?: TrendType;
  risk_score?: number;
}

/**
 * Heat factor information interface
 */
export interface HeatFactor extends RiskFactor {
  extreme_heat_days?: number;
}

/**
 * Air quality information interface
 */
export interface AirQuality extends RiskFactor {
  aqi_score?: number;
}

/**
 * Environmental risk information interface
 */
export interface EnvironmentalRisk {
  flood_factor: RiskFactor;
  fire_factor: RiskFactor;
  heat_factor: HeatFactor;
  wind_factor: RiskFactor;
  air_quality: AirQuality;
}

/**
 * Recorder mortgage information
 */
export interface RecorderMortgage {
  amount?: number;
}

/**
 * Recorder information for a tax assessor
 */
export interface Recorder {
  document_recorded_date?: string;
  recorder_mortgage_v2__recorder_id?: RecorderMortgage[];
}

/**
 * Multifamily unit information
 */
export interface MultifamilyUnit {
  address?: string;
  tax_assessor_id?: string;
  bed_count?: number;
  bath_count?: number;
  hoa_1_fee_value?: number;
  building_type?: string;
  recorder_v2__tax_assessor_id?: Recorder[];
}

/**
 * Multifamily building information
 */
export interface MultifamilyBuilding {
  display_address?: string;
  units?: MultifamilyUnit[];
}

/**
 * Fetch demographics data by zipcode
 * @param zipCode ZIP code to query
 * @returns Demographics data or null if not found
 */
export const fetchDemographicsByZipcode = async (zipCode: string): Promise<DemographicsData | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_DEMOGRAPHICS_BY_ZIPCODE,
      variables: { zipCode }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const demographicsData = result.data?.usa_zip_code_boundary_v2?.[0]?.usa_demographics_v2__geography_id?.[0];
    return demographicsData || null;
  } catch (error) {
    console.log('Error fetching demographics data:', error);
    return null;
  }
};

/**
 * Fetch demographics data by city and state
 * @param city City name to query
 * @param state State abbreviation (e.g., 'CA') to query
 * @returns Demographics data or null if not found
 */
export const fetchDemographicsByCityState = async (city: string, state: string): Promise<DemographicsData | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    // Format query parameters
    const formattedCity = `%${city}%`;
    const formattedState = `%${state}%`;

    const graphql = JSON.stringify({
      query: GET_DEMOGRAPHICS_BY_CITY_STATE,
      variables: { 
        city: formattedCity,
        state: formattedState
      }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const demographicsData = result.data?.usa_demographics_v2;
    return demographicsData || null;
  } catch (error) {
    console.log('Error fetching city/state demographics data:', error);
    return null;
  }
};

/**
 * Fetch property details by address
 * @param address Full address to query
 * @returns Property details or null if not found
 */
export const fetchPropertyDetails = async (address: string): Promise<PropertyDetails | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_PROPERTY_DETAILS,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();
    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const propertyData = result.data?.address?.cherre_address__address;
    if (!propertyData) return null;

    const taxAssessorData = propertyData.tax_assessor_v2__property_address;
    if (!taxAssessorData) return null;

    // Extract and structure the data
    const propertyDetails: PropertyDetails = {
      display_address: propertyData.display_address,
      street_address: propertyData.street_address,
      city: propertyData.city,
      state: propertyData.state,
      zip: propertyData.zip,
      bed_count: taxAssessorData.bed_count,
      bath_count: taxAssessorData.bath_count,
      gross_sq_ft: taxAssessorData.gross_sq_ft,
      lot_size_acre: taxAssessorData.lot_size_acre,
      year_built: taxAssessorData.year_built,
      property_use_code_mapped: taxAssessorData.property_use_code_mapped,
      last_sale_date: taxAssessorData.last_sale_date,
      last_sale_amount: taxAssessorData.last_sale_amount,
      longitude: taxAssessorData.longitude,
      latitude: taxAssessorData.latitude,
      tax_history: taxAssessorData.tax_assessor_history_v2__tax_assessor_id?.map((item: any) => ({
        assessed_tax_year: item.assessed_tax_year,
        assessed_value_land: item.assessed_value_land,
        assessed_value_total: item.assessed_value_total,
        tax_bill_amount: item.tax_bill_amount
      })) || [],
      demographics: taxAssessorData.usa_zip_code_boundary_v2__zip_code?.usa_demographics_v2__geography_id?.[0] || null
    };

    return propertyDetails;
  } catch (error) {
    console.log('Error fetching property details:', error);
    return null;
  }
};

/**
 * Fetch extended property details including unit info, building info, and assessment values
 * @param address Full address to query
 * @returns Extended property details or null if not found
 */
export const fetchExtendedPropertyDetails = async (address: string): Promise<ExtendedPropertyDetails | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_EXTENDED_PROPERTY_DETAILS,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const propertyData = result.data?.address?.cherre_address__address;
    if (!propertyData) return null;

    const taxAssessorData = propertyData.tax_assessor_v2__property_address;
    if (!taxAssessorData) return null;

    // Building data might be optional
    const buildingData = propertyData.usa_building_v2__address || {};
    
    // Units data might be optional
    const unitsData = propertyData.usa_unit_v2__address || [];

    // Extract and structure the data
    const extendedPropertyDetails: ExtendedPropertyDetails = {
      // Building unit information
      unit_count: taxAssessorData.unit_count,
      parcel_number: taxAssessorData.parcel_number,
      zoning_code: taxAssessorData.zoning_code,
      
      // Property assessment values
      assessed_total_value: taxAssessorData.assessed_total_value,
      assessed_land_value: taxAssessorData.assessed_land_value,
      assessed_improvement_value: taxAssessorData.assessed_improvement_value,
      market_value_total: taxAssessorData.market_value_total,
      market_value_land: taxAssessorData.market_value_land,
      market_value_improvement: taxAssessorData.market_value_improvement,
      
      // Building size/area information
      gross_sq_ft: taxAssessorData.gross_sq_ft,
      living_sq_ft: taxAssessorData.living_sq_ft,
      ground_floor_sq_ft: taxAssessorData.ground_floor_sq_ft,
      adjusted_sq_ft: taxAssessorData.adjusted_sq_ft,
      first_floor_sq_ft: taxAssessorData.first_floor_sq_ft,
      basement_sq_ft: taxAssessorData.basement_sq_ft,
      
      // Building characteristics
      building_code: taxAssessorData.building_code,
      building_class_code: taxAssessorData.building_class_code,
      structure_style_code: taxAssessorData.structure_style_code,
      property_use_code: taxAssessorData.property_use_code,
      property_use_code_mapped: taxAssessorData.property_use_code_mapped,
      
      // Building information
      building_info: {
        building_count: buildingData.building_count,
        floors_count: buildingData.floors_count,
        total_area: buildingData.total_area,
        residential_units_count: buildingData.residential_units_count,
        commercial_units_count: buildingData.commercial_units_count,
        year_built: buildingData.year_built,
        zoning: buildingData.zoning
      },
      
      // Units information
      units: unitsData.map((unit: any) => ({
        unit_number: unit.unit_number,
        unit_type: unit.unit_type,
        unit_style: unit.unit_style,
        gross_sq_ft: unit.gross_sq_ft,
        living_sq_ft: unit.living_sq_ft
      }))
    };

    return extendedPropertyDetails;
  } catch (error) {
    console.log('Error fetching extended property details:', error);
    return null;
  }
};

/**
 * Fetch detailed tax assessment history for a property
 * @param address Full address to query
 * @returns Array of tax assessment records or null if not found
 */
export const fetchPropertyTaxAssessment = async (address: string): Promise<TaxHistoryItem[] | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_PROPERTY_TAX_ASSESSMENT,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const propertyData = result.data?.address?.cherre_address__address;
    if (!propertyData) return null;

    const taxAssessorData = propertyData.tax_assessor_v2__property_address;
    if (!taxAssessorData) return null;

    // Tax assessment history
    const taxHistory = taxAssessorData.tax_assessor_history_v2__tax_assessor_id || [];

    // Map the tax history items
    const taxHistoryItems: TaxHistoryItem[] = taxHistory.map((item: any) => ({
      assessed_tax_year: item.assessed_tax_year,
      assessed_value_land: item.assessed_value_land,
      assessed_value_total: item.assessed_value_total,
      assessed_value_improvement: item.assessed_value_improvement,
      market_value_land: item.market_value_land,
      market_value_total: item.market_value_total,
      market_value_improvement: item.market_value_improvement,
      tax_bill_amount: item.tax_bill_amount,
      tax_code_area: item.tax_code_area,
      tax_amount_land: item.tax_amount_land,
      tax_amount_improvement: item.tax_amount_improvement
    }));

    return taxHistoryItems;
  } catch (error) {
    console.log('Error fetching property tax assessment:', error);
    return null;
  }
};

/**
 * Fetch school data by address
 * @param address Full address to query
 * @returns Array of school information or null if not found
 */
export const fetchSchoolData = async (address: string): Promise<SchoolInfo[] | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_SCHOOL_DATA,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();
  
    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    
    return result.data?.address?.cherre_address__address?.tax_assessor_v2__property_address || []
  } catch (error) {
    console.log('Error fetching school data:', error);
    return null;
  }
};

/**
 * Fetch nearby locations (POIs) by address
 * @param address Full address to query
 * @returns Neighborhood information including POIs or null if not found
 */
export const fetchNearbyLocations = async (address: string): Promise<NeighborhoodInfo | null> => {
  console.log(address, 121323)
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_NEARBY_LOCATIONS,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const neighborhoodBridge = result.data?.address?.cherre_address__address?.tax_assessor_v2__property_address?.tax_assessor_v2_usa_neighborhood_boundary_v2__bridge;
    if (!neighborhoodBridge || neighborhoodBridge.length === 0) return null;

    const neighborhood = neighborhoodBridge[0].usa_neighborhood_boundary_v2__geography_id;
    if (!neighborhood) return null;

    // Extract and process POIs
    const poiBridges = neighborhood.usa_points_of_interest_usa_neighborhood_boundary__bridge || [];
    const pois = poiBridges.map((bridge: any) => bridge.usa_points_of_interest__poi_id).filter(Boolean);

    return {
      geography_name: neighborhood.geography_name,
      reference_2: neighborhood.reference_2,
      pois
    };
  } catch (error) {
    console.log('Error fetching nearby locations:', error);
    return null;
  }
};

/**
 * Fetch environmental risk data by address
 * @param address Full address to query
 * @returns Environmental risk information or null if not found
 */
export const fetchEnvironmentalRisk = async (address: string): Promise<EnvironmentalRisk | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_ENVIRONMENTAL_RISK,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const environmentalRisk = result.data?.address?.cherre_address__address?.tax_assessor_v2__property_address?.environmental_risk;
    return environmentalRisk || null;
  } catch (error) {
    console.log('Error fetching environmental risk data:', error);
    return null;
  }
};

/**
 * Fetch multifamily units by address
 * @param address Full address to query
 * @returns Multifamily building information with units or null if not found
 */
export const fetchMultifamilyUnits = async (address: string): Promise<MultifamilyBuilding | null> => {
  try {
    const headers = new Headers();
    headers.append('Authorization', `Bearer ${CHERRE_API_KEY}`);
    headers.append('Content-Type', 'application/json');

    const graphql = JSON.stringify({
      query: GET_MULTIFAMILY_UNITS,
      variables: { address }
    });

    const requestOptions = {
      method: 'POST',
      headers,
      body: graphql,
    };

    const response = await fetch(CHERRE_API_ENDPOINT, requestOptions);
    const result = await response.json();

    if (result.errors) {
      console.log('GraphQL errors:', result.errors);
      return null;
    }

    const addressData = result.data?.address?.cherre_address__address;
    if (!addressData) return null;

    const parcelUnits = addressData.parcel_boundary_v2__address_point || [];
    const units = parcelUnits.flatMap((parcel: any) => {
      return parcel.tax_assessor_v2__tax_assessor_id || [];
    }).filter(Boolean);

    return {
      display_address: addressData.display_address,
      units
    };
  } catch (error) {
    console.log('Error fetching multifamily units:', error);
    return null;
  }
}; 