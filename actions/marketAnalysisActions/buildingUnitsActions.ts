/**
 * Service for building units data
 */

/**
 * Interface for building unit data response
 */
export interface BuildingUnitData {
  count: number;
  location: string;
  is_cached: boolean;
  postal_code: string;
  properties: Record<string, {
    address: string;
    units: Record<string, Array<{
      last_sold_price?: number;
      price_per_sqft?: number;
      last_sold_date?: string;
      list_price?: number;
      list_date?: string;
      listing_type?: string;
      description?: {
        beds?: number;
        baths_full?: number;
        baths_half?: number;
        sqft?: number;
        text?: string;
        sold_price?: number;
        year_built?: string;
        type?: string;
      };
      flags?: {
        is_for_rent?: boolean;
        is_contingent?: boolean;
        is_pending?: boolean;
      };
      photos?: Array<{
        href: string;
      }>;
      primary_photo?: {
        href: string;
      };
    }>>;
  }>;
  timestamp: string;
  past_days: number;
  cached_date: string;
}

/**
 * Fetch building units data by location and postal code
 * @param location The building location/address
 * @param postalCode The postal code
 * @returns Building units data
 */
export const fetchBuildingUnits = async (
  location: string,
  postalCode: string
): Promise<BuildingUnitData> => {
  try {
    const apiKey = process.env.NEXT_PUBLIC_RELM_SERVERLESS_API_KEY || '';
    
    if (!apiKey) {
      throw new Error('Building units API key is not defined in environment variables');
    }
    
    const response = await fetch(
      'https://bmvir06g47.execute-api.us-east-1.amazonaws.com/v1/search-building-units',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey
        },
        body: JSON.stringify({
          location,
          postal_code: postalCode
        })
      }
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch building units: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.log('Error fetching building units:', error);
    throw error;
  }
}

/**
 * Format the building unit data into a more usable structure
 * @param data The raw building unit data
 * @returns Formatted building unit data
 */
export const formatBuildingUnitsData = (data: BuildingUnitData) => {
  const formattedUnits: Array<{
    unitNumber: string;
    bedrooms: number | null;
    bathrooms: number | null;
    squareFeet: number | null;
    lastEvent: string | null;
    price: number | null;
    date: string | null;
    images: string[];
    history: Array<{
      type: string;
      price: number;
      date: string;
      isLatest: boolean;
    }>;
  }> = [];
  
  // Process each property and its units
  Object.values(data.properties).forEach(property => {
    Object.entries(property.units).forEach(([unitNumber, unitData]) => {
      if (unitData.length === 0) return;
      
      // Get all events for this unit
      const history: Array<{
        type: string;
        price: number;
        date: string;
        isLatest: boolean;
      }> = [];
      
      unitData.forEach(unitRecord => {
        // Add sold event
        if (unitRecord.last_sold_price && unitRecord.last_sold_date) {
          history.push({
            type: 'sold',
            price: unitRecord.last_sold_price,
            date: unitRecord.last_sold_date,
            isLatest: false
          });
        }
        
        // Add listed event
        if (unitRecord.list_price && unitRecord.list_date) {
          history.push({
            type: unitRecord.listing_type || 'listed',
            price: unitRecord.list_price,
            date: new Date(unitRecord.list_date).toISOString().split('T')[0],
            isLatest: false
          });
        }
      });
      
      // Sort history by date (newest first) and mark the latest event
      history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      if (history.length > 0) {
        history[0].isLatest = true;
      }
      
      // Get latest record for basic info
      const latestRecord = unitData[0];
      const description = latestRecord.description || {};
      
      // Collect all images
      const images: string[] = [];
      unitData.forEach(record => {
        if (record.primary_photo?.href) {
          images.push(record.primary_photo.href);
        }
        if (record.photos) {
          record.photos.forEach(photo => {
            if (photo.href && !images.includes(photo.href)) {
              images.push(photo.href);
            }
          });
        }
      });
      
      // Calculate full bathrooms (adding half baths)
      const bathsFull = description.baths_full || 0;
      const bathsHalf = description.baths_half || 0;
      const totalBaths = bathsFull + (bathsHalf > 0 ? 0.5 : 0);
      
      // Add formatted unit
      formattedUnits.push({
        unitNumber,
        bedrooms: description.beds || null,
        bathrooms: totalBaths > 0 ? totalBaths : null,
        squareFeet: description.sqft || null,
        lastEvent: history.length > 0 ? history[0].type : null,
        price: history.length > 0 ? history[0].price : null,
        date: history.length > 0 ? history[0].date : null,
        images,
        history
      });
    });
  });
  
  // Sort units alphabetically
  return formattedUnits.sort((a, b) => a.unitNumber.localeCompare(b.unitNumber));
} 