/**
 * GraphQL queries for property data and demographics
 */

export const GET_DEMOGRAPHICS_BY_ZIPCODE = `
  query GetDemographicsByZipcode($zipCode: String!) {
    usa_zip_code_boundary_v2(where: {zip_code: {_eq: $zipCode}}) {
      zip_code
      usa_demographics_v2__geography_id(order_by:{vintage:desc}limit:1) {
        vintage
        population_2020_count
        population_median_age
        median_household_income
        average_household_income
        crime_total_risk
      }
    }
  }
`;

export const GET_DEMOGRAPHICS_BY_CITY_STATE = `
  query GetDemographicsByCityState($city: String!, $state: String!) {
    usa_demographics_v2(
      where: {
        _and: [
          {geography_name: {_ilike: $city}},
          {geography_name: {_ilike: $state}}
        ]
      }
      order_by: {vintage: desc}
      limit: 1
    ) {
      vintage
      population_2020_count
      population_median_age
      median_household_income
      average_household_income
      crime_total_risk
    }
  }
`;

export const GET_PROPERTY_DETAILS = `
  query GetPropertyDetails($address: String!) {
    address(address: $address) {
      cherre_address__address {
        display_address
        street_address
        city
        state
        zip
        tax_assessor_v2__property_address {
          bed_count
          bath_count
          gross_sq_ft
          lot_size_acre
          year_built
          property_use_code_mapped
          hvacc_cooling_code
          hvacc_heating_code
          parking_garage_code
          parking_space_count
          basement_sq_ft
          basement_finished_sq_ft
          basement_unfinished_sq_ft
          flooring_material_code
          has_laundry_room
          fireplace_count
          structure_style_code
          roof_material_code
          driveway_material_code
          construction_code
          roof_construction_code
          exterior_code
          sewer_usage_code
          water_source_code
          last_sale_date
          last_sale_amount
          longitude
          latitude
          tax_assessor_history_v2__tax_assessor_id {
            assessed_tax_year
            assessed_value_land
            assessed_value_total
            tax_bill_amount
          }
          usa_zip_code_boundary_v2__zip_code {
            usa_demographics_v2__geography_id(order_by:{vintage:desc}limit:1) {
              vintage
              population_2020_count
              population_median_age
              median_household_income
              average_household_income
              crime_total_risk
            }
          }
        }
      }
    }
  }
`;

export const GET_EXTENDED_PROPERTY_DETAILS = `
  query GetExtendedPropertyDetails($address: String!) {
    address(address: $address) {
      cherre_address__address {
        tax_assessor_v2__property_address {
          # Building unit information
          unit_count
          parcel_number
          zoning_code
          
          # Property assessment values
          assessed_total_value
          assessed_land_value
          assessed_improvement_value
          market_value_total
          market_value_land
          market_value_improvement
          
          # Building size/area information
          gross_sq_ft
          living_sq_ft
          ground_floor_sq_ft
          adjusted_sq_ft
          first_floor_sq_ft
          basement_sq_ft
          
          # Building characteristics
          building_code
          building_class_code
          structure_style_code
          property_use_code
          property_use_code_mapped
        }
        # Building information
        usa_building_v2__address {
          building_count
          floors_count
          total_area
          residential_units_count
          commercial_units_count
          year_built
          zoning
        }
        # Units information
        usa_unit_v2__address(limit: 10) {
          unit_number
          unit_type
          unit_style
          gross_sq_ft
          living_sq_ft
        }
      }
    }
  }
`;

export const GET_PROPERTY_TAX_ASSESSMENT = `
  query GetPropertyTaxAssessment($address: String!) {
    address(address: $address) {
      cherre_address__address {
        tax_assessor_v2__property_address {
          # Tax assessment history
          tax_assessor_history_v2__tax_assessor_id(order_by: {assessed_tax_year: desc}, limit: 5) {
            assessed_tax_year
            assessed_value_land
            assessed_value_total
            assessed_value_improvement
            market_value_land
            market_value_total
            market_value_improvement
            tax_bill_amount
            tax_code_area
            tax_amount_land
            tax_amount_improvement
          }
        }
      }
    }
  }
`;

export const GET_SCHOOL_DATA = `
  query GetSchoolData($address: String!) {
    address(address: $address) {
      cherre_address__address {
        tax_assessor_v2__property_address {
          tax_assessor_v2_usa_school_boundary_v2__bridge(
            order_by: {usa_school_boundaries_v2__geography_id: {usa_schools_v2__institution_id: {test_rating: desc_nulls_last}}}
            where: {usa_school_boundaries_v2__geography_id: {is_most_grade_served: {_eq: true}}}
          ) {
            usa_school_boundaries_v2__geography_id {
              usa_schools_v2__institution_id {
                institution_id
                test_rating
                institution_name
                institution_address
                county_name
                student_count
                teacher_count
                enrollment_male
                enrollment_female
                grade_level_range_low
                grade_level_range_high
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_NEARBY_LOCATIONS = `
  query GetNearbyLocations($address: String!) {
    address(address: $address) {
      cherre_address__address {
        tax_assessor_v2__property_address {
          tax_assessor_v2_usa_neighborhood_boundary_v2__bridge {
            usa_neighborhood_boundary_v2__geography_id {
              geography_name
              reference_2
              usa_points_of_interest_usa_neighborhood_boundary__bridge {
                usa_points_of_interest__poi_id {
                  street
                  business_name
                  line_of_business
                  address_number
                  city
                  state
                  latitude
                  longitude
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_ENVIRONMENTAL_RISK = `
  query GetEnvironmentalRisk($address: String!) {
    address(address: $address) {
      cherre_address__address {
        tax_assessor_v2__property_address {
          latitude
          longitude
          # Environmental risk data
          environmental_risk {
            flood_factor {
              risk_level
              trend
              risk_score
            }
            fire_factor {
              risk_level
              trend
              risk_score
            }
            heat_factor {
              risk_level
              extreme_heat_days
              risk_score
            }
            wind_factor {
              risk_level
              trend
              risk_score
            }
            air_quality {
              risk_level
              trend
              aqi_score
            }
          }
        }
      }
    }
  }
`;

export const GET_MULTIFAMILY_UNITS = `
  query GetMultifamilyUnits($address: String!) {
    address(address: $address) {
      cherre_address__address {
        display_address
        parcel_boundary_v2__address_point(where: {tax_assessor_id: {_is_null: false}}) {
          tax_assessor_v2__tax_assessor_id {
            address
            tax_assessor_id
            bed_count
            bath_count
            hoa_1_fee_value
            building_type
            recorder_v2__tax_assessor_id{
              document_recorded_date
              recorder_mortgage_v2__recorder_id{
                amount
              }
            }
          }
        }
      }
    }
  }
`; 