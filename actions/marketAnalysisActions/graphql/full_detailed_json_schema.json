{"Cherre Main Menu": {"columns": [{"name": "Unnamed: 0", "type": "object", "sample_values": ["Cherre Data Dictionary - Nationwide ", "General", "Dataset Name", "Last Update & Version", "Description"]}, {"name": "Unnamed: 1", "type": "object", "sample_values": ["Nationwide Data Dictionary ", "V2 (3/18/2025)", "Real estate public data for US nationwide", "Dataset Description", "Property related data from registered documents including transactions and mortgages nationwide."]}, {"name": "Unnamed: 2", "type": "object", "sample_values": ["Object", "Recorder V2", "Recorder <PERSON><PERSON> V2", "Recorder <PERSON><PERSON> V2", "Recorder Legal Description V2"]}, {"name": "Unnamed: 3", "type": "object", "sample_values": ["Update Frequency", "Weekly", "Monthly", "Annually", "Quarterly"]}, {"name": "Unnamed: 4", "type": "object", "sample_values": ["Update Timing", "Mondays @ 12 AM UTC", "On the 12th of every Month \n@\n12 AM UTC", "In the end of September", "On the 18th of every Month\n@\n12 AM UTC"]}, {"name": "Unnamed: 5", "type": "object", "sample_values": ["Description", "Documents, foreclosures, tax, legal, purchase details that were recorded and relate to a property", "Buyer information including name, address, and ownership details", "Seller information including name, address, and ownership details", "Property legal description provided by the county"]}]}, "usa_building_footprint_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Building Footprint PK", "Building Centroid", "Latitude", "Longitude", "Geometry"]}, {"name": "Description", "type": "object", "sample_values": ["Building Footprint primary key", "Building center point", "Latitude component of centroid", "Longitude component of centroid", "Lot boundary polygon"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["building_footprint_id", "centroid", "latitude", "longitude", "geom"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "GEOGRAPHY", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-6019740000000000000, "POINT(-78.8108275596889 42.9280850849337)", 33.64342, -112.38335, "POLYGON((-112.383216311742 33.6435084602458, -112.383236193462 33.643521072672, -112.383253179255 33.6435363619146, -112.38347938945 33.6433259086323, -112.383448552194 33.6432943541736, -112.383216311742 33.6435084602458))"]}, {"name": "Category", "type": "object", "sample_values": ["Building Footprints"]}]}, "cherre_building_footprints_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Building Boundary PK", "Building Centroid", "Latitude", "Longitude", "Building Area"]}, {"name": "Description", "type": "object", "sample_values": ["Building Boundary primary key", "Building center point", "Latitude component of centroid", "Longitude component of centroid", "Area of building"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_building_footprint_pk", "cherre_centroid", "cherre_latitude", "cherre_longitude", "cherre_geom_area"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "GEOGRAPHY", "NUMERIC", "BOOLEAN", "DATE"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["87J3W5HQ+6MM-6-3-6-3", "POINT(-78.8108275596889 42.9280850849337)", 33.64342, -112.38335, 499.47]}, {"name": "Category", "type": "object", "sample_values": ["Building Footprints"]}]}, "parcel_boundary_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["<PERSON><PERSON><PERSON> Parcel Boundary PK", "Latitude", "Longitude", "<PERSON><PERSON><PERSON>", "Parcel Centroid Geometry"]}, {"name": "Description", "type": "object", "sample_values": ["The unique identifier for parcel boundary", "Latitude of the property.", "Longitude of the property.", "Centroid geom of a parcel", "Primary Parcel Number and unique identifier within the county/jurisdiction"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_parcel_boundary_pk", "latitude", "longitude", "parcel_centroid", "parcel_centroid_geometry"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "GEOGRAPHY", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [-42233861334, 33.64342, -112.38335, "POINT(-122.455392344454 37.6455338559429)", 23229130]}, {"name": "Category", "type": "object", "sample_values": ["<PERSON><PERSON><PERSON>"]}]}, "cherre_lot_boundaries_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Lot Boundary PK", "Lot Centroid", "Lot Area", "Latitude", "Longitude"]}, {"name": "Description", "type": "object", "sample_values": ["Lot Boundary primary key", "Lot center point", "Area of lot", "Latitude component of centroid", "Longitude component of centroid"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_lot_boundary_pk", "cherre_centroid", "cherre_geom_area", "cherre_latitude", "cherre_longitude"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "GEOGRAPHY", "NUMERIC", "BOOLEAN", "DATE"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": ["87J3W5HQ+6MM-6-3-6-3", "POINT(-78.8108275596889 42.9280850849337)", 499.47, 33.64342, -112.38335]}, {"name": "Category", "type": "object", "sample_values": ["Cherre Lot Boundaries"]}]}, "usa_points_of_interest": {"columns": [{"name": "Name", "type": "object", "sample_values": ["POI ID", "Cherre Parcel <PERSON>", "Business Name", "Street Address", "City"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier for each point of interest", "Cherre parcel id", "Name of the business/point of interest", "Street Address", "City"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["poi_id", "cherre_parcel_id", "business_name", "street", "city"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "GEOGRAPHY", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [26833379, 1050271715679090048, "<PERSON>", "632 <PERSON>", "Anchorage"]}, {"name": "Category", "type": "object", "sample_values": ["Points of Interest"]}]}, "usa_zip_code_boundary_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geography id", "Postal code geoms", "Geography code", "Postal code", "Reference 1"]}, {"name": "Description", "type": "object", "sample_values": ["Unique geography identifier of usa zip code boundary", "Postal Code geoms", "Geography type", "Numerical portion of the geography id", "Area Name (City & State Abbrev)"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geography_id", "zip_code_geom", "geography_code", "zip_code", "reference_1"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "GEOGRAPHY", "INTEGER", "NUMERIC", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": ["ZI99649", "<PERSON><PERSON><PERSON><PERSON><PERSON>((-157.630717 57.62974, -157.631282 57.629741, -157.629337 57.627942, -157.628332 57.62661, -157.627878 57.626228, -157.627694 57.625796, -157.627663 57.625723, -157.626765 57.624644, -157.626773 57.624455, -157.626407 57.624168, -157.625634 57.623562, -157.62265 57.621895, -157.621019 57.621626, -157.619974 57.621363, -157.615754 57.621379, -157.612945 57.621286, -157.610846 57.621011, -157.610616 57.620883, -157.610033 57.620813, -157.609688 57.620621, -157.609454 57.620618, -157.6067 57.619079, -157.606136 57.618507, -157.605871 57.617712, -157.605611 57.616929, -157.605683 57.615043, -157.605458 57.614789, -157.605478 57.612745, -157.605657 57.612653, -157.606377 57.612221, -157.606496 57.610846, -157.609155 57.605103, -157.61301 57.593432, -157.613037 57.588485, -157.613095 57.577957, -157.613582 57.517786, -157.613638 57.510958, -157.607024 57.495745, -157.606965 57.495608, -157.606911 57.495425, -157.606752 57.494878, -157.606699 57.494696, -157.606592 57.49431, -157.606313 57.493305, -157.606427 57.493199, -157.606721 57.492928, -157.603869 57.491298, -157.600549 57.489401, -157.599737 57.489266, -157.598005 57.488744, -157.596283 57.48797, -157.594949 57.487262, -157.592049 57.485722, -157.592053 57.485596, -157.588739 57.483736, -157.587022 57.482836, -157.586798 57.482582, -157.584503 57.48155, -157.584394 57.48136, -157.584165 57.481232, -157.583937 57.481103, -157.583708 57.480975, -157.58348 57.480847, -157.583251 57.480718, -157.58256 57.48046, -157.58187 57.480202, -157.580098 57.479238, -157.578326 57.478273, -157.576836 57.477564, -157.575345 57.476856, -157.575109 57.476916, -157.574422 57.476594, -157.572878 57.476191, -157.571648 57.475871, -157.571419 57.475743, -157.570836 57.475736, -157.569334 57.475342, -157.568634 57.475334, -157.566896 57.474999, -157.566196 57.474991, -157.565736 57.474798, -157.563986 57.474778, -157.563758 57.474649, -157.562943 57.474577, -157.555828 57.474433, -157.554309 57.474478, -157.55407 57.474601, -157.552554 57.474584, -157.552196 57.474769, -157.550679 57.474751, -157.550322 57.474936, -157.549271 57.474924, -157.548795 57.47517, -157.547745 57.475158, -157.547387 57.475342, -157.545038 57.475692, -157.544204 57.476123, -157.542446 57.476291, -157.54191 57.476568, -157.541374 57.476845, -157.541026 57.476778, -157.54044 57.476834, -157.53901 57.477572, -157.538662 57.477505, -157.537957 57.477623, -157.535811 57.47873, -157.535228 57.478723, -157.53487 57.478908, -157.532724 57.480015, -157.529779 57.480672, -157.52644 57.482394, -157.523604 57.483242, -157.520569 57.483206, -157.518677 57.483813, -157.515876 57.48378, -157.514929 57.484083, -157.512595 57.484056, -157.511768 57.484297, -157.510219 57.484279, -157.509083 57.484266, -157.508492 57.484447, -157.506158 57.48442, -157.505333 57.484599, -157.504047 57.484646, -157.503116 57.484572, -157.501702 57.48487, -157.499718 57.484846, -157.498657 57.485085, -157.49609 57.485055, -157.495151 57.485169, -157.491885 57.485067, -157.490954 57.484993, -157.48876 57.484401, -157.48465 57.482151, -157.484084 57.481704, -157.483839 57.479185, -157.483621 57.478794, -157.478584 57.479998, -157.476341 57.479622, -157.475376 57.477844, -157.474894 57.476991, -157.474572 57.475993, -157.474814 57.475386, -157.475137 57.474852, -157.475432 57.474461, -157.475759 57.474103, -157.477785 57.470612, -157.482556 57.468077, -157.490159 57.466139, -157.497464 57.465394, -157.501042 57.464797, -157.503279 57.463754, -157.505962 57.461518, -157.510733 57.455852, -157.518783 57.447951, -157.522958 57.443478, -157.523405 57.440646, -157.522958 57.438111, -157.521467 57.436322, -157.501042 57.436024, -157.492992 57.437366, -157.490308 57.439602, -157.48509 57.444373, -157.479574 57.447354, -157.470927 57.449889, -157.459895 57.451529, -157.449608 57.450932, -157.43455 57.447653, -157.430226 57.445118, -157.428437 57.442584, -157.428288 57.441093, -157.429208 57.438928, -157.437233 57.432744, -157.442899 57.429017, -157.443197 57.426631, -157.443048 57.424842, -157.439619 57.421861, -157.432612 57.419177, -157.426797 57.418432, -157.421579 57.419624, -157.418449 57.422159, -157.41789 57.424746, -157.417852 57.428122, -157.417256 57.430955, -157.416361 57.432744, -157.413976 57.435278, -157.411293 57.437813, -157.403689 57.441093, -157.396235 57.443329, -157.389973 57.443478, -157.384159 57.442584, -157.378792 57.440198, -157.37417 57.436322, -157.366567 57.432893, -157.361945 57.431849, -157.355385 57.432893, -157.353298 57.434086, -157.351211 57.436471, -157.350466 57.439006, -157.351956 57.443329, -157.353149 57.446311, -157.355833 57.449293, -157.360752 57.451529, -157.363883 57.452125, -157.369549 57.454212, -157.372678 57.456798, -157.374617 57.46107, -157.374021 57.464797, -157.369996 57.470165, -157.36612 57.471954, -157.364331 57.472401, -157.362392 57.472401, -157.357473 57.472848, -157.354193 57.471804, -157.350615 57.468376, -157.344651 57.460325, -157.340477 57.456747, -157.337027 57.454153, -157.332592 57.45396, -157.329795 57.454153, -157.328349 57.455214, -157.325842 57.457528, -157.32266 57.459457, -157.317549 57.460517, -157.312149 57.460614, -157.307038 57.460035, -157.30212 57.458396, -157.299324 57.455985, -157.297781 57.453189, -157.297202 57.450199, -157.297106 57.447692, -157.298938 57.444607, -157.299951 57.443394, -157.303084 57.441328, -157.305592 57.440653, -157.310509 57.439207, -157.314559 57.43776, -157.317838 57.436314, -157.318224 57.435446, -157.317259 57.434674, -157.312438 57.432939, -157.309449 57.430914, -157.308677 57.428889, -157.308099 57.426092, -157.307134 57.425417, -157.305253 57.424769, -157.304531 57.424549, -157.297202 57.42426, -157.283316 57.425031, -157.281099 57.426767, -157.280616 57.428889, -157.28052 57.430528, -157.281195 57.431685, -157.283458 57.434945, -157.283606 57.438339, -157.28322 57.439882, -157.281774 57.441424, -157.27917 57.443546, -157.274638 57.444896, -157.268852 57.445667, -157.264898 57.445667, -157.260945 57.444992, -157.257955 57.444221, -157.255834 57.442678, -157.254098 57.441232, -157.251205 57.437857, -157.246872 57.43394, -157.245096 57.432776, -157.241875 57.42952, -157.239376 57.427252, -157.239537 57.427025, -157.239457 57.427012, -157.238357 57.426837, -157.236669 57.426694, -157.235597 57.426766, -157.231819 57.426769, -157.230587 57.426899, -157.229569 57.427015, -157.227934 57.427175, -157.226916 57.427306, -157.226059 57.427508, -157.224532 57.427856, -157.222657 57.42816, -157.221853 57.428421, -157.221103 57.428508, -157.220541 57.428609, -157.220032 57.428754, -157.219844 57.428913, -157.219764 57.429116, -157.219711 57.429361, -157.219684 57.429477, -157.219711 57.429578, -157.219685 57.429766, -157.219792 57.429853, -157.220114 57.42991, -157.220221 57.43004, -157.220355 57.430185, -157.220462 57.430257, -157.220757 57.430272, -157.220998 57.430228, -157.221159 57.430242, -157.221267 57.430373, -157.22132 57.430459, -157.221267 57.430589, -157.221347 57.430734, -157.221589 57.430806, -157.221616 57.430922, -157.221509 57.431037, -157.221402 57.431225, -157.221402 57.431298, -157.221563 57.43137, -157.221911 57.431384, -157.222045 57.431369, -157.222179 57.431384, -157.222474 57.431427, -157.222608 57.431456, -157.222769 57.43147, -157.22309 57.431456, -157.223342 57.431449, -157.223559 57.431326, -157.22397 57.431016, -157.224319 57.431021, -157.22466 57.431214, -157.224638 57.431654, -157.225206 57.431976, -157.225908 57.431923, -157.226386 57.431679, -157.226972 57.431624, -157.227313 57.431817, -157.227285 57.432383, -157.227512 57.432512, -157.228095 57.43252, -157.228453 57.432337, -157.22892 57.432343, -157.229377 57.432538, -157.229475 57.432917, -157.229686 57.433072, -157.229927 57.433129, -157.230114 57.433115, -157.230222 57.433115, -157.230436 57.433086, -157.230597 57.433071, -157.230784 57.43297, -157.231052 57.432955, -157.231159 57.43297, -157.231294 57.433042, -157.231455 57.433143, -157.231616 57.433273, -157.231589 57.433374, -157.231562 57.433591, -157.23175 57.433721, -157.231965 57.43375, -157.232152 57.43375, -157.23234 57.433793, -157.232501 57.433967, -157.232689 57.434082, -157.232903 57.434241, -157.233064 57.434284, -157.233386 57.43427, -157.233922 57.43411, -157.234699 57.434254, -157.234994 57.434457, -157.234861 57.434775, -157.234566 57.434905, -157.234379 57.435006, -157.234326 57.435223, -157.23446 57.435339, -157.234648 57.435425, -157.235023 57.435382, -157.235505 57.435237, -157.236068 57.435294, -157.236203 57.435685, -157.235989 57.435988, -157.235641 57.436176, -157.235453 57.436292, -157.234971 57.436379, -157.234462 57.436408, -157.233926 57.436336, -157.233604 57.436163, -157.233443 57.436019, -157.233201 57.435946, -157.233014 57.436062, -157.233014 57.436221, -157.233336 57.43651, -157.233337 57.436727, -157.233256 57.436842, -157.232828 57.437146, -157.232507 57.437465, -157.232266 57.437595, -157.232159 57.437797, -157.231731 57.438014, -157.231463 57.438217, -157.231141 57.438347, -157.230847 57.438795, -157.230606 57.438954, -157.230097 57.4392, -157.229401 57.439302, -157.22865 57.439345, -157.22798 57.439346, -157.22723 57.439404, -157.22664 57.439477, -157.226104 57.439535, -157.225434 57.439578, -157.225005 57.439709, -157.224711 57.439882, -157.224229 57.440027, -157.222513 57.440244, -157.221682 57.440399, -157.221575 57.440418, -157.221093 57.440462, -157.220182 57.440665, -157.219351 57.440838, -157.218735 57.441055, -157.218065 57.441186, -157.217582 57.441374, -157.21702 57.441634, -157.216886 57.441764, -157.216243 57.442039, -157.216029 57.442285, -157.216002 57.44256, -157.216003 57.44282, -157.215735 57.443152, -157.215038 57.443456, -157.214342 57.443717, -157.21335 57.443934, -157.21268 57.44405, -157.211634 57.444195, -157.211098 57.444195, -157.210321 57.44421, -157.209222 57.444297, -157.208873 57.444355, -157.208096 57.44434, -157.207694 57.444355, -157.20756 57.444398, -157.207212 57.44463, -157.20697 57.444731, -157.206702 57.444876, -157.206381 57.445107, -157.206274 57.445136, -157.206113 57.445165, -157.205925 57.445252, -157.205282 57.445483, -157.204398 57.445628, -157.20362 57.4457, -157.203298 57.445686, -157.203138 57.445628, -157.202977 57.445614, -157.202789 57.445643, -157.202521 57.445599, -157.202119 57.445484, -157.201984 57.445354, -157.201716 57.445224, -157.201555 57.445195, -157.200966 57.445152, -157.200563 57.445051, -157.199973 57.444848, -157.199598 57.44469, -157.199062 57.444603, -157.198231 57.444488, -157.197587 57.444228, -157.196943 57.444141, -157.196112 57.444054, -157.194584 57.444026, -157.194048 57.444113, -157.193807 57.444243, -157.193727 57.444301, -157.193539 57.444358, -157.19319 57.444373, -157.192815 57.444359, -157.192493 57.444315, -157.192065 57.444214, -157.190992 57.44407, -157.190134 57.443839, -157.189089 57.444576, -157.189035 57.444619, -157.18866 57.444865, -157.188499 57.445096, -157.188392 57.445443, -157.1885 57.445877, -157.1885 57.446007, -157.188312 57.446152, -157.188071 57.446311, -157.187803 57.44686, -157.187508 57.447034, -157.187187 57.447135, -157.186892 57.447149, -157.185954 57.447308, -157.185123 57.447858, -157.184721 57.448132, -157.184211 57.448147, -157.183568 57.448046, -157.182924 57.44767, -157.182441 57.447569, -157.181798 57.447482, -157.181503 57.447526, -157.181235 57.447584, -157.180672 57.447555, -157.180296 57.447555, -157.179948 57.44767, -157.179573 57.447786, -157.178259 57.447743, -157.177481 57.447887, -157.177053 57.448133, -157.176623 57.448263, -157.176221 57.448639, -157.175658 57.448899, -157.173254 57.448439, -157.166044 57.447061, -157.165875 57.447029, -157.164758 57.446816, -157.163641 57.446602, -157.163043 57.447282, -157.162444 57.447963, -157.156836 57.454342, -157.151227 57.460722, -157.150411 57.462154, -157.150361 57.463056, -157.151113 57.46456, -157.152918 57.465513, -157.156777 57.468019, -157.158832 57.467818, -157.159985 57.467317, -157.164296 57.46837, -157.16575 57.46842, -157.168457 57.46842, -157.169459 57.468721, -157.175123 57.470475, -157.17778 57.471929, -157.179284 57.473583, -157.179484 57.474736, -157.178682 57.478194, -157.177429 57.479097, -157.172717 57.479397, -157.172552 57.479415, -157.169108 57.479798, -157.162642 57.480801, -157.161439 57.481603, -157.161238 57.482656, -157.160681 57.483484, -157.160637 57.483808, -157.160812 57.484184, -157.161138 57.484836, -157.161113 57.485062, -157.160737 57.485688, -157.160787 57.486365, -157.160988 57.486966, -157.16189 57.487418, -157.164096 57.488144, -157.164722 57.488871, -157.164697 57.489473, -157.164071 57.489974, -157.16283 57.4902, -157.162053 57.490522, -157.161401 57.491267, -157.161006 57.491569, -157.160401 57.491756, -157.159283 57.492151, -157.158725 57.4925, -157.158585 57.493362, -157.158376 57.494502, -157.157631 57.496201, -157.157817 57.497016, -157.158284 57.497609, -157.158054 57.497707, -157.157887 57.497726, -157.157677 57.497691, -157.157584 57.497761, -157.157363 57.497842, -157.157235 57.497842, -157.157235 57.497947, -157.157175 57.498083, -157.156537 57.498447, -157.156397 57.498505, -157.156106 57.49854, -157.154896 57.498994, -157.15475 57.49912, -157.154128 57.499448, -157.153709 57.499495, -157.153337 57.499448, -157.153197 57.499367, -157.153057 57.499367, -157.153046 57.499425, -157.153046 57.499506, -157.152801 57.499564, -157.152825 57.499669, -157.152836 57.499762, -157.152741 57.499979, -157.152932 57.499959, -157.152985 57.500007, -157.152841 57.500077, -157.152685 57.500184, -157.152525 57.500553, -157.152541 57.500644, -157.152423 57.500767, -157.152397 57.500874, -157.152439 57.500986, -157.152623 57.501044, -157.152616 57.501104, -157.152407 57.501179, -157.152455 57.501334, -157.152342 57.501408, -157.152177 57.501393, -157.152086 57.501446, -157.152054 57.501557, -157.151904 57.501671, -157.151406 57.501729, -157.151289 57.501773, -157.151418 57.501853, -157.151405 57.501902, -157.15092 57.502008, -157.150743 57.501955, -157.150428 57.502014, -157.150128 57.502083, -157.150027 57.502158, -157.149968 57.502238, -157.149898 57.502308, -157.149845 57.502308, -157.149727 57.50226, -157.149599 57.502115, -157.14955 57.502099, -157.149433 57.502147, -157.149401 57.502345, -157.149304 57.502377, -157.149224 57.502345, -157.149181 57.502262, -157.149101 57.502263, -157.148925 57.502236, -157.148826 57.502233, -157.148767 57.502258, -157.148694 57.502348, -157.148631 57.502422, -157.14856 57.502446, -157.148525 57.502446, -157.148483 57.502426, -157.148468 57.502381, -157.148415 57.502336, -157.148321 57.502325, -157.14813 57.502381, -157.148009 57.50246, -157.147938 57.502464, -157.147806 57.502396, -157.147757 57.50234, -157.147682 57.502332, -157.147622 57.502347, -157.147524 57.502347, -157.147396 57.50234, -157.147276 57.502366, -157.147194 57.502436, -157.147067 57.502356, -157.146967 57.502356, -157.146891 57.502389, -157.146705 57.502451, -157.146644 57.502532, -157.146596 57.502561, -157.146549 57.502561, -157.146501 57.502499, -157.146377 57.502513, -157.146339 57.502561, -157.146301 57.502599, -157.14624 57.502603, -157.146164 57.50258, -157.146106 57.502589, -157.14603 57.502618, -157.145897 57.502622, -157.145612 57.502665, -157.145555 57.502746, -157.145586 57.502837, -157.145418 57.50287, -157.145284 57.502843, -157.145119 57.502824, -157.145082 57.502843, -157.145007 57.502873, -157.144924 57.50289, -157.144852 57.502916, -157.144855 57.503005, -157.144832 57.503041, -157.144759 57.503051, -157.14467 57.503058, -157.144558 57.502972, -157.144549 57.5029, -157.144509 57.50287, -157.144407 57.502876, -157.144361 57.502913, -157.144354 57.503018, -157.144308 57.503078, -157.144195 57.503113, -157.144075 57.503053, -157.14398 57.503053, -157.143908 57.503065, -157.143812 57.503125, -157.143716 57.503221, -157.14356 57.503305, -157.143524 57.503281, -157.143416 57.503281, -157.143308 57.503245, -157.143248 57.503245, -157.143176 57.503293, -157.143188 57.503389, -157.143224 57.503473, -157.143176 57.503497, -157.142997 57.503449, -157.142901 57.503425, -157.142889 57.503473, -157.14302 57.503617, -157.142985 57.503641, -157.142901 57.503641, -157.142781 57.503617, -157.142649 57.503593, -157.142613 57.503761, -157.142637 57.503928, -157.142718 57.504065, -157.142487 57.504036, -157.142355 57.504036, -157.142199 57.504048, -157.142007 57.504102, -157.142013 57.504156, -157.142205 57.504234, -157.142265 57.504288, -157.142223 57.504342, -157.142181 57.504354, -157.142007 57.504366, -157.141888 57.504408, -157.141846 57.504456, -157.141864 57.504534, -157.142025 57.504546, -157.142087 57.504594, -157.142079 57.50463, -157.14193 57.504636, -157.141678 57.504612, -157.141534 57.504648, -157.141672 57.504756, -157.141642 57.504833, -157.141492 57.504887, -157.14142 57.504917, -157.141534 57.504965, -157.141607 57.504997, -157.141552 57.505043, -157.141311 57.505112, -157.141412 57.505163, -157.14152 57.505163, -157.141584 57.505213, -157.141505 57.505278, -157.141347 57.505313, -157.141218 57.505335, -157.141182 57.505443, -157.141125 57.505471, -157.141096 57.505522, -157.141146 57.5056, -157.14106 57.505665, -157.1409 57.505679, -157.140799 57.505859, -157.140794 57.505871, -157.140746 57.505986, -157.140612 57.505976, -157.140612 57.505928, -157.140536 57.505871, -157.140392 57.505909, -157.140411 57.506024, -157.140526 57.506091, -157.140526 57.506349, -157.140727 57.506397, -157.140689 57.506474, -157.140565 57.506493, -157.140526 57.506579, -157.140459 57.506618, -157.14043 57.506704, -157.140383 57.506742, -157.140172 57.506752, -157.140114 57.50679, -157.140402 57.506905, -157.140526 57.506943, -157.140526 57.507039, -157.14049 57.507138, -157.140311 57.507101, -157.140019 57.506981, -157.139813 57.506919, -157.139731 57.506934, -157.139741 57.50702, -157.13977 57.507106, -157.139779 57.507183, -157.139741 57.507216, -157.139617 57.507202, -157.13943 57.507159, -157.139248 57.507144, -157.139238 57.507178, -157.139315 57.507245, -157.139415 57.507297, -157.139392 57.507336, -157.13932 57.507403, -157.139181 57.507417, -157.139066 57.507388, -157.138999 57.507408, -157.138895 57.507519, -157.13879 57.507659, -157.138669 57.507676, -157.138574 57.507572, -157.13848 57.507523, -157.138436 57.507606, -157.138362 57.50766, -157.138245 57.507714, -157.138091 57.507722, -157.13805 57.507816, -157.137997 57.507879, -157.137899 57.507904, -157.137807 57.507867, -157.137631 57.507875, -157.137567 57.507904, -157.137531 57.508008, -157.137499 57.508089, -157.137085 57.508236, -157.136844 57.508384, -157.136748 57.508384, -157.136642 57.508315, -157.136567 57.508194, -157.136498 57.508191, -157.136411 57.508225, -157.136417 57.508303, -157.136539 57.508465, -157.136554 57.50854, -157.136492 57.508593, -157.136417 57.508602, -157.136146 57.508684, -157.136036 57.508749, -157.136052 57.508796, -157.136033 57.508855, -157.13598 57.508868, -157.135921 57.508852, -157.135855 57.508758, -157.135843 57.508705, -157.135727 57.508693, -157.13559 57.508715, -157.135562 57.508749, -157.135615 57.50884, -157.135609 57.50889, -157.135621 57.508946, -157.135678 57.508983, -157.135756 57.509095, -157.135743 57.509141, -157.135678 57.509175, -157.135546 57.509153, -157.13549 57.509178, -157.135465 57.509241, -157.135483 57.509392, -157.135461 57.509455, -157.135413 57.509477, -157.135362 57.50947, -157.135277 57.509377, -157.135155 57.50937, -157.135104 57.509403, -157.135111 57.509447, -157.135185 57.509543, -157.135299 57.509609, -157.135435 57.509621, -157.135435 57.509635, -157.135391 57.509668, -157.135273 57.509702, -157.13496 57.509705, -157.134927 57.509753, -157.134931 57.50979, -157.135089 57.509845, -157.135314 57.509923, -157.135343 57.509959, -157.135365 57.510081, -157.135494 57.51035, -157.135564 57.510534, -157.135479 57.510563, -157.135354 57.510554, -157.135198 57.510603, -157.13505 57.510693, -157.134965 57.510764, -157.135037 57.510885, -157.135095 57.51101, -157.135068 57.511051, -157.135001 57.511077, -157.134916 57.511091, -157.134889 57.511162, -157.135023 57.511382, -157.135095 57.511458, -157.135314 57.511449, -157.135372 57.511484, -157.135381 57.511507, -157.135314 57.511543, -157.135068 57.511614, -157.135014 57.511628, -157.134889 57.511789, -157.134898 57.511874, -157.134956 57.511918, -157.134934 57.512039, -157.134894 57.512182, -157.134862 57.512346, -157.134806 57.51255, -157.134806 57.512609, -157.134854 57.512646, -157.134955 57.512673, -157.135024 57.512673, -157.135269 57.512726, -157.135323 57.512801, -157.135269 57.512875, -157.135131 57.512928, -157.135104 57.512966, -157.135264 57.512976, -157.135546 57.51295, -157.135578 57.513014, -157.135598 57.513159, -157.135647 57.513301, -157.135791 57.513306, -157.135796 57.513338, -157.135754 57.51345, -157.135701 57.513578, -157.13569 57.513716, -157.13577 57.513834, -157.135892 57.513956, -157.135977 57.513993, -157.135993 57.514057, -157.136084 57.514084, -157.136169 57.514105, -157.136478 57.514132, -157.136725 57.514339, -157.137079 57.514503, -157.137183 57.514598, -157.137174 57.514632, -157.137105 57.514693, -157.137105 57.514736, -157.137165 57.514813, -157.137312 57.514779, -157.137372 57.514839, -157.137338 57.514943, -157.137165 57.515003, -157.137208 57.515046, -157.137355 57.51509, -157.137338 57.515176, -157.137243 57.515202, -157.137122 57.515228, -157.137114 57.515262, -157.137217 57.515271, -157.137303 57.515348, -157.13726 57.515374, -157.137122 57.515383, -157.137122 57.515426, -157.137217 57.515564, -157.137277 57.515607, -157.137364 57.515685, -157.137364 57.515737, -157.137269 57.515745, -157.137157 57.51584, -157.137062 57.515944, -157.136958 57.515961, -157.136837 57.51603, -157.136777 57.516133, -157.136794 57.51622, -157.136855 57.516272, -157.136751 57.516349, -157.13663 57.516358, -157.136466 57.516435, -157.136415 57.516479, -157.136466 57.516522, -157.136458 57.516574, -157.136302 57.516634, -157.136121 57.516755, -157.136001 57.516806, -157.135837 57.516746, -157.13575 57.516763, -157.135707 57.516824, -157.135699 57.516979, -157.135742 57.517065, -157.135681 57.5171, -157.135319 57.517048, -157.135034 57.517083, -157.134879 57.517173, -157.134805 57.517312, -157.134737 57.517356, -157.134665 57.517332, -157.134461 57.517159, -157.134344 57.517159, -157.134145 57.517457, -157.133808 57.517602, -157.132363 57.517742, -157.132219 57.51788, -157.132433 57.517986, -157.132701 57.518126, -157.132654 57.518185, -157.132072 57.518196, -157.131874 57.518173, -157.131775 57.517978, -157.131542 57.517907, -157.131254 57.517783, -157.131084 57.517776, -157.131045 57.517861, -157.130921 57.518011, -157.130764 57.51824, -157.130499 57.518375, -157.130012 57.518476, -157.129534 57.518463, -157.129377 57.518463, -157.129358 57.518607, -157.129322 57.518741, -157.129317 57.519067, -157.129367 57.519197, -157.129347 57.519387, -157.129287 57.519427, -157.128577 57.519737, -157.128477 57.519747, -157.128327 57.519597, -157.128117 57.519547, -157.127867 57.519597, -157.127608 57.519677, -157.127448 57.519807, -157.127388 57.519947, -157.127458 57.520307, -157.127498 57.520377, -157.127668 57.520407, -157.128047 57.520257, -157.128287 57.520237, -157.128447 57.520357, -157.128447 57.520467, -157.128327 57.520567, -157.127977 57.520847, -157.127839 57.521131, -157.12738 57.521401, -157.126989 57.521619, -157.126662 57.52176, -157.126585 57.521818, -157.126482 57.522241, -157.12645 57.522497, -157.126521 57.522786, -157.126557 57.522989, -157.12627 57.52317, -157.126046 57.523304, -157.125598 57.523461, -157.12512 57.523558, -157.123851 57.52379, -157.123606 57.523861, -157.122809 57.524261, -157.122517 57.524584, -157.122319 57.524673, -157.122091 57.524683, -157.121913 57.524821, -157.121824 57.525049, -157.121873 57.525198, -157.121962 57.525327, -157.122467 57.525802, -157.122765 57.526049, -157.122725 57.526129, -157.12221 57.526168, -157.121606 57.526238, -157.121586 57.526297, -157.122131 57.526891, -157.12217 57.52702, -157.12217 57.527129, -157.121873 57.527574, -157.121665 57.527792, -157.121487 57.527852, -157.12122 57.527703, -157.121032 57.527674, -157.120797 57.527737, -157.12065 57.527916, -157.12065 57.527989, -157.120732 57.528088, -157.120879 57.528243, -157.120805 57.528333, -157.120683 57.528472, -157.12065 57.528586, -157.120724 57.528676, -157.120707 57.528766, -157.120486 57.528946, -157.119652 57.529052, -157.119031 57.529151, -157.118753 57.529052, -157.118647 57.528954, -157.118818 57.528823, -157.119121 57.528619, -157.119121 57.528529, -157.118925 57.528325, -157.118728 57.528259, -157.118328 57.528243, -157.117878 57.528325, -157.117314 57.528456, -157.116921 57.528447, -157.116815 57.528357, -157.116848 57.528251, -157.116758 57.528186, -157.11621 57.527703, -157.116137 57.527605, -157.116267 57.527433, -157.116298 57.527322, -157.115912 57.527387, -157.115594 57.527607, -157.114945 57.527778, -157.114554 57.527803, -157.114566 57.527913, -157.114823 57.528109, -157.115459 57.528525, -157.115508 57.528647, -157.115417 57.528888, -157.115359 57.529228, -157.115369 57.529325, -157.115475 57.529519, -157.115456 57.529616, -157.115504 57.529674, -157.115727 57.529761, -157.115688 57.529828, -157.115553 57.529848, -157.114875 57.529799, -157.114556 57.529664, -157.11444 57.529596, -157.114227 57.529616, -157.114188 57.529703, -157.114217 57.529915, -157.114228 57.530138, -157.114043 57.530332, -157.113646 57.530419, -157.113356 57.530457, -157.113337 57.530564, -157.113385 57.531203, -157.113453 57.53129, -157.114033 57.531396, -157.114237 57.531464, -157.114217 57.531551, -157.114004 57.531648, -157.113598 57.531686, -157.112882 57.531561, -157.112495 57.531619, -157.112369 57.531725, -157.112437 57.531841, -157.11264 57.531909, -157.113221 57.531996, -157.113434 57.531986, -157.113646 57.53217, -157.114024 57.532722, -157.114043 57.532867, -157.113975 57.532983, -157.113792 57.533186, -157.113511 57.53338, -157.113434 57.533535, -157.113434 57.533622, -157.113637 57.533738, -157.113821 57.533796, -157.11445 57.533883, -157.114585 57.533951, -157.114585 57.534057, -157.114585 57.534154, -157.113975 57.534435, -157.113811 57.534541, -157.11383 57.534676, -157.113975 57.534735, -157.114275 57.534783, -157.114672 57.534812, -157.115388 57.53487, -157.11592 57.535015, -157.116598 57.53518, -157.116966 57.535276, -157.117517 57.535354, -157.117643 57.53546, -157.117624 57.535528, -157.117206 57.535905, -157.11744 57.535959, -157.117517 57.535959, -157.118369 57.535973, -157.119109 57.536007, -157.119298 57.536225, -157.119467 57.536457, -157.119559 57.536501, -157.119733 57.536496, -157.120183 57.53652, -157.120338 57.536588, -157.120328 57.53666, -157.120251 57.536704, -157.119844 57.536854, -157.119472 57.536955, -157.119467 57.537125, -157.119573 57.537202, -157.119984 57.537333, -157.120422 57.537405, -157.120945 57.537508, -157.121309 57.537587, -157.12174 57.537587, -157.122093 57.537667, -157.122138 57.537803, -157.122013 57.537906, -157.121604 57.537962, -157.121434 57.538042, -157.121434 57.538224, -157.121479 57.538417, -157.121343 57.538565, -157.120729 57.538735, -157.120684 57.538871, -157.120513 57.539087, -157.120365 57.539156, -157.119411 57.53894, -157.118763 57.53869, -157.118479 57.538542, -157.118225 57.538569, -157.118053 57.53869, -157.11811 57.538832, -157.118178 57.538923, -157.11824 57.539031, -157.118326 57.53911, -157.118507 57.539213, -157.118875 57.539437, -157.118875 57.53959, -157.118848 57.539716, -157.118696 57.539823, -157.118561 57.53985, -157.118265 57.540003, -157.118059 57.540137, -157.117942 57.540155, -157.117601 57.540128, -157.116829 57.540146, -157.116381 57.5402, -157.115932 57.540353, -157.115537 57.540541, -157.115394 57.540649, -157.115385 57.540765, -157.11543 57.540882, -157.115555 57.540954, -157.115726 57.540963, -157.116111 57.541017, -157.116345 57.541088, -157.116623 57.541106, -157.116946 57.541115, -157.117116 57.541151, -157.117305 57.541268, -157.117457 57.541421, -157.117565 57.541447, -157.117709 57.541376, -157.117924 57.541196, -157.118283 57.541187, -157.118505 57.541301, -157.118626 57.541764, -157.118725 57.542033, -157.118806 57.542204, -157.118833 57.542375, -157.118806 57.542537, -157.118698 57.542789, -157.118626 57.542933, -157.118671 57.543077, -157.11877 57.543185, -157.118932 57.543319, -157.119237 57.543454, -157.119717 57.543612, -157.120023 57.543834, -157.120089 57.543965, -157.12046 57.54438, -157.120951 57.544849, -157.121344 57.545024, -157.122535 57.545406, -157.124042 57.545799, -157.125133 57.54605, -157.125843 57.546192, -157.126317 57.546207, -157.12687 57.546242, -157.127385 57.546278, -157.128036 57.546372, -157.128326 57.546491, -157.128361 57.546567, -157.128302 57.54668, -157.128048 57.546709, -157.127616 57.546733, -157.127397 57.546763, -157.127266 57.546816, -157.127093 57.546927, -157.126871 57.547047, -157.125966 57.547315, -157.125106 57.547509, -157.124404 57.547592, -157.124173 57.547657, -157.124118 57.547777, -157.124164 57.547897, -157.124229 57.548008, -157.124386 57.548128, -157.125042 57.548313, -157.125254 57.548415, -157.125356 57.548544, -157.125421 57.548766, -157.125448 57.54895, -157.125568 57.549228, -157.125679 57.549329, -157.125818 57.549385, -157.125993 57.549449, -157.126372 57.549569, -157.126585 57.549689, -157.127139 57.549874, -157.127785 57.550013, -157.128414 57.550105, -157.129116 57.550281, -157.129504 57.550447, -157.129661 57.550641, -157.129707 57.550743, -157.129679 57.550964, -157.129661 57.551214, -157.129679 57.551371, -157.129827 57.551546, -157.130123 57.551713, -157.130529 57.55186, -157.130917 57.551907, -157.13149 57.551879, -157.131721 57.551768, -157.131822 57.551611, -157.131878 57.551426, -157.132275 57.551186, -157.133005 57.5509, -157.133501 57.550694, -157.133884 57.550382, -157.134235 57.550109, -157.13475 57.549773, -157.135078 57.549625, -157.135242 57.549617, -157.135523 57.549617, -157.135796 57.549742, -157.136038 57.549968, -157.136155 57.550226, -157.136171 57.550366, -157.136085 57.550562, -157.135976 57.550679, -157.13596 57.550788, -157.13596 57.550874, -157.136062 57.550975, -157.136561 57.551163, -157.137287 57.55135, -157.138872 57.551545, -157.13977 57.551709, -157.139996 57.551839, -157.140309 57.551904, -157.140418 57.551982, -157.140324 57.552146, -157.140012 57.552342, -157.139762 57.55242, -157.13938 57.552459, -157.138693 57.552506, -157.138419 57.552607, -157.138256 57.552724, -157.138076 57.552951, -157.137935 57.553247, -157.137896 57.553388, -157.137945 57.553672, -157.138398 57.554319, -157.138769 57.554576, -157.13951 57.554889, -157.14048 57.555089, -157.141307 57.555117, -157.141962 57.554889, -157.142618 57.554404, -157.143417 57.554205, -157.143816 57.554262, -157.143959 57.554604, -157.143987 57.554975, -157.143645 57.555688, -157.143103 57.5566, -157.142932 57.557142, -157.142932 57.557627, -157.142875 57.558026, -157.142504 57.559395, -157.141934 57.560364, -157.141164 57.560849, -157.14028 57.56102, -157.13951 57.560963, -157.139054 57.560649, -157.138911 57.56025, -157.139196 57.559423, -157.139425 57.558653, -157.139425 57.558397, -157.139282 57.558168, -157.138484 57.557883, -157.137828 57.557883, -157.136345 57.558397, -157.135461 57.558625, -157.134149 57.559109, -157.134035 57.559452, -157.134149 57.559879, -157.134548 57.560336, -157.134862 57.560564, -157.135375 57.560934, -157.136231 57.561362, -157.136716 57.561904, -157.136773 57.56256, -157.134089 57.563411, -157.136411 57.565127, -157.138991 57.567351, -157.141178 57.571979, -157.145082 57.574716, -157.148417 57.576081, -157.15136 57.576832, -157.158895 57.580215, -157.163913 57.582033, -157.168349 57.582469, -157.170676 57.582178, -157.173439 57.581015, -157.177443 57.579392, -157.184057 57.579706, -157.187184 57.580142, -157.190958 57.582052, -157.192493 57.584142, -157.193802 57.585887, -157.195547 57.589596, -157.198529 57.592505, -157.203328 57.595778, -157.208637 57.59825, -157.212491 57.599486, -157.215909 57.599486, -157.219545 57.598396, -157.222673 57.596432, -157.224751 57.593471, -157.229315 57.587706, -157.232112 57.58385, -157.236066 57.581247, -157.241852 57.579608, -157.248216 57.578836, -157.25217 57.579029, -157.259209 57.58009, -157.265091 57.580668, -157.271473 57.579709, -157.275698 57.580283, -157.276759 57.580379, -157.278502 57.582462, -157.279652 57.586261, -157.281195 57.587708, -157.284666 57.587997, -157.292863 57.586165, -157.305688 57.580186, -157.306074 57.578643, -157.305101 57.574642, -157.306589 57.575354, -157.309172 57.57534, -157.307038 57.57064, -157.304917 57.56765, -157.304531 57.565722, -157.305206 57.562732, -157.30887 57.559743, -157.322949 57.557043, -157.330663 57.554632, -157.333652 57.554343, -157.339535 57.554536, -157.344838 57.556272, -157.348792 57.558393, -157.351588 57.561382, -157.352649", "ZI", 99649, "Pilot Point city, Ugashik CDP, AK"]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_school_boundaries_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geography id", "Area (Square Miles)", "Boundary Count", "School District Level", "School District Name"]}, {"name": "Description", "type": "object", "sample_values": ["The persistent unique identifier for the school attendance area", "The area of the attendance area in square miles", "The count of distinct attendance area shapes for a school. This value would be set to 3 for a PK-5 school having one distinct attendance area for PK, one distinct attendance areas for KG-2, and another distinct attendance area for 3-5.", "The instructional level for the school district - Unified, Elementary, Secondary", "The name of the school district in which the scthool attendance area is located\n Note that for extradistrict school attendance areas this will not be the name of the school district that operates the school, as the school providing the instruction is operated by and located in a different \"home\" (usually adjacent) school district. See extradist description for more information"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geography_id", "area_square_miles", "boundary_count", "district_instructional_level", "district_name"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "INTEGER", "BOOLEAN", "BOOLEAn"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": ["d30b70602d463bfca9756cb77e8524e6", 345.21911919, 1, "Unified", "Bryant Public Schools"]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_census_tract_boundary": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geo id pk", "State name", "State fips code", "County fips code", "Tract ce"]}, {"name": "Description", "type": "object", "sample_values": ["Unique Identifier of census tract boundary", "State name", "State fips code", "County fips code", "Tract code"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geo_id_pk", "state_name", "state_fips_code", "county_fips_code", "tract_ce"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "GEOGRAPHY", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [34025810001, "New Jersey", 34, 25, 810001]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_census_block_group_boundary": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geography ID", "Cherre Ingest Datetime", "Census Block Group Geography", "Core-Based Statistical Area code", "County Fips code"]}, {"name": "Description", "type": "object", "sample_values": ["The unique, persistent identifier for the school attendance area", "Indicates the time/moment when the record was ingested by cherre, this field can be used for the incremental pulling", "Census block group geography", "Core-Based Statistical Area code", "County fips code"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geography_id", "cherre_ingest_datetime", "block_group_geom", "cbsa_fips_code", "county_fips_code"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "TIMESTAMP", "GEOGRAPHY", "NUMERIC"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [10150020021, "2023-12-07 02:38:46.660000 UTC", "POLYG<PERSON>((-85.7543620000063 33.6524980003825, -85.7541330002377 33.6521460003377, -85.7539679998233 33.6519589998081, -85.7537030001932 33.6517219996706, -85.7535549996635 33.6515899998786, -85.7528379998737 33.6511779997649, -85.7526019997824 33.6509629997427, -85.751524999579 33.6499020002772, -85.7514640003633 33.6498460003926, -85.7513339997643 33.6497260002548, -85.7509989996041 33.6494730002788, -85.7505840002514 33.649233000003, -85.750054000092 33.6493019995876, -85.7490349998653 33.6494130002098, -85.7480159996387 33.6495340003938, -85.7470069998731 33.6496850001627, -85.745364999695 33.6500370002074, -85.7438760002772 33.6504080002291, -85.7435860003936 33.6504820000443, -85.7434509995639 33.6505159998136, -85.7430299999345 33.6506099996518, -85.7423890000523 33.6506620002511, -85.7421729999839 33.65065199979, -85.7419559998694 33.6506250003436, -85.7415079998942 33.6505070002979, -85.7412770000335 33.6504359997216, -85.740928000127 33.6503399997912, -85.740604999621 33.6502680000682, -85.7402130004296 33.6502110001376, -85.739732999878 33.6501710000916, -85.7392029997186 33.650165999861, -85.7387189998819 33.6501649998149, -85.7385959996056 33.6501649998149, -85.7363620000062 33.6501700000455, -85.7361110001225 33.6501780004144, -85.7348050001506 33.6501479999303, -85.7345690000593 33.6501429996997, -85.7343810003828 33.6501390004146, -85.7334119997639 33.6501259998151, -85.7322730002987 33.6501110000227, -85.7311410002569 33.6500810004379, -85.7308630000274 33.6500740001151, -85.7300429999844 33.6500429995848, -85.7292209998492 33.649992999977, -85.728661000105 33.6499550000233, -85.7274219996256 33.6498380000237, -85.7271349998803 33.6498109996779, -85.7243829997756 33.6495280001172, -85.7220449998768 33.649242000418, -85.721539999971 33.6492079997494, -85.7210800003417 33.649203999565, -85.7210350000652 33.649203999565, -85.7204730002288 33.649262000441, -85.7201830003452 33.649304999726, -85.7196930002318 33.64942599991, -85.7193200001179 33.6495500002324, -85.7187669997971 33.6498289996087, -85.7183389998449 33.6501000004147, -85.7179390002846 33.6504129995603, -85.7172619996416 33.6509780004344, -85.7171190002416 33.6510880001111, -85.7168769998736 33.6508770002734, -85.7156619996015 33.6518609997854, -85.7137369998626 33.6534320002866, -85.7128569997507 33.6541599996843, -85.7119619998464 33.6548799996123, -85.7108770001734 33.6557670000471, -85.7094980004323 33.6568909997199, -85.7083620002061 33.6578269997163, -85.7077920000008 33.6582910004294, -85.7072829999105 33.6586990003586, -85.7070239996578 33.6589169996199, -85.7067590000278 33.6591339997344, -85.7065949996594 33.6592630002873, -85.7060679996384 33.6597049999858, -85.7053180001255 33.6603130001449, -85.7039799995773 33.661408000279, -85.7032290000183 33.6620369996079, -85.7018870001849 33.6630820001342, -85.7015970003013 33.6633030004332, -85.7012630001873 33.6635480000402, -85.7008429997047 33.663848000385, -85.7004139997063 33.6641479998304, -85.6996949998243 33.6646189999669, -85.6992289999184 33.6649170002194, -85.6987789998509 33.6651959995958, -85.6985570004052 33.6653300003792, -85.6982630003371 33.665500000125, -85.6976069997632 33.6658779995701, -85.6972100003412 33.6660910003994, -85.6967869997202 33.6663269995914, -85.6961869999301 33.6666370003973, -85.6959360000463 33.6667569996359, -85.6955419998633 33.6669539997273, -85.6951160000033 33.6671630003722, -85.6945730001438 33.6674120001637, -85.6938419997085 33.667738999955, -85.6936639995939 33.6678089995857, -85.6934020001022 33.6679170000696, -85.6930370003573 33.6680699999306, -85.6926369998976 33.6682310001606, -85.6922409996224 33.6683850000678, -85.691871999693 33.668530999606, -85.6914780004093 33.6686759999974, -85.69116100018 33.6687879997664, -85.6909830000654 33.6688459997432, -85.6904960000903 33.6690100001115, -85.6893860001637 33.669370999672, -85.6885490002362 33.6696320000169, -85.6873539999871 33.6700019999923, -85.686035000315 33.6704219995757, -85.6849669996273 33.670767000197, -85.6843370002523 33.6709739998503, -85.6837080000241 33.6711699998957, -85.6836330001628 33.671193000057, -85.6836580004164 33.6714249999638, -85.6838409998622 33.6716929997322, -85.684 33.6718160000084, -85.6851609995804 33.6719560001693, -85.6856759999474 33.6723620000063, -85.6858879998313 33.672742000443, -85.685945999808 33.6729629998426, -85.6859130000848 33.6732119996341, -85.6859849998079 33.6733270004408, -85.6858160001083 33.6746080001591, -85.6856250002935 33.6750590002727, -85.6855640001784 33.6755220000403, -85.6855880003858 33.6756679995786, -85.685742000293 33.6758259996702, -85.6860829998305 33.6758930000619, -85.6861599997841 33.6759720001078, -85.686127000061 33.6762209998993, -85.6859599995543 33.6764939998982, -85.6858699999005 33.6769890002422, -85.6863990000138 33.6774699999406, -85.6870769998037 33.6779630001923, -85.687414000056 33.6787139997513, -85.6870739996653 33.6789709999118, -85.68710900038 33.6793710003714, -85.6870050000807 33.6798299999546, -85.6866030004281 33.6802260002298, -85.686118999692 33.6805780002745, -85.6858990003386 33.680918999812, -85.686015000292 33.6810369998577, -85.6862310003604 33.6810569998807, -85.6869209998043 33.680795000389, -85.6871369998726 33.6808140003659, -85.6873679997333 33.681050999604, -85.6874299998946 33.6812370000875, -85.6877019998474 33.6824860001288, -85.6881069996383 33.6830619997116, -85.6880980001226 33.6831330002878, -85.6879539997772 33.6832290002183, -85.6873640004482 33.6833919996411, -85.6871149997574 33.683621000309, -85.6871210000341 33.6839100001464, -85.6873040003793 33.6841789999609, -85.6872759999874 33.6843919998909, -85.6871220000802 33.6845590003976, -85.6869330003577 33.6850420001882, -85.6870680002879 33.6856339996094, -85.6871870003797 33.6857569998857, -85.6872990001487 33.6858709997469, -85.687515000217 33.6858899997238, -85.6876579996169 33.6857949998394, -85.6877090001702 33.6855969997018, -85.6877399998011 33.685515000417, -85.6877290001932 33.6854960004401, -85.6877140004008 33.6853970003713, -85.6877290001932 33.6853619996566, -85.6877369996628 33.685304999726, -85.6877829999854 33.6851830003952, -85.6878620000313 33.6850699996808, -85.6879830002152 33.684968000373, -85.6881299997996 33.6848900003733, -85.6882279998223 33.6848529995663, -85.6882920000757 33.684858999843, -85.6883439997757 33.6849159997736, -85.6884579996369 33.6852499998876, -85.6884900002133 33.6859149999773, -85.688518999752 33.6860309999308, -85.6885419999134 33.6860550001382, -85.6885179997059 33.6862330002528, -85.6886579998668 33.6864969998367, -85.6890809995884 33.6869320001118, -85.6897210003239 33.6873850003176, -85.6898499999775 33.6875180001556, -85.6899140002309 33.6875570001555, -85.690027000046 33.687674000155, -85.6900300001843 33.6878589996931, -85.6899560003691 33.6881099995769, -85.6899429997696 33.6882790001758, -85.6899829998155 33.6883810003829, -85.690594000113 33.6890479996655, -85.6908019998124 33.6893609997104, -85.6908480001351 33.6895300003093, -85.6907960004351 33.689821000239, -85.6907180004353 33.6900219996156, -85.6905400003207 33.6902949996146, -85.6905010003209 33.6904090003751, -85.6901579997918 33.6909100000965, -85.6897499998626 33.6911339996344, -85.689287000095 33.6911050000957, -85.6891690000493 33.6909009996814, -85.6892019997724 33.69065199989, -85.6894070002328 33.6904189999369, -85.689358999818 33.6902880001911, -85.6885569997057 33.6902340003988, -85.6881790002606 33.6904519996601, -85.6879779999847 33.6906499997977, -85.6878789999159 33.6913980001177, -85.687778999801 33.6914970001865, -85.6876979996629 33.691777999655, -85.6875130001248 33.6928420001582, -85.6874910000096 33.6933439999257, -85.6874950001941 33.6933850000178, -85.6875890000323 33.6942529995763, -85.6874350001251 33.6960760000075, -85.6872450003564 33.6965280001671, -85.687149000426 33.6965910003744, -85.6870450001266 33.6973739996104, -85.6868880000811 33.6978999995854, -85.6867829997356 33.698035000415, -85.6867219996205 33.6984980001826, -85.6866259996901 33.6985610003899, -85.686604999621 33.6986350002052, -85.6863789999908 33.6994390004096, -85.6862329995532 33.7005430000594, -85.686178999761 33.7007309997359, -85.6862100002912 33.7007309997359, -85.6883199995683 33.7007580000817, -85.6905429995597 33.7007749999663, -85.6905200002977 33.7034290000483, -85.6913229995567 33.7041350002301, -85.6919610002 33.7045020000673, -85.6925929996672 33.7045240001825, -85.6929659997811 33.7044110003674, -85.6938120001237 33.7037919997011, -85.6939569996158 33.7037189999319, -85.6945360002362 33.7034280000022, -85.6952400003257 33.7028090002352, -85.6956460001628 33.7025809996135, -85.6972549997184 33.7022620001912, -85.6977989996241 33.7022850003526, -85.6978700002003 33.7029050001657, -85.6995219999403 33.7028840000966, -85.702314000091 33.7028490002812, -85.7063999996602 33.7029569998657, -85.7088939995584 33.7030040002345, -85.711795000239 33.703060000119, -85.7125900000285 33.7030970000266, -85.7126599996592 33.7030970000266, -85.7168209999891 33.7031160000034, -85.716869000404 33.7049419996736, -85.7244199996832 33.7050100001115, -85.7253759997026 33.7050189996272, -85.7253759997026 33.7046529998361, -85.725439999956 33.7005759997826, -85.7255139997712 33.6958290003082, -85.7255349998403 33.6898750000313, -85.7258200003927 33.6834240002175, -85.7258210004388 33.6833949997795, -85.7256790001857 33.6798130000699, -85.7261959997455 33.6761799998072, -85.7262500004371 33.675325999995, -85.7262569998606 33.6739179998159, -85.7262240001374 33.672408000329, -85.7369890001422 33.6726109997979, -85.7430079998193 33.6727279997974, -85.7431459998879 33.6722399997762, -85.7431669999571 33.6666750003511, -85.7433110003024 33.6625180002056, -85.7433110003024 33.6623280004369, -85.7433110003024 33.6619619997466, -85.7433110003024 33.6583610000602, -85.7467289996436 33.6583529996913, -85.7500720000227 33.6583460002677, -85.7543870002598 33.6583030000834, -85.7590719995731 33.6582559997146, -85.7588419997584 33.6579859998541, -85.7585990002436 33.6576670004318, -85.7584349998753 33.657325999995, -85.758178999761 33.6566500002973, -85.7580809997383 33.656474000275, -85.7579429996696 33.6561990001838, -85.7570950001341 33.6550880002111, -85.75685199972 33.654753000051, -85.7567739997203 33.65470399959, -85.7565239998826 33.6546760000974, -85.7562479997454 33.6546760000974, -85.7560769999536 33.6545990001438, -85.7560179999307 33.6545489996367, -85.7558730004386 33.65443899996, -85.7556890000473 33.6542799998222, -85.7553149998873 33.653811999824, -85.7552949998643 33.6537739998702, -85.7551680003029 33.6536430001244, -85.7551149996575 33.6533870000101, -85.7549629998425 33.6532229996418, -85.7545600001438 33.6527910004044, -85.7543620000063 33.6524980003825))", 11500, 1015]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_county_boundary": {"columns": [{"name": "Name", "type": "object", "sample_values": ["county_fips_code", "Geo id", "State fips Code", "County gnis code", "County name"]}, {"name": "Description", "type": "object", "sample_values": ["County fips code", "Unique identifier of usa county boundary", "State fips code", "County gnis code", "Name of the county"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["county_fips_code_pk", "geo_id", "state_fips_code", "county_gnis_code", "county_name"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "GEOGRAPHY", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [1025, 1, 161538, "<PERSON>", "Clarke County"]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_state_boundary": {"columns": [{"name": "Name", "type": "object", "sample_values": ["State fips code", "Geo id", "Region code", "Division code", "State gnis code"]}, {"name": "Description", "type": "object", "sample_values": ["State fips code", "Unique identifier of usa state boundary", "Region code", "Division code", "Name of the county"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["state_fips_code_pk", "geo_id", "region_code", "division_code", "state_fips_code"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "GEOGRAPHY", "DATE"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [72, 9, 0, 1779808, "PR"]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_msa_census_boundary": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geo id", "CSA geo id", "Core-Based Statistical Area code", "MSA name", "LSAD name"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of usa msa cencus boundary ", "Core-Based Statistical Area geo id\nCombined statistical area ", "Core-Based Statistical Area code", "Metropolitan Statistical Areas name", "State gnis code"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geo_id", "csa_geo_id", "cbsa_fips_code", "msa_name", "lsad_name"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "GEOGRAPHY"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Value", "type": "object", "sample_values": [31180, 352, "Luboock,TX", "Lubbock, TX Metro Area", "M1"]}, {"name": "Category", "type": "object", "sample_values": ["GEOGRAPHY"]}]}, "usa_owner_unmask_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Owner Unmask PK", "Has Confidence", "Last Seen Date", "Occurrences Count", "Owner ID"]}, {"name": "Description", "type": "object", "sample_values": ["The unique identifier for Cherre Unmasked Owner", "Confident that this is the true owner", "Most recent date we can connect the owner / address to the property", "Number of times we can use the data to connect the owner / address to the property", "Owner ID"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_owner_unmask_pk", "has_confidence", "last_seen_date", "occurrences_count", "owner_id"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "BOOLEAN", "DATE", "INT", "NUMERIC"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-535771761, false, "2020-05-30 00:00:00 UTC", 12, "MILDRED V SPEZIALY::P::AK::112852 PO BOX, <PERSON><PERSON><PERSON><PERSON><PERSON>, AK 99511"]}, {"name": "Category", "type": "object", "sample_values": ["Owner <PERSON><PERSON><PERSON>"]}]}, "recorder_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Recorder ID", "Document Recording State", "Document Recording County", "Document Recording Fips", "Document Type Code"]}, {"name": "Description", "type": "object", "sample_values": ["The unique identifier for a Recorder Document", "The USPS standardized abbreviation for the state where the document was recorded.", "The county name associated with the Federal Information Processing Standards (FIPS) county code.", "5 digit numeric Federal Information Processing Standard (FIPS) code combining state and county codes where the document is recorded.", "Code identifying the type of document; grant deed, quit claim, etc."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["recorder_id", "document_recording_state", "document_recording_county", "document_recording_fips", "document_type_code"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "DATE", "BOOLEAN", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["7852283418248671865", "CA", "LOS ANGELES", 6037, 1]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "recorder_grantee_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Recorder <PERSON><PERSON>", "Recorder ID", "Grantee Name", "Grantee First Name", "<PERSON>ee Middle Name"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of recorder grantee", "The unique identifier for a Recorder Document", "Full name of the first individual on the recorded document.", "First name of the first individual on the recorded document. Left blank if entity is not defined as an individual.", "Middle name or middle initial of the first individual listed on the recorded document."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_recorder_grantee_pk", "recorder_id", "grantee_name", "grantee_first_name", "grantee_middle_name"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-7272407277460809728, "7852283418248671865", "MARY J L MORRIS", "MARY", "J L"]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "recorder_grantor_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Recorder PK", "Recorder ID", "Grantor Name", "Grantor First Name", "<PERSON>or Middle Name"]}, {"name": "Description", "type": "object", "sample_values": ["The unique identifier for a recorder grantor", "The unique identifier for a Recorder Document", "Full name of the first seller listed on the deed.", "First name of the first seller listed on the deed.", "Middle name or middle initial of the first seller listed on the deed."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_recorder_grantor_pk", "recorder_id", "grantor_name", "grantor_first_name", "grantor_middle_name"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-5430763644347310080, "7852283418248671865", "TRACEY L MCCULLOUGH", "TRACEY  ", "L"]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "recorder_v2_summary_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Tax Assessor ID", "Recorder ID", "<PERSON><PERSON><PERSON> Latest <PERSON>", "<PERSON><PERSON><PERSON> Latest <PERSON>", "Cherre Latest Deed <PERSON>"]}, {"name": "Description", "type": "object", "sample_values": ["Tax Assessor ID Number", "The unique identifier for a Recorder Document", "<PERSON><PERSON><PERSON> Latest <PERSON>", "<PERSON><PERSON><PERSON> Latest <PERSON>", "Cherre Latest Deed <PERSON>"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["tax_assessor_id", "recorder_id", "cherre_latest_deed_grantee", "cherre_latest_deed_grantor", "cherre_latest_deed_amount"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "DATE", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [14012947432, "7852283418248671865", "ISIDRO A MAGANA", "JOSEPH A AROS", 137000]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "recorder_legal_description_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Recorder ID", "Legal Description", "Cherre Ingest Datetime", "Main Menu"]}, {"name": "Description", "type": "object", "sample_values": ["The unique identifier for a Recorder Document", "Property legal description provided by the county.", "The timestamp used to track when the record was loaded into Cherre"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["recorder_id", "legal_description", "cherre_ingest_datetime"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["7852283418248671865", 33, "2023-09-18 13:53:43.776000 UTC"]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "recorder_mortgage_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Recorder Mortgage PK", "Recorder ID", "Mortgage Document Number", "Mortgage Book", "Mortgage Page"]}, {"name": "Description", "type": "object", "sample_values": ["Unique Identifier of recorder mortgage", "The unique identifier for a Recorder Document", "Document number created from document book/page or instrument number. Provided to enable a single column reference. Raw data are delivered in 3 additional columns.", "The instrument's book number referenced/stamped for the 1st mortgage/deed of trust.", "The instrument's page number referenced/stamped for the 1st mortgage/deed of trust."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_recorder_mortgage_pk", "recorder_id", "document_number", "book", "page"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "DATE", "BOOLEAN", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Example Value", "type": "object", "sample_values": ["5031711323941740679", "7852283418248671865", "2018R06002", 2654, 3456]}, {"name": "Category", "type": "object", "sample_values": ["Recorder"]}]}, "tax_assessor_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Tax Assessor ID", "Situs State", "Situs County", "Juris<PERSON>", "Fips Code"]}, {"name": "Description", "type": "object", "sample_values": ["Tax Assessor ID Number", "State where the property is situated.", "County where the property is situated.", "Name of the tax jurisdiction. This is typically the county with some exceptions. Exceptions are primarily in the New England area where the townships are the taxing authorities.", "5 digit numeric Federal Information Processing Standard (FIPS) code combining state and county codes where the property is located."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["tax_assessor_id", "situs_state", "situs_county", "jurisdiction", "fips_code"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "BOOLEAN", "DATE", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [*********, "AR", "<PERSON>", "JEFFERSON", 5069]}, {"name": "Category", "type": "object", "sample_values": ["Tax Assessor"]}]}, "tax_assessor_block_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Tax Assessor Block PK", "Block", "Tax Assessor ID", "Cherre Ingest Datetime", "Main Menu"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier or tax assessor block", "Block numbers", "Tax Assessor ID", "The timestamp used to track when the record was loaded into Cherre"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_tax_assessor_block_pk", "block", "tax_assessor_id", "cherre_ingest_datetime"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["8347228943000444183", 3, *********, "2023-08-11 16:54:58.140610 UTC"]}, {"name": "Category", "type": "object", "sample_values": ["Tax Assessor"]}]}, "tax_assessor_lot_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Tax Assessor Lot PK", "Lot", "Tax Assessor ID", "Cherre Ingest Datetime", "Main Menu"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of tax assessor lot", "Lot numbers", "Tax Assessor ID", "The timestamp used to track when the record was loaded into Cherre"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_tax_assessor_lot_pk", "lot", "tax_assessor_id", "cherre_ingest_datetime"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["4206862363579324154", 3, *********, "2023-08-11 16:54:58.140610 UTC"]}, {"name": "Category", "type": "object", "sample_values": ["Tax Assessor"]}]}, "tax_assessor_owner_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Tax Assessor Owner PK", "Owner Name", "Owner First Name", "Owner Middle Name", "Owner Last Name"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of tax assessor owner", "Full, unparsed name of the owners.", "First name of the owners. If name is a company, the name will be present in this field.", "Middle name of the owners.", "Last name of the owners."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_tax_assessor_owner_pk", "owner_name", "owner_first_name", "owner_middle_name", "owner_last_name"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "BOOLEAN", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["4021876336727606343", "OLIVER L BOWSER", "OLIVER", "L", "BOWSER"]}, {"name": "Category", "type": "object", "sample_values": ["Tax Assessor"]}]}, "usa_tax_assessor_history_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Tax Assessor History PK", "Situs State", "Situs County", "Juris<PERSON>", "Fips Code"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of tax assessor history ", "State where the property is situated.", "County where the property is situated.", "Name of the tax jurisdiction. This is typically the county with some exceptions. Exceptions are primarily in the New England area where the townships are the taxing authorities.", "5 digit numeric Federal Information Processing Standard (FIPS) code combining state and county codes where the property is located."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_tax_assessor_history_v2_pk", "situs_state", "situs_county", "jurisdiction", "fips_code"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "BOOLEAN", "DATE", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["8969872019102120191227", "AR", "<PERSON>", "JEFFERSON", 5069]}, {"name": "Category", "type": "object", "sample_values": ["Tax Assessor"]}]}, "usa_neighborhood_boundary_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Geography ID", "Neighborhood Geometry", "Geography Name", "Reference 1", "Reference 2"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier Linking key to other Onboard geographic content using the fully formatted GEO_ID", "Neighborhood geometry.", "Area Name", "Additional ID's to relate to other sources and content (see Reference Lookup)", "Area geographic centroid"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["geography_id", "neighborhood_geom", "geography_name", "reference_1", "reference_2"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "GEOGRAPHY", "NUMERIC", "DATE", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["f099aeb23abcbd40130a416225f45b1e\n", "POLYGON((-95.5120286 29.5033978000001, -95.5121162 29.5033956, -95.5127658 29.5033795000001, -95.5130502 29.5033842, -95.5132005249999 29.5034203220001, -95.5132005 29.5033050000001, -95.513157 29.5032189, -95.5130440999999 29.5029957, -95.512951693 29.5028131070001, -95.5129484 29.5028066, -95.5129...", "Hampton Stead", "Winston-Salem", "Winston-Salem, NC"]}, {"name": "Category", "type": "object", "sample_values": ["Neighborhood Boundary"]}]}, "usa_building_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre usa building pk", "County Building ID (Beta)", "Address", "City", "State"]}, {"name": "Description", "type": "object", "sample_values": ["Cherre usa building pk", "The county-specific building ID.", "The first line of the primary building address.", "The city that the building is located in.", "The state or province the building is located in."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_building_pk", "county_building_id", "address", "city", "state"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "GEOMETRY", "NUMERIC", "INTEGER", "BOOLEAN"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["*********-USA", 0, "TWILIGHT CT", "BANNER ELK", "NC"]}, {"name": "Category", "type": "object", "sample_values": ["Building Attributes"]}]}, "usa_lot_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre USA lot PK", "Tax Jurisdiction", "FIPS Code", "MSA Name", "MSA Code"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of usa lot", "Name of the tax jurisdiction. This is typically the county with some exceptions. Exceptions are primarily in the New England area where the townships are the taxing authorities.", "5 digit numeric Federal Information Processing Standard (FIPS) code combining state and county codes where the property is located.", "Metropolitan Statistical Area Name as defined by the Office of Management and Budget (OMB).", "Metropolitan Statistical Area Code as defined by the Office of Management and Budget (OMB)."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_lot_pk", "tax_jurisdiction", "fips_code", "msa_name", "msa_code"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "INTEGER", "GEOGRAPHY", "BOOLEAN"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["CH0004739510383299405874", "AUTAUGAVILLE", 1001, "Montgomery, AL", 33860]}, {"name": "Category", "type": "object", "sample_values": ["Lot Attribute"]}]}, "usa_unit_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre USA Unit PK", "Addresses", "Unit Number Prefix", "Unit Number", "City"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of usa unit", "Unique standardized addresses assigned to each unit in usa_unit.", "Normalized unit or apartment number prefix.", "Normalized unit or apartment number.", "Site address city name."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_unit_pk", "address", "unit_number_prefix", "unit_number", "city"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "INTEGER", "NUMERIC", "BOOLEAN", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["*********-156-USA", "PACHECO RD HOUSE 156", "HOUSE", 156, "New York"]}, {"name": "Category", "type": "object", "sample_values": ["Address", "Unit Attribute"]}]}, "usa_avm_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre usa avm pk", "Tax Assessor ID", "Estimated Value Amount", "Estimated Minimum Value Amount", "Estimated Maximum Value Amount"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of usa avm", "Unique tax assessor record ID", "Estimated valuation amount", "The minimum estimated valuation amount in the range", "The maximum estimated valuation amount in the range"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_avm_pk", "tax_assessor_id", "estimated_value_amount", "estimated_min_value_amount", "estimated_max_value_amount"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "DATE", "INTEGER"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["HkgVWpqofGp1339igJJkGw==", *********, 232000, 118000, 165000]}, {"name": "Category", "type": "object", "sample_values": ["AVM"]}]}, "usa_demographics_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre Demographic PK", "Geography Type", "Geography ID ", "Geography Code", "Geography Name"]}, {"name": "Description", "type": "object", "sample_values": ["Unique Identifier of usa demographics", "Identifies the geography level of the record:\nBG = Census Block Group\nCT = Census Tract\nZI = Zip Code\nCP = County Subdivison\nMA = County based metro area\nSD = School Districts\nCO = County\nST = State\nUS = National", "Uniquely identifies each record by combining the GEO_TYPE and GEO_CODE values.", "Numeric identify for the geography. Often the federal identification code, but may be a unique value assigned. This value is unique by GEO_TYPE (but not across GEO_TYPEs).", "Standard geography name."]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_demographics_pk", "geography_type", "geography_id", "geography_code", "geography_name"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-7827870000000000000, "BG", "BG170978610112", 170979000000, "BG 2 Lake County Tract 861011"]}, {"name": "Category", "type": "object", "sample_values": ["Demographics"]}]}, "usa_schools_v2": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Institution ID", "School Type", "Diocese", "Catholic School Type", "Charter Primary Authorizing Agency Name"]}, {"name": "Description", "type": "object", "sample_values": ["The unique, persistent identifier for the school", "A categorization that best describes the type of school\nExamples include Regular, Special Education, Vocational Education, Alternative/Other, Montessori, etc", "The diocese or archdiocese for Catholic schools", "The type of Catholic school - Parochial, Diocesan, or Private", "The name assigned to the primary public charter school authorizing agency by the state"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["institution_id", "building_type", "catholic_school_diocese", "catholic_school_type", "charter_authorizing_agency_name_primary"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "BOOLEAN", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["7f30a89b5ba4fb223de465c345e625b4", "Regular", 0, 479, "<PERSON>"]}, {"name": "Category", "type": "object", "sample_values": ["Schools"]}]}, "usa_rural_urban_continuum_codes": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre hash", "Fips", "State", "County Name", "Population (2010)"]}, {"name": "Description", "type": "object", "sample_values": ["Unique Primary Key", "Fips Code", "State", "Name of County", "2010 Population"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_hash", "fips", "state", "county_name", "population_2010"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": [-225713242149799008, 25013, "MA", "Hapden County", 463490]}, {"name": "Category", "type": "object", "sample_values": ["Rural-Urban Continuum Codes"]}]}, "usa_bls_employment": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Cherre usa bls employment pk", "Area", "Area FIPS Code", "Civilian Labor Force Count", "Employment Count"]}, {"name": "Description", "type": "object", "sample_values": ["Unique identifier of usa bls employment", "Area of the data being reported", "Area FIPS Code", "Number of people in the civilian labor force", "The number of people employed"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cherre_usa_bls_employment_pk", "area", "area_fips_code", "civilian_labor_force_count", "employment_count"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "DATE", "TIMESTAMP"]}, {"name": "PK", "type": "object", "sample_values": ["Y"]}, {"name": "Sample Data", "type": "object", "sample_values": ["4965425780971279177", "<PERSON><PERSON><PERSON>-Oxford-Jacksonville, AL MSA", 11500, 49339, 45176]}, {"name": "Category", "type": "object", "sample_values": ["BLS Employment"]}]}, "census_permit_survey_pre": {"columns": [{"name": "Name", "type": "object", "sample_values": ["Five Plus Unit Building Count", "Five Plus Unit Rep Building Count", "Five Plus Unit Rep Unit Count", "Five Plus Unit Rep Value", "Five Plus Unit Value"]}, {"name": "Description", "type": "object", "sample_values": ["Building count for five plus unit permits", "Building count reported for five plus unit permits", "Unit count reported for five plus unit permits", "Unit value reported for five plus unit permits", "Unit value for five plus unit permits"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["five_plus_unit_building_count", "five_plus_unit_rep_building_count", "five_plus_unit_rep_unit_count", "five_plus_unit_rep_value", "five_plus_unit_value"]}, {"name": "Type", "type": "object", "sample_values": ["NUMERIC", "STRING", "DATE"]}, {"name": "PK", "type": "float64", "sample_values": []}, {"name": "Sample Data", "type": "object", "sample_values": [5, 34, 3465, "Albany-Schenectady-Troy  NY", 104]}, {"name": "Category", "type": "object", "sample_values": ["US Census Permit Survey"]}]}, "census_permit_survey_post": {"columns": [{"name": "Name", "type": "object", "sample_values": ["CBSA Code", "CBSA Name", "CSA Code", "Health Coverage Code", "Five Plus Unit Building Count"]}, {"name": "Description", "type": "object", "sample_values": ["CBSA Code", "CBSA Name", "CSA Code", "Blank or “C”, if CSA/CBSA is\ncompletely covered by monthly permit issuing places", "Building count for five plus unit permits"]}, {"name": "API or Egressed Field Name", "type": "object", "sample_values": ["cbsa_code", "cbsa_name", "csa_code", "health_coverage_code", "five_plus_unit_building_count"]}, {"name": "Type", "type": "object", "sample_values": ["STRING", "NUMERIC", "DATE"]}, {"name": "PK", "type": "float64", "sample_values": []}, {"name": "Sample Data", "type": "object", "sample_values": [10580, "Albany-Schenectady-Troy  NY", 104, "C", 5]}, {"name": "Category", "type": "object", "sample_values": ["US Census Permit Survey"]}]}, "Tax Assessor Lookup Values": {"columns": [{"name": "Field Name - Tax Assessor", "type": "object", "sample_values": ["building_sq_ft_code", "flooring_material_code", "view_code", "owner_trust_type_code", "topography_code"]}, {"name": "Code", "type": "object", "sample_values": ["A", "B", "E", "G", "I"]}, {"name": "Description", "type": "object", "sample_values": ["Attic", "Base Area", "Heated Area", "Garage", "Effective Area"]}]}, "Recorder Lookup Values": {"columns": [{"name": "Field Name", "type": "object", "sample_values": ["document_type_code", "partial_interest_code", "owner_relationship_code", "property_group_type", "property_use_standardized_code"]}, {"name": "Code", "type": "object", "sample_values": [1, 2, 3, 4, 5]}, {"name": "Description", "type": "object", "sample_values": ["Assignment of Sub Agreement of Sale", "Assignment of Sub Lease", "Assignment of Commercial Lease", "Administrator’s Deed", "Affidavit"]}, {"name": "Unnamed: 3", "type": "object", "sample_values": ["Description", "Non-Arms length transactions with valid TITLE COMPANY NAME (no accomodations)", "Arms-length transactions (Purchase/Resales)", "New Construction transactions", "REO and Trustee Deeds"]}]}, "Demographics Lookup Values": {"columns": [{"name": "Field Name", "type": "object", "sample_values": ["geography_type", "Main Menu"]}, {"name": "Code", "type": "object", "sample_values": ["BG", "CT", "ZI", "CP", "MA"]}, {"name": "Geography_code", "type": "float64", "sample_values": [1.0, 2.0, 4.0, 5.0, 6.0]}, {"name": "Description", "type": "object", "sample_values": ["Census Block Group", "Census Tract", "Zip Code", "County Subdivision", "County Based Metro Area"]}]}, "Neighborhood Lookup Values": {"columns": [{"name": "Field Name", "type": "object", "sample_values": ["reference_4", "Main Menu"]}, {"name": "Code", "type": "object", "sample_values": ["level_1", "level_2", "level_3", "level_4"]}, {"name": "Description", "type": "object", "sample_values": ["Macro-Neighborhoods", "Neighborhoods", "Sub-Neighborhoods", "Residential Subdivisions / Condos / Townhomes / Apartments / etc"]}]}, "Rural-Urban Lookup Values": {"columns": [{"name": "Field Name", "type": "object", "sample_values": ["rucc_2013 ", "Main Menu"]}, {"name": "Code", "type": "float64", "sample_values": [1.0, 2.0, 3.0, 4.0, 5.0]}, {"name": "Description", "type": "object", "sample_values": ["Metro counties: Counties in metro areas of 1 million population or more", "Metro counties: Counties in metro areas of 250,000 to 1 million population\n", "Metro counties: Counties in metro areas of fewer than 250,000 population", "Nonmetro counties: Urban population of 20,000 or more, adjacent to a metro area", "Nonmetro counties: Urban population of 20,000 or more, not adjacent to a metro area"]}]}}