/**
 * Service for searching images using Serper.dev (previously DuckDuckGo)
 */

export interface ImageSearchResult {
  width: number | undefined;
  height: number | undefined;
  thumbnail: string | undefined;
  url: string;
  title: string;
  image: string;
}

export interface PropertyImageSearchResponse {
  images: ImageSearchResult[];
  allImageUrls: string[];
}

/**
 * Search for images using Serper.dev
 * @param query - Search query string
 * @param limit - Maximum number of results to return (default: 10)
 * @returns Array of image search results and all image URLs
 */
export const searchImages = async (query: string, limit: number = 10): Promise<PropertyImageSearchResponse> => {
  try {
    if (!query || query.trim() === '') {
      console.warn('Empty search query provided to searchImages');
      return { images: [], allImageUrls: [] };
    }

    // Build the URL with query parameters
    const params = new URLSearchParams({
      query: query.trim(),
      limit: limit.toString()
    });
    
    const response = await fetch(`/api/image-search?${params.toString()}`);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Image search failed: ${response.status} - ${errorData.error || 'Unknown error'}`);
    }
    
    const data = await response.json();
    
    // Handle different response formats
    if (Array.isArray(data)) {
      return { images: data, allImageUrls: [] };
    } else if (data.images && Array.isArray(data.images)) {
      return { 
        images: data.images,
        allImageUrls: data.allImageUrls || []
      };
    } else if (data.results && Array.isArray(data.results)) {
      return { 
        images: data.results,
        allImageUrls: data.allImageUrls || []
      };
    } else if (data.error) {
      console.log('Error from image search API:', data.error);
      return { images: [], allImageUrls: [] };
    }
    
    return { images: [], allImageUrls: [] };
  } catch (error) {
    console.log('Error searching for images:', error);
    return { images: [], allImageUrls: [] };
  }
};

/**
 * Search for property images based on property details
 * @param address - Full property address or street address
 * @param city - Property city
 * @param state - Property state
 * @param zipCode - Property ZIP code
 * @param propertyType - Type of property (house, apartment, etc.)
 * @param limit - Maximum number of results to return (default: 1)
 * @returns Image search results and all image URLs for storage
 */
export const searchPropertyImages = async (
  address: string,
  city: string = '',
  state: string = '',
  zipCode: string = '',
  propertyType: string = 'house',
  limit: number = 1
): Promise<PropertyImageSearchResponse> => {
  try {
    if (!address && !city) {
      console.warn('Neither address nor city provided to searchPropertyImages');
      return { images: [], allImageUrls: [] };
    }

    // Use our property images API endpoint
    const params = new URLSearchParams();
    
    if (address) params.append('address', address.trim());
    if (city) params.append('city', city.trim());
    if (state) params.append('state', state.trim());
    if (zipCode) params.append('zipCode', zipCode.trim());
    if (limit) params.append('count', limit.toString());
    
    console.log('Searching property images with params:', Object.fromEntries(params.entries()));
    
    // Set a timeout for the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout
    
    try {
      const response = await fetch(`/api/property-images?${params.toString()}`, {
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Property image search failed:', response.status, errorData);
        return { images: [], allImageUrls: [] };
      }
      
      const data = await response.json();
      console.log('Property image search results:', data);
      
      // Validate images and extract all image URLs
      const images = data.images || [];
      const validImages = images.filter((img: ImageSearchResult) => 
        img && img.image && typeof img.image === 'string' && img.image.startsWith('http')
      );
      
      return { 
        images: validImages,
        allImageUrls: data.allImageUrls || []
      };
    } catch (fetchError: any) {
      if (fetchError.name === 'AbortError') {
        console.log('Property image search request timed out');
      } else {
        console.error('Property image search fetch error:', fetchError);
      }
      return { images: [], allImageUrls: [] };
    }
  } catch (error) {
    console.log('Error searching for property images:', error);
    return { images: [], allImageUrls: [] };
  }
};

/**
 * Search for neighborhood images
 * @param neighborhood - Neighborhood name
 * @param city - City name
 * @param state - State name
 * @param limit - Maximum number of results to return (default: 6)
 * @returns Array of image search results
 */
export const searchNeighborhoodImages = async (
  neighborhood: string,
  city: string,
  state: string,
  limit: number = 6
): Promise<PropertyImageSearchResponse> => {
  if (!neighborhood && !city) {
    console.warn('Neither neighborhood nor city provided to searchNeighborhoodImages');
    return { images: [], allImageUrls: [] };
  }
  
  // Build a search query based on neighborhood details
  const searchQuery = `${neighborhood || ''} ${neighborhood ? 'neighborhood' : ''} ${city} ${state}`.trim();
  
  return searchImages(searchQuery, limit);
}; 