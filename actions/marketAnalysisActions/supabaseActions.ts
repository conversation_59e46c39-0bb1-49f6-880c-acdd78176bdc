import { createClient } from '@supabase/supabase-js';
import { fetchDemographicsByZipcode, DemographicsData } from './cherreActions';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
);

/**
 * Fetches zipcodes for a given city and state from Supabase
 * @param city City name (case insensitive)
 * @param stateCode State code (2-letter, uppercase)
 * @returns Array of zipcodes
 */
export const fetchZipcodesByCityState = async (city: string, stateCode: string): Promise<string[]> => {
  try {
    // Validate inputs
    if (!city || !stateCode) {
      console.warn('City and state code are required for fetching zipcodes');
      return [];
    }
    
    // Check if Supabase client is properly initialized
    if (!supabase) {
      console.log('Supabase client not initialized, check environment variables');
      // Use fallback zipcodes for common cities
      return getFallbackZipcodes(city, stateCode);
    }
    
    // Query the database
    const { data, error } = await supabase
      .from('zipcode_mapping')
      .select('zipcode')
      .ilike('city', city)
      .eq('state', stateCode.toUpperCase());
  
    if (error) {
      console.log('Error fetching zipcodes:', error);
      // Use fallback if available
      return getFallbackZipcodes(city, stateCode);
    }
    
    if (!data || data.length === 0) {
      console.warn(`No zipcodes found for ${city}, ${stateCode}`);
      // Use fallback if available
      return getFallbackZipcodes(city, stateCode);
    }
    
    // Extract unique zipcodes
    const uniqueZipcodes = [...new Set(data.map(item => item.zipcode))];
    return uniqueZipcodes;
  } catch (error) {
    console.log('Error in fetchZipcodesByCityState:', error);
    // Use fallback if available
    return getFallbackZipcodes(city, stateCode);
  }
};

/**
 * Get fallback zipcodes for common cities when the database lookup fails
 */
function getFallbackZipcodes(city: string, stateCode: string): string[] {
  const cityLower = city.toLowerCase();
  const stateLower = stateCode.toLowerCase();
  
  // Common zipcodes for major US cities
  const fallbackMap: Record<string, Record<string, string[]>> = {
    'ny': {
      'new york': ['10001', '10002', '10003', '10004', '10005', '10006', '10007'],
      'brooklyn': ['11201', '11203', '11205', '11206', '11207', '11208'],
      'queens': ['11101', '11102', '11103', '11104', '11105'],
      'bronx': ['10451', '10452', '10453', '10454', '10455'],
      'staten island': ['10301', '10302', '10303', '10304', '10305']
    },
    'ca': {
      'los angeles': ['90001', '90002', '90003', '90004', '90005'],
      'san francisco': ['94102', '94103', '94104', '94105', '94107'],
      'san diego': ['92101', '92102', '92103', '92104', '92105']
    },
    'il': {
      'chicago': ['60601', '60602', '60603', '60604', '60605']
    },
    'tx': {
      'houston': ['77001', '77002', '77003', '77004', '77005'],
      'dallas': ['75201', '75202', '75203', '75204', '75205'],
      'austin': ['78701', '78702', '78703', '78704', '78705']
    }
  };
  
  // Return fallback zipcodes if available
  if (fallbackMap[stateLower]?.[cityLower]) {
    console.log(`Using fallback zipcodes for ${city}, ${stateCode}`);
    return fallbackMap[stateLower][cityLower];
  }
  
  // If no fallback available, return a single default zipcode to prevent errors
  if (stateLower === 'ny') {
    return ['10001']; // Manhattan default
  }
  
  return ['00000']; // Last resort fallback
}

/**
 * Fetches combined demographics data for all zipcodes in a city
 * @param city City name
 * @param stateCode State code (2-letter)
 * @returns Combined demographics data with averages and totals
 */
export const fetchCombinedDemographics = async (city: string, stateCode: string): Promise<DemographicsData | null> => {
  try {
    // Fetch all zipcodes for the city
    const zipcodes = await fetchZipcodesByCityState(city, stateCode);
    
    if (zipcodes.length === 0) {
      console.warn(`No zipcodes found for ${city}, ${stateCode}`);
      return null;
    }
    
    // Fetch demographics for each zipcode
    const demographicsPromises = zipcodes.map(zipcode => fetchDemographicsByZipcode(zipcode));
    const demographicsResults = await Promise.all(demographicsPromises);
    
    // Filter out null results
    const validDemographics = demographicsResults.filter(Boolean) as DemographicsData[];
    
    if (validDemographics.length === 0) {
      console.warn(`No demographics data found for any zipcode in ${city}, ${stateCode}`);
      return null;
    }
    
    // Calculate combined demographics with averages
    const combinedDemographics: DemographicsData = {
      vintage: validDemographics[0].vintage, // Use the first available vintage
      population_2020_count: 0,
      population_median_age: 0,
      median_household_income: 0,
      average_household_income: 0,
      crime_total_risk: 0
    };
    
    // Sum up the values
    validDemographics.forEach(data => {
      if (data.population_2020_count) combinedDemographics.population_2020_count! += data.population_2020_count;
      if (data.population_median_age) combinedDemographics.population_median_age! += data.population_median_age;
      if (data.median_household_income) combinedDemographics.median_household_income! += data.median_household_income;
      if (data.average_household_income) combinedDemographics.average_household_income! += data.average_household_income;
      if (data.crime_total_risk) combinedDemographics.crime_total_risk! += data.crime_total_risk;
    });
    
    // Calculate averages for applicable fields
    const count = validDemographics.length;
    combinedDemographics.population_median_age = Math.round(combinedDemographics.population_median_age! / count);
    combinedDemographics.median_household_income = Math.round(combinedDemographics.median_household_income! / count);
    combinedDemographics.average_household_income = Math.round(combinedDemographics.average_household_income! / count);
    combinedDemographics.crime_total_risk = Math.round(combinedDemographics.crime_total_risk! / count);
    
    return combinedDemographics;
  } catch (error) {
    console.log('Error in fetchCombinedDemographics:', error);
    return null;
  }
}; 