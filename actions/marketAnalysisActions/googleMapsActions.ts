/**
 * Service for interacting with Google Maps Places API
 */

// Import the POIInfo interface but extend it as needed locally
import { POIInfo as BasePOIInfo } from './cherreActions';

// Extended POIInfo with additional properties needed for Google Places
interface POIInfo extends BasePOIInfo {
  place_id?: string;
  rating?: number;
}

// Define place types for various categories
export const PLACE_CATEGORIES = {
  groceries: ['grocery_or_supermarket', 'supermarket'],
  restaurants: ['restaurant', 'cafe', 'meal_takeaway', 'bakery'],
  shopping: ['shopping_mall', 'clothing_store', 'department_store', 'electronics_store', 'home_goods_store'],
  parks: ['park', 'campground', 'natural_feature'],
  entertainment: ['movie_theater', 'amusement_park', 'art_gallery', 'bowling_alley', 'casino', 'museum', 'night_club', 'zoo'],
  services: ['bank', 'post_office', 'laundry', 'car_repair', 'car_wash', 'gas_station', 'hair_care', 'spa'],
  health: ['hospital', 'pharmacy', 'physiotherapist', 'dentist', 'doctor', 'veterinary_care'],
  education: ['school', 'university', 'library', 'book_store'],
  custom: [],
  my_locations: []
};

// Group POIs by category
export interface GroupedPOIs {
  groceries: POIInfo[];
  restaurants: POIInfo[];
  shopping: POIInfo[];
  parks: POIInfo[];
  entertainment: POIInfo[];
  services: POIInfo[];
  health: POIInfo[];
  education: POIInfo[];
  custom: POIInfo[];
  my_locations: POIInfo[];
  [key: string]: POIInfo[];
}

/**
 * Helper function to properly capitalize place names
 * Handles cases like "trader joe's", "cvs pharmacy", etc.
 */
const capitalizePlaceName = (name: string): string => {
  if (!name) return '';
  
  // Special case for acronyms and common brand names that should be all caps
  const allCapsTerms = ['cvs', 'kfc', 'ikea', 'amc', 'bp', 'atm', 'h&m', 'tj', 'rbc', 'ups', 'usps'];
  
  // Words that should remain lowercase (unless at beginning of sentence)
  const lowercaseWords = ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'of'];
  
  // Split the name into words
  return name.split(' ').map((word, index) => {
    // Check if it's a word that should be all caps
    if (allCapsTerms.includes(word.toLowerCase())) {
      return word.toUpperCase();
    }
    
    // For words with apostrophes like "joe's"
    if (word.includes("'")) {
      const parts = word.split("'");
      // Capitalize first part, keep apostrophe, properly handle second part
      return parts[0].charAt(0).toUpperCase() + parts[0].slice(1) + "'" + 
        (parts[1] ? parts[1].toLowerCase() : '');
    }
    
    // Handle lowercase words (not at the beginning)
    if (index > 0 && lowercaseWords.includes(word.toLowerCase())) {
      return word.toLowerCase();
    }
    
    // Default: capitalize first letter
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }).join(' ');
};

/**
 * Convert Google Place to POIInfo
 * @param place Google Place result
 * @param propertyLocation Property coordinates
 * @returns POIInfo object
 */
const convertPlaceToPOI = (place: google.maps.places.PlaceResult, propertyLocation: { lat: number, lng: number }): POIInfo => {
  // Calculate distance if coordinates are available
  let distance = 0;
  if (place.geometry?.location && propertyLocation) {
    const placeLocation = place.geometry.location;
    distance = calculateDistance(
      propertyLocation.lat,
      propertyLocation.lng,
      placeLocation.lat(),
      placeLocation.lng()
    );
  }

  // Format the address components
  let street = place.vicinity || '';
  if (place.address_components) {
    const streetNumber = place.address_components.find(c => c.types.includes('street_number'))?.long_name || '';
    const route = place.address_components.find(c => c.types.includes('route'))?.long_name || '';
    if (streetNumber && route) {
      street = `${streetNumber} ${route}`;
    }
  }

  // Determine line of business based on types
  let lineOfBusiness = '';
  if (place.types && place.types.length > 0) {
    // Convert types like 'grocery_or_supermarket' to 'Grocery or Supermarket'
    lineOfBusiness = place.types[0]
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Properly capitalize the business name
  const capitalizedName = capitalizePlaceName(place.name || '');

  return {
    business_name: capitalizedName,
    street: street,
    line_of_business: lineOfBusiness || place.types?.join(', ') || '',
    distance: distance,
    latitude: place.geometry?.location?.lat() || 0,
    longitude: place.geometry?.location?.lng() || 0,
    place_id: place.place_id,
    rating: place.rating
  };
};

/**
 * Calculate distance between two coordinates in miles
 */
const calculateDistance = (
  lat1: number, 
  lon1: number, 
  lat2: number, 
  lon2: number
): number => {
  const R = 3958.8; // Radius of the Earth in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  return parseFloat(distance.toFixed(2));
};

/**
 * Fetch nearby places using Google Places API
 * @param latitude Property latitude
 * @param longitude Property longitude
 * @param types Place types to search for
 * @param radius Search radius in meters
 * @param maxResults Maximum number of results to return
 * @returns Promise with array of POIInfo
 */
const fetchPlacesForCategory = async (
  latitude: number,
  longitude: number,
  types: string[],
  radius = 2000,
  maxResults = 10
): Promise<POIInfo[]> => {
  if (!latitude || !longitude || !window.google || !window.google.maps) {
    console.error('Google Maps API not loaded or coordinates invalid');
    return [];
  }

  try {
    const service = new google.maps.places.PlacesService(
      document.createElement('div')
    );

    // Create a location object for the search
    const location = new google.maps.LatLng(latitude, longitude);
    const propertyLocation = { lat: latitude, lng: longitude };

    // Fetch places for all types and combine results
    let allResults: POIInfo[] = [];
    
    // Use Promise.all to make parallel requests for each type
    const typeRequests = types.map(type => {
      return new Promise<google.maps.places.PlaceResult[]>((resolve) => {
        const request: google.maps.places.PlaceSearchRequest = {
          location,
          radius,
          type: type // Type is a string in the Google Maps API
        };

        service.nearbySearch(request, (results, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && results) {
            resolve(results);
          } else {
            console.log(`No results found for type: ${type} or status: ${status}`);
            resolve([]);
          }
        });
      });
    });

    const typeResults = await Promise.all(typeRequests);
    
    // Flatten and convert to POIInfo format
    const flatResults = typeResults.flat();
    
    // Remove duplicates (same place_id) and convert to POIInfo
    const uniqueResults = Array.from(
      new Map(flatResults.map(place => [place.place_id, place])).values()
    );
    
    allResults = uniqueResults
      .slice(0, maxResults)
      .map(place => convertPlaceToPOI(place, propertyLocation));

    return allResults;
  } catch (error) {
    console.error('Error fetching places from Google Maps API:', error);
    return [];
  }
};

/**
 * Fetch nearby places for all categories
 * @param address Property address (for fallback)
 * @param latitude Property latitude
 * @param longitude Property longitude
 * @returns Promise with grouped POIs
 */
export const fetchNearbyPlacesWithGoogleMaps = async (
  address: string,
  latitude?: number,
  longitude?: number
): Promise<GroupedPOIs> => {
  // Initialize empty result structure
  const result: GroupedPOIs = {
    groceries: [],
    restaurants: [],
    shopping: [],
    parks: [],
    entertainment: [],
    services: [],
    health: [],
    education: [],
    custom: [],
    my_locations: []
  };

  // If no coordinates provided, try to geocode the address
  if (!latitude || !longitude) {
    try {
      const geocoder = new google.maps.Geocoder();
      const geocodeResult = await new Promise<google.maps.GeocoderResult | null>((resolve, reject) => {
        geocoder.geocode({ address }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
            resolve(results[0]);
          } else {
            resolve(null);
          }
        });
      });

      if (geocodeResult?.geometry?.location) {
        latitude = geocodeResult.geometry.location.lat();
        longitude = geocodeResult.geometry.location.lng();
      } else {
        console.error('Could not geocode the address:', address);
        return result;
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
      return result;
    }
  }

  try {
    // Fetch places for each category in parallel
    const categoryPromises = Object.entries(PLACE_CATEGORIES)
      .filter(([category]) => !['custom', 'my_locations'].includes(category)) // Skip custom and my_locations
      .map(async ([category, types]) => {
        const places = await fetchPlacesForCategory(latitude!, longitude!, types);
        return { category, places };
      });

    const categoryResults = await Promise.all(categoryPromises);

    // Populate the result object
    categoryResults.forEach(({ category, places }) => {
      result[category] = places;
    });

    return result;
  } catch (error) {
    console.error('Error fetching nearby places:', error);
    return result;
  }
}; 