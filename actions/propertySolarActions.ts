'use server'
import { createClient } from "@/utils/supabase/server";

/*export async function getPropertySolarData(lat: number, lng: number) {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/solar?lat=${lat}&lng=${lng}`)
    const data = await response.json()
    return data
}*/

export async function getPropertySolarDataDB(address_id: string) {
    const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    
    if(propError){
        throw new Error(`Error fetching property data: ${propError?.message}`);
    }

    const {data: solarData, error: solarError} = await supabase.from('prop_solar_potential').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).limit(1);
    
    if(solarError){
        console.error('Solar data error:', solarError?.message);
        return null; // Return null instead of throwing error
    }

    return solarData?.[0]?.data || null;
}