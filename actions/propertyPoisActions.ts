'use server'
import { createClient } from "@/utils/supabase/server";

export async function getPropertyPoisDB(address_id: string, type: string){
    const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: poisData, error: poisError} = await supabase.from('pois').select('*').eq('prop_id', propData?.id).eq('type', type).order('created_at', { ascending: false }).order('id', { ascending: false }).single();
    
    if(propError || poisError){
        console.log('Error fetching property pois:', propError || poisError);
        throw new Error(`Error fetching property pois: ${propError?.message || poisError?.message}`);
    }

    return poisData?.data
}
