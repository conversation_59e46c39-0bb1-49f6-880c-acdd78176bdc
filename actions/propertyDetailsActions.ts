'use server'
import { createClient } from "@/utils/supabase/server";

export async function getPropertyDetailsDB(address_id: string){
    const supabase = await createClient();
    const {data: propAddressData, error: propAddressError} = await supabase.from('prop_addresses').select('*').eq('id', address_id).single();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: propertyDetailsData, error: propertyDetailsError} = await supabase.from('prop_details').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).single();
    
    if(propError || propertyDetailsError){
        console.log('Error fetching property details:', propError || propertyDetailsError);
        throw new Error(`Error fetching property details: ${propError?.message || propertyDetailsError?.message}`);
    }
    
    return {
        timestamp: propData?.updated_at,
        address: {
            street_address: propAddressData?.address,
            city: propAddressData?.city,
            state: propAddressData?.state,
            zip: propAddressData?.zip,
            latitude: propAddressData?.latitude,
            longitude: propAddressData?.longitude
        },
        data: propertyDetailsData,
    }
}

export async function updatePropertyDetails(address_id: string, data: { [key: string]: any }){
    const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: propertyDetailsData, error: propertyDetailsError} = await supabase.from('prop_details').update(data)
    .eq('prop_id', propData?.id).single();
    
    if(propError || propertyDetailsError){
        console.log('Error updating property details:', propError || propertyDetailsError);
        throw new Error(`Error updating property details: ${propError?.message || propertyDetailsError?.message}`);
    }
}