'use server'

import { fetchDemographicsByCityState } from './marketAnalysisActions/cherreActions'
import { fetchCombinedDemographics } from './marketAnalysisActions/supabaseActions'

// OpenAI integration for state facts
const OPENAI_API_KEY = process.env.OPENAI_API_KEY

/**
 * Interface for state information
 */
export interface StateInfo {
  abbreviation: string
  name: string
  facts: string[]
  demographics: any
  solarPotential: SolarPotential
  taxInfo: TaxInfo
}

export interface SolarPotential {
  averageAnnualSunHours: number
  solarIrradiance: number // kWh/m²/day
  rooftopSolarPotential: string
  incentives: string[]
  paybackPeriod: string
}

export interface TaxInfo {
  stateTaxRate: number
  propertyTaxRate: number
  salesTaxRate: number
  capitalGainsTax: string
  propertyTaxExemptions: string[]
  notes: string
}

/**
 * Get interesting facts about a state using OpenAI
 */
export const getStateFacts = async (stateName: string): Promise<string[]> => {
  if (!OPENAI_API_KEY) {
    console.warn('OpenAI API key not configured')
    return [
      `${stateName} is a state in the United States.`,
      'This state has unique geographical features.',
      'The state has a rich history and culture.',
      'Economic opportunities vary across the state.',
      'The state offers various recreational activities.'
    ]
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are a knowledgeable assistant providing interesting facts about US states. Focus on real estate, economy, demographics, geography, and unique characteristics that would be relevant to property investors and real estate professionals.'
          },
          {
            role: 'user',
            content: `Provide exactly 5 interesting and factual bullet points about ${stateName}. Each fact should be concise (1-2 sentences) and focus on aspects relevant to real estate, economy, demographics, or unique state characteristics. Return only the facts as a JSON array of strings.`
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content
    
    if (content) {
      try {
        const facts = JSON.parse(content)
        if (Array.isArray(facts) && facts.length === 5) {
          return facts
        }
      } catch (parseError) {
        console.warn('Failed to parse OpenAI response as JSON, extracting facts manually')
        // Fallback: extract facts from text
        const lines = content.split('\n').filter((line: string) => line.trim().length > 0)
        const extractedFacts = lines.slice(0, 5).map((line: string) => 
          line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim()
        )
        if (extractedFacts.length > 0) {
          return extractedFacts
        }
      }
    }

    // Fallback facts if API fails
    return [
      `${stateName} has unique economic opportunities for real estate investors.`,
      'The state features diverse geographical regions with varying property values.',
      'Local demographics and population trends influence housing demand.',
      'State and local tax policies impact real estate investment returns.',
      'The state offers various incentives for property development and investment.'
    ]
  } catch (error) {
    console.error('Error fetching state facts from OpenAI:', error)
    return [
      `${stateName} is a state with diverse real estate opportunities.`,
      'The state has varying property markets across different regions.',
      'Economic factors influence real estate investment potential.',
      'Local regulations and taxes affect property ownership costs.',
      'The state offers unique advantages for different types of investors.'
    ]
  }
}

/**
 * Get state demographics using Cherre API
 */
export const getStateDemographics = async (stateAbbreviation: string): Promise<any> => {
  // Map state abbreviations to major cities for better demographic data
  const majorCities: Record<string, string> = {
    'NY': 'New York',
    'CA': 'Los Angeles',
    'TX': 'Houston',
    'FL': 'Miami',
    'IL': 'Chicago',
    'PA': 'Philadelphia',
    'OH': 'Columbus',
    'GA': 'Atlanta',
    'NC': 'Charlotte',
    'MI': 'Detroit',
    'NJ': 'Newark',
    'TN': 'Nashville',
    'IN': 'Indianapolis',
    'AZ': 'Phoenix',
    'MA': 'Boston',
    'WA': 'Seattle',
    'CO': 'Denver',
    'MD': 'Baltimore',
    'MN': 'Minneapolis',
    'MO': 'Kansas City',
    'WI': 'Milwaukee',
    'AL': 'Birmingham',
    'LA': 'New Orleans',
    'KY': 'Louisville',
    'OR': 'Portland',
    'OK': 'Oklahoma City',
    'CT': 'Hartford',
    'UT': 'Salt Lake City',
    'IA': 'Des Moines',
    'NV': 'Las Vegas',
    'AR': 'Little Rock',
    'MS': 'Jackson',
    'KS': 'Wichita',
    'NM': 'Albuquerque',
    'NE': 'Omaha',
    'WV': 'Charleston',
    'ID': 'Boise',
    'NH': 'Manchester',
    'ME': 'Portland',
    'RI': 'Providence',
    'MT': 'Billings',
    'DE': 'Wilmington',
    'SD': 'Sioux Falls',
    'ND': 'Fargo',
    'AK': 'Anchorage',
    'VT': 'Burlington',
    'WY': 'Cheyenne',
    'HI': 'Honolulu',
    'DC': 'Washington'
  }

  const majorCity = majorCities[stateAbbreviation]
  if (!majorCity) {
    console.warn(`No major city mapped for state: ${stateAbbreviation}`)
    return null
  }

  try {
    // Try combined demographics first (uses zipcode mapping)
    const combinedDemographics = await fetchCombinedDemographics(majorCity, stateAbbreviation)
    if (combinedDemographics) {
      return combinedDemographics
    }

    // Fallback to direct city/state query
    const directDemographics = await fetchDemographicsByCityState(majorCity, stateAbbreviation)
    return directDemographics
  } catch (error) {
    console.error(`Error fetching demographics for ${stateAbbreviation}:`, error)
    return null
  }
}

/**
 * Get solar potential data for a state (hardcoded data)
 */
export const getSolarPotential = async (stateAbbreviation: string): Promise<SolarPotential> => {
  const solarData: Record<string, SolarPotential> = {
    'AZ': {
      averageAnnualSunHours: 3800,
      solarIrradiance: 6.5,
      rooftopSolarPotential: 'Excellent',
      incentives: ['Federal Tax Credit', 'Net Metering', 'Property Tax Exemption'],
      paybackPeriod: '6-8 years'
    },
    'CA': {
      averageAnnualSunHours: 3200,
      solarIrradiance: 5.8,
      rooftopSolarPotential: 'Excellent',
      incentives: ['Federal Tax Credit', 'SGIP Rebate', 'Net Metering'],
      paybackPeriod: '7-9 years'
    },
    'FL': {
      averageAnnualSunHours: 2800,
      solarIrradiance: 5.2,
      rooftopSolarPotential: 'Very Good',
      incentives: ['Federal Tax Credit', 'Property Tax Exemption', 'Net Metering'],
      paybackPeriod: '8-10 years'
    },
    'TX': {
      averageAnnualSunHours: 2900,
      solarIrradiance: 5.3,
      rooftopSolarPotential: 'Very Good',
      incentives: ['Federal Tax Credit', 'Local Rebates', 'Property Tax Exemption'],
      paybackPeriod: '8-11 years'
    },
    'NY': {
      averageAnnualSunHours: 2200,
      solarIrradiance: 4.2,
      rooftopSolarPotential: 'Good',
      incentives: ['Federal Tax Credit', 'NY-Sun Incentive', 'Net Metering'],
      paybackPeriod: '9-12 years'
    },
    'NC': {
      averageAnnualSunHours: 2600,
      solarIrradiance: 4.8,
      rooftopSolarPotential: 'Good',
      incentives: ['Federal Tax Credit', 'Net Metering', 'Property Tax Exemption'],
      paybackPeriod: '8-11 years'
    }
  }

  // Default solar data for states not specifically listed
  const defaultSolar: SolarPotential = {
    averageAnnualSunHours: 2400,
    solarIrradiance: 4.5,
    rooftopSolarPotential: 'Moderate',
    incentives: ['Federal Tax Credit', 'Net Metering'],
    paybackPeriod: '10-13 years'
  }

  return solarData[stateAbbreviation] || defaultSolar
}

/**
 * Get tax information for a state (hardcoded data)
 */
export const getTaxInfo = async (stateAbbreviation: string): Promise<TaxInfo> => {
  const taxData: Record<string, TaxInfo> = {
    'NY': {
      stateTaxRate: 8.82,
      propertyTaxRate: 1.69,
      salesTaxRate: 8.25,
      capitalGainsTax: 'Same as income tax',
      propertyTaxExemptions: ['STAR Exemption', 'Senior Exemption', 'Veterans Exemption'],
      notes: 'High property taxes but various exemption programs available'
    },
    'CA': {
      stateTaxRate: 13.3,
      propertyTaxRate: 0.75,
      salesTaxRate: 7.25,
      capitalGainsTax: 'Same as income tax',
      propertyTaxExemptions: ['Homeowners Exemption', 'Proposition 13 Protection'],
      notes: 'Proposition 13 limits property tax increases'
    },
    'TX': {
      stateTaxRate: 0,
      propertyTaxRate: 1.80,
      salesTaxRate: 6.25,
      capitalGainsTax: 'No state capital gains tax',
      propertyTaxExemptions: ['Homestead Exemption', 'Over-65 Exemption'],
      notes: 'No state income tax, but higher property taxes'
    },
    'FL': {
      stateTaxRate: 0,
      propertyTaxRate: 0.98,
      salesTaxRate: 6.0,
      capitalGainsTax: 'No state capital gains tax',
      propertyTaxExemptions: ['Homestead Exemption', 'Save Our Homes'],
      notes: 'No state income tax, homestead exemption caps increases'
    },
    'WA': {
      stateTaxRate: 0,
      propertyTaxRate: 1.03,
      salesTaxRate: 6.5,
      capitalGainsTax: '7% on capital gains over $250k',
      propertyTaxExemptions: ['Senior/Disabled Exemption'],
      notes: 'No state income tax, new capital gains tax on high earners'
    }
  }

  // Default tax data for states not specifically listed
  const defaultTax: TaxInfo = {
    stateTaxRate: 5.0,
    propertyTaxRate: 1.2,
    salesTaxRate: 6.0,
    capitalGainsTax: 'Varies by income level',
    propertyTaxExemptions: ['Homestead Exemption'],
    notes: 'Tax rates vary by locality within the state'
  }

  return taxData[stateAbbreviation] || defaultTax
}

/**
 * Get comprehensive state information
 */
export const getStateInfo = async (stateAbbreviation: string, stateName: string): Promise<StateInfo> => {
  try {
    const [facts, demographics, solarPotential, taxInfo] = await Promise.all([
      getStateFacts(stateName),
      getStateDemographics(stateAbbreviation),
      getSolarPotential(stateAbbreviation),
      getTaxInfo(stateAbbreviation)
    ])

    return {
      abbreviation: stateAbbreviation,
      name: stateName,
      facts,
      demographics,
      solarPotential,
      taxInfo
    }
  } catch (error) {
    console.error(`Error getting state info for ${stateName}:`, error)
    
    // Return basic info even if some data fails
    return {
      abbreviation: stateAbbreviation,
      name: stateName,
      facts: [`${stateName} offers various real estate opportunities.`],
      demographics: null,
      solarPotential: await getSolarPotential(stateAbbreviation),
      taxInfo: await getTaxInfo(stateAbbreviation)
    }
  }
} 