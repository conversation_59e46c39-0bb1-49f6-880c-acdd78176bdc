'use server'

import { createClient } from 'redis';

const FRED_API_KEY = process.env.FRED_API_KEY;

// Redis client setup
let redisClient: any = null;
let redisAvailable = false;

async function getRedisClient() {
  if (!redisClient && process.env.REDIS_URL) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL
      });
      
      redisClient.on('error', (err: any) => {
        console.error('Redis Client Error', err);
        redisAvailable = false;
      });
      
      await redisClient.connect();
      redisAvailable = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      redisAvailable = false;
      redisClient = null;
    }
  }
  return redisClient;
}

export interface FredDataPoint {
  date: string;
  value: number;
}

export interface FredChart {
  id: string;
  title: string;
  description: string;
  units: string;
  data: FredDataPoint[];
  lastUpdated: string;
}

// FRED series IDs for real estate data
const FRED_SERIES = {
  MORTGAGE_RATES: 'MORTGAGE30US',
  HOME_PRICES: 'MSPUS',
  HOUSING_STARTS: 'HOUST',
  EXISTING_HOME_SALES: 'EXHOSLUSM495S',
  PENDING_HOME_SALES: 'PENLISCOUUS', // Housing Inventory: Pending Listing Count
  UNEMPLOYMENT: 'UNRATE',
  GDP: 'GDP',
  INFLATION: 'CPIAUCSL'
};

/**
 * Fetch data from FRED API for a specific series
 */
async function fetchFredSeries(seriesId: string, limit: number = 100): Promise<FredDataPoint[]> {
  try {
    const response = await fetch(
      `https://api.stlouisfed.org/fred/series/observations?series_id=${seriesId}&api_key=${FRED_API_KEY}&file_type=json&limit=${limit}&sort_order=desc`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`FRED API error: ${response.status}`);
    }

    const data = await response.json();
    
    return (data.observations || [])
      .filter((obs: any) => obs.value !== '.')
      .map((obs: any) => ({
        date: obs.date,
        value: parseFloat(obs.value),
      }))
      .reverse(); // Show chronological order
  } catch (error) {
    console.error(`Error fetching FRED series ${seriesId}:`, error);
    return [];
  }
}

/**
 * Get series metadata from FRED API
 */
async function getFredSeriesInfo(seriesId: string): Promise<{ title: string; units: string; lastUpdated: string }> {
  try {
    const response = await fetch(
      `https://api.stlouisfed.org/fred/series?series_id=${seriesId}&api_key=${FRED_API_KEY}&file_type=json`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`FRED API error: ${response.status}`);
    }

    const data = await response.json();
    const series = data.seriess?.[0];
    
    return {
      title: series?.title || seriesId,
      units: series?.units || '',
      lastUpdated: series?.last_updated || new Date().toISOString(),
    };
  } catch (error) {
    console.error(`Error fetching FRED series info ${seriesId}:`, error);
    return {
      title: seriesId,
      units: '',
      lastUpdated: new Date().toISOString(),
    };
  }
}

/**
 * Fetch multiple FRED charts for real estate data with global caching (1 day for all users) - graceful fallback without Redis
 */
export async function fetchFredCharts(page: number = 1, chartsPerPage: number = 3): Promise<FredChart[]> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable, proceeding without cache');
    }
    
    // Try to get cached data first (if Redis is available)
    if (redis && redisAvailable) {
      try {
        const cacheKey = `global_fred_charts:${page}:${chartsPerPage}`;
        const cachedCharts = await redis.get(cacheKey);
        if (cachedCharts) {
          console.log('Returning cached FRED charts data');
          return JSON.parse(cachedCharts);
        }
      } catch (cacheError) {
        console.log('Cache read failed, fetching fresh FRED data');
      }
    }

    const seriesEntries = Object.entries(FRED_SERIES);
    const startIndex = (page - 1) * chartsPerPage;
    const endIndex = startIndex + chartsPerPage;
    const selectedSeries = seriesEntries.slice(startIndex, endIndex);

    const chartPromises = selectedSeries.map(async ([key, seriesId]) => {
      const [data, info] = await Promise.all([
        fetchFredSeries(seriesId, 50), // Get last 50 data points
        getFredSeriesInfo(seriesId),
      ]);

      // Add descriptions for better context
      const descriptions: Record<string, string> = {
        MORTGAGE_RATES: 'Average 30-year fixed mortgage rates in the United States',
        HOME_PRICES: 'Median sales price of houses sold for the United States',
        HOUSING_STARTS: 'New privately-owned housing units started',
        EXISTING_HOME_SALES: 'Existing home sales, seasonally adjusted annual rate',
        PENDING_HOME_SALES: 'Housing inventory pending listing count in the United States',
        UNEMPLOYMENT: 'Unemployment rate in the United States',
        GDP: 'Gross Domestic Product',
        INFLATION: 'Consumer Price Index for All Urban Consumers'
      };

      return {
        id: seriesId,
        title: info.title,
        description: descriptions[key] || info.title,
        units: info.units,
        data,
        lastUpdated: info.lastUpdated,
      };
    });

    const charts = await Promise.all(chartPromises);
    const validCharts = charts.filter(chart => chart.data.length > 0);
    
    // Cache FRED charts if Redis is available
    if (redis && redisAvailable) {
      try {
        const cacheKey = `global_fred_charts:${page}:${chartsPerPage}`;
        await redis.setEx(cacheKey, 86400, JSON.stringify(validCharts));
      } catch (cacheError) {
        console.log('Failed to cache FRED data, continuing without cache');
      }
    }
    
    return validCharts;
  } catch (error) {
    console.error('Error fetching FRED charts:', error);
    throw new Error('Failed to fetch FRED economic data');
  }
}

/**
 * Generate AI summary for FRED charts with global caching (1 day for all users) - graceful fallback without Redis
 */
export async function generateFredChartsSummary(charts: FredChart[]): Promise<string> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable, proceeding without cache');
    }
    
    // Try to get cached summary first (if Redis is available)
    if (redis && redisAvailable) {
      try {
        const chartSignature = charts.map(chart => {
          const latestValue = chart.data[chart.data.length - 1];
          return `${chart.id}:${latestValue?.value || 0}`;
        }).join('|');
        
        const cacheKey = `global_fred_summary:${Buffer.from(chartSignature).toString('base64')}`;
        const cachedSummary = await redis.get(cacheKey);
        if (cachedSummary) {
          console.log('Returning cached FRED summary');
          return cachedSummary;
        }
      } catch (cacheError) {
        console.log('Cache read failed, generating fresh FRED summary');
      }
    }

    // Import OpenAI dynamically to avoid module-level initialization
    const { default: OpenAI } = await import('openai');
    
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Prepare chart data for analysis
    const chartData = charts.map(chart => {
      const latestValue = chart.data[chart.data.length - 1];
      const previousValue = chart.data[chart.data.length - 2];
      const trend = latestValue && previousValue ? 
        (latestValue.value > previousValue.value ? 'increasing' : 'decreasing') : 'stable';
      
      return {
        indicator: chart.title,
        description: chart.description,
        latestValue: latestValue?.value,
        trend,
        units: chart.units
      };
    });

    const prompt = `Analyze these economic indicators and provide a 2-3 sentence summary of what they mean for the real estate market:

${JSON.stringify(chartData, null, 2)}

Focus on the key trends and their implications for real estate investors and the housing market.`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a real estate market analyst. Provide concise, insightful summaries of economic data.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 200,
      temperature: 0.3,
    });

    const summary = completion.choices[0]?.message?.content || 'Economic analysis not available';
    
    // Cache AI summary if Redis is available
    if (redis && redisAvailable) {
      try {
        const chartSignature = charts.map(chart => {
          const latestValue = chart.data[chart.data.length - 1];
          return `${chart.id}:${latestValue?.value || 0}`;
        }).join('|');
        
        const cacheKey = `global_fred_summary:${Buffer.from(chartSignature).toString('base64')}`;
        await redis.setEx(cacheKey, 86400, summary);
      } catch (cacheError) {
        console.log('Failed to cache FRED summary, continuing without cache');
      }
    }
    
    return summary;
  } catch (error) {
    console.error('Error generating FRED charts summary:', error);
    return 'Economic analysis not available';
  }
} 