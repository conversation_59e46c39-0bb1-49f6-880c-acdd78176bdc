'use server'
import apiEndpointsServer from "@/constants/apiEndpointsServer";
import httpAgent from "@/helpers/httpAgent/httpAgent";
import { createClient } from "@/utils/supabase/server";
import { v4 as uuidv4 } from 'uuid';
import { getBaseUrl } from '@/lib/utils';

// Enhanced metadata builder that includes value tracking
const buildMetadata = (existingMetadata: any, fieldName: string, year: number, source: 'human' | 'ai' = 'human', value?: string) => {
    const metadata = existingMetadata || {};
    const attribution = metadata.attribution || {};
    
    // Ensure field exists in attribution
    if (!attribution[fieldName]) {
        attribution[fieldName] = {
            field_name: fieldName,
            year: year,
            history: []
        };
    }
    
    // Add new history entry with value
    const historyEntry: any = {
        source: source,
        datetime: new Date().toISOString(),
        year: year
    };
    
    // Include the value if provided
    if (value !== undefined && value !== null) {
        historyEntry.value = value;
    }
    
    attribution[fieldName].history.push(historyEntry);
    
    // Update the year for the field
    attribution[fieldName].year = year;
    
    return {
        ...metadata,
        attribution: attribution
    };
};

// Cache helper function (calls backend API for Redis caching)
const cacheFinancialData = async (prop_id: string, data: { [key: string]: string }, year: number) => {
    try {
        const cacheKey = `prop_financials_${prop_id}_year_${year}`;
        const body = {
            cache_key: cacheKey,
            data: data,
            ttl: 3600 // 1 hour TTL, adjust as needed
        };
        
        const baseUrl = getBaseUrl();
        const url = new URL('/api/redis-cache', baseUrl).toString();
        
        // Use local API endpoint for Redis caching
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        });
        
        if (!response.ok) {
            throw new Error(`Cache API responded with status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('Financial data cached:', result);
        
    } catch (error) {
        console.log('Error caching financial data:', error);
        // Don't throw error, just log it as caching is not critical for core functionality
    }
};

export const getPropertyFinancialsDB = async (address_id: string) => {
    const supabase = await createClient();

    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();

    // Fetch existing financial records
    const {data: financials, error: financialsError} = await supabase
        .from('prop_financials')
        .select('*')
        .eq('prop_id', propData?.id)
        .order('year', { ascending: true });

    if(propError || financialsError){
        console.log('Error fetching property financials:', propError || financialsError);
        throw new Error(`Error fetching property financials: ${propError?.message || financialsError?.message}`);
    }

    // Ensure we have rows for Year 1-10.  Insert missing years with default null values so frontend always has full dataset.
    const expectedYears = [1,2,3,4,5,6,7,8,9,10];
    const existingYears = (financials || []).map(f => f.year);
    const missingYears = expectedYears.filter(y => !existingYears.includes(y));

    if(missingYears.length){
        const now = new Date().toISOString();
        const defaultRow = {
            rental_income: null,
            long_term_rental: null,
            short_term_rental: null,
            other_income: null,
            vacancy_loss: null,
            credit_loss: null,
            effective_gross_income: null,
            property_tax: null,
            insurance: null,
            repairs: null,
            maintenance: null,
            professional_fees: null,
            management_fees: null,
            leasing_fees: null,
            legal_fees: null,
            accounting_fees: null,
            engineering_fees: null,
            marketing_fees: null,
            consulting_fees: null,
            utilities: null,
            services: null,
            reserves: null,
            total_operating_expenses: null,
            net_operating_income: null,
            annual_debt_service: null,
            dscr: null,
            cash_flow_before_taxes: null,
            cash_flow_after_taxes: null,
            cumulative_cash_flow: null,
            cap_rate: null,
            gross_rent_multiplier: null,
            equity_multiple: null,
            cash_on_cash_return: null,
            total_acquisition_cost: null,
            aggregated_noi: null,
            blended_cap_rate: null,
            portfolio_irr: null,
            metadata: {},
        };

        // Build insert payloads
        const rowsToInsert = missingYears.map(yr => ({
            id: uuidv4(),
            prop_id: propData?.id,
            year: yr,
            ...defaultRow,
            created_at: now,
            updated_at: now
        }));

        const { error: insertErr } = await supabase.from('prop_financials').insert(rowsToInsert);
        if(insertErr){
            console.log('Error inserting missing financial rows:', insertErr);
            throw new Error(`Error inserting missing financial rows: ${insertErr.message}`);
        }

        // Re-fetch full dataset after insertion
        const { data: refreshed, error: refreshErr } = await supabase
            .from('prop_financials')
            .select('*')
            .eq('prop_id', propData?.id)
            .order('year', { ascending: true });
        if(refreshErr){
            console.log('Error refetching property financials:', refreshErr);
            throw new Error(`Error refetching property financials: ${refreshErr.message}`);
        }
        return refreshed;
    }

    return financials;
}

export const updatePropertyFinancials = async (
    address_id: string, 
    data: { [key: string]: string }, 
    year: number, 
    source: 'human' | 'ai' = 'human'
) => {
    const supabase = await createClient();

    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    
    if (propError) {
        console.log('Error fetching property:', propError);
        throw new Error(`Error fetching property: ${propError.message}`);
    }

    // Get existing financial record to preserve metadata
    const {data: existingFinancials, error: fetchError} = await supabase
        .from('prop_financials')
        .select('metadata')
        .eq('prop_id', propData?.id)
        .eq('year', year)
        .single();

    if (fetchError) {
        console.log('Error fetching existing financials:', fetchError);
        throw new Error(`Error fetching existing financials: ${fetchError.message}`);
    }

    // Build updated metadata for each field being changed
    let updatedMetadata = existingFinancials?.metadata || {};
    
    // Filter and validate data before processing - prevent empty strings and invalid values
    const validData: { [key: string]: string } = {};
    Object.entries(data).forEach(([fieldName, fieldValue]) => {
        // Only include valid numeric values that can be converted to numbers
        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
            const numValue = parseFloat(fieldValue);
            if (!isNaN(numValue) && isFinite(numValue)) {
                validData[fieldName] = fieldValue;
                updatedMetadata = buildMetadata(updatedMetadata, fieldName, year, source, fieldValue);
            } else {
                console.warn(`Skipping invalid value for ${fieldName}:`, fieldValue);
            }
        } else {
            console.warn(`Skipping empty/null value for ${fieldName}:`, fieldValue);
        }
    });

    // If no valid data to update, return success without database call
    if (Object.keys(validData).length === 0) {
        console.log('No valid data to update, skipping database operation');
        return { success: true, metadata: updatedMetadata };
    }

    // Prepare data with metadata using only valid data
    const updateData = {
        ...validData,
        metadata: updatedMetadata,
        updated_at: new Date().toISOString()
    };

    // Update in Supabase
    const {error: financialsError} = await supabase
        .from('prop_financials')
        .update(updateData)
        .eq('prop_id', propData?.id)
        .eq('year', year);

    if (financialsError) {
        console.log('Error updating property financials:', financialsError);
        throw new Error(`Error updating property financials: ${financialsError.message}`);
    }

    return { success: true, metadata: updatedMetadata };
};

export const getPropertyFinancialsUnits = async (address_id: string) => {
    const supabase = await createClient();

    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: units, error: unitsError} = await supabase.from('prop_units').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false });

    if(propError || unitsError){
        console.log('Error fetching property units:', propError || unitsError);
        throw new Error(`Error fetching property units: ${propError?.message || unitsError?.message}`);
    }

    return units;
}

export async function propertyFinancialsUpdateUnit(unit: { [key: string]: any }){
	const supabase = await createClient();
	const {data: unitData, error: unitError} = await supabase.from('prop_units').update({
        unit: unit.unit || 'N/A',
        beds: unit.beds || 0,
        baths: unit.baths || 0,
        hoa_fee: unit.hoa_fee || 0,
        sqft: unit.sqft || 0,
		document_recorded_date: unit.document_recorded_date || null,
		feature_tags: unit.feature_tags || [],
		amenities_tags: unit.amenities_tags || [],
		img_urls: unit.img_urls || [],
		rent: unit.rent || 0,
        price: unit.price || 0
    }).eq('id', unit.id).select();
    

	if(unitError){
		console.log('Error updating unit:', unitError);
		throw new Error(`Error updating unit: ${unitError?.message}`);
	}

	return unitData;
}

export async function propertyFinancialsAddUnit(address_id: string){
	const supabase = await createClient();
	const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
	const {data: unitNewData, error: unitNewError} = await supabase.from('prop_units').insert({
        id: uuidv4(),
		prop_id: propData?.id,
		unit: 'N/A',
		beds: 0,
		baths: 0,
		hoa_fee: 0,
        sqft: 0,
		rent: 0,
        price: 0,
        feature_tags: [],
        amenities_tags: [],
        img_urls: []
	})

	const {data: unitData, error: unitError} = await supabase.from('prop_units').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).select();

	if(propError || unitNewError || unitError){
		console.log('Error adding unit:', unitError || unitNewError || propError);
		throw new Error(`Error adding unit: ${unitError?.message || unitNewError?.message || propError?.message}`);
	}

	return unitData;
}

export async function propertyFinancialsAICalculate(address_id: string, task: string, year: number){
	const supabase = await createClient();

    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();

    if (propError) {
        console.log('Error fetching property for AI calculation:', propError);
        throw new Error(`Error fetching property for AI calculation: ${propError.message}`);
    }

    const body = {
        task,
        prop_id: propData?.id,
        portfolio_id: propData?.portfolio_id,
        year,
        params: {}
    }
    
    try {
        // Call AI calculation endpoint
        const aiResult = await httpAgent('POST', `${apiEndpointsServer.finance.calculate}`, {body});
        
        // If AI calculation returns data to save, update with 'ai' source
        if (aiResult?.data && typeof aiResult.data === 'object') {
            const calculatedData: { [key: string]: string } = {};
            
            // Convert AI result to string format for saving
            Object.keys(aiResult.data).forEach(key => {
                if (aiResult.data[key] !== null && aiResult.data[key] !== undefined) {
                    calculatedData[key] = String(aiResult.data[key]);
                }
            });
            
            // Save with 'ai' source for metadata tracking
            if (Object.keys(calculatedData).length > 0) {
                const result = await updatePropertyFinancials(address_id, calculatedData, year, 'ai');
                console.log('AI calculated data saved with metadata:', result?.metadata);
            }
        }
        
        return aiResult;
    } catch (error) {
        console.log('Error in AI calculation:', error);
        throw error;
    }
}

export async function updatePropertyFinancialsWithAutoCalc(
    addressId: string,
    data: { [key: string]: string },
    year: number,
    source: 'manual' | 'calculator' | 'ai' = 'manual',
    portfolioId?: string
) {
    try {
        // Map source to the expected type
        const mappedSource: 'human' | 'ai' = source === 'ai' ? 'ai' : 'human';
        
        // First update the financial data
        const result = await updatePropertyFinancials(addressId, data, year, mappedSource);
        
        // Then trigger auto-recalculation if this was a manual change
        if (source === 'manual' && portfolioId) {
            // Get the first changed field to use as trigger
            const triggerField = Object.keys(data)[0];
            
            // Call the recalculate API
            const response = await fetch('/api/recalculate-financials', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    addressId,
                    portfolioId,
                    triggerField
                })
            });
            
            if (!response.ok) {
                console.error('Auto-recalculation failed, but data was saved');
            }
        }
        
        return result;
    } catch (error) {
        console.error('Error updating financials with auto-calc:', error);
        throw error;
    }
}