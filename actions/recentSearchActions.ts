'use server'

import { createClient } from '@/utils/supabase/server';
import { storeRecentSearch, getRecentSearches, RecentSearch } from '@/utils/redis';

/**
 * Store a recent search for the current user
 */
export async function storeUserRecentSearch(search: Omit<RecentSearch, 'timestamp'>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    const searchWithTimestamp: RecentSearch = {
      ...search,
      timestamp: Date.now()
    };

    await storeRecentSearch(user.id, searchWithTimestamp);
    
    return { success: true };
  } catch (error) {
    console.error('Error in storeUserRecentSearch:', error);
    return { success: false, error: 'Failed to store recent search' };
  }
}

/**
 * Get recent searches for the current user
 */
export async function getUserRecentSearches(): Promise<{ success: boolean; data?: RecentSearch[]; error?: string }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    const searches = await getRecentSearches(user.id);
    
    return { success: true, data: searches };
  } catch (error) {
    console.error('Error in getUserRecentSearches:', error);
    return { success: false, error: 'Failed to get recent searches' };
  }
} 