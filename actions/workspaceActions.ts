'use server'
import { createClient } from "@/utils/supabase/server";
import { v4 as uuidv4 } from 'uuid';
import { getSession } from "./userActions";


export async function createWorkspace(name: string) {
    try {
      const supabase = await createClient();
      const userData = await getSession();
      const userId = userData?.id;


      const { data: ownerRole, error: roleError } = await (await createClient())
        .from('roles')
        .select('id')
        .eq('name', 'owner')
        .single() as { data: { id: string } | null, error: any };

      if (roleError) {
        console.log('Error getting owner role:', roleError);
        throw new Error(`Failed to get owner role: ${roleError.message}`);
      }

      if (!ownerRole) {
        throw new Error('Owner role not found');
      }

      // Create the workspace
      const { data: workspace, error: workspaceError } = await (await createClient())
        .from('workspaces')
        .insert([
          {
            name,
            owner_id: userId,
            is_personal: false
          },
        ])
        .select(`
          id,
          name,
          owner_id,
          created_at,
          updated_at
        `)
        .single();

      if (workspaceError) {
        throw new Error(`Error creating workspace: ${workspaceError.message}`);
      }

      if (!workspace) {
        throw new Error('Failed to create workspace');
      }

      // Add the owner as a member
      const { data: member, error: memberError } = await (await createClient())
        .from('workspace_members')
        .insert([
          {
            workspace_id: workspace.id,
            user_id: userId,
            role_id: ownerRole.id,
            is_personal: false
          }
        ])
        .select(`
          is_personal,
          roles (
            name
          )
        `)
        .single() as { data: { is_personal: boolean, roles: { name: string } } | null, error: any };

      if (memberError) {
        // If adding member fails, clean up the workspace
        await (await createClient()).from('workspaces').delete().eq('id', workspace.id);
        throw new Error(`Failed to add owner as member: ${memberError.message}`);
      }

      if (!member || !member.roles) {
        throw new Error('Failed to create workspace member');
      }

      return {
        ...workspace,
        role: member.roles.name,
        is_personal: member.is_personal
      };
    } catch (error: any) {
      console.log('Error in createWorkspace:', error);
      throw new Error(error.message || 'Failed to create workspace');
    }
  }

export async function getUserWorkspaces() {
    try {
	    const userData = await getSession();
	    const userId = userData?.id;

      const { data: workspaces, error } = await (await createClient())
        .from('workspace_members')
        .select(`
          workspace_id,
          is_personal,
          roles!inner (
            name
          ),
          workspaces!inner (
            id,
            name,
            owner_id,
            created_at,
            updated_at,
            logo_url
          )
        `)
        .eq('workspaces.is_deleted', false)
        .order('created_at', { ascending: false })
        .eq('user_id', userId) as { data: any, error: any };

      if (error) {
        console.log('Error in getUserWorkspaces:', error?.message);
        throw error;
      }

      if (!workspaces || !Array.isArray(workspaces)) {
        return [];
      }

      return workspaces
        .filter(workspace => 
          workspace?.workspaces && 
          workspace?.roles?.name && 
          workspace?.workspace_id
        )
        .map(workspace => ({
          id: workspace.workspaces.id,
          name: workspace.workspaces.name,
          owner_id: workspace.workspaces.owner_id,
          created_at: workspace.workspaces.created_at,
          updated_at: workspace.workspaces.updated_at,
          logo_url: workspace.workspaces.logo_url,
          role: workspace.roles.name,
          is_personal: workspace.is_personal
        }));
    } catch (error) {
      console.log('Error in getUserWorkspaces:', error);
      throw new Error('Failed to get workspaces');
    }
  }

export async function getWorkspace(id: string) {
	const userData = await getSession();
	const userId = userData?.id;

	try {
		console.log('Fetching workspace:', id, 'for user:', userId);
		const { data, error } = await (await createClient())
			.from('workspace_members')
			.select(`
          workspace_id,
          user_id,
          is_personal,
          workspaces:workspace_id (
            id,
            name,
            owner_id,
            created_at,
            updated_at,
            logo_url
          ),
          roles (
            name
          )
        `)
			.eq('workspace_id', id)
			.eq('user_id', userId)
			.single() as { data: any | null, error: any };

		if (error) {
			console.log('Error fetching workspace:', error);
			throw new Error(`Error fetching workspace: ${error.message}`);
		}

		if (!data || !data.workspaces || !data.roles) {
			console.log('No workspace data found:', data);
			throw new Error('Workspace not found');
		}

		console.log('Workspace fetched successfully:', id);
		return {
			id: data.workspaces.id,
			name: data.workspaces.name,
			owner_id: data.workspaces.owner_id,
			created_at: data.workspaces.created_at,
			updated_at: data.workspaces.updated_at,
			logo_url: data.workspaces.logo_url,
			role: data.roles.name,
			is_personal: data.is_personal
		};
	} catch (error) {
		console.log('Error in getWorkspace:', error);
		throw error;
	}
}

export async function deleteWorkspace(id: string) {
	try {
		const userData = await getSession();
		const userId = userData?.id;

		// Check if the workspace is personal
		const { data: workspaceData, error: workspaceError } = await (await createClient())
			.from('workspaces')
			.select('is_personal')
			.eq('id', id)
			.single();

		if (workspaceError) {
			console.log('Error fetching workspace for delete check:', workspaceError);
			throw workspaceError;
		}

		if (workspaceData?.is_personal) {
			throw new Error('First Workspace 🏡 cannot be deleted.');
		}

		const { error } = await (await createClient())
			.from('workspaces')
			.update({
				is_deleted: true
			})
			.eq('id', id)
			.eq('owner_id', userId);

		if (error) {
			console.log('Error deleteWorkspace:', error);
			throw error;
		}
	} catch (error) {
		console.log('Error in deleteWorkspace:', error);
		throw error;
	}
}

export async function updateWorkspaceName(id: string, name: string, logoUrl?: string) {
	try {
		const userData = await getSession();
		const userId = userData?.id;

		if (!name || name.trim().length === 0) {
			throw new Error('Workspace name cannot be empty');
		}

		// Check if user has permission to update this workspace
		const { data: memberData, error: memberError } = await (await createClient())
			.from('workspace_members')
			.select(`
				roles (
					name
				)
			`)
			.eq('workspace_id', id)
			.eq('user_id', userId)
			.single() as { data: { roles: { name: string } } | null, error: any };

		if (memberError || !memberData) {
			throw new Error('You do not have permission to update this workspace');
		}

		// Only owners and admins can rename workspaces
		if (memberData.roles.name !== 'owner' && memberData.roles.name !== 'admin') {
			throw new Error('You do not have permission to rename this workspace');
		}

		const updateData: any = {
			name: name.trim(),
			updated_at: new Date().toISOString()
		};

		// Only update logo_url if it's provided (including empty string to clear it)
		if (logoUrl !== undefined) {
			updateData.logo_url = logoUrl.trim() || null;
		}

		const { data: workspace, error } = await (await createClient())
			.from('workspaces')
			.update(updateData)
			.eq('id', id)
			.select('id, name, updated_at, logo_url')
			.single();

		if (error) {
			console.log('Error updateWorkspaceName:', error);
			throw error;
		}

		return workspace;
	} catch (error) {
		console.log('Error in updateWorkspaceName:', error);
		throw error;
	}
}

export async function getMemberWorkspace(workspaceId: string) {
	const { data, error } = await (await createClient())
		.from('workspace_members')
		.select(`
        workspace_id,
        user_id,
        workspaces!inner (
          id,
          name,
          owner_id,
          created_at,
          updated_at
        ),
        roles!inner (
          name
        ),
        is_personal
      `)
		.eq('workspace_id', workspaceId) as { data: any | null, error: any };

	if (error) {
		console.log('Error getting getMemberWorkspace:', error);
		throw new Error(`Failed to get getMemberWorkspace: ${error.message}`);
	}

	return data || [];
}

export async function addMemberWorkspace(workspaceId: string, email: string, role: string) {
  try {
    const supabase = await createClient();

    // Check if the workspace is personal
    const { data: workspaceData, error: workspaceError } = await supabase
      .from('workspaces')
      .select('is_personal')
      .eq('id', workspaceId)
      .single();

    if (workspaceError) {
      console.log('Error fetching workspace for add member check:', workspaceError);
      throw workspaceError;
    }

    if (workspaceData?.is_personal) {
      throw new Error('Cannot add members to a personal workspace.');
    }

    const userDataSession = await getSession();
    const userId = userDataSession?.id;

    const { data: currentUserRole, error: roleError } = await (await createClient())
        .from('workspace_members')
        .select('role_id, roles(name)')
        .eq('workspace_id', workspaceId)
        .eq('user_id', userId)
        .single() as { data: any, error: any };

      if (roleError || !currentUserRole) {
        throw new Error('Failed to verify user permissions');
      }

      if (currentUserRole.roles.name !== 'owner' && currentUserRole.roles.name !== 'admin') {
        throw new Error('You do not have permission to add team members');
      }

      // Get the role ID for the new member
      const { data: roleData, error: roleLookupError } = await (await createClient())
        .from('roles')
        .select('id')
        .eq('name', role)
        .single();

      if (roleLookupError || !roleData) {
        throw new Error('Invalid role specified');
      }

      // Look up the user by email
      const { data: userData, error: userError } = await (await createClient())
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      if (userError || !userData) {
        throw new Error('User not found');
      }

      // Check if the user is already a member
      const { data: existingMember} = await (await createClient())
        .from('workspace_members')
        .select('id')
        .eq('workspace_id', workspaceId)
        .eq('user_id', userData.id)
        .single();

      if (existingMember) {
        throw new Error('User is already a member of this workspace');
      }

      const timestamp = new Date().toISOString();

      // Add the new member
      const { error: insertError } = await (await createClient())
        .from('workspace_members')
        .insert([
          {
            id: uuidv4(),
            workspace_id: workspaceId,
            user_id: userData.id,
            role_id: roleData.id,
            created_at: timestamp,
            updated_at: timestamp,
            is_personal: false
          }
        ]);

      if (insertError) {
        console.log('Error adding workspace member:', insertError);
        throw new Error(insertError.message);
      }
    } catch (error) {
      console.log('Error in addMemberWorkspace:', error);
      throw error;
    }
}

export async function createPortfolio(portfolioName: string, workspaceId: string) {
	const { data: portfolio, error } = await (await createClient())
		.from('portfolios')
		.insert({
			name: portfolioName.trim(),
			workspace_id: workspaceId,
		})
		.select('*')
		.single();

    const {data: newFinancials, error: newFinancialsError} = await (await createClient())
    .from('portfolio_financials')
    .insert(
        Array.from({ length: 5 }, (_, i) => ({
            portfolio_id: portfolio.id,
            year: i + 1,
        }))
    )

	if (error) {
		console.log('Error fetching createPortfolio:', error);
		throw new Error(`Error fetching createPortfolio: ${error.message}`);
	}

  return portfolio;
}

export async function ensureWorkspaceLogosBucket() {
	// Bucket and policies are now created via migration
	// This function is kept for backward compatibility but no longer needed
	console.log('Workspace logos bucket is managed via database migration');
}