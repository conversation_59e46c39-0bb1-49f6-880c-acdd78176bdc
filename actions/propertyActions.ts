'use server'
import apiEndpointsServer from "@/constants/apiEndpointsServer"
import httpAgent from "@/helpers/httpAgent/httpAgent"
import { DemographicsType } from "@/types/DemographicsType"
import { PropertyDetailsType } from "@/types/PropertyDetailsType"
import { TaxHistoryType } from "@/types/TaxHistoryType"
import { UnitDataType } from "@/types/UnitDataType"


export async function cachePropData(portfolio_id: string, property_id: string) {
	const body = {
        property_data:{
			portfolio_id,
            property_id,
		}
    }
    return await httpAgent('POST', `${apiEndpointsServer.aiSummary}`, {body})
}

export async function getAiSummary(address: string){
    const body = {
        property_data:{
			address
		}
    }
    return await httpAgent('POST', `${apiEndpointsServer.aiSummary}`, {body})
}

export async function fetchPropertyDetails(address: string, city: string, state: string, zipcode: string): Promise<PropertyDetailsType>{
    const body = {
        address,
        city,
        state,
        zipcode
    }
    console.log('fetchPropertyDetails', body)
    return await httpAgent('POST', `${apiEndpointsServer.property.data}`, {body})
}

export async function fetchTaxHistory(address: string, city: string, state: string, zipcode: string): Promise<TaxHistoryType>{
    const body = {
        address,
        city,
        state,
        zipcode
    }
    
    return await httpAgent('POST', `${apiEndpointsServer.property.taxHistory}`, {body})
}

export async function fetchUnitData(address: string, city: string, state: string, zipcode: string): Promise<UnitDataType>{
    const body = {
        address,
        city,
        state,
        zipcode
    }
    
    return await httpAgent('POST', `${apiEndpointsServer.property.unitData}`, {body})
}

export async function fetchDemographics(address: string, city: string, state: string, zipcode: string): Promise<DemographicsType>{
    const body = {
        address,
        city,
        state,
        zipcode
    }
    return await httpAgent('POST', `${apiEndpointsServer.property.demographics}`, {body})
}