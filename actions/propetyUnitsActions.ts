'use server'
import { createClient } from "@/utils/supabase/server";
import { v4 as uuidv4 } from 'uuid';
import { canonicalizeListingType } from "@/utils/formatters";

export async function getUnitDataDB(address_id: string){
	const supabase = await createClient();
	
	const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
	
	if(propError) {
		console.log('Error fetching property data:', propError);
		throw new Error(`Error fetching property data: ${propError.message}`);
	}
	
	const {data: unitData, error: unitError} = await supabase
		.from('prop_units')
		.select('*')
		.eq('prop_id', propData?.id)
		.order('created_at', { ascending: false })
		.order('id', { ascending: false });
		
	if(unitError) {
		console.log('Error fetching unit data:', unitError);
		throw new Error(`Error fetching unit data: ${unitError.message}`);
	}
	
	// Fetch market data separately for each unit
	if (unitData && unitData.length > 0) {
		const unitIds = unitData.map(unit => unit.id);
		const {data: marketData, error: marketError} = await supabase
			.from('prop_market_data')
			.select('*')
			.eq('prop_id', propData?.id)
			.in('unit_id', unitIds);
			
		if(marketError) {
			console.log('Error fetching market data:', marketError);
			throw new Error(`Error fetching market data: ${marketError.message}`);
		}
		
		// Attach market data to each unit
		return unitData.map(unit => {
			const unitMarketData = marketData?.filter(market => market.unit_id === unit.id) || [];
			return {
				...unit,
				prop_market_data: unitMarketData
			};
		});
	}

	return unitData || [];
}

export async function updateUnit(unit: { [key: string]: any }){
	const supabase = await createClient();
	const {data: unitData, error: unitError} = await supabase.from('prop_units').update({
        unit: unit.unit || 'N/A',
        beds: unit.beds || 0,
        baths: unit.baths || 0,
        hoa_fee: unit.hoa_fee || 0,
        sqft: unit.sqft || 0,
		document_recorded_date: unit.document_recorded_date || null,
		feature_tags: unit.feature_tags || [],
		amenities_tags: unit.amenities_tags || [],
		img_urls: unit.img_urls || [],
		rent: unit.rent || 0,
        price: unit.price || 0
    }).eq('id', unit.id).select();
    

	if(unitError){
		console.log('Error updating unit:', unitError);
		throw new Error(`Error updating unit: ${unitError?.message}`);
	}

	return unitData;
}

export async function removeUnit(id: string){
	const supabase = await createClient();
	const {data: unitData, error: unitError} = await supabase.from('prop_units').delete().eq('id', id);

	if(unitError){
		console.log('Error removing unit:', unitError);
		throw new Error(`Error removing unit: ${unitError?.message}`);
	}
    
}

export async function addUnit(address_id: string){
	const supabase = await createClient();
	const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
	
	const {data: unitNewData, error: unitNewError} = await supabase.from('prop_units').insert({
		id: uuidv4(),
		prop_id: propData?.id,
		unit: 'N/A',
		beds: 0,
		baths: 0,
		hoa_fee: 0,
		sqft: 0,
		rent: 0,
        price: 0,
        feature_tags: [],
        amenities_tags: [],
        img_urls: []
	})

	const unitData = await getUnitDataDB(address_id);

	return unitData;
}


export async function updateUnits(address_id: string, units: { [key: string]: any }[]){
	const supabase = await createClient();
	const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
	const {data: unitData, error: unitError} = await supabase.from('prop_units').delete().eq('prop_id', propData?.id);

	const unitsDataInsert = units?.map((unit: any) => ({
		id: uuidv4(),
		prop_id: propData?.id,
		unit: unit?.tax_assessor_v2__tax_assessor_id?.address ? unit.tax_assessor_v2__tax_assessor_id.address.split(' ').slice(-1)[0] : 'N/A',
		beds: unit?.tax_assessor_v2__tax_assessor_id?.bed_count || 0,
		baths: unit?.tax_assessor_v2__tax_assessor_id?.bath_count || 0,
		hoa_fee: unit?.tax_assessor_v2__tax_assessor_id?.hoa_1_fee_value || 0,
		sqft: unit?.tax_assessor_v2__tax_assessor_id?.floor_area_sqft || 0,
		rent: 0,
        price: 0,
        feature_tags: [],
        amenities_tags: [],
        img_urls: []
	}))

	const { data: newUnits, error: newUnitsError } = await supabase
		.from('prop_units')
		.insert(unitsDataInsert)
		.select()

	if(propError || unitError || newUnitsError){
		console.log('Error updating units:', newUnitsError || unitError || propError);
		throw new Error(`Error updating units: ${newUnitsError?.message || unitError?.message || propError?.message}`);
	}

	return newUnits;
}


export async function addUnitMarketData(addressId: string, unitId: string, unitNumber: string){
	const supabase = await createClient();
	const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId).single();
	
	const {data: unitNewData, error: unitNewError} = await supabase.from('prop_market_data').insert({
        prop_id: propData?.id,
        unit_id: unitId,
        unit: unitNumber || 'N/A',
        data: [{
			hoa: {
				fee: 0,
			},
			  tags: [],
			  flags: {
				is_pending: null,
				is_for_rent: null,
				is_contingent: null,
			},
			  photos: [],
			  source: {
				id: "",
				agents: [],
				disclaimer: {
				  href: null,
				  logo: null,
				  text: "",
				},
				listing_id: "",
			},
			  status: "",
			  location: {
				county: {
				  name: "",
				  fips_code: "",
				},
				address: {
				  city: "",
				  line: "",
				  unit: "",
				  coordinate: {
					lat: 0,
					lon: 0,
				  },
				  state_code: "",
				  postal_code: "",
				  street_name: "",
				  street_number: "",
				  street_suffix: null,
				  street_direction: null,
				},
				neighborhoods: null,
			},
			list_date: "",
			list_price: 0,
			tax_record: {
				public_record_id: "",
			},
			  advertisers: [],
			  description: {
				beds: 0,
				name: null,
				sqft: 0,
				text: "",
				type: "",
				garage: 0,
				stories: 0,
				lot_sqft: null,
				baths_full: 0,
				baths_half: null,
				sold_price: 0,
				year_built: null,
			},
			property_id: "",
			listing_type: "",
			pending_date: null,
			primary_photo: {
				href: "",
			},
			last_sold_date: "",
			list_price_max: null,
			list_price_min: null,
			price_per_sqft: 0,
			last_sold_price: 0,
		}]
    });

	const unitData = await getUnitDataDB(addressId);

	return unitData;
	
}

export async function updateUnitMarketData(unit: { [key: string]: any }, unitId: string, indexData: number, marketId: string, addressId: string){
	const supabase = await createClient();
    const {data: unitData, error: unitError} = await supabase.from('prop_market_data').select('*').eq('id', marketId).eq('unit_id', unitId).single();
    
    // Canonicalize the listing type before saving
    const updatedUnit = {
        ...unit,
        listing_type: canonicalizeListingType(unit.listing_type || 'unknown')
    };
    
    unitData.data[indexData] = updatedUnit;

	
    const {data: marketData, error: marketError} = await supabase.from('prop_market_data').update({
        data: unitData.data
    }).eq('unit_id', unitId).select();

    if(marketError){
        console.log('Error updating market data:', marketError);
        throw new Error(`Error updating market data: ${marketError?.message}`);
    }
	
	const unitDataNew = await getUnitDataDB(addressId);

	return unitDataNew;
}

export async function deleteUnitMarketDataItem(unitId: string, indexData: number, marketId: string, addressId: string){
	const supabase = await createClient();
    
    // Fetch the specific market data record
    const {data: unitData, error: unitError} = await supabase
        .from('prop_market_data')
        .select('*')
        .eq('id', marketId)
        .eq('unit_id', unitId)
        .single();
    
    if(unitError || !unitData){
        console.log('Error fetching market data for deletion:', unitError);
        throw new Error(`Error fetching market data for deletion: ${unitError?.message || 'Record not found'}`);
    }

    // Ensure we have valid data array
    if (!Array.isArray(unitData.data)) {
        console.log('Invalid data structure for market data:', unitData);
        throw new Error('Invalid market data structure');
    }

    // Verify the index is valid
    if (indexData < 0 || indexData >= unitData.data.length) {
        console.log('Invalid index for deletion:', indexData, 'Array length:', unitData.data.length);
        throw new Error(`Invalid index for deletion: ${indexData}`);
    }

    // Create a new array without the item at the specified index
    const updatedData = unitData.data.filter((_: any, index: number) => index !== indexData);
    
    console.log(`Deleting market data item at index ${indexData} for unit ${unitId}. Items before: ${unitData.data.length}, Items after: ${updatedData.length}`);
	
    // Update the record with the new data array
    const {data: marketData, error: marketError} = await supabase
        .from('prop_market_data')
        .update({
            data: updatedData
        })
        .eq('id', marketId)
        .eq('unit_id', unitId)
        .select();

    if(marketError){
        console.log('Error deleting market data item:', marketError);
        throw new Error(`Error deleting market data item: ${marketError?.message}`);
    }
	
    // Return updated unit data
	const unitDataNew = await getUnitDataDB(addressId);
	return unitDataNew;
}