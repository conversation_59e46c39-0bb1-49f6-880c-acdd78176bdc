'use server'
import { createClient } from "@/utils/supabase/server";

export const getPropertyMarketDataDB = async (address_id: string) => {
    const supabase = await createClient();

    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: marketData, error: marketError} = await supabase.from('prop_market_data').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).select();

    if(propError || marketError){
        console.log('Error fetching property market data:', propError || marketError);
        throw new Error(`Error fetching property market data: ${propError?.message || marketError?.message}`);
    }

    return marketData;
}

export async function updateUnit(unit: { [key: string]: any }, unitId: string, indexData: number, unitNumber: string, marketId: string){
	const supabase = await createClient();
   
    const {data: unitData, error: unitError} = await supabase.from('prop_market_data').select('*').eq('id', marketId).eq('unit_id', unitId).single();
    unitData.data[indexData] = unit

    
    const {data: marketData, error: marketError} = await supabase.from('prop_market_data').update({
        unit: unitNumber,
        data: unitData.data
    }).eq('unit_id', unitId).select();

    if(marketError){
        console.log('Error updating market data:', marketError);
        throw new Error(`Error updating market data: ${marketError?.message}`);
    }
	
	return marketData;
}


export async function removeUnit(id: string){
	const supabase = await createClient();
	const {data: marketData, error: marketError} = await supabase.from('prop_market_data').delete().eq('id', id);

	if(marketError){
		console.log('Error removing market data:', marketError);
		throw new Error(`Error removing market data: ${marketError?.message}`);
	}
}

export async function addUnit(addressId: string){
	const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId).single();
    const {data: unitNewData, error: unitNewError} = await supabase.from('prop_market_data').insert({
        prop_id: propData?.id,
        unit: 'N/A',
        beds: 0,
        baths: 0,
        sqft: 0,
        data: {
            bathrooms: 0,
            bedrooms: 0,
            squareFeet: 0,
            unitNumber: 0,
            price: 0,
            date: new Date().toISOString().split('T')[0],
            images: [],
            history: [],
            lastEvent: 'Off Market',
        }
    })

    const {data: marketData, error: marketError} = await supabase.from('prop_market_data').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).select();


    if(propError || unitNewError || marketError){
        console.log('Error adding market data:', propError || unitNewError || marketError);
        throw new Error(`Error adding market data: ${propError?.message || unitNewError?.message || marketError?.message}`);
    }

    return marketData;
}

export async function addUnitMarketData(addressId: string, unitId: string, unitNumber: string){
	const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId).single();
    
    const {data: unitNewData, error: unitNewError} = await supabase.from('prop_market_data').insert({
        prop_id: propData?.id,
        unit_id: unitId,
        unit: unitNumber || 'N/A',
        beds: 0,
        baths: 0,
        sqft: 0,
        data: [{
            bathrooms: 0,
            bedrooms: 0,
            squareFeet: 0,
            unitNumber: unitNumber || 'N/A',
            price: 0,
            date: new Date().toISOString().split('T')[0],
            images: [],
            history: [],
            lastEvent: 'Off Market',
            listing_type: 'Off Market',
            list_price: 0,
            last_sold_price: 0
        }]
    });

    if (propError || unitNewError) {
        console.log('Error adding unit market data:', propError || unitNewError);
        throw new Error(`Error adding unit market data: ${propError?.message || unitNewError?.message}`);
    }

    // Get all market data for this property to return
    const {data: allMarketData, error: marketError} = await supabase
        .from('prop_market_data')
        .select('*')
        .eq('prop_id', propData?.id)
        .order('created_at', { ascending: false })
        .order('id', { ascending: false });

    if (marketError) {
        console.log('Error fetching market data:', marketError);
        throw new Error(`Error fetching market data: ${marketError?.message}`);
    }

    return allMarketData;
}
