'use server'
import httpAgent from "@/helpers/httpAgent/httpAgent"
import apiEndpointsServer from "@/constants/apiEndpointsServer"
import { ChatCreateType } from "@/types/ChatCreateType"
import { ChatHistoryType } from "@/types/ChatHistoryType"
import { ChatActiveType } from "@/types/ChatActiveType"
import { ChatListType } from "@/types/ChatListType"

export async function createChat(portfolio_id: string): Promise<ChatCreateType>{
    return await httpAgent('POST', `${apiEndpointsServer.chat.create}?portfolio_id=${portfolio_id}`)
}

export async function getChatHistory(chat_id: string): Promise<ChatHistoryType>{
    return await httpAgent('GET', `${apiEndpointsServer.chat.histtory}/${chat_id}`)
}

export async function getChatActive(portfolio_id: string): Promise<ChatActiveType>{
    return await httpAgent('GET', `${apiEndpointsServer.chat.active}?portfolio_id=${portfolio_id}`)
}

export async function getChatList(portfolio_id: string): Promise<ChatListType> {
    return await httpAgent('GET', `${apiEndpointsServer.chat.list}?portfolio_id=${portfolio_id}`)
}

export async function deleteChat(chat_id: string) {
    return  await httpAgent('DELETE', `${apiEndpointsServer.chat.delete}/${chat_id}`)
}

export async function deleteChatMessages(chat_id: string) {
    return  await httpAgent('DELETE', `${apiEndpointsServer.chat.deleteMessages}/${chat_id}`)
}