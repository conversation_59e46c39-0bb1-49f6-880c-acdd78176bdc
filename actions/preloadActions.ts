'use server'

import { fetchFredCharts } from './fredActions';
import { fetchRealEstateNewsWithSummaries } from './newsActions';
import { createClient } from 'redis';

// All US states
const ALL_STATES = [
  'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
  'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
  'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
  'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
  'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

// Redis client setup
let redisClient: any = null;
let redisAvailable = false;

async function getRedisClient() {
  if (!redisClient && process.env.REDIS_URL) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL
      });
      
      redisClient.on('error', (err: any) => {
        console.error('Redis Client Error', err);
        redisAvailable = false;
      });
      
      await redisClient.connect();
      redisAvailable = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      redisAvailable = false;
      redisClient = null;
    }
  }
  return redisClient;
}

/**
 * Preload all FRED economic data and cache for 1 day
 */
export async function preloadFredData(): Promise<void> {
  try {
    console.log('Starting FRED data preload...');
    
    // Preload all FRED chart pages (12 charts total, 3 per page = 4 pages)
    const totalCharts = 12;
    const chartsPerPage = 3;
    const totalPages = Math.ceil(totalCharts / chartsPerPage);
    
    const preloadPromises = [];
    for (let page = 1; page <= totalPages; page++) {
      preloadPromises.push(fetchFredCharts(page, chartsPerPage));
    }
    
    await Promise.all(preloadPromises);
    console.log(`FRED data preloaded for ${totalPages} pages`);
  } catch (error) {
    console.error('Error preloading FRED data:', error);
  }
}

/**
 * Preload news data for all states and cache for 1 hour
 */
export async function preloadNewsData(): Promise<void> {
  try {
    console.log('Starting news data preload for all states...');
    
    // Process states in batches to avoid overwhelming the API
    const batchSize = 10;
    const batches = [];
    
    for (let i = 0; i < ALL_STATES.length; i += batchSize) {
      batches.push(ALL_STATES.slice(i, i + batchSize));
    }
    
    for (const batch of batches) {
      try {
        await fetchRealEstateNewsWithSummaries(batch, 1, {});
        console.log(`News data preloaded for states: ${batch.join(', ')}`);
        
        // Small delay between batches to be respectful to the API
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Error preloading news for batch ${batch.join(', ')}:`, error);
      }
    }
    
    console.log('News data preload completed for all states');
  } catch (error) {
    console.error('Error preloading news data:', error);
  }
}

/**
 * Preload all market data (FRED + News) for optimal performance
 */
export async function preloadAllMarketData(): Promise<void> {
  try {
    console.log('Starting comprehensive market data preload...');
    
    // Run FRED and news preloading in parallel
    await Promise.all([
      preloadFredData(),
      preloadNewsData()
    ]);
    
    console.log('All market data preload completed successfully');
  } catch (error) {
    console.error('Error in comprehensive market data preload:', error);
  }
}

/**
 * Check cache status for monitoring - graceful fallback without Redis
 */
export async function getCacheStatus(): Promise<{
  fredCacheKeys: string[];
  newsCacheKeys: string[];
  aiSummaryCacheKeys: string[];
  marketScoreCacheKeys: string[];
}> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable for cache status check');
    }
    
    if (redis && redisAvailable) {
      const [fredKeys, newsKeys, aiKeys, marketKeys] = await Promise.all([
        redis.keys('global_fred_charts:*'),
        redis.keys('state_news:*'),
        redis.keys('ai_summary:*'),
        redis.keys('state_market_score:*')
      ]);
      
      return {
        fredCacheKeys: fredKeys || [],
        newsCacheKeys: newsKeys || [],
        aiSummaryCacheKeys: aiKeys || [],
        marketScoreCacheKeys: marketKeys || []
      };
    }
    
    return {
      fredCacheKeys: [],
      newsCacheKeys: [],
      aiSummaryCacheKeys: [],
      marketScoreCacheKeys: []
    };
  } catch (error) {
    console.error('Error checking cache status:', error);
    return {
      fredCacheKeys: [],
      newsCacheKeys: [],
      aiSummaryCacheKeys: [],
      marketScoreCacheKeys: []
    };
  }
}

/**
 * Clear all cached data (for maintenance) - graceful fallback without Redis
 */
export async function clearAllCache(): Promise<void> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable for cache clearing');
      return;
    }
    
    if (redis && redisAvailable) {
      const allKeys = await redis.keys('*');
      if (allKeys.length > 0) {
        await redis.del(allKeys);
        console.log(`Cleared ${allKeys.length} cache keys`);
      } else {
        console.log('No cache keys to clear');
      }
    } else {
      console.log('Redis not available, no cache to clear');
    }
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
} 