'use server'
import { createClient } from "@/utils/supabase/server";

export async function getPropertyDemographicsDB(address_id: string){
    const supabase = await createClient();
    const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', address_id).single();
    const {data: demographicsData, error: demographicsError} = await supabase.from('prop_demographics').select('*').eq('prop_id', propData?.id).order('created_at', { ascending: false }).order('id', { ascending: false }).single();
    
    if(propError || demographicsError){
        console.log('Error fetching property demographics:', propError || demographicsError);
        throw new Error(`Error fetching property demographics: ${propError?.message || demographicsError?.message}`);
    }

    return {
        timestamp: propData?.updated_at,
        demographics: demographicsData,
    }
}