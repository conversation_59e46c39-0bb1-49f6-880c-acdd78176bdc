'use server'

import { createClient } from 'redis';
import OpenAI from 'openai';
import * as cheerio from 'cheerio';
import { fetchPexelsImage } from '@/utils/pexelsService';

const SERPER_API_KEY = process.env.SERPER_API_KEY;

export interface NewsArticle {
  title: string;
  link: string;
  snippet: string;
  date?: string;
  source?: string;
  imageUrl?: string;
  aiSummary?: string;
  sentimentScore?: number; // 0-100, higher = more positive/important
}

export interface StateNewsResponse {
  state: string;
  stateFlag: string;
  articles: NewsArticle[];
  hasMore: boolean;
  nextPage: number;
}

export interface NewsFilters {
  dateRange?: 'day' | 'week' | 'month' | 'year';
  propertyType?: 'residential' | 'commercial' | 'multifamily' | 'industrial';
}

// Redis client setup
let redisClient: any = null;
let redisAvailable = false;

async function getRedisClient() {
  if (!redisClient && process.env.REDIS_URL) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL
      });
      
      redisClient.on('error', (err: any) => {
        console.error('Redis Client Error', err);
        redisAvailable = false;
      });
      
      await redisClient.connect();
      redisAvailable = true;
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      redisAvailable = false;
      redisClient = null;
    }
  }
  return redisClient;
}

/**
 * Validate and sanitize image URLs for Next.js Image component
 * Falls back to a safe placeholder if the hostname is not configured
 */
function validateAndSanitizeImageUrl(imageUrl: string | null): string | null {
  if (!imageUrl) return null;
  
  try {
    const url = new URL(imageUrl);
    
    // List of known safe hostnames that should work with Next.js Image
    const safeHostnames = [
      'images.unsplash.com',
      'source.unsplash.com',
      'flagcdn.com',
      'gamblespot-images.s3.us-east-1.amazonaws.com',
      'cdn.pixabay.com',
      'images.pexels.com',
      'img.freepik.com',
      'static.vecteezy.com',
      'media.istockphoto.com',
      'thumbs.dreamstime.com',
      'st.depositphotos.com',
      'image.shutterstock.com',
      'cdn.britannica.com',
      'upload.wikimedia.org',
      'www.realtor.com',
      'photos.zillowstatic.com',
      'ap.rdcpix.com',
      // News and media sites
      'tapinto-production.s3.amazonaws.com',
      'bostonglobe-prod.cdn.arcpublishing.com',
      'www.providencejournal.com',
      'www.golocalprov.com',
      'www.wpri.com',
      'static01.nyt.com',
      'www.washingtonpost.com',
      'cdn.cnn.com',
      'www.reuters.com',
      'www.bloomberg.com',
      'fortune.com',
      'www.wsj.com',
      'www.cnbc.com',
      'www.marketwatch.com',
      // Real estate sites
      'www.zillow.com',
      'www.redfin.com',
      'www.realtor.com',
      'www.trulia.com',
      'www.apartments.com',
      'www.rent.com',
      // Generic CDNs and image hosts
      'i.imgur.com',
      'imgur.com',
      'cdn.jsdelivr.net',
      'cdnjs.cloudflare.com',
      'assets.website-files.com',
      'images.squarespace-cdn.com',
      'static.wixstatic.com',
      // AWS and cloud storage
      's3.amazonaws.com',
      'cloudfront.net',
      'googleusercontent.com',
      'ggpht.com',
      // Social media
      'pbs.twimg.com',
      'scontent.xx.fbcdn.net',
      'instagram.com',
      'linkedin.com'
    ];
    
    // Check if hostname is in our safe list or contains safe patterns
    const isSafeHostname = safeHostnames.some(hostname => 
      url.hostname.includes(hostname) || hostname.includes(url.hostname)
    );
    
    // Also allow any amazonaws.com or cloudfront.net domains
    const isAWSOrCloudfront = url.hostname.includes('amazonaws.com') || 
                             url.hostname.includes('cloudfront.net') ||
                             url.hostname.includes('googleusercontent.com');
    
    if (isSafeHostname || isAWSOrCloudfront) {
      return imageUrl;
    }
    
    // For unknown hostnames, return a safe fallback
    console.log(`Using fallback for hostname: ${url.hostname}`);
    return `https://source.unsplash.com/1200x800/?real-estate,house,property`;
    
  } catch (error) {
    console.error('Invalid image URL:', imageUrl, error);
    return `https://source.unsplash.com/1200x800/?real-estate,house,property`;
  }
}

/**
 * Enhanced image URL optimization for better quality
 */
function enhanceImageQuality(imageUrl: string): string {
  if (!imageUrl) return imageUrl;
  
  let enhancedUrl = imageUrl;
  
  // Google Images optimization
  if (enhancedUrl.includes('googleusercontent.com') || enhancedUrl.includes('ggpht.com')) {
    // Remove existing size parameters and set high quality
    enhancedUrl = enhancedUrl.replace(/=w\d+-h\d+/g, '=w1200-h800');
    enhancedUrl = enhancedUrl.replace(/=s\d+/g, '=s1200');
    enhancedUrl = enhancedUrl.replace(/&q=\d+/g, '&q=95');
    if (!enhancedUrl.includes('=w') && !enhancedUrl.includes('=s')) {
      enhancedUrl += enhancedUrl.includes('?') ? '&w=1200&h=800&q=95' : '?w=1200&h=800&q=95';
    }
  }
  
  // Bing Images optimization
  if (enhancedUrl.includes('bing.com') || enhancedUrl.includes('th?id=')) {
    enhancedUrl = enhancedUrl.replace(/&w=\d+&h=\d+/g, '&w=1200&h=800');
    enhancedUrl = enhancedUrl.replace(/&q=\d+/g, '&q=95');
    if (!enhancedUrl.includes('&w=')) {
      enhancedUrl += enhancedUrl.includes('?') ? '&w=1200&h=800&q=95' : '?w=1200&h=800&q=95';
    }
  }
  
  // Yahoo Images optimization
  if (enhancedUrl.includes('yimg.com')) {
    enhancedUrl = enhancedUrl.replace(/\/\d+x\d+\//g, '/1200x800/');
  }
  
  // Generic URL parameters for quality
  if (enhancedUrl.includes('width=') || enhancedUrl.includes('height=')) {
    enhancedUrl = enhancedUrl.replace(/width=\d+/g, 'width=1200');
    enhancedUrl = enhancedUrl.replace(/height=\d+/g, 'height=800');
  }
  
  // Add quality parameters if none exist
  if (!enhancedUrl.includes('quality') && !enhancedUrl.includes('q=')) {
    const separator = enhancedUrl.includes('?') ? '&' : '?';
    enhancedUrl += `${separator}quality=95`;
  }
  
  return enhancedUrl;
}

/**
 * Fetch news for a single state with proper error handling
 */
async function fetchNewsForState(
  state: string, 
  page: number = 1, 
  filters: NewsFilters = {}
): Promise<NewsArticle[]> {
  try {
    // Build search query based on filters
    let query: string;
    
    if (state === 'NATIONAL') {
      // For national news, search for general US real estate news
      query = `"real estate" OR "housing market" OR "property market" United States news`;
    } else {
      // For state-specific news
      query = `"real estate" OR "housing market" OR "property market" ${state} news`;
    }
    
    if (filters.propertyType) {
      const propertyTypeMap = {
        residential: 'residential housing homes',
        commercial: 'commercial office retail',
        multifamily: 'multifamily apartment complex',
        industrial: 'industrial warehouse logistics'
      };
      query += ` ${propertyTypeMap[filters.propertyType]}`;
    }

    // Validate API key before making request
    if (!SERPER_API_KEY) {
      console.error(`❌ Serper API key not configured for ${state}`);
      throw new Error('Serper API key not configured. Please set SERPER_API_KEY environment variable.');
    }

    // Date range mapping
    const dateRangeMap = {
      day: 'qdr:d',
      week: 'qdr:w', 
      month: 'qdr:m',
      year: 'qdr:y'
    };

    const response = await fetch('https://google.serper.dev/news', {
      method: 'POST',
      headers: {
        'X-API-KEY': SERPER_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: query,
        gl: 'us',
        hl: 'en',
        num: 20,
        page: page,
        tbs: filters.dateRange ? dateRangeMap[filters.dateRange] : 'qdr:m',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Serper API error for ${state}:`, response.status, errorText);
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Extract news articles from the news endpoint response
    const articles: NewsArticle[] = (data.news || []).slice(0, 15).map((item: any) => {
      return {
        title: item.title || '',
        link: item.link || '',
        snippet: item.snippet || '',
        date: item.date || '',
        source: item.source || '',
        imageUrl: null, // Will be populated by OG image fetching
        sentimentScore: 50, // Default neutral score, will be updated by AI
      };
    }).filter((article: NewsArticle) => 
      // Filter out articles that don't have essential content
      article.title && article.link && article.snippet
    );

    return articles;
  } catch (error) {
    console.error(`Error fetching news for ${state}:`, error);
    return [];
  }
}

/**
 * Get state flag image URL (using S3 images like in property search)
 */
function getStateFlag(state: string): string {
  if (state === 'NATIONAL') {
    return 'https://flagcdn.com/w320/us.png'; // US flag for national
  }
  return `https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${state.toLowerCase()}.webp`;
}

/**
 * Generate AI sentiment score and summary for articles
 */
export async function generateArticleSentimentAndSummary(article: NewsArticle, state: string): Promise<{ summary: string; sentimentScore: number }> {
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const stateContext = state === 'NATIONAL' 
      ? 'the national US real estate market' 
      : `the ${state} real estate market specifically`;

    const prompt = `Analyze this real estate news article for ${stateContext} and provide:
1. A 2-3 sentence summary focusing on key market insights relevant to ${stateContext}
2. A sentiment/importance score from 0-100 where:
   - 0-20: Very negative/unimportant news for ${stateContext}
   - 21-40: Somewhat negative news for ${stateContext}
   - 41-60: Neutral news for ${stateContext}
   - 61-80: Positive/important news for ${stateContext}
   - 81-100: Very positive/highly important news for ${stateContext}

Consider factors like market impact, investor relevance, and overall sentiment specifically for ${stateContext}.

Title: ${article.title}
Content: ${article.snippet}

IMPORTANT: You must respond with valid JSON containing "summary" and "sentimentScore" fields. Do not include any other text, explanations, or markdown formatting:
{
  "summary": "Your 2-3 sentence summary here",
  "sentimentScore": 75
}`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `You are a real estate market analyst specializing in ${stateContext}. You must respond with valid JSON containing "summary" and "sentimentScore" fields.`,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 200,
      temperature: 0.3,
      response_format: { type: "json_object" }
    });

    const responseContent = completion.choices[0]?.message?.content?.trim();
    if (!responseContent) {
      return { summary: 'Summary not available', sentimentScore: 50 };
    }

    // Log the raw response for debugging
    console.log(`AI Response for ${state}:`, responseContent);

    try {
      // Try to extract JSON if the response contains extra text
      let jsonString = responseContent;
      
      // Look for JSON object in the response
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonString = jsonMatch[0];
      }
      
      const parsed = JSON.parse(jsonString);
      
      // Validate the parsed response has required fields
      if (typeof parsed.summary !== 'string' || typeof parsed.sentimentScore !== 'number') {
        throw new Error('Invalid response structure');
      }
      
      return {
        summary: parsed.summary || 'Summary not available',
        sentimentScore: Math.max(0, Math.min(100, parsed.sentimentScore || 50))
      };
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.error('Raw response was:', responseContent);
      
      // Fallback: try to extract meaningful content from non-JSON response
      const fallbackSummary = responseContent.length > 10 
        ? responseContent.substring(0, 200).replace(/[{}"\[\]]/g, '').trim()
        : 'Summary not available';
      
      return { 
        summary: fallbackSummary, 
        sentimentScore: 50 
      };
    }
  } catch (error) {
    console.error('Error generating article sentiment and summary:', error);
    return { summary: 'Summary not available', sentimentScore: 50 };
  }
}

/**
 * Fetch real estate news with state-wide caching (1 hour for all users) - graceful fallback without Redis
 */
export async function fetchRealEstateNewsWithSummaries(
  states: string[], 
  page: number = 1, 
  filters: NewsFilters = {}
): Promise<StateNewsResponse[]> {
  try {
    let redis = null;
    
    // Try to get Redis client, but don't fail if unavailable
    try {
      redis = await getRedisClient();
    } catch (error) {
      console.log('Redis unavailable, proceeding without cache');
    }
    
    const results: StateNewsResponse[] = [];
    
    for (const state of states) {
      try {
        let stateResult = null;
        
        // Try to get cached news first (if Redis is available)
        if (redis && redisAvailable) {
          try {
            const newsCacheKey = `state_news:${state.toLowerCase()}:${page}:${JSON.stringify(filters)}`;
            const cachedNews = await redis.get(newsCacheKey);
            if (cachedNews) {
              console.log(`Returning cached news data for ${state}`);
              const parsedNews = JSON.parse(cachedNews);
              results.push(parsedNews);
              continue;
            }
          } catch (cacheError) {
            console.log(`Cache read failed for ${state}, fetching fresh data`);
          }
        }

        const stateFlag = getStateFlag(state);
        const articles = await fetchNewsForState(state, page, filters);
        
        // Fetch Open Graph images for all articles
        await fetchOpenGraphImages(articles, redis);
        
        // Process articles with AI sentiment and summaries (with caching if available)
        const articlesWithSentiment = await Promise.all(
          articles.map(async (article) => {
            let aiData = null;
            
            // Try to get cached AI analysis (if Redis is available)
            if (redis && redisAvailable) {
              try {
                const analysisKey = `ai_analysis:${state.toLowerCase()}:${Buffer.from(article.title + article.snippet).toString('base64')}`;
                const cachedAnalysis = await redis.get(analysisKey);
                if (cachedAnalysis) {
                  aiData = JSON.parse(cachedAnalysis);
                }
              } catch (cacheError) {
                console.log('AI analysis cache read failed, generating new');
              }
            }
            
            if (!aiData) {
              // Generate new AI analysis
              aiData = await generateArticleSentimentAndSummary(article, state);
              
              // Cache AI analysis if Redis is available - Cache for 1.1 days
              if (redis && redisAvailable) {
                try {
                  const analysisKey = `ai_analysis:${state.toLowerCase()}:${Buffer.from(article.title + article.snippet).toString('base64')}`;
                  await redis.setEx(analysisKey, 95040, JSON.stringify(aiData)); // 1.1 days cache
                } catch (cacheError) {
                  console.log('Failed to cache AI analysis, continuing without cache');
                }
              }
            }
            
            // Ensure all data is serializable and clean
            return {
              title: String(article.title || ''),
              link: String(article.link || ''),
              snippet: String(article.snippet || ''),
              date: article.date ? String(article.date) : undefined,
              source: article.source ? String(article.source) : undefined,
              imageUrl: article.imageUrl ? String(article.imageUrl) : undefined,
              aiSummary: String(aiData.summary || ''),
              sentimentScore: Number(aiData.sentimentScore || 50)
            };
          })
        );
        
        // Sort articles by sentiment score (highest first) for better top stories
        articlesWithSentiment.sort((a, b) => (b.sentimentScore || 50) - (a.sentimentScore || 50));
        
        stateResult = {
          state: String(state),
          stateFlag: String(stateFlag),
          articles: articlesWithSentiment,
          hasMore: Boolean(articlesWithSentiment.length >= 10),
          nextPage: Number(page + 1)
        };
        
        results.push(stateResult);
        
        // Cache news results if Redis is available - Cache for 1.1 days
        if (redis && redisAvailable) {
          try {
            const newsCacheKey = `state_news:${state.toLowerCase()}:${page}:${JSON.stringify(filters)}`;
            await redis.setEx(newsCacheKey, 95040, JSON.stringify(stateResult)); // 1.1 days cache
          } catch (cacheError) {
            console.log('Failed to cache news data, continuing without cache');
          }
        }
        
      } catch (error) {
        console.error(`Error fetching news for ${state}:`, error);
        // Continue with other states even if one fails - return empty but valid structure
        results.push({
          state: String(state),
          stateFlag: String(getStateFlag(state)),
          articles: [],
          hasMore: false,
          nextPage: Number(page + 1)
        });
      }
    }
    
    return results;
  } catch (error) {
    console.error('Error in fetchRealEstateNewsWithSummaries:', error);
    // Return empty results instead of throwing to prevent complete failure
    return states.map(state => ({
      state: String(state),
      stateFlag: String(getStateFlag(state)),
      articles: [],
      hasMore: false,
      nextPage: Number(page + 1)
    }));
  }
}

/**
 * Generate AI summary for a news article using OpenAI (legacy function for compatibility)
 */
export async function generateArticleSummary(article: NewsArticle): Promise<string> {
  const result = await generateArticleSentimentAndSummary(article, 'NATIONAL');
  return result.summary;
}

/**
 * Fetch a random real estate image from Pexels as fallback
 */
async function fetchFallbackRealEstateImage(redis: any = null): Promise<string | null> {
  try {
    // Try to get cached fallback images first (if Redis is available)
    if (redis && redisAvailable) {
      try {
        const fallbackCacheKey = 'fallback_real_estate_images';
        const cachedImages = await redis.get(fallbackCacheKey);
        if (cachedImages) {
          const imageArray = JSON.parse(cachedImages);
          if (imageArray.length > 0) {
            // Return a random image from cache
            const randomIndex = Math.floor(Math.random() * imageArray.length);
            return imageArray[randomIndex];
          }
        }
      } catch (cacheError) {
        console.log('Fallback image cache read failed, fetching fresh');
      }
    }

    // Use Pexels API to get a random real estate image
    const pexelsImage = await fetchPexelsImage();
    if (pexelsImage) {
      // Cache the Pexels image if Redis is available
      if (redis && redisAvailable) {
        try {
          const fallbackCacheKey = 'fallback_real_estate_images';
          await redis.setEx(fallbackCacheKey, 95040, JSON.stringify([pexelsImage])); // Cache for 1.1 days
        } catch (cacheError) {
          console.log('Failed to cache fallback image');
        }
      }
      return pexelsImage;
    }

    // Ultimate fallback - use a static placeholder
    return 'https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg';
  } catch (error) {
    console.error('Error fetching fallback real estate image:', error);
    // Ultimate fallback - use a static placeholder
    return 'https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg';
  }
}

/**
 * Fetch Open Graph image from a single article URL with caching
 */
async function fetchOpenGraphImage(url: string, redis: any = null): Promise<string | null> {
  try {
    // Try to get cached OG image first (if Redis is available)
    if (redis && redisAvailable) {
      try {
        const ogImageCacheKey = `og_image:${Buffer.from(url).toString('base64')}`;
        const cachedImage = await redis.get(ogImageCacheKey);
        if (cachedImage) {
          return cachedImage === 'null' ? null : cachedImage;
        }
      } catch (cacheError) {
        console.log('OG image cache read failed, fetching fresh');
      }
    }

    // Fetch the HTML content with timeout using AbortController
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const html = await response.text();
    const $ = cheerio.load(html);
    
    // Try multiple meta tags for images
    let ogImage: string | null = $('meta[property="og:image"]').attr('content') ||
                  $('meta[property="og:image:url"]').attr('content') ||
                  $('meta[name="twitter:image"]').attr('content') ||
                  $('meta[name="twitter:image:src"]').attr('content') ||
                  null;

    // If we found an image, enhance its quality
    if (ogImage) {
      // Handle relative URLs
      if (ogImage.startsWith('/')) {
        const urlObj = new URL(url);
        ogImage = `${urlObj.protocol}//${urlObj.host}${ogImage}`;
      } else if (!ogImage.startsWith('http')) {
        const urlObj = new URL(url);
        ogImage = `${urlObj.protocol}//${urlObj.host}/${ogImage}`;
      }
      
      ogImage = enhanceImageQuality(ogImage);
    } else {
      // No OG image found, use fallback real estate image
      const fallbackImage = await fetchFallbackRealEstateImage(redis);
      ogImage = fallbackImage;
    }

    // Cache the result (even if null) for 1.1 days
    if (redis && redisAvailable) {
      try {
        const ogImageCacheKey = `og_image:${Buffer.from(url).toString('base64')}`;
        await redis.setEx(ogImageCacheKey, 95040, ogImage || 'null'); // 1.1 days cache
      } catch (cacheError) {
        console.log('Failed to cache OG image, continuing without cache');
      }
    }

    // Validate and sanitize the image URL before returning
    return validateAndSanitizeImageUrl(ogImage);
  } catch (error) {
    console.error(`Error fetching OG image for ${url}:`, error);
    
    // On error, try to get a fallback real estate image
    try {
      const fallbackImage = await fetchFallbackRealEstateImage(redis);
      if (fallbackImage) {
        const validatedFallback = validateAndSanitizeImageUrl(fallbackImage);
        // Cache the validated fallback for this URL for 1.1 days
        if (redis && redisAvailable && validatedFallback) {
          try {
            const ogImageCacheKey = `og_image:${Buffer.from(url).toString('base64')}`;
            await redis.setEx(ogImageCacheKey, 95040, validatedFallback);
          } catch (cacheError) {
            console.log('Failed to cache fallback image');
          }
        }
        return validatedFallback;
      }
    } catch (fallbackError) {
      console.error('Fallback image fetch also failed:', fallbackError);
    }
    
    // Cache the failure for 1.1 days to avoid repeated attempts
    if (redis && redisAvailable) {
      try {
        const ogImageCacheKey = `og_image:${Buffer.from(url).toString('base64')}`;
        await redis.setEx(ogImageCacheKey, 95040, 'null'); // 1.1 days cache for failures
      } catch (cacheError) {
        console.log('Failed to cache OG image failure');
      }
    }
    
    return null;
  }
}

/**
 * Fetch Open Graph images for multiple articles in parallel
 */
async function fetchOpenGraphImages(articles: NewsArticle[], redis: any = null): Promise<void> {
  // Process articles in batches to avoid overwhelming servers
  const batchSize = 5;
  const batches = [];
  
  for (let i = 0; i < articles.length; i += batchSize) {
    batches.push(articles.slice(i, i + batchSize));
  }
  
  for (const batch of batches) {
    await Promise.all(
      batch.map(async (article) => {
        if (article.link) {
          const ogImage = await fetchOpenGraphImage(article.link, redis);
          if (ogImage) {
            article.imageUrl = ogImage;
          }
        }
      })
    );
    
    // Small delay between batches to be respectful
    if (batches.length > 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
} 