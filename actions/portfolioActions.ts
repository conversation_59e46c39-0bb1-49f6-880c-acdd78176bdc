'use server'
import { createClient } from "@/utils/supabase/server";
import apiEndpointsServer from "@/constants/apiEndpointsServer";
import httpAgent from "@/helpers/httpAgent/httpAgent";
import { getBaseUrl } from '@/lib/utils';

// Enhanced metadata helper function for portfolio financials
const buildPortfolioMetadata = (existingMetadata: any, fieldName: string, year: number, source: 'human' | 'ai' = 'human', value?: string) => {
    const metadata = existingMetadata || {};
    const attribution = metadata.attribution || {};
    
    // Ensure field exists in attribution
    if (!attribution[fieldName]) {
        attribution[fieldName] = {
            field_name: fieldName,
            year: year,
            history: []
        };
    }
    
    // Add new history entry with value
    const historyEntry: any = {
        source: source,
        datetime: new Date().toISOString(),
        year: year
    };
    
    // Include the value if provided
    if (value !== undefined && value !== null) {
        historyEntry.value = value;
    }
    
    attribution[fieldName].history.push(historyEntry);
    
    // Update the year for the field
    attribution[fieldName].year = year;
    
    return {
        ...metadata,
        attribution: attribution
    };
};

// Cache helper function for portfolio financials
const cachePortfolioFinancialData = async (portfolio_id: string, data: { [key: string]: string }, year: number) => {
    try {
        const cacheKey = `portfolio_financials_${portfolio_id}_year_${year}`;
        const body = {
            cache_key: cacheKey,
            data: data,
            ttl: 3600 // 1 hour TTL, adjust as needed
        };
        
        const baseUrl = getBaseUrl();
        const url = new URL('/api/redis-cache', baseUrl).toString();
        
        // Use local API endpoint for Redis caching
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        });
        
        if (!response.ok) {
            throw new Error(`Cache API responded with status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('Portfolio financial data cached:', result);
        
    } catch (error) {
        console.log('Error caching portfolio financial data:', error);
        // Don't throw error, just log it as caching is not critical for core functionality
    }
};

export async function getPortfolios(id: string) {
    const supabase = await createClient();

    // Return empty array if id is empty to prevent UUID type error
    if (!id) {
        return [];
    }

	const { data, error } = await supabase
		.from('portfolios')
		.select('*')
		.eq('workspace_id', id)
		.eq('is_deleted', false)
		.order('updated_at', { ascending: false });

	if (error) {
		console.log('Error fetching getPortfolios:', error);
		throw new Error(`Error fetching getPortfolios: ${error.message}`);
	}

	return data;
}

export async function deletePortfolio(id: string) {
    const supabase = await createClient();

	const { error } = await supabase
		.from('portfolios')
		.update({
			is_deleted: true
		})
		.eq('id', id);

	if (error) {
		console.log('Error fetching deletePortfolio:', error);
		throw new Error(`Error fetching deletePortfolio: ${error.message}`);
	}
}

export async function updatePortfolioName(id: string, name: string) {
    const supabase = await createClient();

	const { error } = await supabase
		.from('portfolios')
		.update({
			name: name.trim(),
			updated_at: new Date().toISOString()
		})
		.eq('id', id);

	if (error) {
		console.log('Error updating portfolio name:', error);
		throw new Error(`Error updating portfolio name: ${error.message}`);
	}
}

export async function getPortfolioData(portfolioId: string) {
	const supabase = await createClient();
    
	const { data: prop, error: propError } = await supabase.from('prop').select('*').eq('portfolio_id', portfolioId).eq('is_deleted', false);
    const addressId = prop ? prop?.map((item) => item.address_id) : null;
	const { data: propAddress, error: propAddressError } = await supabase.from('prop_addresses').select('*').in('id', addressId || []);

	if (propError || propAddressError) {
		console.log('Error fetching getPortfolioData:', propError || propAddressError);
		throw new Error(`Error fetching getPortfolioData: ${propError?.message || propAddressError?.message}`);
	}
	
    return propAddress
}

export async function getPortfolioPropertiesWithImages(portfolioId: string) {
	const supabase = await createClient();
    
	// Get properties with their address data and images
	const { data: properties, error: propertiesError } = await supabase
		.from('prop')
		.select(`
			id,
			address_id,
			main_img_url,
			img_urls,
			ai_summary,
			prop_addresses (
				id,
				address,
				city,
				state,
				zip,
				lat,
				lon
			)
		`)
		.eq('portfolio_id', portfolioId)
		.eq('is_deleted', false);

	if (propertiesError) {
		console.log('Error fetching portfolio properties with images:', propertiesError);
		throw new Error(`Error fetching portfolio properties with images: ${propertiesError.message}`);
	}

	// Flatten the structure to match the expected format
	const flattenedProperties = properties?.map(prop => ({
		// @ts-expect-error - Ignoring type error for prop_addresses.id
		id: prop.prop_addresses?.id ,
		prop_id: prop.id,
		// @ts-expect-error - Ignoring type error for prop_addresses.address
		address: prop.prop_addresses?.address,
		// @ts-expect-error - Ignoring type error for prop_addresses.city
		city: prop.prop_addresses?.city,
		// @ts-expect-error - Ignoring type error for prop_addresses.state
		state: prop.prop_addresses?.state,
		// @ts-expect-error - Ignoring type error for prop_addresses.zip
		zip: prop.prop_addresses?.zip,
		// @ts-expect-error - Ignoring type error for prop_addresses.lat
		lat: prop.prop_addresses?.lat,
		// @ts-expect-error - Ignoring type error for prop_addresses.lon
		lon: prop.prop_addresses?.lon,
		main_img_url: prop.main_img_url,
		img_urls: prop.img_urls,
		ai_summary: prop.ai_summary
	})) || [];
	
    return flattenedProperties;
}

export async function getPropDataById(addressId: string) {
	const supabase = await createClient();
	const { data: propAddress, error: propAddressError } = await supabase.from('prop_addresses').select('*').eq('id', addressId).single();
    const { data: prop, error: propError } = await supabase.from('prop').select('*').eq('address_id', propAddress?.id).single();
    
    if (propAddressError || propError) {
		console.log('Error fetching getPortfolioDataById:', propAddressError || propError);
		throw new Error(`Error fetching getPortfolioDataById: ${propAddressError?.message || propError?.message}`);
	}

	return prop
}

export async function getPropAddressDataById(addressId: string) {
	const supabase = await createClient();

	const { data: propAddress, error: propAddressError } = await supabase.from('prop_addresses').select('*').eq('id', addressId).single();

    if (propAddressError) {
		console.log('Error fetching getPortfolioDataById:', propAddressError);
		throw new Error(`Error fetching getPortfolioDataById: ${propAddressError?.message}`);
	}

	return propAddress
}

export async function deletePortfolioItem(portfolioId: string, addressId: string) {
	const supabase = await createClient();

	const { data: prop, error: propError } = await supabase.from('prop').update({is_deleted: true}).eq('address_id', addressId);

	if (propError) {
		console.log('Error fetching deletePortfolioItem:', propError);
		throw new Error(`Error fetching deletePortfolioItem: ${propError?.message}`);
	}

}

export async function getPortfolioFinancials(portfolioId: string) {
	const supabase = await createClient();
	const { data: portfolioFinancials, error: portfolioFinancialsError } = await supabase.from('portfolio_financials').select('*').eq('portfolio_id', portfolioId);
    
    
	return portfolioFinancials;
}

export async function getPortfolioFinancialsAggregated(portfolioId: string) {
	try {
		// First try to get existing portfolio financials from the database
		const existingFinancials = await getPortfolioFinancials(portfolioId);
		
		// Get the latest aggregated data from properties via API
		const baseUrl = getBaseUrl();
		const url = new URL('/api/portfolio-financials-aggregation', baseUrl).toString();
		
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				portfolio_id: portfolioId,
				action: 'update'
			})
		});

		if (!response.ok) {
			throw new Error(`API request failed: ${response.statusText}`);
		}

		// Return the updated portfolio financials
		return await getPortfolioFinancials(portfolioId);
	} catch (error) {
		console.error('Error getting aggregated portfolio financials:', error);
		// Fallback to regular portfolio financials
		return await getPortfolioFinancials(portfolioId);
	}
}

export async function refreshPortfolioFinancials(portfolioId: string, year?: number) {
	try {
		const baseUrl = getBaseUrl();
		const url = new URL('/api/portfolio-financials-aggregation', baseUrl).toString();
		
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				portfolio_id: portfolioId,
				year: year,
				action: 'update'
			})
		});

		if (!response.ok) {
			throw new Error(`API request failed: ${response.statusText}`);
		}

		const result = await response.json();
		return { success: result.success };
	} catch (error) {
		console.error('Error refreshing portfolio financials:', error);
		// @ts-expect-error - Ignoring error type checking
		throw new Error(`Error refreshing portfolio financials: ${error.message}`);
	}
}

export async function updatePortfolioFinancials(
    portfolioId: string, 
    data: { [key: string]: string }, 
    year: number, 
    source: 'human' | 'ai' = 'human'
) {
    const supabase = await createClient();
    
    // Get existing financial record to preserve metadata
    const {data: existingFinancials, error: fetchError} = await supabase
        .from('portfolio_financials')
        .select('metadata')
        .eq('portfolio_id', portfolioId)
        .eq('year', year)
        .single();

    if (fetchError) {
        console.log('Error fetching existing portfolio financials:', fetchError);
        throw new Error(`Error fetching existing portfolio financials: ${fetchError.message}`);
    }

    // Build updated metadata for each field being changed
    let updatedMetadata = existingFinancials?.metadata || {};
    
    // Filter and validate data before processing - prevent empty strings and invalid values
    const validData: { [key: string]: string } = {};
    Object.entries(data).forEach(([fieldName, fieldValue]) => {
        // Only include valid numeric values that can be converted to numbers
        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
            const numValue = parseFloat(fieldValue);
            if (!isNaN(numValue) && isFinite(numValue)) {
                validData[fieldName] = fieldValue;
                updatedMetadata = buildPortfolioMetadata(updatedMetadata, fieldName, year, source, fieldValue);
            } else {
                console.warn(`Skipping invalid portfolio value for ${fieldName}:`, fieldValue);
            }
        } else {
            console.warn(`Skipping empty/null portfolio value for ${fieldName}:`, fieldValue);
        }
    });

    // If no valid data to update, return success without database call
    if (Object.keys(validData).length === 0) {
        console.log('No valid portfolio data to update, skipping database operation');
        return { success: true, metadata: updatedMetadata };
    }

    // Prepare data with metadata using only valid data
    const updateData = {
        ...validData,
        metadata: updatedMetadata,
        updated_at: new Date().toISOString()
    };

    const { error: financialsError } = await supabase
        .from('portfolio_financials')
        .update(updateData)
        .eq('portfolio_id', portfolioId)
        .eq('year', year);

    if (financialsError) {
        console.log('Error updating portfolio financials:', financialsError);
        throw new Error(`Error updating portfolio financials: ${financialsError.message}`);
    }

    return { success: true, metadata: updatedMetadata };
}

export async function getPortfoliosWithProperties(workspaceId: string) {
    const supabase = await createClient();

    // Return empty array if id is empty to prevent UUID type error
    if (!workspaceId) {
        return [];
    }

    // Get portfolios
    const { data: portfolios, error: portfoliosError } = await supabase
        .from('portfolios')
        .select('*')
        .eq('workspace_id', workspaceId)
        .eq('is_deleted', false)
        .order('updated_at', { ascending: false });

    if (portfoliosError) {
        console.log('Error fetching portfolios:', portfoliosError);
        throw new Error(`Error fetching portfolios: ${portfoliosError.message}`);
    }

    if (!portfolios || portfolios.length === 0) {
        return [];
    }

    // Get the first property for each portfolio for the main image
    const portfoliosWithProperties = await Promise.all(
        portfolios.map(async (portfolio) => {
            try {
                // Get all properties for this portfolio (for count and first property)
                const { data: props } = await supabase
                    .from('prop')
                    .select('id, address_id, main_img_url')
                    .eq('portfolio_id', portfolio.id)
                    .eq('is_deleted', false);

                const propertyCount = props?.length || 0;

                if (props && props.length > 0) {
                    const firstProp = props[0];

                    // Get address details for the first property
                    const { data: address } = await supabase
                        .from('prop_addresses')
                        .select('*')
                        .eq('id', firstProp.address_id)
                        .single();

                    return {
                        ...portfolio,
                        mainProperty: address,
                        mainImage: firstProp.main_img_url,
                        propertyCount: propertyCount
                    };
                }

                return {
                    ...portfolio,
                    mainProperty: null,
                    mainImage: null,
                    propertyCount: propertyCount
                };
            } catch (error) {
                console.log('Error fetching property for portfolio:', portfolio.id, error);
                return {
                    ...portfolio,
                    mainProperty: null,
                    mainImage: null,
                    propertyCount: 0
                };
            }
        })
    );

    return portfoliosWithProperties;
}