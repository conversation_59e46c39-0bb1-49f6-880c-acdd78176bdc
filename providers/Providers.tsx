'use client'
import { AuthProvider } from "@/context/AuthContext";
import { ModalProvider } from "@/context/ModalContext";
import { StatusProvider } from "@/context/StatusContext";


const providers = [
	<PERSON>thProvider,
	ModalProvider,
	StatusProvider,
];

export default function Providers({children}: {children: React.ReactNode}) {
	return providers.reduceRight(
		(acc, Provider) => <Provider>{acc}</Provider>,
		children
	);
}