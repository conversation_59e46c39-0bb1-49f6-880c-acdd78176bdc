'use client'

import { Suspense, ReactNode } from 'react'
import SuspenseSearchParamsWrapper from "@/components/SuspenseSearchParamsWrapper";

interface SafeClientWrapperProps {
  children: ReactNode
  fallback?: ReactNode
}

/**
 * A wrapper component that provides a Suspense boundary for client components
 * that use hooks like useSearchParams, ensuring they work with Next.js 15.x requirements
 */
function SafeClientWrapperInner({ 
  children, 
  fallback = <div>Loading...</div> 
}: SafeClientWrapperProps) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
} 

// Wrap the inner component with SuspenseSearchParamsWrapper to handle useSearchParams safely
export default function SafeClientWrapper({ children, fallback }: SafeClientWrapperProps) {
  return (
    <SuspenseSearchParamsWrapper>
      <SafeClientWrapperInner fallback={fallback}>
        {children}
      </SafeClientWrapperInner>
    </SuspenseSearchParamsWrapper>
  );
}