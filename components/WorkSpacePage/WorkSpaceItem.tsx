import modalType from "@/constants/modalType";
import pathName from "@/constants/pathName";
import { useModal } from "@/context/ModalContext";
import { useAuth } from "@/context/AuthContext";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser, faBuilding, faHome, faPen, faTimes } from "@fortawesome/free-solid-svg-icons";
import Image from "next/image";

interface Props {
    workspace:{ [key: string]: any }
}

export default function WorkSpaceItem({ workspace }: Props) {
    const { showModal, updateModalData } = useModal()
    const { workspaceSubscriptionDetails } = useAuth()

    // Determine if this is a personal workspace
    const isPersonal = workspace?.is_personal || workspace?.name === 'First Workspace 🏡';
    
    // Get image from workspace logo or first profile's first property
    const getWorkspaceImage = () => {
        // First priority: workspace logo_url
        if (workspace?.logo_url) {
            return workspace.logo_url;
        }
        
        // Second priority: first profile's first property image
        const firstProfile = workspace?.profiles?.[0];
        const firstProperty = firstProfile?.properties?.[0];
        return firstProperty?.image || firstProperty?.images?.[0] || null;
    };

    // Format date function
    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        const options: Intl.DateTimeFormatOptions = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric'
        };
        return date.toLocaleDateString('en-US', options);
    };
    
    // Get workspace styling based on type
    const getWorkspaceStyles = () => {
        if (isPersonal) {
            return {
                cardClass: "bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 border border-emerald-200 hover:border-emerald-300",
                iconBg: "bg-gradient-to-r from-emerald-500 to-teal-600",
                icon: faHome,
                titleColor: "text-gray-900 group-hover:text-emerald-700",
                subtitleColor: "text-gray-600",
                arrowColor: "text-emerald-500 group-hover:text-emerald-600",
                dateColor: "text-emerald-600",
                buttonBg: "bg-emerald-500 hover:bg-emerald-600"
            };
        } else {
            return {
                cardClass: "bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 border border-gray-300 hover:border-gray-400",
                iconBg: "bg-gradient-to-r from-gray-700 to-black",
                icon: faBuilding,
                titleColor: "text-gray-900 group-hover:text-gray-800",
                subtitleColor: "text-gray-600",
                arrowColor: "text-gray-700 group-hover:text-black",
                dateColor: "text-gray-700",
                buttonBg: "bg-gray-700 hover:bg-black"
            };
        }
    };

    const styles = getWorkspaceStyles();
    const workspaceImage = getWorkspaceImage();

    const handleEditClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        updateModalData({...workspace})
        showModal(modalType.editWorkspace)
    };

    const handleDeleteClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        updateModalData({...workspace})
        showModal(modalType.deleteWorkspace)
    };

    return (
        <Link key={workspace.id} href={`${workspaceSubscriptionDetails?.find(w => w.id === workspace.id)?.has_active_subscription ? `${pathName.workspace}/${workspace.id}/search` : `${pathName.workspace}/${workspace.id}${pathName.pricing}`}`}>
            <div className={`${styles.cardClass} p-6 rounded-xl shadow-sm hover:shadow-lg text-left hover:scale-[1.02] h-[180px] flex flex-col justify-between group relative transition-all duration-300 overflow-hidden`}>
                {/* Glare effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none" style={{ transform: 'skewX(-20deg)', transition: 'transform 0.5s ease-in-out' }}></div>
                
                {/* Background decoration */}
                <div className={`absolute top-0 right-0 w-16 h-16 ${isPersonal ? 'bg-gradient-to-br from-emerald-100 to-teal-100' : 'bg-gradient-to-br from-gray-200 to-gray-300'} rounded-full -translate-y-8 translate-x-8 opacity-30 group-hover:opacity-50 transition-opacity`}></div>
                <div className={`absolute bottom-0 left-0 w-12 h-12 ${isPersonal ? 'bg-gradient-to-tr from-teal-100 to-emerald-100' : 'bg-gradient-to-tr from-gray-300 to-gray-400'} rounded-full translate-y-6 -translate-x-6 opacity-25 group-hover:opacity-40 transition-opacity`}></div>
                
                <div className="flex items-start gap-4 relative z-10">
                    {/* Image/Icon on the left */}
                    <div className="flex-shrink-0">
                        {workspaceImage ? (
                            <div className="w-12 h-12 rounded-xl overflow-hidden transition-shadow">
                                <Image 
                                    src={workspaceImage} 
                                    alt="Workspace" 
                                    width={48} 
                                    height={48} 
                                    className="w-full h-full object-cover"
                                />
                            </div>
                        ) : (
                            <div className={`w-12 h-12 ${styles.iconBg} rounded-xl flex items-center justify-center transition-shadow`}>
                                <FontAwesomeIcon icon={styles.icon} className="h-6 w-6 text-white" />
                            </div>
                        )}
                    </div>
                    
                    {/* Content on the right */}
                    <div className="flex-1 min-w-0">
                        <h3 className={`text-lg font-semibold ${styles.titleColor} transition-colors mb-1`}>
                            {workspace?.name === 'First Workspace 🏡' ? 'First Workspace 🏡' : workspace?.name}
                        </h3>
                        {isPersonal && (
                            <p className={`text-sm ${styles.subtitleColor} mb-2`}>
                                Your private workspace
                            </p>
                        )}
                        
                        {/* Date information */}
                        <div className="space-y-1">
                            <div className={`text-xs ${styles.dateColor} font-medium`}>
                                Created: {formatDate(workspace?.created_at)}
                            </div>
                            <div className={`text-xs ${styles.dateColor} font-medium`}>
                                Updated: {formatDate(workspace?.updated_at)}
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Bottom section with Open button and action buttons */}
                <div className="flex items-center justify-between relative z-10">
                    <div className={`${styles.arrowColor} flex items-center gap-1 text-xs font-medium transition-colors`}>
                        <span>{isPersonal ? 'Personal' : 'Open'}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                        </svg>
                    </div>
                    
                    {/* Action buttons - only show for non-personal workspaces */}
                    {!workspace?.is_personal && (
                        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <button
                                onClick={handleEditClick}
                                className="bg-gray-700 bg-opacity-50 text-white p-2 rounded-full shadow-md transition-all duration-100 hover:shadow-lg hover:scale-105 h-[33px] w-[33px]"
                                title="Edit workspace"
                            >
                                <FontAwesomeIcon icon={faPen} className="h-3 w-3" />
                            </button>
                            <button
                                onClick={handleDeleteClick}
                                className="bg-red-500 bg-opacity-50 text-white p-2 rounded-full shadow-md transition-all duration-200 hover:shadow-lg hover:scale-105 h-[33px] w-[33px]"
                                title="Delete workspace"
                            >
                                <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </Link>
    )
}