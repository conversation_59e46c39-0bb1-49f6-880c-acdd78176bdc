'use client'

import { useEffect, useState, useRef } from "react";

export default function Carousel() {

    const [activeQuote, setActiveQuote] = useState(0);
    const quoteCount = 10;
    const autoRotateRef = useRef<NodeJS.Timeout>(null);
    const [isPaused, setIsPaused] = useState(false);

    // Auto-rotate quotes
    useEffect(() => {
        if (!isPaused) {
        // Auto-rotate carousel
        autoRotateRef.current = setTimeout(() => {
            setActiveQuote((prev) => (prev + 1) % quoteCount);
        }, 5000); // Change to 5 seconds
        }
        
        return () => {
        if (autoRotateRef.current) {
            clearTimeout(autoRotateRef.current);
        }
        };
    }, [activeQuote, isPaused]);
    
    // Handle manual navigation
    const goToQuote = (index: number) => {
        setActiveQuote(index);
        
        // Reset timer when manually navigating
        if (autoRotateRef.current) {
        clearTimeout(autoRotateRef.current);
        }
    };
    
    // Pause/resume auto-rotation on hover
    const pauseCarousel = () => {
        setIsPaused(true);
        if (autoRotateRef.current) {
        clearTimeout(autoRotateRef.current);
        }
    };
    
    const resumeCarousel = () => {
        setIsPaused(false);
    };
    
    // Updated testimonials for national scope
    const testimonials = [
        {
        quote: "Relm Intelligence has transformed how I analyze real estate markets nationwide. The AI-powered insights have helped me identify investment opportunities across multiple cities.",
        title: "Real Estate Investor"
        },
        {
        quote: "The predictive analytics helped me identify emerging neighborhoods across different markets before prices increased. My portfolio's performance has improved significantly since I started using Relm.",
        title: "Property Developer"
        },
        {
        quote: "As a real estate agent working across multiple states, I need reliable data to advise my clients. Relm provides comprehensive insights that help me give informed recommendations with confidence.",
        title: "Real Estate Agent"
        },
        {
        quote: "The market cycle analysis feature helped me time my investments perfectly across different regional markets. I've been able to buy low and sell high consistently using Relm's predictive tools.",
        title: "Investment Advisor"
        },
        {
        quote: "I've tried many real estate analytics platforms, but Relm's AI capabilities are genuinely impressive. The risk assessment tool has saved me from several potentially bad investments across various markets.",
        title: "Portfolio Manager"
        },
        {
        quote: "As a first-time investor, the platform's intuitive interface and clear recommendations made navigating different real estate markets much more accessible.",
        title: "First-time Investor"
        },
        {
        quote: "Relm's neighborhood analysis tools helped me discover emerging areas with strong growth potential across multiple cities that weren't on my radar. My returns have exceeded expectations.",
        title: "Property Entrepreneur"
        },
        {
        quote: "The AI Market Sentiment feature has been incredibly accurate at predicting market shifts across different regions. I've been able to adjust my strategy ahead of market changes consistently.",
        title: "Market Analyst"
        },
        {
        quote: "I've been investing in real estate for over 20 years, and Relm provides insights I've never had access to before. It's like having an expert team working for you across every market.",
        title: "Veteran Real Estate Investor"
        },
        {
        quote: "Using Relm's platform has significantly reduced the time I spend researching properties nationwide. The automated insights and recommendations streamline my investment process.",
        title: "Real Estate Fund Manager"
        }
    ];

    return (
        <div className="relative overflow-hidden" 
            onMouseEnter={pauseCarousel}
            onMouseLeave={resumeCarousel}
            >
            <div className="relative">
                {testimonials.map((testimonial, index) => (
                <div 
                    key={index}
                    className={`transition-all duration-700 ease-in-out bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-sm border border-gray-100 ${
                    index === activeQuote ? 'opacity-100 relative transform translate-y-0 scale-100' : 'opacity-0 absolute top-0 left-0 w-full invisible transform translate-y-4 scale-95'
                    }`}
                    style={{ zIndex: index === activeQuote ? 10 : 0 }}
                >
                    <blockquote className="text-center text-xl font-medium text-gray-900 leading-relaxed">
                    <p>{testimonial.quote}</p>
                    </blockquote>
                    <div className="mt-6 text-center">
                    <p className="text-indigo-600 font-medium">{testimonial.title}</p>
                    </div>
                </div>
                ))}
            </div>
            
            {/* Navigation dots */}
            <div className="flex justify-center space-x-2 mt-8">
                {testimonials.map((_, index) => (
                <button
                    key={index}
                    onClick={() => goToQuote(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 cursor-pointer ${
                    index === activeQuote ? 'bg-gradient-to-r from-indigo-600 to-violet-600 w-4' : 'bg-gray-300 hover:bg-indigo-300'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                    aria-current={index === activeQuote ? 'true' : 'false'}
                />
                ))}
            </div>
        </div>
    );
}