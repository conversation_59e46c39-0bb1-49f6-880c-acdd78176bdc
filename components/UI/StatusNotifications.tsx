'use client';

import React from 'react';
import { useStatus, StatusItem } from '@/context/StatusContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
    faSpinner, 
    faCheckCircle, 
    faExclamationTriangle, 
    faInfoCircle,
    faTimes
} from '@fortawesome/free-solid-svg-icons';

interface StatusNotificationItemProps {
    item: StatusItem;
    onClose: (id: string) => void;
}

function StatusNotificationItem({ item, onClose }: StatusNotificationItemProps) {
    const getIcon = () => {
        switch (item.type) {
            case 'loading':
                return <FontAwesomeIcon icon={faSpinner} className="animate-spin h-4 w-4" />;
            case 'success':
                return <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4" />;
            case 'error':
                return <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4" />;
            case 'info':
            default:
                return <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4" />;
        }
    };

    const getColors = () => {
        switch (item.type) {
            case 'loading':
                return 'bg-blue-50 border-blue-200 text-blue-800';
            case 'success':
                return 'bg-green-50 border-green-200 text-green-800';
            case 'error':
                return 'bg-red-50 border-red-200 text-red-800';
            case 'info':
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };

    const getIconColors = () => {
        switch (item.type) {
            case 'loading':
                return 'text-blue-500';
            case 'success':
                return 'text-green-500';
            case 'error':
                return 'text-red-500';
            case 'info':
            default:
                return 'text-gray-500';
        }
    };

    return (
        <div className={`
            relative rounded-lg border shadow-lg p-4 mb-3 min-w-80 max-w-96
            transform transition-all duration-300 ease-in-out
            animate-in slide-in-from-right-5
            ${getColors()}
        `}>
            <div className="flex items-start">
                <div className={`flex-shrink-0 ${getIconColors()}`}>
                    {getIcon()}
                </div>
                <div className="ml-3 w-0 flex-1">
                    <p className="text-sm font-medium">
                        {item.title}
                    </p>
                    <p className="mt-1 text-sm opacity-90">
                        {item.message}
                    </p>
                    {item.progress !== undefined && (
                        <div className="mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                        item.type === 'loading' ? 'bg-blue-500' :
                                        item.type === 'success' ? 'bg-green-500' :
                                        item.type === 'error' ? 'bg-red-500' :
                                        'bg-gray-500'
                                    }`}
                                    style={{ width: `${Math.max(0, Math.min(100, item.progress))}%` }}
                                />
                            </div>
                            <p className="text-xs mt-1 opacity-75">
                                {Math.round(item.progress)}% complete
                            </p>
                        </div>
                    )}
                </div>
                <div className="ml-4 flex-shrink-0">
                    <button
                        className={`
                            inline-flex rounded-md p-1.5 hover:opacity-75 transition-opacity
                            focus:outline-none focus:ring-2 focus:ring-offset-2
                            ${item.type === 'loading' ? 'focus:ring-blue-500' :
                              item.type === 'success' ? 'focus:ring-green-500' :
                              item.type === 'error' ? 'focus:ring-red-500' :
                              'focus:ring-gray-500'}
                        `}
                        onClick={() => onClose(item.id)}
                    >
                        <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
                    </button>
                </div>
            </div>
        </div>
    );
}

export default function StatusNotifications() {
    const { statusItems, removeStatus } = useStatus();

    if (statusItems.length === 0) {
        return null;
    }

    return (
        <div className="fixed bottom-4 right-4 z-[9999] space-y-2">
            {statusItems.map((item) => (
                <StatusNotificationItem
                    key={item.id}
                    item={item}
                    onClose={removeStatus}
                />
            ))}
        </div>
    );
} 