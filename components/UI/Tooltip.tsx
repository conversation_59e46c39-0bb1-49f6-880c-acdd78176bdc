'use client'
import { useState, useRef, useEffect } from 'react';
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface TooltipProps {
    title: string;
    definition: string;
    calculation: string;
    aiAssistance?: string;
    className?: string;
}

export default function Tooltip({ title, definition, calculation, aiAssistance, className = '' }: TooltipProps) {
    const [isVisible, setIsVisible] = useState(false);
    const [position, setPosition] = useState({ top: 0, left: 0 });
    const tooltipRef = useRef<HTMLDivElement>(null);
    const triggerRef = useRef<HTMLButtonElement>(null);

    const updatePosition = () => {
        if (triggerRef.current && tooltipRef.current) {
            const triggerRect = triggerRef.current.getBoundingClientRect();
            const tooltipRect = tooltipRef.current.getBoundingClientRect();
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            let top = triggerRect.bottom + 8;
            let left = triggerRect.left;

            // Adjust horizontal position if tooltip would overflow
            if (left + tooltipRect.width > viewport.width - 16) {
                left = viewport.width - tooltipRect.width - 16;
            }
            if (left < 16) {
                left = 16;
            }

            // Adjust vertical position if tooltip would overflow
            if (top + tooltipRect.height > viewport.height - 16) {
                top = triggerRect.top - tooltipRect.height - 8;
            }

            setPosition({ top, left });
        }
    };

    useEffect(() => {
        if (isVisible) {
            updatePosition();
            window.addEventListener('scroll', updatePosition);
            window.addEventListener('resize', updatePosition);
        }

        return () => {
            window.removeEventListener('scroll', updatePosition);
            window.removeEventListener('resize', updatePosition);
        };
    }, [isVisible]);

    const handleMouseEnter = () => {
        setIsVisible(true);
    };

    const handleMouseLeave = () => {
        setIsVisible(false);
    };

    return (
        <>
            <button
                ref={triggerRef}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                className={`inline-flex items-center justify-center w-4 h-4 ml-1 text-gray-400 hover:text-gray-600 transition-colors ${className}`}
                type="button"
            >
                <FontAwesomeIcon icon={faInfoCircle} className="w-3 h-3" />
            </button>

            {isVisible && (
                <div
                    ref={tooltipRef}
                    style={{
                        position: 'fixed',
                        top: `${position.top}px`,
                        left: `${position.left}px`,
                        zIndex: 10000
                    }}
                    className="bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className="space-y-3">
                        <h4 className="font-semibold text-gray-900 text-sm border-b border-gray-200 pb-2">
                            {title}
                        </h4>
                        
                        <div>
                            <p className="text-xs font-medium text-gray-700 mb-1">Definition:</p>
                            <p className="text-xs text-gray-600 leading-relaxed">{definition}</p>
                        </div>

                        <div>
                            <p className="text-xs font-medium text-gray-700 mb-1">Calculation:</p>
                            <p className="text-xs text-gray-600 leading-relaxed font-mono bg-gray-50 px-2 py-1 rounded">
                                {calculation}
                            </p>
                        </div>

                        {aiAssistance && (
                            <div>
                                <p className="text-xs font-medium text-indigo-700 mb-1">🤖 AI Assistance:</p>
                                <p className="text-xs text-indigo-600 leading-relaxed bg-indigo-50 px-2 py-1 rounded">
                                    {aiAssistance}
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </>
    );
} 