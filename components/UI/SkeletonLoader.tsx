import React from 'react';

interface SkeletonLoaderProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  className = '',
  variant = 'rectangular',
  width = '100%',
  height = '1rem',
  lines = 1
}) => {
  const baseClasses = 'animate-pulse bg-gray-200 rounded';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'circular':
        return 'rounded-full';
      case 'text':
        return 'rounded h-4';
      case 'rectangular':
      default:
        return 'rounded';
    }
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`${baseClasses} ${getVariantClasses()}`}
            style={{
              width: index === lines - 1 ? '75%' : '100%',
              height: typeof height === 'number' ? `${height}px` : height,
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={`${baseClasses} ${getVariantClasses()} ${className}`}
      style={style}
    />
  );
};

// Market Analysis Skeleton Component
export const MarketAnalysisSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-full">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <SkeletonLoader variant="circular" width={24} height={24} />
        <SkeletonLoader variant="text" width="60%" height={20} />
      </div>

      {/* Score Circle */}
      <div className="flex justify-center mb-8">
        <SkeletonLoader variant="circular" width={200} height={200} />
      </div>

      {/* Metrics Row */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <SkeletonLoader variant="text" width="80%" height={16} className="mb-2" />
          <SkeletonLoader variant="text" width="60%" height={24} />
        </div>
        <div className="text-center">
          <SkeletonLoader variant="text" width="80%" height={16} className="mb-2" />
          <SkeletonLoader variant="text" width="60%" height={24} />
        </div>
        <div className="text-center">
          <SkeletonLoader variant="text" width="80%" height={16} className="mb-2" />
          <SkeletonLoader variant="text" width="60%" height={24} />
        </div>
      </div>

      {/* Analysis Section */}
      <div className="mb-6">
        <SkeletonLoader variant="text" width="40%" height={18} className="mb-3" />
        <SkeletonLoader variant="text" lines={4} height={16} />
      </div>

      {/* Key Factors */}
      <div>
        <SkeletonLoader variant="text" width="30%" height={18} className="mb-3" />
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-2">
              <SkeletonLoader variant="circular" width={8} height={8} />
              <SkeletonLoader variant="text" width="85%" height={14} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SkeletonLoader; 