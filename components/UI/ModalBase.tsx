import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faXmark } from '@fortawesome/free-solid-svg-icons';

interface ModalBaseProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    subtitle?: string;
    icon?: any;
    iconBgColor?: string;
    iconColor?: string;
    children: React.ReactNode;
    actions: React.ReactNode;
    maxWidth?: string;
    showCloseButton?: boolean;
}

/**
 * Base modal component with pinned bottom actions for mobile
 * Ensures action buttons are always visible without scrolling
 */
export default function ModalBase({
    isOpen,
    onClose,
    title,
    subtitle,
    icon,
    iconBgColor = 'bg-purple-100',
    iconColor = 'text-purple-600',
    children,
    actions,
    maxWidth = 'max-w-lg',
    showCloseButton = true
}: ModalBaseProps) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className={`bg-white rounded-xl shadow-2xl ${maxWidth} w-full flex flex-col max-h-[90vh] relative`}>
                {/* Close Button */}
                {showCloseButton && (
                    <button
                        onClick={onClose}
                        className="absolute top-4 right-4 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors z-10"
                    >
                        <FontAwesomeIcon icon={faXmark} className="text-gray-600 h-4 w-4" />
                    </button>
                )}

                {/* Header */}
                <div className="p-6 pb-4 flex-shrink-0">
                    <div className="flex items-center gap-3">
                        {icon && (
                            <div className={`w-12 h-12 ${iconBgColor} rounded-xl flex items-center justify-center`}>
                                <FontAwesomeIcon icon={icon} className={`${iconColor} h-6 w-6`} />
                            </div>
                        )}
                        <div>
                            <h3 className="text-xl font-semibold text-gray-800">{title}</h3>
                            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                        </div>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto px-6">
                    {children}
                </div>

                {/* Pinned Actions */}
                <div className="flex-shrink-0 bg-white border-t border-gray-100 p-6 rounded-b-xl">
                    {actions}
                </div>
            </div>
        </div>
    );
} 