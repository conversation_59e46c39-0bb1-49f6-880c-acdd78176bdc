import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTrashCan, faUserPlus } from '@fortawesome/free-solid-svg-icons';
import {useAuth} from "@/context/AuthContext";
import {useModal} from "@/context/ModalContext";
import modalType from "@/constants/modalType";

export default function ProfileWorkspace(){
	const {showModal, updateModalData} = useModal()
	const {workspaces} = useAuth()

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toISOString().slice(0, 19).replace('T', ' ');
	}

	return(
		<>
			<h2 className="text-xl font-semibold mb-6">Workspace Settings</h2>
			<div className='max-w-3xl mx-auto'>
				{/* <div className="mb-8">
					<h1 className="text-2xl font-bold text-gray-900">Workspace Settings</h1>
					<p className="mt-1 text-sm text-gray-500">Manage your workspaces and their settings</p>
				</div> */}
				<div className='bg-white shadow rounded-lg p-6'>
					<div className="flex justify-between items-center mb-6"><h2
						className="text-lg font-medium text-gray-900">My Workspaces</h2>
						<button
							onClick={() => showModal(modalType.createWorkspace)}
							className="cursor-pointer inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
							<FontAwesomeIcon icon={faPlus} className="mr-2 w-3 h-3"/>
							Create Workspace
						</button>
					</div>

					<div className={'flex flex-col gap-y-5'}>
						{
							workspaces && workspaces.length > 0 ?
								workspaces.map((workspace) => (
									<div key={workspace?.id} className="border border-gray-200 rounded-lg p-6 hover:border-indigo-300 transition-colors duration-200">
										<div className="flex items-center justify-between mb-4">
											<div>
												<h3 className="text-lg font-medium text-gray-900">{workspace?.name}</h3>
												<p className="text-sm text-gray-500">Created {workspace?.created_at ? formatDate(workspace.created_at) : ''}</p>
											</div>
											<div className="flex items-center gap-3">
												<button
													onClick={() => {
														showModal(modalType.membersWorkspace)
														updateModalData({...workspace})
													}}
													className={`cursor-pointer flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                        workspace?.is_personal ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      disabled={workspace?.is_personal}
													>
													<FontAwesomeIcon icon={faUserPlus} className="mr-2 w-3 h-3" />
													Manage Members
												</button>
												<button
													onClick={() => {
														showModal(modalType.deleteWorkspace)
														updateModalData({...workspace})
													}}
													className={`cursor-pointer flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${
                        workspace?.is_personal ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      disabled={workspace?.is_personal}
													>
													<FontAwesomeIcon icon={faTrashCan} className="mr-2 w-3 h-3" />
													Delete
												</button>
											</div>
										</div>
									</div>
								)) : null
						}
					</div>

				</div>
			</div>
		</>
	)
}