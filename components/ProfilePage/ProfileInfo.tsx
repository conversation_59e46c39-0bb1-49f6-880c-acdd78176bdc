import { getUserData, updateUserData } from "@/actions/userActions";
import { useAuth } from "@/context/AuthContext";
import { faBuilding, faMapMarker, faPhone, faCamera, faUser, faCreditCard } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Image from "next/image";
import { useEffect, useRef } from "react";
import { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { subscriptionPortal } from "@/actions/subscriptionActions";
import Spinner from '@/components/UI/Spinner';

export default function ProfileInfo() {
    const { user } = useAuth();
    const [isUpdated, setIsUpdated] = useState(false);
    const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);
    const [photoError, setPhotoError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [userData, setUserData] = useState<{ [key: string]: { value: string | null, valid: boolean, error: string | null } }>({
		photo: { value: null, valid: false, error: null },
		email: { value: null, valid: false, error: null },
		phone: { value: null, valid: false, error: null },
		organization: { value: null, valid: false, error: null },
		address: { value: null, valid: false, error: null }	
	})
    const [displayPhotoUrl, setDisplayPhotoUrl] = useState<string | null>(null)

	useEffect(() => {
		if(user?.user){
			getUserData(user?.user?.id).then((data) => {
				setUserData({
					photo: { value: data?.photo, valid: false, error: null },
					email: { value: data?.email, valid: false, error: null },
					phone: { value: data?.phone, valid: false, error: null },
					organization: { value: data?.organization, valid: false, error: null },
					address: { value: data?.address, valid: false, error: null }	
				})
			})
		}	
	}, [user])

    // Resolve avatar URL (handles storage path)
    useEffect(() => {
        const resolve = async () => {
            const raw = userData?.photo?.value || user?.user?.user_metadata?.avatar_url
            if(!raw){ setDisplayPhotoUrl(null); return }

            if(raw.startsWith('http')){ setDisplayPhotoUrl(raw); return }

            const supabase = createClient()
            // try signed url
            const { data: signed } = await supabase.storage.from('avatars').createSignedUrl(raw, 60*60)
            if(signed?.signedUrl){ setDisplayPhotoUrl(signed.signedUrl); return }
            const { data: pub } = await supabase.storage.from('avatars').getPublicUrl(raw)
            setDisplayPhotoUrl(pub?.publicUrl || null)
        }
        resolve()
    }, [userData?.photo?.value, user?.user?.user_metadata?.avatar_url])

    const formatPhoneNumber = (value: string): string => {
        if (!value) return '';
        
        // Remove all non-numeric characters
        const phoneNumber = value.replace(/\D/g, '');
        
        // Format according to pattern (*************
        if (phoneNumber.length <= 3) {
            return phoneNumber.length ? `(${phoneNumber}` : '';
        } else if (phoneNumber.length <= 6) {
            return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
        } else {
            return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setUserData({
            ...userData,
            organization: {
                ...userData.organization,
                error: null
            },
            address: {
                ...userData.address,
                error: null
            },
            phone: {
                ...userData.phone,
                error: null
            }
        })
        
        if(userData?.organization?.error || userData?.address?.error || userData?.phone?.error){
            return
        }
        let isValid = true
        if(!userData?.organization?.value){
            setUserData({
                ...userData,
                organization: {
                    value: '',
                    valid: false,
                    error: 'Organization name is required'
                }
            })
            isValid = false
        }

        if(!userData?.address?.value){
            setUserData({
                ...userData,
                address: {
                    value: '',
                    valid: false,
                    error: 'Address is required'
                }
            })
            isValid = false
        }

        if(!userData?.phone?.value){
            setUserData({
                ...userData,
                phone: {
                    value: '',
                    valid: false,
                    error: 'Phone number is required'
                }
            })
            isValid = false
        }

        if(isValid){
            setIsUpdated(true)
            setTimeout(() => {
                setIsUpdated(false)
            }, 3000)
            updateUserData(user?.user?.id || '', userData)
        }
    }
    
    const handlePhotoClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Only allow image files
        if (!file.type.startsWith('image/')) {
            setPhotoError('Please select an image file');
            return;
        }

        setIsUploadingPhoto(true);
        setPhotoError(null);

        try {
            const supabase = createClient();
            const userId = user?.user?.id;
            
            if (!userId) {
                throw new Error('User ID not found');
            }

            // Create a unique filename
            const timestamp = new Date().getTime();
            const fileExtension = file.name.split('.').pop();
            const fileName = `avatar-${userId}-${timestamp}.${fileExtension}`;
            const filePath = `avatars/${fileName}`;
            
            // Upload the file to Supabase Storage
            const { data: uploadData, error: uploadError } = await supabase.storage
                .from('avatars')
                .upload(filePath, file);
            
            if (uploadError) {
                throw new Error(`Error uploading file: ${uploadError.message}`);
            }
            
            // Get the public URL of the uploaded file
            const { data: { publicUrl } } = supabase.storage
                .from('avatars')
                .getPublicUrl(filePath);
                
            // Update the user data in Supabase
            await updateUserData(userId, {
                ...userData,
                photo: { value: publicUrl, valid: true, error: null }
            });
            
            // Update the local state
            setUserData({
                ...userData,
                photo: { value: publicUrl, valid: true, error: null }
            });
            
            // Show success message
            setIsUpdated(true);
            setTimeout(() => {
                setIsUpdated(false);
            }, 3000);
            
        } catch (error) {
            console.error('Error uploading photo:', error);
            setPhotoError(error instanceof Error ? error.message : 'An error occurred while uploading the photo');
        } finally {
            setIsUploadingPhoto(false);
        }
    };
    
	return (
		<>
            <h2 className="text-xl font-semibold mb-6">Profile</h2>
            <div className="max-w-3xl mx-auto">
                <div className="lg:flex justify-between items-center space-x-4 mb-6">
                    <div className="flex items-center space-x-4">
                        <div className="relative group">
                            {userData?.photo?.value || user?.user?.user_metadata?.avatar_url ? (
                                <div className="relative h-20 w-20 rounded-full overflow-hidden bg-indigo-100 border-2 border-white shadow-lg">
                                <Image 
                                    src={userData?.photo?.value || user?.user?.user_metadata?.avatar_url} 
                                    alt={user?.user?.email || ''} 
                                    width={80}
                                    height={80}
                                    className="h-full w-full object-cover"
                                />
                                </div>
                            ) : (
                                <div 
                                className="h-20 w-20 rounded-full bg-indigo-100 flex items-center justify-center border-2 border-white shadow-lg"
                                
                                >
                                <span className="text-2xl font-medium text-indigo-600">
                                    {user?.user?.email?.charAt(0).toUpperCase()}
                                </span>
                                </div>
                            )}
                            <button 
                                className="absolute w-8 h-8 flex items-center justify-center bottom-0 right-0 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors border border-gray-200"
                                onClick={handlePhotoClick}
                                disabled={isUploadingPhoto}
                            >
                                {isUploadingPhoto ? (
                                    <Spinner size="sm" color="indigo" />
                                ) : (
                                    <FontAwesomeIcon 
                                        icon={faCamera} 
                                        className="h-4 w-4 text-gray-600" 
                                    />
                                )}
                            </button>
                            <input
                                type="file"
                                ref={fileInputRef}
                                className="hidden"
                                accept="image/*"
                                onChange={handleFileChange}
                            />
                            </div>
                            <div>
                            <h2 className="text-2xl font-bold text-gray-900">
                                {user?.user?.user_metadata?.full_name || user?.user?.email?.split('@')[0] || 'User'}
                            </h2>
                            <p className="text-gray-600">{user?.user?.email}</p>
                            {photoError && (
                                <p className="text-red-500 text-sm mt-1">{photoError}</p>
                            )}
                        </div>
                    </div>
                    <div>
                        <button 
                        onClick={() => {
                            subscriptionPortal().then((data) => {
                                window.open(data?.portal_url, '_blank')
                            })
                        }}
                        className="mt-3 lg:mt-0 cursor-pointer h-12 px-6 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition-colors duration-200">
                            <FontAwesomeIcon icon={faCreditCard} className="mr-2 w-3 h-3" />
                            Manage Subscription
                        </button>
                    </div>
                </div>
                <form className="space-y-6" onSubmit={handleSubmit}>
                    <div>
                        <label htmlFor="organization" className="block text-sm font-medium text-gray-700 mb-2">
                            Organization Name
                        </label>
                        <div className="relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <FontAwesomeIcon icon={faBuilding} className="text-gray-400 text-lg" />
                            </div>
                            <input
                                type="text"
                                id="organization"
                                value={userData?.organization?.value || ''}
                                onChange={(e) => {
                                    setUserData({
                                        ...userData,
                                        organization: {
                                            value: e.target.value,
                                            valid: true,
                                            error: null
                                        }
                                    })
                                }}
                                className={`pl-11 block w-full h-12 rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-base ${userData?.organization?.error ? 'border border-red-500' : ''}`}
                                placeholder="Enter organization name"
                            />
                        </div>
                        {
                            userData?.organization?.error && (
                                <div className="rounded-md bg-red-50 p-4 mt-2">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-red-800">{userData?.organization?.error}</h3>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                    </div>

                    <div>
                        <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                            Address
                        </label>
                        <div className="relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <FontAwesomeIcon icon={faMapMarker} className="text-gray-400 text-lg" />
                            </div>
                            <input
                                type="text"
                                id="organization"
                                value={userData?.address?.value || ''}
                                onChange={(e) => {
                                    setUserData({
                                        ...userData,
                                        address: {
                                            value: e.target.value,
                                            valid: true,
                                            error: null
                                        }
                                    })
                                }}
                                className={`pl-11 block w-full h-12 rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-base ${userData?.address?.error ? 'border border-red-500' : ''}`}
                                placeholder="Enter address"
                            />
                        </div>
                        {
                            userData?.address?.error && (
                                <div className="rounded-md bg-red-50 p-4 mt-2">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-red-800">{userData?.address?.error}</h3>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                    </div>

                    <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <div className="relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <FontAwesomeIcon icon={faPhone} className="text-gray-400 text-lg" />    
                            </div>
                            <input
                                type="tel"
                                id="phone"
                                value={formatPhoneNumber(userData?.phone?.value || '')}
                                onChange={(e) => {
                                    setUserData({
                                        ...userData,
                                        phone: {
                                            value: e.target.value,
                                            valid: true,
                                            error: null
                                        }
                                    })
                                }}
                                className={`pl-11 block w-full h-12 rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-base ${userData?.phone?.error ? 'border border-red-500' : ''}`}
                                placeholder="(*************"
                            />
                        </div>
                        {
                            userData?.phone?.error && (
                                <div className="rounded-md bg-red-50 p-4 mt-2">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-red-800">{userData?.phone?.error}</h3>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                    </div>
                    
                    
                    {
                        isUpdated && (
                            <div className="rounded-md bg-green-50 p-4 mt-2">
                                <div className="flex">
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-green-800">Profile updated successfully</h3>
                                    </div>
                                </div>
                            </div>
                        )
                    }
                    <div className="flex justify-end pt-4">
                    <button
                        type="submit"
                        className="cursor-pointer h-12 px-6 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition-colors duration-200"
                    >
                        Save Changes
                    </button>
                    </div>
                </form>
            </div>
        </>
	)
}