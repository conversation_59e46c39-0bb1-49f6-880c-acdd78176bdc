import pathName from "@/constants/pathName";
import Link from "next/link";
import { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBuilding } from "@fortawesome/free-solid-svg-icons";
import Image from "next/image";
import { searchPropertyImages } from "@/actions/marketAnalysisActions/imageSearchActions";

interface Props {
    sharedItem: { [key: string]: any }
}

export default function SharedPortfolioItem({ sharedItem }: Props) {
    const [portfolioImage, setPortfolioImage] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    // Format date function
    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        const options: Intl.DateTimeFormatOptions = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric'
        };
        return date.toLocaleDateString('en-US', options);
    };

    // Get first portfolio image
    useEffect(() => {
        const fetchPortfolioImage = async () => {
            try {
                setLoading(true);
                
                // Get the first property from the portfolio
                const portfolio = sharedItem?.portfolioData;
                const firstProperty = portfolio?.mainProperty;
                
                if (firstProperty?.address && firstProperty?.city && firstProperty?.state) {
                    const response = await searchPropertyImages(
                        firstProperty.address,
                        firstProperty.city,
                        firstProperty.state,
                        firstProperty.zip || '',
                        'house',
                        1
                    );

                    if (response.images && response.images.length > 0) {
                        const image = response.images[0];
                        if (image.image && image.image.startsWith('http')) {
                            setPortfolioImage(image.image);
                        } else if (image.thumbnail && image.thumbnail.startsWith('http')) {
                            setPortfolioImage(image.thumbnail);
                        }
                    }
                }
            } catch (error) {
                console.log('Error fetching portfolio image:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchPortfolioImage();
    }, [sharedItem]);

    return (
        <Link href={`${pathName.portfolio}/${sharedItem.portfolioData.id}`}>
            <div className="bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 border border-gray-300 hover:border-gray-400 p-6 rounded-xl shadow-sm hover:shadow-lg text-left hover:scale-[1.02] h-[180px] flex flex-col justify-between group relative transition-all duration-300 overflow-hidden">
                {/* Glare effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300 pointer-events-none" style={{ transform: 'skewX(-20deg)', transition: 'transform 0.5s ease-in-out' }}></div>
                
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full -translate-y-8 translate-x-8 opacity-30 group-hover:opacity-50 transition-opacity"></div>
                <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-gray-300 to-gray-400 rounded-full translate-y-6 -translate-x-6 opacity-25 group-hover:opacity-40 transition-opacity"></div>
                
                <div className="flex items-start gap-4 relative z-10">
                    {/* Image/Icon on the left */}
                    <div className="flex-shrink-0">
                        {loading ? (
                            <div className="w-12 h-12 bg-gradient-to-r from-gray-700 to-black rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </div>
                        ) : portfolioImage ? (
                            <div className="w-12 h-12 rounded-xl overflow-hidden shadow-lg group-hover:shadow-xl transition-shadow">
                                <Image 
                                    src={portfolioImage} 
                                    alt="Portfolio" 
                                    width={48} 
                                    height={48} 
                                    className="w-full h-full object-cover"
                                />
                            </div>
                        ) : (
                            <div className="w-12 h-12 bg-gradient-to-r from-gray-700 to-black rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                                <FontAwesomeIcon icon={faBuilding} className="h-6 w-6 text-white" />
                            </div>
                        )}
                    </div>
                    
                    {/* Content on the right */}
                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-gray-800 transition-colors mb-1">
                            {sharedItem?.portfolioData?.name}
                        </h3>
                        
                        {/* Date and invitation information */}
                        <div className="space-y-1">
                            <div className="text-xs text-gray-700 font-medium">
                                Invited: {formatDate(sharedItem?.created_at)}
                            </div>
                            <div className="text-xs text-gray-700 font-medium">
                                By: {sharedItem?.senderData?.full_name}
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Bottom section with Open button */}
                <div className="flex items-center justify-between relative z-10">
                    <div className="text-gray-700 group-hover:text-black flex items-center gap-1 text-xs font-medium transition-colors">
                        <span>Open</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </Link>
    )
} 