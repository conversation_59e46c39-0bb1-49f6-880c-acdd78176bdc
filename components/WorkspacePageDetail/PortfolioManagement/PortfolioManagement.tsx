import { useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { usePortfolio } from "@/context/PortfolioContext";
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import modalTriggerType from "@/constants/modalTriggerType";
import { useParams } from "next/navigation";
import Spinner from "@/components/UI/Spinner";
import PortfolioItem from "./PortfolioItem";


export default function PortfolioManagement() {
  const { portfolios, isLoadingPortfolios, selectedPortfolio, updatePortfolioState } = usePortfolio();
  const { showModal, updateModalData, updateModalTrigger } = useModal();
  const params = useParams();

  const handleCreatePortfolio = () => {
    showModal(modalType.createPortfolio);
    updateModalData({
      workspaceId: params?.id as string,
    });
  };

  const handlePortfolioSelect = (portfolio: { [key: string]: any }) => {
    updatePortfolioState({ selectedPortfolio: portfolio });
  };

  // Debug log and fetch property counts if needed
  useEffect(() => {
    if (portfolios) {
      console.log('Portfolio data:', portfolios[0]);
      
      // If any portfolio doesn't have propertyCount, we need to trigger a refresh
      const needsRefresh = portfolios.some(p => p.propertyCount === undefined);
      if (needsRefresh) {
        console.log('Some portfolios missing propertyCount, triggering refresh...');
        updateModalTrigger(modalTriggerType.updatePortfolio);
      }
    }
  }, [portfolios, updateModalTrigger]);

  if (isLoadingPortfolios) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <Spinner size="lg" text="Loading portfolios..." />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 ml-4 mr-4 mt-10">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Portfolio Management</h2>
          <p className="text-sm text-gray-500 mt-1">
            Manage your portfolios and select one to view its contents below
          </p>
        </div>
        <button
          onClick={handleCreatePortfolio}
          className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-2 h-4 w-4" />
          Create Portfolio
        </button>
      </div>

      {/* Card Grid */}
      {portfolios && portfolios.length > 0 ? (
        <div className="p-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {portfolios.map((portfolio) => (
            <PortfolioItem
              key={portfolio.id}
              portfolio={portfolio}
              onUpdateTrigger={updateModalTrigger}
              edit={true}
              onPortfolioSelect={handlePortfolioSelect}
            />
          ))}
        </div>
      ) : (
        <div className="py-12 flex flex-col items-center">
          <p className="text-sm text-gray-500">No portfolios found</p>
          <button onClick={handleCreatePortfolio} className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium">
            Create your first portfolio
          </button>
        </div>
      )}
    </div>
  );
} 