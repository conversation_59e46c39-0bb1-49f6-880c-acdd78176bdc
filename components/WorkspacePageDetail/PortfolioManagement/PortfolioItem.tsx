import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faTrash, faPlus, faCheck, faTimes, faBuilding } from "@fortawesome/free-solid-svg-icons";
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import modalTriggerType from "@/constants/modalTriggerType";
import { updatePortfolioName } from "@/actions/portfolioActions";
import PropertyImage from "./PropertyImage";
import Image from "next/image";
import { usePortfolio } from "@/context/PortfolioContext";

interface PortfolioItemProps {
  portfolio: any;
  onUpdateTrigger: (triggerType: string) => void;
  edit: boolean;
  onPortfolioSelect: (portfolio: any) => void;
}

export default function PortfolioItem({ portfolio, onUpdateTrigger, edit, onPortfolioSelect }: PortfolioItemProps) {
  const { showModal, updateModalData } = useModal();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState<string>("");
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleEditStart = (portfolio: any) => {
    setEditingId(portfolio.id);
    setEditingName(portfolio.name);
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditingName("");
  };

  const handleEditSave = async (portfolioId: string) => {
    if (!editingName.trim()) return;
    
    setIsUpdating(portfolioId);
    try {
      await updatePortfolioName(portfolioId, editingName.trim());
      // Trigger portfolio context refresh
      onUpdateTrigger(modalTriggerType.updatePortfolio);
      setEditingId(null);
      setEditingName("");
    } catch (error) {
      console.error('Error updating portfolio name:', error);
    } finally {
      setIsUpdating(null);
    }
  };

  const handleDeletePortfolio = (portfolio: any) => {
    showModal(modalType.deletePortfolio);
    updateModalData({
      id: portfolio.id,
      name: portfolio.name
    });
  };

  return (
    <div
      key={portfolio.id}
      onClick={() => onPortfolioSelect(portfolio)}
      className="cursor-pointer bg-white rounded-lg shadow hover:shadow-lg transition-shadow overflow-hidden"
    >
      {/* Image */}
      <div className="h-48 bg-gray-100 relative group">
        {portfolio.mainImage ? (
          // High-res stored image
          <Image 
            src={portfolio.mainImage} 
            alt={portfolio.name} 
            fill
            className="object-cover"
            quality={95}
            priority={true}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          />
        ) : portfolio.mainProperty ? (
          <PropertyImage
            address={portfolio.mainProperty.address}
            city={portfolio.mainProperty.city}
            state={portfolio.mainProperty.state}
            zip={portfolio.mainProperty.zip}
            className="w-full h-full rounded-none"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <FontAwesomeIcon icon={faPlus} className="h-6 w-6 text-gray-400" />
          </div>
        )}
        
        {/* Property Count Badge */}
        {true && (
          <div className="absolute top-3 right-3">
            <div className="flex items-center gap-1 px-2 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-gray-700">
              <FontAwesomeIcon icon={faBuilding} className="h-3 w-3" />
              {portfolio.propertyCount ?? 0} {(portfolio.propertyCount ?? 0) === 1 ? 'Property' : 'Properties'}
            </div>
          </div>
        )}
      </div>

      {/* Info */}
      <div className="p-4">
        {editingId === portfolio.id ? (
          <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
            <input
              type="text"
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleEditSave(portfolio.id);
                if (e.key === 'Escape') handleEditCancel();
              }}
            />
            <button
              onClick={() => handleEditSave(portfolio.id)}
              disabled={isUpdating === portfolio.id}
              className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50"
            >
              {isUpdating === portfolio.id ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
              ) : (
                <FontAwesomeIcon icon={faCheck} className="h-4 w-4" />
              )}
            </button>
            <button onClick={handleEditCancel} className="p-1 text-gray-600 hover:text-gray-800">
              <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
            </button>
          </div>
        ) : (
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
            {portfolio.name}
          </h3>
        )}

        {/* Metadata Grid */}
        <div className="mt-3 grid grid-cols-2 gap-3 text-xs">
          <div>
            <span className="text-gray-500 font-medium">Created</span>
            <p className="text-gray-700 mt-0.5">{formatDate(portfolio.created_at)}</p>
          </div>
          <div>
            <span className="text-gray-500 font-medium">Updated</span>
            <p className="text-gray-700 mt-0.5">{formatDate(portfolio.updated_at || portfolio.created_at)}</p>
          </div>
        </div>
        
        {/* Status Bar */}
        <div className="mt-4 flex items-center">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-gray-500 font-medium">Active</span>
          </div>
        </div>

        {/* Action buttons */}
        <div className="mt-4 flex items-center justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
          {
            edit && (
              <>
                <button
                  onClick={() => handleEditStart(portfolio)}
                  className="p-1 text-gray-600 hover:text-indigo-600 transition-colors"
                  title="Rename portfolio"
                >
                  <FontAwesomeIcon icon={faEdit} className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeletePortfolio(portfolio)}
                  className="p-1 text-gray-600 hover:text-red-600 transition-colors"
                  title="Delete portfolio"
                >
                  <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                </button>
              </>
            )
          }
        </div>
      </div>
    </div>
  );
}