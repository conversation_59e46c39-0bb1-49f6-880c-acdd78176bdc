import { useState, useEffect } from "react";
import { searchPropertyImages } from "@/actions/marketAnalysisActions/imageSearchActions";
import Image from "next/image";

interface PropertyImageProps {
  address: string;
  city: string;
  state: string;
  zip?: string;
  className?: string;
}

const DEFAULT_IMAGE = "/default-home-list-img.svg";

export default function PropertyImage({ address, city, state, zip, className = "" }: PropertyImageProps) {
  const [imageUrl, setImageUrl] = useState<string>(DEFAULT_IMAGE);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        setLoading(true);
        
        // Use the existing image search function
        const response = await searchPropertyImages(
          address,
          city,
          state,
          zip || '',
          'house',
          1
        );

        if (response.images && response.images.length > 0) {
          const image = response.images[0];
          if (image.image && image.image.startsWith('http')) {
            setImageUrl(image.image);
          } else if (image.thumbnail && image.thumbnail.startsWith('http')) {
            setImageUrl(image.thumbnail);
          }
        }
      } catch (error) {
        console.log('Error fetching property image:', error);
        // Keep default image
      } finally {
        setLoading(false);
      }
    };

    if (address && city) {
      fetchImage();
    } else {
      setLoading(false);
    }
  }, [address, city, state, zip]);

  return (
    <div className={`relative overflow-hidden bg-gray-100 border border-gray-200 ${className}`}>
      {loading ? (
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        </div>
      ) : (
        <Image 
          src={imageUrl} 
          alt={`Property at ${address}`}
          width={50} 
          height={50} 
          className="w-full h-full object-cover object-center"
          onError={() => setImageUrl(DEFAULT_IMAGE)}
        />
      )}
    </div>
  );
} 