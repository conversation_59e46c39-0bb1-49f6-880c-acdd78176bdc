import React, { useEffect, useState } from 'react';

// Fetch inflation rate for years > 1
/*React.useEffect(() => {
    if (year > 1) {
        fetch('/api/finance/inflation')  // Use our backend endpoint instead of direct FRED call
            .then(res => res.json())
            .then(data => {
                setInflationRate(data.error ? 3.0 : data.rate);
            })
            .catch(() => setInflationRate(3.0));
    }
}, [year]); */