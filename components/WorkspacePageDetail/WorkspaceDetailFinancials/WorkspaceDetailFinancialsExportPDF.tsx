'use client';

import React from 'react';
import { Page, Text, View, Document, StyleSheet, PDFViewer, Image, Font } from '@react-pdf/renderer';
import { faFileExport } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

// Register fonts
Font.register({
    family: 'Inter',
    fonts: [
        { src: '/fonts/Inter-Regular.ttf' },
        { src: '/fonts/Inter-Bold.ttf', fontWeight: 700 }
    ]
});

const styles = StyleSheet.create({
    page: {
        padding: 40,
        fontFamily: 'Inter'
    },
    header: {
        flexDirection: 'row',
        marginBottom: 20,
        borderBottom: 1,
        borderBottomColor: '#E5E7EB',
        paddingBottom: 20
    },
    propertyImage: {
        width: 200,
        height: 150,
        marginRight: 20,
        borderRadius: 4
    },
    propertyInfo: {
        flex: 1
    },
    title: {
        fontSize: 24,
        fontWeight: 700,
        marginBottom: 8
    },
    address: {
        fontSize: 14,
        color: '#4B5563',
        marginBottom: 4
    },
    section: {
        marginBottom: 20
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 700,
        marginBottom: 12,
        padding: 8,
        backgroundColor: '#F3F4F6'
    },
    table: {
        width: '100%'
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
        paddingVertical: 8
    },
    tableHeader: {
        backgroundColor: '#F9FAFB',
        fontWeight: 700
    },
    tableCell: {
        flex: 1,
        fontSize: 12,
        paddingHorizontal: 8
    },
    tableCellAmount: {
        flex: 1,
        fontSize: 12,
        textAlign: 'right',
        paddingHorizontal: 8
    },
    negativeAmount: {
        color: '#EF4444'
    },
    totalRow: {
        fontWeight: 700,
        backgroundColor: '#F9FAFB'
    }
});

interface Props {
    propertyImage: string;
    propertyName: string;
    propertyAddress: string;
    financials: any[];
    sections: any[];
}

const FinancialsPDF = ({ propertyImage, propertyName, propertyAddress, financials, sections }: Props) => (
    <Document>
        <Page size="LETTER" style={styles.page}>
            {/* Header with Property Image and Info */}
            <View style={styles.header}>
                {/* Property image for PDF export - alt text not applicable to PDF format */}
                {/* eslint-disable-next-line jsx-a11y/alt-text */}
                <Image src={propertyImage} style={styles.propertyImage} />
                <View style={styles.propertyInfo}>
                    <Text style={styles.title}>{propertyName}</Text>
                    <Text style={styles.address}>{propertyAddress}</Text>
                </View>
            </View>

            {/* Financial Sections */}
            {sections.map((section, sectionIndex) => (
                <View key={sectionIndex} style={styles.section}>
                    <Text style={styles.sectionTitle}>{section.title}</Text>
                    <View style={styles.table}>
                        {/* Table Header */}
                        <View style={[styles.tableRow, styles.tableHeader]}>
                            <Text style={styles.tableCell}>Item</Text>
                            <Text style={styles.tableCellAmount}>Year 1</Text>
                            <Text style={styles.tableCellAmount}>Year 2</Text>
                            <Text style={styles.tableCellAmount}>Year 3</Text>
                            <Text style={styles.tableCellAmount}>Year 4</Text>
                            <Text style={styles.tableCellAmount}>Year 5</Text>
                        </View>

                        {/* Table Rows */}
                        {section.items.map((item: any, itemIndex: number) => {
                            const isTotal = item.isSectionTotal;
                            return (
                                <View key={itemIndex} style={[styles.tableRow, isTotal && styles.totalRow]}>
                                    <Text style={styles.tableCell}>{item.label}</Text>
                                    {[1, 2, 3, 4, 5].map(year => {
                                        const value = financials.find(f => f.year === year)?.[item.key] || '0';
                                        const isNegative = parseFloat(value) < 0;
                                        const formattedValue = Math.abs(parseFloat(value)).toLocaleString('en-US', {
                                            style: 'currency',
                                            currency: 'USD',
                                            minimumFractionDigits: 0,
                                            maximumFractionDigits: 0
                                        });
                                        return (
                                            <Text 
                                                key={year} 
                                                style={[
                                                    styles.tableCellAmount,
                                                    isNegative ? styles.negativeAmount : {}
                                                ]}
                                            >
                                                {isNegative ? `(${formattedValue})` : formattedValue}
                                            </Text>
                                        );
                                    })}
                                </View>
                            );
                        })}
                    </View>
                </View>
            ))}
        </Page>
    </Document>
);

export default function WorkspaceDetailFinancialsExportPDF({ 
    propertyImage, 
    propertyName, 
    propertyAddress, 
    financials, 
    sections 
}: Props) {
    const [isExporting, setIsExporting] = React.useState(false);

    const handleExport = async () => {
        setIsExporting(true);
        try {
            const { pdf } = await import('@react-pdf/renderer');
            const blob = await pdf(
                <FinancialsPDF 
                    propertyImage={propertyImage}
                    propertyName={propertyName}
                    propertyAddress={propertyAddress}
                    financials={financials}
                    sections={sections}
                />
            ).toBlob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${propertyName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_financials.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error generating PDF:', error);
        }
        setIsExporting(false);
    };

    return (
        <button
            onClick={handleExport}
            disabled={isExporting}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
            <FontAwesomeIcon 
                icon={faFileExport} 
                className={`h-4 w-4 mr-2 ${isExporting ? 'animate-pulse' : ''}`} 
            />
            {isExporting ? 'Exporting...' : 'Export PDF'}
        </button>
    );
} 