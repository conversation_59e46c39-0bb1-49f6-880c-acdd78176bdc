import { useState } from 'react';
import { faHexagonNodes, faPen } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { FinanceCalculationService } from '@/services/financeCalculationService';
import { FinanceSource } from '@/types/finance';
import { usePortfolio } from '@/context/PortfolioContext';
import { useSearchParams } from 'next/navigation';
import Spinner from '@/components/UI/Spinner';

interface Props {
    itemKey: string;
    year: number;
    value: string | number | null;
    onValueChange: (value: number) => void;
    isEditing: boolean;
    setIsEditing: (value: boolean) => void;
    overrideUserInput: boolean;
    onLoadingChange?: (isLoading: boolean) => void;
}

export default function WorkspaceDetailFinancialsItemAI({
    itemKey,
    year,
    value,
    onValueChange,
    isEditing,
    setIsEditing,
    overrideUserInput,
    onLoadingChange
}: Props) {
    const { selectedPortfolio } = usePortfolio();
    const searchParams = useSearchParams();
    const addressId = searchParams.get('addressId');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleCalculate = async () => {
        if (!selectedPortfolio?.id) return;

        setIsLoading(true);
        setError(null);
        
        if (onLoadingChange) {
            onLoadingChange(true);
        }

        try {
            const params = {
                prop_address_id: addressId || '',
                portfolio_id: selectedPortfolio.id,
                return_projections: true,
                override_user_input: overrideUserInput,
                year: year,
                params: {
                    default_source: 'user_provided',
                    skip_validation: true,
                    force_completion: true
                }
            };

            let result;
            switch (itemKey) {
                case 'other_income':
                    result = await FinanceCalculationService.calculateOtherIncome(params);
                    break;
                case 'vacancy_loss':
                    result = await FinanceCalculationService.calculateVacancyLoss(params);
                    break;
                case 'credit_loss':
                    result = await FinanceCalculationService.calculateCreditLoss(params);
                    break;
                case 'long_term_rental':
                    result = await FinanceCalculationService.calculateLongTermRental(params);
                    break;
                default:
                    throw new Error('Invalid calculation type');
            }

            if (result?.success && result?.data?.[year]) {
                const calculatedValue = result.data[year][itemKey];
                console.log(`AI calculated value for ${itemKey} in year ${year}:`, calculatedValue);
                if (calculatedValue !== null && calculatedValue !== undefined) {
                    onValueChange(calculatedValue);
                } else {
                    console.warn(`AI calculation returned null/undefined for ${itemKey} in year ${year}`);
                }
            } else {
                console.error('AI calculation failed or returned invalid data:', result);
                setError('Calculation failed - invalid response');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to calculate value');
        } finally {
            setIsLoading(false);
            
            if (onLoadingChange) {
                onLoadingChange(false);
            }
        }
    };

    // Show the AI calculation icon
    return (
        <div className="flex items-center space-x-2">
            <button
                onClick={handleCalculate}
                disabled={isLoading}
                className="relative group"
                title={`AI Calculate ${itemKey.replace('_', ' ')}`}
            >
                {isLoading ? (
                    <Spinner size="sm" />
                ) : (
                    <FontAwesomeIcon
                        icon={faHexagonNodes}
                        className="h-3 w-3 text-gray-400"
                    />
                )}
            </button>
            {/*value && !isLoading && (
                <button
                    onClick={() => setIsEditing(true)}
                    className="relative group"
                    title="Edit manually"
                >
                    <FontAwesomeIcon
                        icon={faPen}
                        className="h-3 w-3 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    />
                </button>
            )*/}
            {error && (
                <span className="text-xs text-red-500 max-w-24 truncate" title={error}>
                    Error
                </span>
            )}
        </div>
    );
} 