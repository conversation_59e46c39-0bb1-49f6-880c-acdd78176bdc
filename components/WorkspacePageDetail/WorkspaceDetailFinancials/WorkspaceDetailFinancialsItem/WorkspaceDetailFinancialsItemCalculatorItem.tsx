import { propertyFinancialsUpdateUnit } from "@/actions/propertyFinancials";    
import { updateUnit } from "@/actions/propertyMarketDataActions";
import { useDebounce } from "@/helpers/hooks/useDebounce";
import { faPen } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
export default function WorkspaceDetailFinancialsItemCalculatorItem({ unit, handleUnitChange }: { unit: { [key: string]: any }, handleUnitChange: (index: number, value: number) => void }) {
    const [isEditing, setIsEditing] = useState(false);

    const handleUpdateUnit = useDebounce(async (unit) => {
        await propertyFinancialsUpdateUnit(unit)
    }, 500)
    
    return (
        <tr className="hover:bg-gray-50">
            <td className="px-4 py-2 text-sm whitespace-nowrap text-left">{unit.unit}</td>
            <td className="px-4 py-2 text-sm text-center">
                {isEditing ? (
                    <input type="number" className="w-28 border border-indigo-300 rounded p-1 text-right text-sm" value={unit?.rent} onChange={(e) => {
                        handleUnitChange(unit.id, parseFloat(e.target.value))
                        handleUpdateUnit({...unit, rent: parseFloat(e.target.value)})
                    }} />
                ) : (
                    <>
                        {
                            unit?.rent ? (
                                <span className="px-4 py-2 text-sm text-black">${unit.rent}</span>
                            ) : (
                                <span className="text-gray-400">Not set</span>
                            )
                        }
                    </>
                )}
            </td>
            <td className="px-4 py-2 text-sm text-center">
                {
                    unit?.rent ? (
                        <span className="px-4 py-2 text-sm text-black">${(unit.rent * 12).toLocaleString('en-US')}</span>
                    ) : (
                        <span className="text-gray-400">Not set</span>
                    )
                }
            </td>
            <td className="px-2 py-2 text-right">
                <button type="button" onClick={() => setIsEditing(!isEditing)} className="cursor-pointer p-1 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-full"><FontAwesomeIcon icon={faPen} className="h-3.5 w-3.5" />
                </button>
            </td>
        </tr>
    )
}