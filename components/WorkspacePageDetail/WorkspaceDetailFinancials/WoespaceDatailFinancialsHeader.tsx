import { faSync, faCalculator, faHexagonNodes, faGear, faChevronRight, faArrowLeft, faWandMagicSparkles } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useSearchParams, useRouter } from "next/navigation";
import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { usePortfolio } from "@/context/PortfolioContext";
import { FinanceCalculationService } from "@/services/financeCalculationService";
import { updatePropertyFinancials } from "@/actions/propertyFinancials";
import { updatePortfolioFinancials } from "@/actions/portfolioActions";
import Spinner from "@/components/UI/Spinner";

export default function WorkspaceDetailFinancialsHeader()  {
    const searchParams = useSearchParams()
    const router = useRouter()
    const { selectedPortfolio } = usePortfolio()
    const addressId = searchParams.get('addressId')
    const isFocused = searchParams.get('focus') === 'true'

    const [showAIPreferences, setShowAIPreferences] = useState(false);
    const [overrideUserValues, setOverrideUserValues] = useState(false);
    const [isRecalculating, setIsRecalculating] = useState(false);
    const [calculationProgress, setCalculationProgress] = useState<{
        current: string;
        completed: string[];
        total: number;
        percentage: number;
    } | null>(null);
    const aiPreferencesRef = useRef<HTMLDivElement>(null);

    const handleBack = () => {
        const newSearchParams = new URLSearchParams(searchParams.toString())
        newSearchParams.delete('focus')
        router.push(`${window.location.pathname}?${newSearchParams.toString()}`)
    }

    const handleRecalculate = async () => {
        if (!selectedPortfolio || isRecalculating) return;
        
        setIsRecalculating(true);
        setCalculationProgress({
            current: 'Initializing calculations...',
            completed: [],
            total: 7,
            percentage: 0
        });

        try {
            const response = await fetch('/api/recalculate-financials', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    addressId: addressId || null,
                    portfolioId: selectedPortfolio.id,
                    triggerField: 'manual_recalculate'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                setCalculationProgress({
                    current: 'Calculations completed successfully!',
                    completed: ['Income Projections', 'Expense Projections', 'Net Operating Income', 'Debt Service', 'Cash Flow', 'Valuation Metrics', 'Portfolio Metrics'],
                    total: 7,
                    percentage: 100
                });

                // Update portfolio completion if provided
                if (data.portfolioCompletion !== undefined) {
                    // Trigger a custom event to update portfolio completion
                    window.dispatchEvent(new CustomEvent('portfolioCompletionUpdate', { 
                        detail: { completion: data.portfolioCompletion } 
                    }));
                }

                // Trigger a custom event to reload financial data without page refresh
                window.dispatchEvent(new CustomEvent('financialsRecalculated', { 
                    detail: { data: data.data, addressId, portfolioId: selectedPortfolio.id } 
                }));
            } else {
                throw new Error(data.error || 'Calculation failed');
            }
        } catch (error) {
            console.error('Error recalculating financials:', error);
            setCalculationProgress({
                current: 'Error occurred during calculations',
                completed: [],
                total: 7,
                percentage: 0
            });
        } finally {
            setTimeout(() => {
                setIsRecalculating(false);
                setCalculationProgress(null);
            }, 2000);
        }
    }

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (aiPreferencesRef.current && !aiPreferencesRef.current.contains(event.target as Node)) {
                setShowAIPreferences(false);
            }
        }

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [aiPreferencesRef]);

    return (
        <div className="flex flex-col space-y-2">
            {/* Clean header with recalculate button */}
            <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                    Financial projections and calculations
                </div>
                {/* TODO: fix recalculate all functionality */}
                {/* <button 
                    onClick={handleRecalculate}
                    disabled={isRecalculating}
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 rounded-md hover:from-purple-700 hover:to-indigo-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                    style={{
                        filter: isRecalculating ? 'drop-shadow(0 0 6px rgba(147,51,234,0.5))' : 'drop-shadow(0 0 3px rgba(147,51,234,0.3))'
                    }}
                >
                    {isRecalculating ? (
                        <>
                            <FontAwesomeIcon 
                                icon={faWandMagicSparkles} 
                                className="h-3 w-3 mr-1.5 animate-pulse"
                                style={{ filter: 'drop-shadow(0 0 3px rgba(147,51,234,0.6))' }}
                            />
                            <span>Calculating...</span>
                        </>
                    ) : (
                        <>
                            <FontAwesomeIcon 
                                icon={faWandMagicSparkles} 
                                className="h-3 w-3 mr-1.5 transition-transform duration-200 group-hover:scale-110"
                                style={{ filter: 'drop-shadow(0 0 3px rgba(147,51,234,0.5))' }}
                            />
                            Recalculate All
                        </>
                    )}
                </button> */}
            </div>

            {/* Progress Modal */}
            {calculationProgress && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <div className="text-center">
                            <FontAwesomeIcon 
                                icon={faWandMagicSparkles} 
                                className="h-12 w-12 text-purple-600 mx-auto mb-4 animate-pulse"
                                style={{ filter: 'drop-shadow(0 0 8px rgba(147,51,234,0.6))' }}
                            />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                Recalculating Financials
                            </h3>
                            <p className="text-sm text-gray-600 mb-4">
                                {calculationProgress.current}
                            </p>
                            
                            {/* Progress Bar */}
                            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                                <div 
                                    className="bg-gradient-to-r from-purple-600 to-indigo-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${calculationProgress.percentage}%` }}
                                ></div>
                            </div>
                            
                            <div className="text-sm text-gray-500">
                                {calculationProgress.percentage}% Complete
                            </div>
                            
                            {/* Completed Steps */}
                            {calculationProgress.completed.length > 0 && (
                                <div className="mt-4 text-left">
                                    <p className="text-xs text-gray-500 mb-2">Completed:</p>
                                    <div className="space-y-1">
                                        {calculationProgress.completed.map((step, index) => (
                                            <div key={index} className="flex items-center text-xs text-green-600">
                                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                                {step}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}