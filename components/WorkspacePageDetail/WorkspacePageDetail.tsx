'use client'
import {useEffect, useState} from "react";
import { usePortfolio } from "@/context/PortfolioContext";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import Spinner from "@/components/UI/Spinner";
import PortfolioManagement from "./PortfolioManagement/PortfolioManagement";
import PortfolioContent from "./PortfolioContent/PortfolioContent";
import WorkspaceDetailChat from "./WorkspaceDetailChat/WorkspaceDetailChat";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faChartBar, faDownload, faList, faShare, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { createChat, getChatList } from "@/actions/chatActions";
import { useModal } from "@/context/ModalContext";
import modalTriggerType from "@/constants/modalTriggerType";
import WorksoacePageDocumentsProcess from "./WorksoacePageDocumentsProcess";
import { useAuth } from "@/context/AuthContext";
import { useCheckPay } from "@/helpers/hooks/useCheckPay";
import { useDownloadAllDocuments } from "@/helpers/hooks/marketAnalysis/useDownloadAllDocuments";
import modalType from "@/constants/modalType";
import ModalPropgressDataRoom from "./DataRoom/ModalPropgressDataRoom";

// Create the inner component that uses useSearchParams
export default function WorkspacePageDetail()  {
    const { isLoadingPortfolios, selectedPortfolio, updatePortfolioState, chatActive, chatList, documentProcessStatus } = usePortfolio()
    const searchParams = useSearchParams()
    const router = useRouter()
    const addressId = searchParams.get('addressId')
    const [isChatExpanded, setIsChatExpanded] = useState(false)
    
    const {modalTrigger, modalData, updateModalTrigger, showModal, updateModalData} = useModal()
    const {workspaceSubscriptionDetails, user} = useAuth()
    const {id} = useParams()
    const {isPay} = useCheckPay()
    const portfolioIdParam = searchParams.get('portfolioId')
    
    const { isDownloading, downloadProgress, setDownloadProgress, handleDownloadAll } = useDownloadAllDocuments();  
    
    const handleBackToPortfolios = () => {
        if (addressId) {
            // Remove addressId from URL to go back to portfolio view
            const params = new URLSearchParams(searchParams.toString());
            params.delete('addressId');
            params.delete('marketAddress');
            router.push(`${window.location.pathname}?${params.toString()}`);
        } else if(portfolioIdParam) {
            const params = new URLSearchParams(searchParams.toString());
            params.delete('portfolioId');
            router.push(`${window.location.pathname}?${params.toString()}`);
            updatePortfolioState({ selectedPortfolio: null });
        } else {
            // We're in portfolio view, so go back to portfolio management
            updatePortfolioState({ selectedPortfolio: null });
            
        }
    };

    const handleChatActive = (chat_id: string) => {
        updatePortfolioState({chatActive: chatList?.chats?.find(item => item?.id === chat_id)})
    }

    const handleCreateChat = async () => {
        const newChat = await createChat(selectedPortfolio?.id)
        const chatList = await getChatList(selectedPortfolio?.id)
        updatePortfolioState({chatList, chatActive: chatList?.chats?.find(item => item?.id === newChat?.id)})
    }

    useEffect(() => {
        if(modalTrigger === modalTriggerType.deleteChat){
            getChatList(selectedPortfolio?.id).then((data) => {
                
                updatePortfolioState({chatList: data})

                if(modalData?.chatId === chatActive?.id && data?.chats?.length > 0){
                    updatePortfolioState({chatActive: data?.chats[0]})
                }
            })
            updateModalTrigger(null)
        }
    }, [modalTrigger])

    if (isLoadingPortfolios || !workspaceSubscriptionDetails) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading portfolio data..." />
            </div>
        );
    }

    // Check if we're in property view mode
    const isPropertyView = !!addressId;
    
    if(!isPay){
        return (
            <div className="min-h-screen flex flex-col">
                <div className="flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
                    <div className="text-center">
                                <div className="text-6xl mb-4">
                                    <FontAwesomeIcon icon={faList} className="text-indigo-600" />
                                </div>
                        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Portfolios</h2>
                        <p className="text-gray-600 mb-4">
                            Subscribe to states to access your Workspace Portfolios.
                        </p>
                        <a
                                    href={`/workspace/${id}/pricing`}
                            className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                        >
                            Manage Subscriptions
                        </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="h-full flex flex-col overflow-hidden">
            {/* Main content container */}
            <div className="flex-1 overflow-hidden">
                <div className="h-full bg-white overflow-hidden flex flex-col">
                    {/* Content section - scrollable */}
                    <div className="flex-1 overflow-hidden">
                        <div 
                            className="h-full transition-all duration-300" 
                            style={{ 
                                paddingRight: isChatExpanded ? '400px' : '0'
                            }}
                        >
                            <div className="h-full flex flex-col max-w-full">
                                {!selectedPortfolio && !portfolioIdParam ? (
                                    // Portfolio Management View
                                    <div className="flex-1 w-full">
                                        <PortfolioManagement  />
                                    </div>
                                ) : (
                                    // Portfolio Detail View
                                    <>
                                        {
                                            !addressId ? (
                                                <div className="flex items-center justify-between gap-x-4 mt-10 mx-10">
                                                    {/* Back Button */}
                                                    <div className="flex items-center">
                                                        <button
                                                            onClick={handleBackToPortfolios}
                                                            className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-indigo-600 transition-colors cursor-pointer"
                                                        >
                                                            <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
                                                            Back to Portfolios
                                                        </button>
                                                        {selectedPortfolio && (
                                                            <h1 className="ml-4 text-xl font-semibold text-gray-900">
                                                                {selectedPortfolio.name}
                                                            </h1>
                                                        )}
                                                    </div>
                                                    {/* Share and Download All buttons - only show when data-room tab is selected */}
                                                    {selectedPortfolio && (
                                                        <div className="flex gap-3">
                                                            <button 
                                                            className={`cursor-pointer text-sm px-4 py-2 rounded-md transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform font-medium min-w-[140px] justify-center ${
                                                                isDownloading 
                                                                ? 'bg-gray-400 cursor-not-allowed' 
                                                                : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white hover:scale-105'
                                                            }`}
                                                            onClick={handleDownloadAll}
                                                            disabled={isDownloading}
                                                            title={isDownloading ? downloadProgress.status : 'Download all documents as ZIP'}
                                                            >
                                                            {isDownloading ? (
                                                                <>
                                                                    <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                                                                    <span className="text-xs">
                                                                        {downloadProgress.total > 0 ? `${downloadProgress.current}/${downloadProgress.total}` : 'Preparing...'}
                                                                    </span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                <FontAwesomeIcon icon={faDownload} className="h-3 w-3" />
                                                                Download All
                                                                </>
                                                            )}
                                                            </button>
                                                            
                                                            <button 
                                                            className="cursor-pointer text-sm bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white px-4 py-2 rounded-md transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 font-medium" 
                                                            onClick={() => {
                                                                showModal(modalType.portfolioShare)
                                                                updateModalData({
                                                                    ownerId: user?.user?.id,
                                                                    portfolioId: selectedPortfolio?.id as string,
                                                                    senderId: user?.user?.id,
                                                                    portfolioName: selectedPortfolio?.name
                                                                })
                                                            }}
                                                            >
                                                            <FontAwesomeIcon icon={faShare} className="h-3 w-3" />
                                                            Share
                                                            </button>
                                                        </div>
                                                    )}
                                                </div>
                                            ) : null
                                        }
                                        
                                        {/* Portfolio Content */}
                                        <div className="flex-1 overflow-hidden min-h-0 m-4">
                                            <PortfolioContent isChatExpanded={isChatExpanded} isPropertyView={isPropertyView} />
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                <div 
                        className={`${isChatExpanded ? 'fixed right-8 top-[140px] bottom-8 w-[360px] bg-white border border-gray-200 rounded-[20px] shadow-xl z-50 overflow-hidden mb-5 drop-shadow-lg shadow-indigo-200/20' : 'absolute left-0 right-0 bottom-8 z-40'}`}
                    >
                        <WorkspaceDetailChat setIsChatExpanded={setIsChatExpanded} isChatExpanded={isChatExpanded} chatActive={chatActive} chatList={chatList} handleChatActive={handleChatActive} handleCreateChat={handleCreateChat} portfolioId={selectedPortfolio?.id} />
                    </div>
            </div>

            

            { documentProcessStatus && documentProcessStatus?.documents?.length > 0 && localStorage.getItem('task_token') && <WorksoacePageDocumentsProcess /> }
            {/* Download Progress Modal */}
            {(isDownloading || downloadProgress.status) && <ModalPropgressDataRoom isDownloading={isDownloading} downloadProgress={downloadProgress} setDownloadProgress={setDownloadProgress} />}
        </div>
    )
}
