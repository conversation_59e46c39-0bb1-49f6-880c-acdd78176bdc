import { faBuilding } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { usePortfolio } from "@/context/PortfolioContext"
import { useEffect, useState } from "react"

const tabs = [
	{ id: 'properties', name: 'Properties' },
	{ id: 'financials', name: 'Financials' },
	{ id: 'changelog', name: 'Change Log' },
	{ id: 'data-room', name: 'Data Room' },
] as const;

interface Props {
	selectedTabPortfolio: typeof tabs[number]['id']
	setSelectedTabPortfolio: React.Dispatch<React.SetStateAction<typeof tabs[number]['id']>>
}

export default function PortfolioHeader({ selectedTabPortfolio, setSelectedTabPortfolio }: Props) {
	const { selectedPortfolio } = usePortfolio()
	const [isCollapsed, setIsCollapsed] = useState(false)

	useEffect(() => {
		const handleScroll = () => {
			if (window.scrollY > 80) {
				setIsCollapsed(true)
			} else {
				setIsCollapsed(false)
			}
		}

		window.addEventListener('scroll', handleScroll)
		return () => {
			window.removeEventListener('scroll', handleScroll)
		}
	}, [])

	return (
		<div className={`pt-4 border-b border-gray-200 mb-6 sticky top-0 z-40 bg-white transition-all duration-300 ${isCollapsed ? 'shadow-md' : ''}`}>
			<div className={`flex flex-col transition-all duration-300 ${isCollapsed ? 'py-2' : 'mb-5'}`}>
				{/* Header with portfolio title */}
				<div className="flex justify-between items-center mb-3">
					<h1 className={`font-semibold transition-all duration-300 flex items-center ${isCollapsed ? 'text-base text-indigo-600' : 'text-xl text-gray-800'}`}>
						<FontAwesomeIcon icon={faBuilding} className={`${isCollapsed ? 'mr-2 h-4 w-4' : 'mr-3 h-5 w-5'} text-indigo-500`} />
						{selectedPortfolio?.name || 'Portfolio'}
					</h1>
				</div>
			</div>

			{/* Tabs navigation */}
			<div className="flex border-b border-gray-200">
				{tabs.filter((tab) => tab?.id !== 'financials' && tab?.id !== 'changelog').map((tab) => (
					<button
						onClick={() => setSelectedTabPortfolio(tab?.id)}
						key={tab?.id}
						className={`
							cursor-pointer relative px-6 py-3 text-sm font-medium transition-colors
							${tab?.id === selectedTabPortfolio 
								? 'text-indigo-600 border-b-2 border-indigo-500 -mb-[1px]' 
								: 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}
						`}
					>
						{tab?.name}
						{tab?.id === selectedTabPortfolio && (
							<span className="absolute bottom-0 left-0 w-full h-[2px] bg-indigo-500"></span>
						)}
					</button>
				))}
			</div>
		</div>
	)
}