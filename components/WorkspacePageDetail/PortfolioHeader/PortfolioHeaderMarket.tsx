import { faArrowLeft, faChevronRight, faFilePdf, faBuilding, faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import pathName from "@/constants/pathName"
import Link from "next/link"
import modalType from "@/constants/modalType"
import { useModal } from "@/context/ModalContext"
import { useParams, useSearchParams } from "next/navigation"
import { usePortfolio } from "@/context/PortfolioContext"
import { useEffect, useState } from "react"


const tabs = [
	{ id: 'info', name: 'Info' },
	{ id: 'financials', name: 'Financials' },
] as const;

interface Props {       
	selectedTabMarket: typeof tabs[number]['id']
	setSelectedTabMarket: React.Dispatch<React.SetStateAction<typeof tabs[number]['id']>>
	compactMode?: boolean
}

export default function PortfolioHeaderMarket({ selectedTabMarket, setSelectedTabMarket, compactMode = false }: Props) {
    const { showModal, updateModalData } = useModal()
    const params = useParams()
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const marketAddress = searchParams.get('marketAddress')
    const { selectedPortfolio } = usePortfolio()
    const [isCollapsed, setIsCollapsed] = useState(false)

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 80) {
                setIsCollapsed(true)
            } else {
                setIsCollapsed(false)
            }
        }

        window.addEventListener('scroll', handleScroll)
        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

	return (
		<div className={`${compactMode ? 'px-6 py-2' : 'border-b border-gray-200 mb-2 sticky top-0 z-40'} transition-all duration-300 ${isCollapsed ? 'shadow-md' : ''}`}>
            {/* Only show breadcrumb and document upload when not in compact mode */}
            {!compactMode && (
                <div className={`flex flex-col transition-all duration-300`}>
                    {/* Breadcrumb navigation */}
                    <div className="flex items-center justify-between mb-2 ml-4">
                        <div className="flex items-center">
                            <Link   
                                href={`${pathName.workspace}/${params?.id}`}
                                className="inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700 hover:underline transition-colors"
                            >
                                <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-3 w-3" />
                                <span className="flex items-center">
                                    <FontAwesomeIcon icon={faBuilding} className="mr-1.5 h-3.5 w-3.5 text-indigo-500" />
                                    {selectedPortfolio?.name || 'Portfolio'}
                                </span>
                            </Link>
                            <FontAwesomeIcon icon={faChevronRight} className="mx-2 h-2.5 w-2.5 text-gray-400" />
                            <span className="text-gray-700 font-medium flex items-center">
                                <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-1.5 h-3.5 w-3.5 text-indigo-400" />
                                {marketAddress}
                            </span>
                        </div>
                        
                        <button 
                            onClick={() => {
                                showModal(modalType.documentUpload)
                                updateModalData({
                                    propId: addressId,
                                    selectedPortfolioId: selectedPortfolio?.id,
                                    individual: true
                                })
                            }}
                            className="flex items-center px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors"
                        >
                            <FontAwesomeIcon icon={faFilePdf} className="mr-2 h-4 w-4" />
                            Document Upload
                        </button>
                    </div>
                </div>
            )}

            {/* Tabs navigation */}
            <div className={`flex ${compactMode ? '' : 'border-b border-gray-200'}`}>
                {tabs.map((tab) => (
                    <button
                        onClick={() => setSelectedTabMarket(tab?.id)}
                        key={tab?.id}
                        className={`
                            relative px-6 py-3 text-sm font-medium transition-colors
                            ${tab?.id === selectedTabMarket 
                                ? 'text-indigo-600 border-b-2 border-indigo-500 -mb-[1px]' 
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                        `}
                    >
                        {tab?.name}
                        {tab?.id === selectedTabMarket && (
                            <span className="absolute bottom-0 left-0 w-full h-[2px] bg-indigo-500"></span>
                        )}
                    </button>
                ))}
            </div>
        </div>
	)
}