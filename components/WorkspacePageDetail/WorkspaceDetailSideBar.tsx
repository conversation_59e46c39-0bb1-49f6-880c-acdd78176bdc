import Link from "next/link";
import { faTrash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { usePortfolio } from "@/context/PortfolioContext";
import modalType from "@/constants/modalType";
import { useModal } from "@/context/ModalContext";
import { useParams } from "next/navigation";

export default function WorkspaceDetailSideBar() {
	const { portfolios, selectedPortfolio, updatePortfolioState } = usePortfolio()
	const { showModal, updateModalData } = useModal()
	const params = useParams()
	return(
		<div className={'h-full bg-white shadow-lg rounded-lg transition-all duration-300 ease-in-out flex flex-col z-50 w-64'}>
			<div className="px-4 pt-4 pb-2 flex-shrink-0">
				<h1 className={`text-lg font-medium text-gray-900`}>
					Portfolios
				</h1>
				<button
					onClick={() => {
						showModal(modalType.createPortfolio)
						updateModalData({
							workspaceId: params?.id as string,
						})
					}}
					className="mt-1 cursor-pointer rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500  focus-visible:outline-indigo-600"
				>
					+ Create Portfolio
				</button>
				<div className="mt-2 border-b border-gray-200"></div>
			</div>

			<div className={'flex-1 overflow-y-auto min-h-0'}>
				
				<div className={'px-3 pb-4'}>
					{
						portfolios?.map((portfolio) => (
							<div
								key={portfolio?.id}
								onClick={() => updatePortfolioState({ selectedPortfolio: portfolio })}
								className={`cursor-pointer px-3 py-2 rounded-[7px] flex items-center justify-between ${selectedPortfolio?.id == portfolio?.id ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700'}  hover:bg-gray-50 group `}>
								<div>
									<span className="text-sm font-medium">{portfolio?.name}</span>
								</div>
								<div className={'hidden group-hover:block'}>
									<FontAwesomeIcon
										icon={faTrash}
										className="w-3 h-3 text-red-500 cursor-pointer"
										onClick={(e) => {
											e.stopPropagation()
											showModal(modalType.deletePortfolio)
											updateModalData({
												id: portfolio.id,
												name: portfolio.name
											})
										}}
									/>
								</div>
							</div>
						))
					}
				</div>
			</div>
		</div>
	)
}