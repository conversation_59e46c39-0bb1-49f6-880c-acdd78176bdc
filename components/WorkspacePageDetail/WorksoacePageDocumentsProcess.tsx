import { usePortfolio } from "@/context/PortfolioContext";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronUp, faChevronDown, faFileText, faXmark } from "@fortawesome/free-solid-svg-icons";

export default function WorksoacePageDocumentsProcess() {
    const { documentProcessStatus, updatePortfolioState } = usePortfolio();
    const [isExpanded, setIsExpanded] = useState(true);

    useEffect(() => {
        if(documentProcessStatus?.documents && documentProcessStatus?.documents?.length > 0 && documentProcessStatus?.documents?.find(doc => doc.status === 'processing')) {
            setIsExpanded(true)
        } else {
            setIsExpanded(false)
        }
    }, [documentProcessStatus])

    if (!documentProcessStatus?.documents || documentProcessStatus.documents.length === 0) {
        return null;
    }

    const processingCount = documentProcessStatus.documents.filter(doc => doc.status === 'processing').length;

    return (
        <div className="fixed bottom-15 left-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-w-sm w-[230px]">
            {/* Header - always visible */}
            <div 
                className="flex items-center justify-between p-3 bg-gray-50 rounded-t-lg cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={faFileText} className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">File Processing</h3>
                    {processingCount > 0 && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    )}
                </div>
                <div className="flex items-center gap-1">
                    <button className="p-1 hover:bg-gray-200 rounded">
                        {isExpanded ? (
                            <FontAwesomeIcon icon={faChevronDown} className="h-4 w-4 text-gray-600" />
                        ) : (
                            <FontAwesomeIcon icon={faChevronUp} className="h-4 w-4 text-gray-600" />
                        )}
                    </button>
                    {
                        documentProcessStatus?.documents?.find(doc => doc.status !== 'processing') && (
                            <button className="p-1 hover:bg-gray-200 rounded cursor-pointer" onClick={() => {
                                localStorage.removeItem('task_token')
                                updatePortfolioState({ documentProcessStatus: null })
                            }}>
                                <FontAwesomeIcon icon={faXmark} className="h-4 w-4 text-gray-600" />
                            </button>    
                        )
                    }   
                </div>
            </div>

            {/* Content - collapsible */}
            {isExpanded && (
                <div className="p-3 max-h-64 overflow-y-auto">
                    {/* File list */}
                    <div className="space-y-2">
                        {documentProcessStatus.documents.map((document) => (
                            <div key={document.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                        {document.name}
                                    </p>
                                </div>
                                <div className="ml-2 flex-shrink-0">
                                    {document.status === 'processing' && (
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse mr-1"></div>
                                            Processing
                                        </span>
                                    )}
                                    {document.status === 'complete' && (
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                            ✓ Done
                                        </span>
                                    )}
                                    {document.status === 'failed' && (
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                                            ✗ Error
                                        </span>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}