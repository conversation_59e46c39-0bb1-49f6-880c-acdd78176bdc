'use client'
import { useAuth } from "@/context/AuthContext"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faCopy, faCheck, faBuilding, faHome, faChartLine, faInfoCircle } from "@fortawesome/free-solid-svg-icons"
import { useState, useEffect } from "react"
import { usePathname, useParams } from "next/navigation"
import { PortfolioStatsService, PortfolioStats } from "@/services/portfolioStatsService"

/**
 * Component to display portfolio and property IDs with copy functionality
 */
export default function PortfolioIdInfo() {
  const { workspaceSubscriptionDetails } = useAuth()
  const [copied, setCopied] = useState(false)
  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats | null>(null)
  const [isLoadingStats, setIsLoadingStats] = useState(false)
  const pathname = usePathname()
  const params = useParams()
  
  // Hide portfolio ID on Market Brief, Property Search, and Pricing pages
  const shouldHidePortfolioId = pathname?.includes('/market-brief') || pathname?.includes('/search') || pathname?.includes('/pricing')
  
  // Get current workspace from the array
  const currentWorkspace = workspaceSubscriptionDetails?.find(w => w.id === params?.id)
  const portfolioId = currentWorkspace?.id

  // Load portfolio statistics
  useEffect(() => {
    if (portfolioId && !shouldHidePortfolioId) {
      setIsLoadingStats(true)
      PortfolioStatsService.calculatePortfolioStats(portfolioId)
        .then(stats => {
          setPortfolioStats(stats)
        })
        .catch(error => {
          console.error('Error loading portfolio stats:', error)
        })
        .finally(() => {
          setIsLoadingStats(false)
        })
    }
  }, [portfolioId, shouldHidePortfolioId])

  if (shouldHidePortfolioId) {
    return null
  }

  const copyToClipboard = async () => {
    if (portfolioId) {
      try {
        await navigator.clipboard.writeText(portfolioId)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy: ', err)
      }
    }
  }

  if (!portfolioId) {
    return null
  }

  return (
    <div className="bg-white px-4 py-3 border-b border-gray-100 mb-20">
      <div className="max-w-7xl mx-auto">
        {/* Portfolio ID Row */}
        <div className="flex items-center justify-center mb-3">
          <div className="flex items-center gap-x-2 text-sm text-gray-600">
            <span>Portfolio ID: {portfolioId}</span>
            <button
              onClick={copyToClipboard}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
              title="Copy portfolio ID"
            >
              <FontAwesomeIcon 
                icon={copied ? faCheck : faCopy} 
                className={`text-xs ${copied ? 'text-green-600' : 'text-gray-500'}`}
              />
            </button>
          </div>
        </div>

        {/* Loading State */}
        {isLoadingStats && (
          <div className="flex items-center justify-center">
            <div className="animate-pulse flex items-center gap-x-8 text-sm">
              <div className="flex items-center gap-x-2">
                <div className="w-6 h-6 bg-gray-200 rounded-lg"></div>
                <div className="w-8 h-4 bg-gray-200 rounded"></div>
                <div className="w-16 h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="flex items-center gap-x-2">
                <div className="w-6 h-6 bg-gray-200 rounded-lg"></div>
                <div className="w-8 h-4 bg-gray-200 rounded"></div>
                <div className="w-12 h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="flex items-center gap-x-2">
                <div className="w-6 h-6 bg-gray-200 rounded-lg"></div>
                <div className="w-12 h-4 bg-gray-200 rounded"></div>
                <div className="w-16 h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="w-24 h-2 bg-gray-200 rounded-full"></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}