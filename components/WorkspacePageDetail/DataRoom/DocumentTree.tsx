import React from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFolder, faFile, faChevronRight, faChevronDown, faDownload, faTrash, faFileLines, faCloudArrowUp } from "@fortawesome/free-solid-svg-icons";
import { deletePortfolioDocument } from "@/actions/dataRoomActions";
import { usePortfolio } from '@/context/PortfolioContext';
import { useModal } from '@/context/ModalContext';
import modalType from '@/constants/modalType';

interface DocumentNode {
  id: string;
  type: 'folder' | 'file';
  name: string;
  document_type?: string;
  children?: DocumentNode[];
  url?: string;
  created_at?: string;
  uploaded_at?: string;
  summary?: string;
  page_count?: number;
  preview_url?: string;
  property_id?: string;
  sub_type?: string;
}

interface DocumentTreeProps {
  data: DocumentNode;
  level?: number;
  isLast?: boolean;
  onFileSelect?: (url: string) => void;
  portfolioId?: string;
  selectedFile?: string | null;
  isSharedView?: boolean;
  showUploadButton?: boolean;
  collapseAllTrigger?: number;
}

// Utility function to filter out orphan files
const filterOrphanFiles = (node: DocumentNode): DocumentNode | null => {
  if (node.type === 'file') {
    // Keep files that have proper categorization or are in specific allowed categories
    if (node.document_type === 'offering_memorandum' || 
        (node.property_id && node.document_type) ||
        (node.sub_type && node.document_type)) {
      return node;
    }
    // Filter out orphan files
    return null;
  }
  
  if (node.type === 'folder' && node.children) {
    // Recursively filter children
    const filteredChildren = node.children
      .map(child => filterOrphanFiles(child))
      .filter((child): child is DocumentNode => child !== null);
    
    // Only keep folders that have children after filtering
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren
      };
    }
    // Remove empty folders
    return null;
  }
  
  return node;
};

export default function DocumentTree ({ data, level = 0, isLast = true, onFileSelect, portfolioId, selectedFile, isSharedView = false, showUploadButton = false, collapseAllTrigger = 0}: DocumentTreeProps) {
  const [isOpen, setIsOpen] = React.useState(true);
  const [showTooltip, setShowTooltip] = React.useState(false);
  const [showDocumentPreview, setShowDocumentPreview] = React.useState(false);
  const [tooltipPosition, setTooltipPosition] = React.useState({ x: 0, y: 0 });
  const [previewPosition, setPreviewPosition] = React.useState({ x: 0, y: 0 });
  const [isTextOverflowing, setIsTextOverflowing] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);
  const [pdfError, setPdfError] = React.useState(false);
  const [hideTimeout, setHideTimeout] = React.useState<NodeJS.Timeout | null>(null);
  const textRef = React.useRef<HTMLSpanElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { showModal, updateModalData } = useModal();
  const filteredData = React.useMemo(() => {
    return filterOrphanFiles(data) || data;
  }, [data]);

  // Handle collapse all trigger
  React.useEffect(() => {
    if (collapseAllTrigger > 0 && filteredData.type === 'folder') {
      setIsOpen(false);
    }
  }, [collapseAllTrigger, filteredData.type]);

  // Check if text is overflowing
  React.useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const isOverflowing = textRef.current.scrollWidth > textRef.current.clientWidth;
        setIsTextOverflowing(isOverflowing);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => {
      window.removeEventListener('resize', checkOverflow);
      // Cleanup timeout on unmount
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [filteredData.name, hideTimeout]);

  const handleToggle = () => {
    if (filteredData.type === 'folder') {
      setIsOpen(!isOpen);
    }
  };

  const handleFileClick = (e: React.MouseEvent) => {
    if (filteredData.type === 'file' && filteredData.url) {
      e.stopPropagation();
      if (onFileSelect) {
        onFileSelect(filteredData.url);
      }
    }
  };

  const handleDownload = (e: React.MouseEvent) => {
    if (filteredData.type === 'file' && filteredData.url) {
      e.stopPropagation();
      // Open the document in a new tab
      window.open(filteredData.url, '_blank');
      
      // Also trigger the download
      const link = document.createElement('a');
      link.href = filteredData.url;
      link.download = filteredData.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleDelete = async (e: React.MouseEvent) => {
    if (filteredData.type === 'file' && filteredData.id) {
      e.stopPropagation();
      updateModalData({
        id: filteredData.id,
        portfolioId: portfolioId,
        name: filteredData.name,
      })
      showModal(modalType.deletePortfolioDocument)
    }
  };

  const handleFolderMouseEnter = (e: React.MouseEvent) => {
    if (filteredData.type === 'folder' && filteredData.children && filteredData.children.length > 0) {
      const rect = e.currentTarget.getBoundingClientRect();
      setTooltipPosition({
        x: rect.right + 10,
        y: rect.top
      });
      setShowTooltip(true);
    }
  };

  const handleFolderMouseLeave = () => {
    setShowTooltip(false);
  };

  const handleDocumentMouseEnter = (e: React.MouseEvent) => {
    if (filteredData.type === 'file') {
      // Clear any existing timeout
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }

      const rect = e.currentTarget.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const previewWidth = 320; // Width of preview popup
      const previewHeight = 500; // Increased estimated height to account for all content + buttons
      const margin = 15; // Margin from screen edges
      
      // Smart horizontal positioning
      let xPosition = rect.right + 10; // Default to right
      if (rect.right + previewWidth + margin > viewportWidth) {
        // Not enough space on right, try left
        xPosition = rect.left - previewWidth - 10;
        if (xPosition < margin) {
          // Not enough space on left either, center it
          xPosition = Math.max(margin, (viewportWidth - previewWidth) / 2);
        }
      }
      
      // Smart vertical positioning - prioritize keeping buttons visible
      let yPosition = rect.top; // Default to align with item
      
      // Check if popup would overflow bottom
      if (rect.top + previewHeight + margin > viewportHeight) {
        // Try positioning above the item
        yPosition = rect.bottom - previewHeight;
        
        // If still overflows top, center it vertically
        if (yPosition < margin) {
          yPosition = Math.max(margin, (viewportHeight - previewHeight) / 2);
        }
      }
      
      // Final bounds check to ensure popup stays within viewport
      setPreviewPosition({
        x: Math.max(margin, Math.min(xPosition, viewportWidth - previewWidth - margin)),
        y: Math.max(margin, Math.min(yPosition, viewportHeight - previewHeight - margin))
      });
      setShowDocumentPreview(true);
      setPdfError(false); // Reset PDF error state
    }
  };

  const handleDocumentMouseLeave = () => {
    // Add a delay before hiding to allow mouse movement to popup
    const timeout = setTimeout(() => {
      setShowDocumentPreview(false);
    }, 100); // 500ms delay for better UX
    setHideTimeout(timeout);
  };

  const handlePreviewMouseEnter = () => {
    // Clear timeout when mouse enters the preview popup
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
  };

  const handlePreviewMouseLeave = () => {
    // Hide immediately when leaving the preview popup
    setShowDocumentPreview(false);
  };

  const handleTextMouseEnter = () => {
    setIsHovering(true);
  };

  const handleTextMouseLeave = () => {
    setIsHovering(false);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Check if file is PDF
  const isPDF = (filename: string) => {
    return filename.toLowerCase().endsWith('.pdf');
  };

  // Generate PDF preview URL for first page
  const getPDFPreviewUrl = (url: string) => {
    if (!url || !isPDF(filteredData.name)) return null;
    // This assumes your backend can generate PDF previews
    // You might need to adjust this based on your PDF service
    return `${url}#page=1&view=FitH&toolbar=0&navpanes=0`;
  };

  const handleUploadDocuments = () => {
    showModal(modalType.documentUpload);
    updateModalData({
      propId: null,
      selectedPortfolioId: portfolioId,
      individual: false
    });
  };

  const isSelected = selectedFile === filteredData.url;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1  data-room-wrap custom-scrollbar">
        <div 
          ref={containerRef}
          className="flex items-center group relative"
          onMouseEnter={filteredData.type === 'file' ? handleDocumentMouseEnter : undefined}
          onMouseLeave={filteredData.type === 'file' ? handleDocumentMouseLeave : undefined}
        >
          {/* Vertical lines for hierarchy - 50% smaller indentation */}
          {level > 0 && (
            <div className="flex">
              {Array.from({ length: level }).map((_, index) => (
                <div
                  key={index}
                  className={`w-3 border-l border-slate-200/50 ${
                    index === level - 1 && isLast ? 'border-transparent' : ''
                  }`}
                />
              ))}
            </div>
          )}
          
          {/* Content */}
          <div
            className={`flex items-center gap-2 py-1 px-3 rounded-lg transition-all duration-200 flex-grow backdrop-blur-sm ${
              filteredData.type === 'folder' 
                ? 'cursor-pointer hover:bg-white/60 hover:shadow-sm border border-transparent hover:border-slate-200/50' 
                : isSelected
                  ? 'bg-[#5E48F8]/10 border border-[#5E48F8]/30 shadow-sm backdrop-blur-md'
                  : 'cursor-pointer hover:bg-[#5E48F8]/5 hover:shadow-sm border border-transparent hover:border-[#5E48F8]/20'
            }`}
            onClick={filteredData.type === 'folder' ? handleToggle : handleFileClick}
            onMouseEnter={handleFolderMouseEnter}
            onMouseLeave={handleFolderMouseLeave}
            title={filteredData.name}
          >
            {/* Icons */}
            {filteredData.type === 'folder' ? (
              <>
                <FontAwesomeIcon 
                  icon={isOpen ? faChevronDown : faChevronRight} 
                  className="h-3 w-3 text-slate-400 transition-transform duration-200 flex-shrink-0" 
                />
                <FontAwesomeIcon 
                  icon={faFolder} 
                  className="h-4 w-4 text-blue-500 flex-shrink-0" 
                />
              </>
            ) : (
              <FontAwesomeIcon 
                icon={faFile} 
                className="h-3.5 w-3.5 text-slate-400 ml-6 flex-shrink-0" 
              />
            )}
            
            {/* Name with ticker effect */}
            <div 
              className="flex-grow overflow-hidden min-w-0"
              onMouseEnter={handleTextMouseEnter}
              onMouseLeave={handleTextMouseLeave}
            >
              <span 
                ref={textRef}
                className={`block transition-colors duration-200 ${
                  filteredData.type === 'folder' 
                    ? 'text-base font-semibold text-slate-700' 
                    : isSelected
                      ? 'text-sm text-[#5E48F8] font-medium'
                      : 'text-sm text-slate-600 hover:text-[#5E48F8]'
                } ${
                  isTextOverflowing && isHovering 
                    ? 'ticker-text scrolling' 
                    : 'truncate'
                }`}
              >
                {filteredData.name}
              </span>
            </div>
          </div>
        </div>
        
        {/* Folder hover tooltip */}
        {showTooltip && filteredData.type === 'folder' && filteredData.children && filteredData.children.length > 0 && (
          <div
            className="fixed z-50 bg-white/95 backdrop-blur-md rounded-lg shadow-lg border border-slate-200/50 p-3 min-w-48 max-w-64"
            style={{
              left: `${tooltipPosition.x}px`,
              top: `${tooltipPosition.y}px`,
            }}
          >
            <div className="space-y-1">
              <div className="text-xs font-medium text-slate-500 mb-2 border-b border-slate-200 pb-1">
                {filteredData.name} contents:
              </div>
              {filteredData.children.slice(0, 6).map((child) => (
                <div key={child.id} className="flex items-center gap-2 py-1">
                  <FontAwesomeIcon 
                    icon={child.type === 'folder' ? faFolder : faFile} 
                    className={`h-3 w-3 ${
                      child.type === 'folder' ? 'text-blue-500' : 'text-slate-400'
                    }`}
                  />
                  <span className="text-sm text-slate-600 truncate">
                    {child.name}
                  </span>
                </div>
              ))}
              {filteredData.children.length > 6 && (
                <div className="text-xs text-slate-400 italic pt-1 border-t border-slate-100">
                  +{filteredData.children.length - 6} more items...
                </div>
              )}
            </div>
          </div>
        )}

        {/* Document preview popup */}
        {showDocumentPreview && filteredData.type === 'file' && (
          <div
            className="fixed z-50 bg-white/95 backdrop-blur-md rounded-lg shadow-xl border border-slate-200/50 p-4 w-80 max-h-[90vh] overflow-y-auto"
            style={{
              left: `${previewPosition.x}px`,
              top: `${previewPosition.y}px`,
            }}
            onMouseEnter={handlePreviewMouseEnter}
            onMouseLeave={handlePreviewMouseLeave}
          >
            <div className="space-y-3">
              {/* Header with full name */}
              <div className="flex items-center gap-2 border-b border-slate-200 pb-2">
                <FontAwesomeIcon icon={faFileLines} className="h-4 w-4 text-[#5E48F8]" />
                <div className="flex-grow min-w-0">
                  <div className="text-sm font-medium text-slate-700 break-words">
                    {filteredData.name}
                  </div>
                  <div className="text-xs text-slate-500">
                    {filteredData.page_count ? `${filteredData.page_count} pages` : 'Document'}
                    {filteredData.document_type && ` • ${filteredData.document_type.replace('_', ' ')}`}
                  </div>
                </div>
              </div>

              {/* Preview content */}
              <div className="space-y-2">
                {filteredData.url && isPDF(filteredData.name) && !pdfError ? (
                  <div className="bg-slate-50 rounded-lg p-2 border border-slate-200">
                    <iframe
                      src={getPDFPreviewUrl(filteredData.url) || filteredData.url}
                      className="pdf-preview"
                      title="PDF Preview"
                      onError={() => setPdfError(true)}
                    />
                    <div className="text-xs text-slate-500 mt-1 text-center">
                      Preview
                    </div>
                  </div>
                ) : filteredData.preview_url ? (
                  <div className="bg-slate-50 rounded-lg p-2 border border-slate-200">
                    <img 
                      src={filteredData.preview_url} 
                      alt="Document preview"
                      className="w-full h-32 object-cover rounded border"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    <div className="text-xs text-slate-500 mt-1 text-center">
                      Preview
                    </div>
                  </div>
                ) : (
                  <div className="bg-slate-50 rounded-lg p-4 border border-slate-200 text-center">
                    <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-slate-400 mb-2" />
                    <div className="text-xs text-slate-500">Preview not available</div>
                  </div>
                )}

                {/* Document info */}
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="bg-slate-50 rounded p-2">
                    <div className="text-slate-500">Type</div>
                    <div className="font-medium text-slate-700 capitalize">
                      {filteredData.document_type?.replace('_', ' ') || 'Document'}
                    </div>
                  </div>
                  <div className="bg-slate-50 rounded p-2">
                    <div className="text-slate-500">Uploaded</div>
                    <div className="font-medium text-slate-700">
                      {formatDate(filteredData.uploaded_at || filteredData.created_at) || 'Unknown'}
                    </div>
                  </div>
                </div>

                {/* Page count if available */}
                {filteredData.page_count && (
                  <div className="bg-[#5E48F8]/10 rounded-lg p-2 border border-[#5E48F8]/20">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-[#5E48F8] font-medium">Pages</span>
                      <span className="text-sm font-bold text-[#5E48F8]">{filteredData.page_count}</span>
                    </div>
                  </div>
                )}

                {/* Summary if available */}
                {filteredData.summary && (
                  <div className="bg-[#5E48F8]/10 rounded-lg p-2 border border-[#5E48F8]/20">
                    <div className="text-xs text-[#5E48F8] font-medium mb-1">Summary</div>
                    <div className="text-xs text-[#5E48F8]/80 line-clamp-3">
                      {filteredData.summary}
                    </div>
                  </div>
                )}

                {/* Action buttons */}
                <div className="flex items-center gap-2 pt-2 border-t border-slate-200">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(e);
                      setShowDocumentPreview(false);
                    }}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 rounded-lg transition-colors duration-200 text-sm font-medium"
                    title="Download file"
                  >
                    <FontAwesomeIcon icon={faDownload} className="h-3.5 w-3.5" />
                    Download
                  </button>
                  {filteredData.document_type !== 'offering_memorandum' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(e);
                        setShowDocumentPreview(false);
                      }}
                      className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 rounded-lg transition-colors duration-200 text-sm font-medium"
                      title="Delete file"
                    >
                      <FontAwesomeIcon icon={faTrash} className="h-3.5 w-3.5" />
                      Delete
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Children */}
        {isOpen && filteredData.children && (
          <div className="space-y-0">
            {filteredData.children.map((child, index) => (
              <DocumentTree 
                key={child.id} 
                data={child} 
                level={level + 1} 
                isLast={index === filteredData.children!.length - 1}
                onFileSelect={onFileSelect}
                portfolioId={portfolioId}
                selectedFile={selectedFile}
                isSharedView={isSharedView}
                showUploadButton={false}
                collapseAllTrigger={collapseAllTrigger}
              />
            ))}
          </div>
        )}
      </div>

      {/* Fixed Upload Documents Button at bottom */}
      {showUploadButton && !isSharedView && (
        <div className="flex-shrink-0 p-4 border-t border-slate-200/50 bg-white/95 backdrop-blur-sm">
          <button
            onClick={handleUploadDocuments}
            className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-gradient-to-r from-[#5E48F8] to-[#7C69FF] hover:from-[#4A3BC7] hover:to-[#5E48F8] text-white rounded-lg transition-all duration-200 font-medium shadow-sm hover:shadow-md transform hover:scale-[1.02]"
          >
            <FontAwesomeIcon icon={faCloudArrowUp} className="h-4 w-4" />
            Upload Documents
          </button>
        </div>
      )}
    </div>
  );
};