import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight, faStepBackward, faStepForward, faExpand, faCompress, faTimes } from '@fortawesome/free-solid-svg-icons';

interface PDFViewerProps {
  url: string;
  totalPages?: number; // Made optional for backward compatibility
  pageCount?: number; // Add pageCount as alternative prop name
  onFullscreenToggle?: () => void; // Add fullscreen toggle handler
  onClose?: () => void; // Add close handler
  isFullscreen?: boolean; // Add fullscreen state
}

export default function PDFViewer({ url, totalPages, pageCount, onFullscreenToggle, onClose, isFullscreen = false }: PDFViewerProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [detectedPageCount, setDetectedPageCount] = useState<number | null>(null);
  
  // Use pageCount if provided, otherwise fall back to totalPages, then detectedPageCount, default to 1
  // Temporary: Set minimum to 5 for testing if we don't have page count data
  const actualTotalPages = pageCount || totalPages || detectedPageCount || (url.toLowerCase().includes('.pdf') ? 5 : 1);

  // Debug logging
  useEffect(() => {
    console.log('PDFViewer props:', { url, totalPages, pageCount, detectedPageCount, actualTotalPages });
  }, [url, totalPages, pageCount, detectedPageCount, actualTotalPages]);

  // Try to detect page count from PDF if not provided
  useEffect(() => {
    if ((!pageCount || pageCount === 1) && (!totalPages || totalPages === 1) && url) {
      // Create a hidden iframe to load the PDF and try to detect page count
      const testFrame = document.createElement('iframe');
      testFrame.style.display = 'none';
      testFrame.src = url;
      document.body.appendChild(testFrame);
      
      // Try to detect page count after a delay
      const timeout = setTimeout(() => {
        try {
          // Try different page numbers to find the maximum
          let maxPage = 1;
          for (let i = 2; i <= 50; i++) { // Check up to 50 pages
            const testUrl = `${url}#page=${i}`;
            // This is a basic approach - in a real implementation you'd want to use PDF.js
            // For now, we'll assume documents have more than 1 page if they're PDFs
            if (url.toLowerCase().includes('.pdf')) {
              maxPage = 10; // Default assumption for PDFs
              break;
            }
          }
          if (maxPage > 1) {
            setDetectedPageCount(maxPage);
          }
        } catch (error) {
          console.log('Could not detect page count:', error);
        }
        document.body.removeChild(testFrame);
      }, 1000);

      return () => {
        clearTimeout(timeout);
        if (document.body.contains(testFrame)) {
          document.body.removeChild(testFrame);
        }
      };
    }
  }, [url, pageCount, totalPages]);

  // Ensure currentPage is always within valid range
  useEffect(() => {
    if (currentPage < 1) {
      setCurrentPage(1);
    } else if (currentPage > actualTotalPages) {
      setCurrentPage(actualTotalPages);
    }
  }, [currentPage, actualTotalPages]);

  // PDF.js parameters for single page view that always fits
  const srcWithPage = `${url}#toolbar=0&navpanes=0&scrollbar=0&view=Fit&page=${currentPage}&zoom=page-fit`;

  const handlePrevPage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Previous page clicked, current:', currentPage);
    setCurrentPage((prevPage) => Math.max(prevPage - 1, 1));
  };

  const handleNextPage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Next page clicked, current:', currentPage, 'total:', actualTotalPages);
    setCurrentPage((prevPage) => Math.min(prevPage + 1, actualTotalPages));
  };

  const handleFirstPage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('First page clicked');
    setCurrentPage(1);
  };

  const handleLastPage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Last page clicked, total:', actualTotalPages);
    setCurrentPage(actualTotalPages);
  };

  const handlePageInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const pageNum = parseInt(e.target.value);
    console.log('Page input changed:', pageNum);
    if (!isNaN(pageNum) && pageNum > 0 && pageNum <= actualTotalPages) {
      setCurrentPage(pageNum);
    }
  };

  return (
    <div className="w-full h-full bg-[#242424] rounded-[20px] shadow-lg p-4 flex flex-col">
      {/* Navigation Controls */}
      <div className="flex items-center justify-between mb-4 pb-3 relative z-20 px-4 py-3">
        <div className="flex items-center gap-3">
          <button
            onClick={handleFirstPage}
            disabled={currentPage <= 1}
            className="flex items-center justify-center w-8 h-8 rounded-md bg-gray-700/80 hover:bg-gray-600/80 disabled:opacity-30 disabled:cursor-not-allowed transition-colors active:bg-gray-500/80 border border-white/20 text-white"
            title="First page"
            style={{ pointerEvents: 'auto', zIndex: 10 }}
          >
            <FontAwesomeIcon icon={faStepBackward} className="h-3 w-3" />
          </button>

          <button
            onClick={handlePrevPage}
            disabled={currentPage <= 1}
            className="flex items-center justify-center w-8 h-8 rounded-md bg-gray-700/80 hover:bg-gray-600/80 disabled:opacity-30 disabled:cursor-not-allowed transition-colors active:bg-gray-500/80 border border-white/20 text-white"
            title="Previous page"
            style={{ pointerEvents: 'auto', zIndex: 10 }}
          >
            <FontAwesomeIcon icon={faChevronLeft} className="h-3 w-3" />
          </button>

          <div className="flex items-center gap-2">
            <span className="text-sm text-white/80">Page</span>
            <input
              type="number"
              value={currentPage}
              onChange={handlePageInput}
              className="w-16 px-2 py-1 text-sm border border-white/20 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/40 bg-gray-700/80 text-white placeholder-white/50"
              min="1"
              max={actualTotalPages}
              style={{ zIndex: 10 }}
            />
            <span className="text-sm text-white/80">of {actualTotalPages}</span>
          </div>

          <button
            onClick={handleNextPage}
            disabled={currentPage >= actualTotalPages}
            className="flex items-center justify-center w-8 h-8 rounded-md bg-gray-700/80 hover:bg-gray-600/80 disabled:opacity-30 disabled:cursor-not-allowed transition-colors active:bg-gray-500/80 border border-white/20 text-white"
            title="Next page"
            style={{ pointerEvents: 'auto', zIndex: 10 }}
          >
            <FontAwesomeIcon icon={faChevronRight} className="h-3 w-3" />
          </button>

          <button
            onClick={handleLastPage}
            disabled={currentPage >= actualTotalPages}
            className="flex items-center justify-center w-8 h-8 rounded-md bg-gray-700/80 hover:bg-gray-600/80 disabled:opacity-30 disabled:cursor-not-allowed transition-colors active:bg-gray-500/80 border border-white/20 text-white"
            title="Last page"
            style={{ pointerEvents: 'auto', zIndex: 10 }}
          >
            <FontAwesomeIcon icon={faStepForward} className="h-3 w-3" />
          </button>
        </div>

        <div className="flex items-center gap-3">
          <span className="text-sm text-white/70">
            Slide {currentPage} of {actualTotalPages}
          </span>
          
          {/* Fullscreen/Close Buttons */}
          <div className="flex items-center gap-2 ml-2">
            {onFullscreenToggle && (
              <button
                onClick={onFullscreenToggle}
                className="flex items-center justify-center w-8 h-8 rounded-md bg-gray-300 hover:bg-gray-300 transition-colors text-black"
                title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                style={{ pointerEvents: 'auto', zIndex: 10 }}
              >
                <FontAwesomeIcon 
                  icon={isFullscreen ? faCompress : faExpand} 
                  className="h-3 w-3" 
                />
              </button>
            )}
            
            {/* {onClose && isFullscreen && (
              <button
                onClick={onClose}
                className="flex items-center justify-center w-8 h-8 rounded-md bg-red-100 hover:bg-red-200 transition-colors border border-red-300"
                title="Close fullscreen"
                style={{ pointerEvents: 'auto', zIndex: 10 }}
              >
                <FontAwesomeIcon 
                  icon={faTimes} 
                  className="h-3 w-3 text-red-600" 
                />
              </button>
            )} */}
          </div>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="flex-1 overflow-hidden relative">
        <iframe
          key={currentPage} // Force re-render when page changes
          src={srcWithPage}
          className="w-full h-full"
          title={`PDF Viewer - Page ${currentPage}`}
          style={{
            backgroundColor: 'white', // Ensure background is white
            overflow: 'hidden',
            position: 'relative',
            zIndex: 1
          }}
        />
      </div>
    </div>
  );
}