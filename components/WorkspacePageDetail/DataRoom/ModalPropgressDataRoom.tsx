import { usePortfolio } from "@/context/PortfolioContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";

interface Props {
    isDownloading: boolean
    downloadProgress: {
        current: number;
        total: number;
        status: string;
        isComplete: boolean;
        isError: boolean;
    }
    setDownloadProgress: React.Dispatch<React.SetStateAction<{
        current: number;
        total: number;
        status: string;
        isComplete: boolean;
        isError: boolean;
    }>>;
}

export default function ModalPropgressDataRoom({ isDownloading, downloadProgress, setDownloadProgress }: Props) {
    const { selectedPortfolio } = usePortfolio();
    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className={`max-w-md w-full mx-4 rounded-xl shadow-2xl transition-all duration-300 transform ${
            downloadProgress.isError 
              ? 'bg-red-50 border-2 border-red-200' 
              : downloadProgress.isComplete 
                ? 'bg-green-50 border-2 border-green-200' 
                : 'bg-white border-2 border-green-200'
          }`}>
            {/* Modal Header */}
            <div className={`px-6 py-4 border-b ${
              downloadProgress.isError 
                ? 'border-red-200 bg-red-100/50' 
                : downloadProgress.isComplete 
                  ? 'border-green-200 bg-green-100/50' 
                  : 'border-green-200 bg-green-100/50'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  downloadProgress.isError 
                    ? 'bg-gradient-to-r from-red-500 to-red-600' 
                    : downloadProgress.isComplete 
                      ? 'bg-gradient-to-r from-green-500 to-green-600' 
                      : 'bg-gradient-to-r from-green-500 to-green-600'
                }`}>
                  {downloadProgress.isError ? (
                    <span className="text-white font-bold text-lg">✕</span>
                  ) : downloadProgress.isComplete ? (
                    <span className="text-white font-bold text-lg">✓</span>
                  ) : (
                    <FontAwesomeIcon icon={faSpinner} className="h-5 w-5 text-white animate-spin" />
                  )}
                </div>
                <div>
                  <h3 className={`text-lg font-semibold ${
                    downloadProgress.isError 
                      ? 'text-red-800' 
                      : downloadProgress.isComplete 
                        ? 'text-green-800' 
                        : 'text-green-800'
                  }`}>
                    {downloadProgress.isError 
                      ? 'Download Failed' 
                      : downloadProgress.isComplete 
                        ? 'Download Complete!' 
                        : 'Downloading Documents'}
                  </h3>
                  <p className={`text-sm ${
                    downloadProgress.isError 
                      ? 'text-red-600' 
                      : downloadProgress.isComplete 
                        ? 'text-green-600' 
                        : 'text-green-600'
                  }`}>
                    {selectedPortfolio?.name || 'Portfolio Documents'}
                  </p>
                </div>
              </div>
            </div>

            {/* Modal Body */}
            <div className="px-6 py-6">
              <div className="space-y-4">
                {/* Status Message */}
                <div className={`text-sm font-medium ${
                  downloadProgress.isError 
                    ? 'text-red-700' 
                    : downloadProgress.isComplete 
                      ? 'text-green-700' 
                      : 'text-gray-700'
                }`}>
                  {downloadProgress.status}
                </div>

                {/* Progress Bar and Counter */}
                {downloadProgress.total > 0 && !downloadProgress.isError && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className={downloadProgress.isComplete ? 'text-green-600' : 'text-gray-600'}>
                        Files processed
                      </span>
                      <span className={`font-mono font-medium ${
                        downloadProgress.isComplete ? 'text-green-600' : 'text-gray-600'
                      }`}>
                        {downloadProgress.current}/{downloadProgress.total}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div 
                        className={`h-3 rounded-full transition-all duration-500 ease-out ${
                          downloadProgress.isComplete 
                            ? 'bg-gradient-to-r from-green-500 to-green-600' 
                            : 'bg-gradient-to-r from-green-400 to-green-500'
                        }`}
                        style={{ 
                          width: `${(downloadProgress.current / downloadProgress.total) * 100}%` 
                        }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      {Math.round((downloadProgress.current / downloadProgress.total) * 100)}% complete
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                {(downloadProgress.isComplete || downloadProgress.isError) && (
                  <div className="flex justify-end pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setDownloadProgress({ current: 0, total: 0, status: '', isComplete: false, isError: false });
                      }}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                        downloadProgress.isError
                          ? 'bg-red-100 text-red-700 hover:bg-red-200'
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      }`}
                    >
                      Close
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
    )
}