import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBuilding, faChevronLeft, faFilePdf, faShare, faDownload, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { usePortfolio } from "@/context/PortfolioContext";
import { useSearchParams, useRouter } from "next/navigation";
import WorkspaceDetailProperties from "@/components/WorkspacePageDetail/WorkspaceDetailProperties";
import WorkspaceDetailFinancials from "@/components/WorkspacePageDetail/WorkspaceDetailFinancials/WorkspaceDetailFinancials";
import WorkspaceDataRoom from "@/components/WorkspacePageDetail/DataRoom/WorkspaceDataRoom";
import { MarketAnalysisProvider } from "@/context/MarketAnalysisContext";
import MarketAnalysisContent from "@/components/WorkspacePageDetail/MarketAnalysis/MarketAnalysisContent";
import PortfolioHeaderMarket from "@/components/WorkspacePageDetail/PortfolioHeader/PortfolioHeaderMarket";
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import { useAuth } from "@/context/AuthContext";
import { useDownloadAllDocuments } from "@/helpers/hooks/marketAnalysis/useDownloadAllDocuments";
import ModalPropgressDataRoom from "../DataRoom/ModalPropgressDataRoom";


const tabs = [
  { id: 'properties', name: 'Properties' },
  { id: 'financials', name: 'Financials' },
  { id: 'changelog', name: 'Change Log' },
  { id: 'data-room', name: 'Data Room' },
] as const;

const marketTabs = [
  { id: 'info', name: 'Info' },
  { id: 'financials', name: 'Financials' },
] as const;

interface PortfolioContentProps {
  isChatExpanded: boolean;
  isPropertyView: boolean;
}

export default function PortfolioContent({ isChatExpanded, isPropertyView }: PortfolioContentProps) {
  const { selectedPortfolio } = usePortfolio();
  const [selectedTabPortfolio, setSelectedTabPortfolio] = useState<typeof tabs[number]['id']>('properties');
  const [selectedTabMarket, setSelectedTabMarket] = useState<typeof marketTabs[number]['id']>('info');
  
  const searchParams = useSearchParams();
  const addressId = searchParams.get('addressId');
  const { showModal, updateModalData } = useModal();
  const { user } = useAuth();


  

  if (!selectedPortfolio) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 h-full flex items-center justify-center">
        <div className="text-center">
          <FontAwesomeIcon icon={faBuilding} className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Portfolio Selected</h3>
          <p className="text-sm text-gray-500">
            Select a portfolio from the table above to view its contents and manage properties
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg overflow-hidden h-full flex flex-col">
      {/* Portfolio Header - minimized when in property view */}
      <div className={`px-6 flex-shrink-0 ${isPropertyView ? 'py-2' : 'py-4'}`}>

        {/* Market Analysis Header for addressId */}
        {addressId && (
          <div className={isPropertyView ? 'mt-2' : 'mt-4'}>
            <PortfolioHeaderMarket 
              selectedTabMarket={selectedTabMarket} 
              setSelectedTabMarket={setSelectedTabMarket} 
            />
          </div>
        )}

        {/* Portfolio Tabs for regular portfolio view */}
        {!addressId && (
          <div className="flex justify-between items-center border-b border-gray-200 mt-4">
            <div className="flex">
              {tabs.filter(tab => tab.id !== 'changelog').map((tab) => (
                <button
                  onClick={() => setSelectedTabPortfolio(tab.id)}
                  key={tab.id}
                  className={`
                    cursor-pointer relative px-6 py-3 text-sm font-medium transition-colors
                    ${tab.id === selectedTabPortfolio 
                      ? 'text-indigo-600 border-b-2 border-indigo-500 -mb-[1px]' 
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                  `}
                >
                  {tab.name}
                  {tab.id === selectedTabPortfolio && (
                    <span className="absolute bottom-0 left-0 w-full h-[2px] bg-indigo-500"></span>
                  )}
                </button>
              ))}
            </div>
            
            
          </div>
        )}
      </div>

      {/* Content Area - fills remaining space */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6 h-full">
          {/* Portfolio Content */}
          {!addressId && (
            <>
              {selectedTabPortfolio === 'properties' && <WorkspaceDetailProperties />}
              {selectedTabPortfolio === 'financials' && <WorkspaceDetailFinancials />}
              {selectedTabPortfolio === 'data-room' && <WorkspaceDataRoom isChatExpanded={isChatExpanded} />}
              {selectedTabPortfolio === 'changelog' && (
                <div className="text-center py-12">
                  <p className="text-gray-500">Change Log functionality coming soon...</p>
                </div>
              )}
            </>
          )}

          {/* Market Analysis Content */}
          {addressId && (
            <>
              {selectedTabMarket === 'info' && (
                <MarketAnalysisProvider>
                  <MarketAnalysisContent />
                </MarketAnalysisProvider>
              )}
              {selectedTabMarket === 'financials' && <WorkspaceDetailFinancials />}
            </>
          )}
        </div>
      </div>
    </div>
  );
} 