export default function EnvironmentalRisks() {
    return (
        <div className="bg-white shadow-sm rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-800">🌲 Environmental Risks</h3>
            <div className="flex items-center gap-x-2 py-4 border-b border-gray-100">
                <div>
                    <div>💧</div>
                </div>
                <div>
                    <p className="text-lg font-semibold">Flood Factor</p>
                </div>
                <div>
                    <span className="font-medium text-red-600">Severe</span>
                </div>
                <div>
                    <span className="text-sm text-gray-600">This property's flood risk is increasing.</span>
                </div>
            </div>
            <div className="flex items-center gap-x-2 py-4 border-b border-gray-100">
                <div>
                    <div>🔥</div>
                </div>
                <div>
                    <p className="text-lg font-semibold">Fire Factor</p>
                </div>
                <div>
                    <span className="font-medium text-green-600">Minimal</span>
                </div>
                <div>
                    <span className="text-sm text-gray-600">This property's wildfire risk is not changing.</span>
                </div>
            </div>
            <div className="flex items-center gap-x-2 py-4 border-b border-gray-100">
                <div>
                    <div>💧</div>
                </div>
                <div>
                    <p className="text-lg font-semibold">Heat Factor</p>
                </div>
                <div>
                    <span className="font-medium text-red-600">Severe</span>
                </div>
                <div>
                    <span className="text-sm text-gray-600">7 days above 99°F this year</span>
                </div>
            </div>
            <div className="flex items-center gap-x-2 py-4 border-b border-gray-100">
                <div>
                    <div>💨</div>
                </div>
                <div>
                    <p className="text-lg font-semibold">Wind Factor</p>
                </div>
                <div>
                    <span className="font-medium text-orange-600">Major</span>
                </div>
                <div>
                    <span className="text-sm text-gray-600">Major risk of severe winds over next 30 years</span>
                </div>
            </div>
            <div className="flex items-center gap-x-2 py-4 border-b border-gray-100">
                <div>
                    <div>💨</div>
                </div>
                <div>
                    <p className="text-lg font-semibold">Air Factor</p>
                </div>
                <div>
                    <span className="font-medium text-yellow-600">Moderate</span>
                </div>
                <div>
                    <span className="text-sm text-gray-600">Risk of poor air quality is increasing</span>
                </div>
            </div>
        </div>
    )
}