import { usePlaceSearchStyles } from '@/helpers/hooks/marketAnalysis/usePlaceSearchStyles';
import { useRef, useEffect, useState } from 'react';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import { useSearchParams } from 'next/navigation';
import { getPropAddressDataById } from '@/actions/portfolioActions';
import { storeSearchHistory } from '@/actions/searchHistoryActions';
import { addToSearchHistory } from '@/utils/searchHistory';


interface Props {
    searchPlace: { [key: string]: any } | string
    setSearchPlace: React.Dispatch<React.SetStateAction<{ [key: string]: any } | string>>
    selectedStates: {name: string, abbreviation: string}[]
    setErrorState: React.Dispatch<React.SetStateAction<{name: string, abbreviation: string} | null>>
}

export default function SearchPlace({ isSearchPageStart }: { isSearchPageStart: boolean }) {
    const { searchPlace, selectedStates, updateMarketAnalysisState } = useMarketAnalysis();
    const inputRef = useRef<HTMLInputElement>(null);
    const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
    const geocoderRef = useRef<google.maps.Geocoder | null>(null);
    usePlaceSearchStyles();
    const searchParams = useSearchParams()
	const marketAddress = searchParams.get('marketAddress')

    // Professional property search placeholders for B2B platform
    const placeholderOptions = [
        "Search commercial property address...",
        "Enter investment property location...",
        "Find multi-family property address...",
        "Locate retail property for analysis...",
        "Search office building address...",
        "Enter mixed-use development location...",
        "Find industrial property address...",
        "Search apartment complex location...",
        "Enter warehouse property address...",
        "Locate shopping center for evaluation...",
        "Search hotel property address...",
        "Find medical office building location...",
        "Enter senior living facility address...",
        "Search student housing property...",
        "Locate self-storage facility address...",
        "Find distribution center location...",
        "Enter flex space property address...",
        "Search Class A office building...",
        "Locate ground lease opportunity...",
        "Find value-add property address...",
        "Enter core asset location...",
        "Search opportunistic investment property...",
        "Locate stabilized asset address...",
        "Find trophy asset location...",
        "Enter income-producing property address...",
        "Search net lease property location...",
        "Locate build-to-suit opportunity...",
        "Find sale-leaseback property address...",
        "Enter redevelopment opportunity location...",
        "Search land development site...",
        "Locate institutional-grade property...",
        "Find REO property address...",
        "Enter distressed asset location...",
        "Search 1031 exchange property...",
        "Locate cap rate opportunity...",
        "Find cash flow property address...",
        "Enter property for due diligence...",
        "Search comparable property location...",
        "Locate market analysis subject property...",
        "Find acquisition target address...",
        "Enter portfolio property location...",
        "Search tenant-occupied property...",
        "Locate vacant property for analysis...",
        "Find property for valuation...",
        "Enter asset for underwriting...",
        "Search property for market study...",
        "Locate investment opportunity address...",
        "Find property for financial analysis...",
        "Enter real estate asset location...",
        "Search property for competitive analysis..."
    ];

    const [currentPlaceholder, setCurrentPlaceholder] = useState('');
    const [hasTyped, setHasTyped] = useState(false);
    const typewriterRef = useRef<{
        intervals: NodeJS.Timeout[];
        timeouts: NodeJS.Timeout[];
        isRunning: boolean;
    }>({ intervals: [], timeouts: [], isRunning: false });

    // Typewriter effect on page load
    useEffect(() => {
        // Prevent multiple runs
        if (hasTyped || typewriterRef.current.isRunning) return;
        
        typewriterRef.current.isRunning = true;
        const finalText = "Search a property address...";
        
        // Pick 2 random strings
        const shuffled = [...placeholderOptions].sort(() => 0.5 - Math.random());
        const firstText = shuffled[0];
        const secondText = shuffled[1];

        // Helper to clear all intervals and timeouts
        const clearAllTimers = () => {
            typewriterRef.current.intervals.forEach(clearInterval);
            typewriterRef.current.timeouts.forEach(clearTimeout);
            typewriterRef.current.intervals = [];
            typewriterRef.current.timeouts = [];
        };

        // Type a string with given duration
        const typeString = (text: string, duration: number): Promise<void> => {
            return new Promise((resolve) => {
                let charIndex = 0;
                setCurrentPlaceholder(''); // Clear first
                
                const interval = setInterval(() => {
                    if (charIndex < text.length) {
                        setCurrentPlaceholder(text.substring(0, charIndex + 1));
                        charIndex++;
                    } else {
                        clearInterval(interval);
                        // Remove from tracking array
                        const index = typewriterRef.current.intervals.indexOf(interval);
                        if (index > -1) typewriterRef.current.intervals.splice(index, 1);
                        resolve();
                    }
                }, duration / text.length);
                
                typewriterRef.current.intervals.push(interval);
            });
        };

        // Helper to wait
        const wait = (ms: number): Promise<void> => {
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    // Remove from tracking array
                    const index = typewriterRef.current.timeouts.indexOf(timeout);
                    if (index > -1) typewriterRef.current.timeouts.splice(index, 1);
                    resolve();
                }, ms);
                typewriterRef.current.timeouts.push(timeout);
            });
        };

        // Run the sequence
        const runTypewriterSequence = async () => {
            try {
                // await typeString(firstText, 200);
                // await wait(500);
                // await typeString(secondText, 300);
                // await wait(400);
                await typeString(finalText, 400);
                setHasTyped(true);
                typewriterRef.current.isRunning = false;
            } catch (error) {
                // Handle any errors (like component unmounting)
                clearAllTimers();
                typewriterRef.current.isRunning = false;
            }
        };

        runTypewriterSequence();

        // Cleanup function
        return () => {
            clearAllTimers();
            typewriterRef.current.isRunning = false;
        };
    }, [hasTyped]);

    // Initialize Google Places autocomplete
    useEffect(() => {
        // Skip if input ref isn't available
        if (!inputRef.current) return;
        
        let cleanupFn: (() => void) | undefined;
        let timeoutId: NodeJS.Timeout;
        
        const initAutocomplete = () => {
            // Check if Google Maps API is loaded
            if (!window.google?.maps?.places) {
                timeoutId = setTimeout(initAutocomplete, 1000);
                return;
            }
            
            const input = inputRef.current;
            if (!input) return;
            
            // Create autocomplete instance
            const autocomplete = new google.maps.places.Autocomplete(input, {
                fields: ['address_components', 'formatted_address', 'geometry'],
                types: ['address'],
                componentRestrictions: { country: 'us' }
            });
            
            autocompleteRef.current = autocomplete;
            
            // Add positioning fix for dropdown
            const fixDropdownPosition = () => {
                const pacContainer = document.querySelector('.pac-container') as HTMLElement;
                if (pacContainer && input) {
                    const inputRect = input.getBoundingClientRect();
                    // Just match the input width and position relative to input
                    pacContainer.style.width = `${inputRect.width}px`;
                }
            };
            
            // Fix position when dropdown appears
            const observer = new MutationObserver(() => {
                const pacContainer = document.querySelector('.pac-container');
                if (pacContainer) {
                    fixDropdownPosition();
                }
            });
            
            observer.observe(document.body, { childList: true, subtree: true });
            
            // Also fix on window resize
            const handleResize = () => fixDropdownPosition();
            window.addEventListener('resize', handleResize);
            
            // Initialize geocoder
            geocoderRef.current = new google.maps.Geocoder();
            
            // Set up place selection handler
            const listener = google.maps.event.addListener(
                autocomplete,
                'place_changed',
                async () => {
                    const place = autocomplete.getPlace();
                    if (place?.formatted_address) {
                        console.log(place.geometry);
                        updateMarketAnalysisState({ errorState: null })

                        // Extract state from address components
                        const stateComponent = place.address_components?.find(
                            component => component.types.includes('administrative_area_level_1')
                        );
                        
                        const stateCode = stateComponent?.short_name; // State abbreviation (e.g., "CA")
                        const stateName = stateComponent?.long_name; // Full state name (e.g., "California")
                        
                        // Check if the state is in the selectedStates list
                        const stateIsSelected = selectedStates.some(
                            state => state.abbreviation === stateCode || state.name === stateName
                        );
                        
                        if (stateIsSelected || selectedStates.length === 0) {
                            updateMarketAnalysisState({ searchPlace: place });
                            
                            // Store search history locally in cookies
                            try {
                                addToSearchHistory(place.formatted_address);
                            } catch (error) {
                                console.error('Error storing local search history:', error);
                            }
                            
                            // Store search history in database instead of Redis
                            try {
                                console.log('Storing search history for:', place.formatted_address); // Debug log
                                const result = await storeSearchHistory(place.formatted_address);
                                console.log('Search history store result:', result); // Debug log
                            } catch (error) {
                                console.error('Error storing search history:', error);
                            }
                        } else {
                            updateMarketAnalysisState({ searchPlace: '', errorState: {name: stateName || '', abbreviation: stateCode || ''} })
                        }
                    }
                }
            );
            // Create cleanup function
            cleanupFn = () => {
                google.maps.event.removeListener(listener);
                observer.disconnect();
                window.removeEventListener('resize', handleResize);
                autocompleteRef.current = null;
                geocoderRef.current = null;
            };
        };
        
        // Start initialization
        initAutocomplete();
        
        // Cleanup function
        return () => {
            if (timeoutId) clearTimeout(timeoutId);
            if (cleanupFn) cleanupFn();
        };
    }, [selectedStates]);
    
    // Function to get place details from a hardcoded address
    const getPlaceFromAddress = (address: string) => {
        if (!geocoderRef.current || !window.google?.maps) {
            console.error("Google Maps API not loaded yet");
            return;
        }
        
        geocoderRef.current.geocode({ address }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
                const place = {
                    address_components: results[0].address_components,
                    formatted_address: results[0].formatted_address,
                    geometry: results[0].geometry,
                };
                
                // Extract state from address components
                const stateComponent = place.address_components?.find(
                    component => component.types.includes('administrative_area_level_1')
                );
                
                const stateCode = stateComponent?.short_name;
                const stateName = stateComponent?.long_name;
                
                // Check if the state is in the selectedStates list
                const stateIsSelected = selectedStates.some(
                    state => state.abbreviation === stateCode || state.name === stateName
                );
                
                updateMarketAnalysisState({ searchPlace: place });

                // Store search history locally in cookies
                try {
                    addToSearchHistory(place.formatted_address);
                } catch (error) {
                    console.error('Error storing local search history:', error);
                }

                // Update the input field to show the formatted address
                if (inputRef.current) {
                    inputRef.current.value = place.formatted_address;
                }
            } else {
                console.error("Geocoding failed:", status);
            }
        });
    };

    useEffect(() => {
        if (marketAddress) {
            getPlaceFromAddress(marketAddress)
        }
    }, [marketAddress])

    // Add custom styles for Google Places dropdown to replace emoji with pin icon in gray background
    useEffect(() => {
        const addCustomStyles = () => {
            // Check if styles already exist
            if (document.getElementById('custom-places-styles')) return;
            
            const style = document.createElement('style');
            style.id = 'custom-places-styles';
            style.textContent = `
                .pac-container {
                    border-radius: 12px !important;
                    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
                    border: 1px solid #e5e7eb !important;
                    font-family: inherit !important;
                    z-index: 1100 !important;
                    transform: none !important;
                    margin-top: 4px !important;
                }
                
                .pac-container .pac-item {
                    padding: 12px !important;
                    border-bottom: 1px solid #f3f4f6 !important;
                    font-family: inherit !important;
                }
                
                .pac-container .pac-item:hover {
                    background-color: #f9fafb !important;
                }
                
                .pac-container .pac-item .pac-icon {
                    background-image: none !important;
                    width: 32px !important;
                    height: 32px !important;
                    margin-right: 12px !important;
                    position: relative !important;
                    background-color: #f3f4f6 !important;
                    border-radius: 8px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
                
                .pac-container .pac-item .pac-icon::before {
                    content: "" !important;
                    width: 16px !important;
                    height: 16px !important;
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23666666' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z'%3E%3C/path%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 11a3 3 0 11-6 0 3 3 0 016 0z'%3E%3C/path%3E%3C/svg%3E") !important;
                    background-size: contain !important;
                    background-repeat: no-repeat !important;
                    background-position: center !important;
                }
                
                .pac-container .pac-item .pac-icon-marker::before {
                    content: "" !important;
                    width: 16px !important;
                    height: 16px !important;
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23666666' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z'%3E%3C/path%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 11a3 3 0 11-6 0 3 3 0 016 0z'%3E%3C/path%3E%3C/svg%3E") !important;
                    background-size: contain !important;
                    background-repeat: no-repeat !important;
                    background-position: center !important;
                }
                
                .pac-container .pac-item .pac-item-query {
                    font-weight: 500 !important;
                    color: #111827 !important;
                    font-family: inherit !important;
                }
                
                .pac-container .pac-item .pac-matched {
                    font-weight: 600 !important;
                    color: #7c3aed !important;
                }
            `;
            document.head.appendChild(style);
        };

        // Add styles when component mounts
        addCustomStyles();

        // Cleanup function to remove styles when component unmounts
        return () => {
            const existingStyles = document.getElementById('custom-places-styles');
            if (existingStyles) {
                existingStyles.remove();
            }
        };
    }, []);

    const [isFocused, setIsFocused] = useState(false);

    return (
        <div className={`${isSearchPageStart ? 'px-0' : 'px-4'}`}>
            <div className="relative group">
                {/* Modern Search Input */}
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input
                        ref={inputRef}
                        id="address-search"
                        type="text"
                        value={typeof searchPlace === 'string' 
                          ? searchPlace 
                          : searchPlace?.formatted_address || ''}
                        onChange={(e) => updateMarketAnalysisState({ searchPlace: e.target.value })}
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                        placeholder={currentPlaceholder}
                        className={`
                            w-full pl-14 pr-6 py-5 text-lg font-medium
                            bg-gray-100 text-gray-800 placeholder-gray-500
                            rounded-2xl transition-all duration-300 ease-out
                            focus:outline-none focus:bg-gray-50
                            ${isFocused ? 'shadow-lg' : 'shadow-sm hover:shadow-md'}
                            border-0 relative z-10 antialiased
                        `}
                        style={{ 
                            fontSmooth: 'always', 
                            WebkitFontSmoothing: 'antialiased',
                            MozOsxFontSmoothing: 'grayscale'
                        }}
                    />
                    
                </div>
            </div>
        </div>
    )
}