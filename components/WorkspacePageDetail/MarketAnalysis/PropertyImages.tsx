import React, { useState, useEffect, memo, useRef } from 'react';
import { searchPropertyImages, ImageSearchResult, PropertyImageSearchResponse } from '@/actions/marketAnalysisActions/imageSearchActions';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import { useSearchParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUpload, faCloudUploadAlt, faTimes } from '@fortawesome/free-solid-svg-icons';
import Spinner from "@/components/UI/Spinner";

interface PropertyImagesProps {
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  propertyType?: string;
  isLoading?: boolean;
}

// Default fallback image as base64 data URI (simple house icon)
const DEFAULT_IMAGE = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Cpath d='M50,20L20,45v35h60V45L50,20z M70,70H30V50h40V70z' fill='%23d9d9d9'/%3E%3C/svg%3E";

/**
 * Displays a single property image fetched from Serper API
 */
const PropertyImages: React.FC<PropertyImagesProps> = ({ 
  address, 
  city = '',
  state = '',
  zipCode = '', 
  propertyType = 'house',
  isLoading = false 
}) => {
  const [image, setImage] = useState<string | null>(null);
  const [imageResult, setImageResult] = useState<ImageSearchResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [usedFallback, setUsedFallback] = useState(false);
  const { mainImage, propertyImage, updateMarketAnalysisState } = useMarketAnalysis()
  const searchParams = useSearchParams()
  const addressId = searchParams.get('addressId')
  const [showUploadModal, setShowUploadModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  // Fetch images using our image search service
  useEffect(() => {
    if (!address || isLoading) return;
    
    // Set a timeout to use fallback image if API takes too long
    const fallbackTimer = setTimeout(() => {
      if (loading && !image) {
        console.log('Image search timeout - using fallback image');
        setImage(DEFAULT_IMAGE);
        setLoading(false);
        setUsedFallback(true);
      }
    }, 8000); // 8 second timeout
    
    const fetchPropertyImages = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching property image for:', { address, city, state, zipCode });
        
        // Clean and validate the address
        const cleanAddress = address.trim();
        const cleanCity = city?.trim() || '';
        const cleanState = state?.trim() || '';
        const cleanZipCode = zipCode?.trim() || '';
        
        if (!cleanAddress && !cleanCity) {
          setError('Address or city is required to search for property images');
          setImage(DEFAULT_IMAGE); // Use fallback image
          setLoading(false);
          setUsedFallback(true);
          return;
        }
        
        // Extract address components if not provided
        let searchCity = cleanCity;
        let searchState = cleanState;
        let searchZip = cleanZipCode;
        let streetAddress = cleanAddress;
        
        // If we have a full address but no city/state/zip, try to extract them
        if ((!cleanCity || !cleanState || !cleanZipCode) && cleanAddress && cleanAddress.includes(',')) {
          const addressParts = cleanAddress.split(',');
          
          // First part is assumed to be street address
          streetAddress = addressParts[0].trim();
          
          // Extract city, state, zip from the remaining parts if possible
          if (!cleanCity && addressParts.length >= 2) {
            searchCity = addressParts[1].trim();
          }
          
          if ((!cleanState || !cleanZipCode) && addressParts.length >= 3) {
            const lastPart = addressParts[addressParts.length - 1].trim();
            const stateZipParts = lastPart.split(' ');
            
            if (!cleanState && stateZipParts.length > 0) {
              searchState = stateZipParts[0].trim();
            }
            
            if (!cleanZipCode && stateZipParts.length > 1) {
              searchZip = stateZipParts[1].trim();
            }
          }
        }
        
        // Fetch the property image using our service with a timeout
        const fetchPromise = searchPropertyImages(
          streetAddress,
          searchCity,
          searchState,
          searchZip,
          propertyType,
          1 // Only need one image
        );
        
        // Create a timeout promise 
        const timeoutPromise = new Promise<PropertyImageSearchResponse>((_, reject) => {
          setTimeout(() => reject(new Error('API request timeout')), 7000);
        });
        
        // Race the fetch against the timeout
        let searchResponse: PropertyImageSearchResponse;
        try {
          searchResponse = await Promise.race([fetchPromise, timeoutPromise]);
        } catch (timeoutErr) {
          console.warn('Property image search timed out:', timeoutErr);
          if (!usedFallback) {
            setImage(DEFAULT_IMAGE);
            setUsedFallback(true);
          }
          setLoading(false);
          return;
        }
        
        const results = searchResponse.images;
        const allImageUrls = searchResponse.allImageUrls;
        
        if (results && results.length > 0) {
          // Set image result
          const result = results[0];
          setImageResult(result);
          
          // Extract image URL and validate it
          if (result.image && result.image.startsWith('http')) {
            setImage(result.image);
            updateMarketAnalysisState({ 
              mainImage: result.image,
              allImageUrls: allImageUrls
            });
            setError(null);
          } else if (result.thumbnail && result.thumbnail.startsWith('http')) {
            // Use thumbnail as fallback if main image is invalid
            setImage(result.thumbnail);
            updateMarketAnalysisState({ 
              mainImage: result.thumbnail,
              allImageUrls: allImageUrls
            });
            setError(null);
          } else if (result.url && result.url.startsWith('http')) {
            // Use URL as last resort
            setImage(result.url);
            updateMarketAnalysisState({ 
              mainImage: result.url,
              allImageUrls: allImageUrls
            });
            setError(null);
          } else {
            setError('No valid image found for this property');
            setImage(DEFAULT_IMAGE); // Use fallback image
            setUsedFallback(true);
          }
        } else {
          // If no results and we haven't tried multiple times, retry with different query
          if (retryCount < 2) {
            setRetryCount(prev => prev + 1);
            // Try again with simplified query
            try {
              console.log('Retrying with simplified query...');
              const retryResponse = await searchPropertyImages(
                `${streetAddress} ${searchCity}`, 
                '', 
                searchState, 
                '', 
                'building',
                1
              );
              
              const retryResults = retryResponse.images;
              const retryAllImageUrls = retryResponse.allImageUrls;
              
              if (retryResults && retryResults.length > 0) {
                const result = retryResults[0];
                setImageResult(result);
                
                if (result.image && result.image.startsWith('http')) {
                  setImage(result.image);
                  updateMarketAnalysisState({ 
                    mainImage: result.image,
                    allImageUrls: retryAllImageUrls
                  });
                  setError(null);
                } else if (result.thumbnail && result.thumbnail.startsWith('http')) {
                  setImage(result.thumbnail);
                  updateMarketAnalysisState({ 
                    mainImage: result.thumbnail,
                    allImageUrls: retryAllImageUrls
                  });
                  setError(null);
                } else {
                  console.log('No valid image URL in results, using fallback');
                  setError('No valid image found for this property');
                  setImage(DEFAULT_IMAGE); // Use fallback image
                  setUsedFallback(true);
                }
              } else {
                console.log('No results found in retry, using fallback');
                setError('No image found for this property');
                setImage(DEFAULT_IMAGE); // Use fallback image
                setUsedFallback(true);
              }
            } catch (retryErr) {
              console.log('Error during retry:', retryErr);
              setError('Failed to load property image');
              setImage(DEFAULT_IMAGE); // Use fallback image
              setUsedFallback(true);
            }
          } else {
            console.log('Max retries reached, using fallback');
            setError('No image found for this property');
            setImage(DEFAULT_IMAGE); // Use fallback image
            setUsedFallback(true);
          }
        }
      } catch (err) {
        console.log('Error fetching image:', err);
        setError('Failed to load property image');
        setImage(DEFAULT_IMAGE); // Use fallback image
        setUsedFallback(true);
      } finally {
        setLoading(false);
        clearTimeout(fallbackTimer);
      }
    };
    
    fetchPropertyImages();
    
    return () => {
      clearTimeout(fallbackTimer);
    };
  }, [address, city, state, zipCode, propertyType, isLoading, retryCount]);

  // Handle direct internet search
  const handleInternetSearch = () => {
    // Create a clean search query
    let searchQuery = address;
    
    if (city) searchQuery += `, ${city}`;
    if (state) searchQuery += `, ${state}`;
    if (zipCode) searchQuery += ` ${zipCode}`;
    
    searchQuery += ' property building';
    
    const encodedQuery = encodeURIComponent(searchQuery);
    window.open(`https://duckduckgo.com/?q=${encodedQuery}&ia=images&iax=images`, '_blank');
  };

  // Handle image file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setUploadedImage(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Trigger file input click
  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle image upload to database
  const handleUpload = async () => {
    if (!uploadedImage || !addressId) return;
    
    try {
      setUploading(true);
      
      // Here you would normally call an API to save the image
      // For example:
      // await savePropertyImage(addressId, uploadedImage);
      
      // For now, we'll simulate the upload and just update the state
      setTimeout(() => {
        updateMarketAnalysisState({ 
          propertyImage: uploadedImage,
          mainImage: uploadedImage,
        });
        
        setShowUploadModal(false);
        setUploading(false);
      }, 1000);
    } catch (error) {
      console.error('Error uploading image:', error);
      setUploading(false);
    }
  };

  // Upload Modal UI
  const renderUploadModal = () => {
    if (!showUploadModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-5 rounded-lg max-w-lg w-full">
          <div className="flex justify-between items-center m-4">
            <h3 className="text-lg font-medium">Upload Property Image</h3>
            <button 
              onClick={() => setShowUploadModal(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
          
          <div className="mb-4">
            {uploadedImage ? (
              <div className="relative">
                <img 
                  src={uploadedImage} 
                  alt="Upload preview" 
                  className="w-full h-64 object-cover border rounded-lg"
                />
                <button
                  onClick={() => setUploadedImage(null)}
                  className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                >
                  <FontAwesomeIcon icon={faTimes} className="h-4 w-4 text-gray-500" />
                </button>
              </div>
            ) : (
              <div 
                className="border-2 border-dashed border-gray-300 p-8 text-center rounded-lg cursor-pointer hover:bg-gray-50"
                onClick={handleBrowseClick}
              >
                <div className="flex flex-col items-center">
                  <FontAwesomeIcon icon={faCloudUploadAlt} className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-gray-600 mb-2">Drag and drop your image here, or click to browse</p>
                  <button 
                    className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                  >
                    Browse Files
                  </button>
                </div>
              </div>
            )}
            <input 
              type="file" 
              ref={fileInputRef} 
              className="hidden" 
              accept="image/*" 
              onChange={handleFileChange}
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <button 
              onClick={() => setShowUploadModal(false)}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button 
              onClick={handleUpload}
              disabled={!uploadedImage || uploading}
              className={`px-4 py-2 rounded text-white ${
                !uploadedImage || uploading
                  ? 'bg-indigo-400 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700'
              }`}
            >
              {uploading ? 'Uploading...' : 'Upload Image'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  if ((isLoading || loading) && !addressId) {
    return (
      <div className="h-full flex items-center justify-center bg-black">
        <div className="flex flex-col items-center">
            <Spinner size="lg" text="Loading image..." />
        </div>
      </div>
    );
  }

  if(addressId && !propertyImage){
    return (
      <div className="h-full flex items-center justify-center bg-black">
        <Spinner size="sm" />
      </div>
    );
  }

  if (error && !image) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-black p-4">
        <svg className="w-10 h-10 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <p className="text-gray-300 text-center text-sm mb-3">{error || 'No image found'}</p>
        <div className="flex space-x-2">
          <button 
            onClick={handleInternetSearch}
            className="px-3 py-1 bg-indigo-600 text-white rounded text-sm"
          >
            Search Images Online
          </button>
          <button
            onClick={() => setShowUploadModal(true)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center"
          >
            <FontAwesomeIcon icon={faUpload} className="mr-1 h-3 w-3" />
            Upload Image
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-black relative overflow-hidden">
      {/* Render the upload modal */}
      {renderUploadModal()}
      
      {/* Main image */}
      <div className="relative flex-grow">
        {
          propertyImage && addressId ?
            <>
              <img
                src={propertyImage}
                alt="Property Image"
                className="absolute inset-0 h-full w-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.onerror = null; // Prevent infinite error loop
                  console.log('Image loading error, using fallback');
                  // Use a base64 SVG as fallback to ensure it always works
                  target.src = DEFAULT_IMAGE;
                }}
              />
            </>
            : null
        }

        {
          !addressId && (
            <img
              src={image || DEFAULT_IMAGE}
              alt={imageResult?.title || `Property at ${address}`}
              className="absolute inset-0 h-full w-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null; // Prevent infinite error loop
                console.log('Image loading error, using fallback');
                // Use a base64 SVG as fallback to ensure it always works
                target.src = DEFAULT_IMAGE;
              }}
            />
          )
        }
        
        <div className="absolute bottom-2 right-2 flex space-x-2">
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-green-600 text-white text-xs px-2 py-1 rounded flex items-center"
          >
            <FontAwesomeIcon icon={faUpload} className="mr-1 h-3 w-3" />
            Upload
          </button>
          <button
            onClick={handleInternetSearch}
            className="bg-indigo-600 text-white text-xs px-2 py-1 rounded"
          >
            More Images
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(PropertyImages); 