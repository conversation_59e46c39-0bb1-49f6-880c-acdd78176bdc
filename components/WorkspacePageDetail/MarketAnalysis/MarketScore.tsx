import { useState, useEffect } from 'react';
import { generateMarketScore, MarketScore as MarketScoreType } from '@/actions/marketScoreActions';
import { NewsArticle } from '@/actions/newsActions';
import { FredChart } from '@/actions/fredActions';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRobot, faArrowTrendUp, faArrowTrendDown, faEquals, faRefresh, faFire, faSnowflake, faThermometerHalf } from '@fortawesome/free-solid-svg-icons';
import Spinner from '@/components/UI/Spinner';

interface MarketScoreProps {
  newsArticles: NewsArticle[];
  fredCharts: FredChart[];
}

export default function MarketScore({ newsArticles, fredCharts }: MarketScoreProps) {
  const [marketScore, setMarketScore] = useState<MarketScoreType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'analysis' | 'factors'>('analysis');

  useEffect(() => {
    // Reset market score when data changes to force regeneration
    setMarketScore(null);
    setError(null);
    
    // Only generate if we have data
    if (newsArticles.length > 0 || fredCharts.length > 0) {
      generateScore();
    }
  }, [newsArticles, fredCharts]); // Depend on the actual arrays, not just their length

  const generateScore = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const score = await generateMarketScore(newsArticles, fredCharts);
      setMarketScore(score);
    } catch (err) {
      console.error('Error generating market score:', err);
      setError('Failed to generate market score. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 81) return '#ef4444'; // red-500
    if (score >= 61) return '#f97316'; // orange-500
    if (score >= 41) return '#eab308'; // yellow-500
    if (score >= 21) return '#3b82f6'; // blue-500
    return '#1e40af'; // blue-700
  };

  const getScoreColorEnd = (score: number) => {
    if (score >= 81) return '#f97316'; // orange-500
    if (score >= 61) return '#eab308'; // yellow-500
    if (score >= 41) return '#22c55e'; // green-500
    if (score >= 21) return '#06b6d4'; // cyan-500
    return '#1e3a8a'; // blue-900
  };

  const getScoreLabel = (score: number) => {
    if (score >= 81) return 'Very Hot';
    if (score >= 61) return 'Hot';
    if (score >= 41) return 'Neutral';
    if (score >= 21) return 'Cold';
    return 'Very Cold';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 61) return faFire;
    if (score >= 41) return faThermometerHalf;
    return faSnowflake;
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'Bullish': return faArrowTrendUp;
      case 'Bearish': return faArrowTrendDown;
      default: return faEquals;
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'Bullish': return 'text-green-600';
      case 'Bearish': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="flex flex-col items-center justify-center py-10">
          <Spinner size="lg" className="mb-3" />
          <p className="text-sm text-gray-600">Analyzing market conditions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="text-center py-10">
          <FontAwesomeIcon icon={faRobot} className="text-gray-400 text-4xl mb-4" />
          <p className="text-red-600 mb-2">{error}</p>
          <button 
            onClick={generateScore}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!marketScore) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="text-center py-10">
          <FontAwesomeIcon icon={faRobot} className="text-gray-400 text-4xl mb-4" />
          <p className="text-gray-600">No data available for market analysis.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-6">
        {/* Main Score Display */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Left Column - Circular Rating */}
          <div className="flex justify-center lg:justify-start">
            <div className="relative">
              {/* Circular Progress */}
              <div className="relative w-48 h-48">
                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="#e5e7eb"
                    strokeWidth="8"
                    fill="none"
                  />
                  {/* Progress circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="url(#scoreGradient)"
                    strokeWidth="8"
                    fill="none"
                    strokeLinecap="round"
                    strokeDasharray={`${2 * Math.PI * 40}`}
                    strokeDashoffset={`${2 * Math.PI * 40 * (1 - marketScore.score / 100)}`}
                    className="transition-all duration-1000 ease-out"
                  />
                  {/* Gradient definition */}
                  <defs>
                    <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor={getScoreColor(marketScore.score)} />
                      <stop offset="100%" stopColor={getScoreColorEnd(marketScore.score)} />
                    </linearGradient>
                  </defs>
                </svg>
                
                {/* Score text in center */}
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-4xl font-bold text-gray-900 mb-1">
                    {marketScore.score}
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    {getScoreLabel(marketScore.score)}
                  </div>
                  <FontAwesomeIcon 
                    icon={getScoreIcon(marketScore.score)} 
                    className={`text-2xl ${marketScore.score >= 61 ? 'text-red-500' : marketScore.score >= 41 ? 'text-yellow-500' : 'text-blue-500'}`}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Metrics Stack */}
          <div className="flex flex-col justify-center space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Sentiment</span>
                <div className={`text-l font-semibold ${getSentimentColor(marketScore.sentiment)}`}>
                  {marketScore.sentiment}
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Confidence</span>
                <div className="text-l font-semibold text-indigo-600">
                  {marketScore.confidence}%
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Updated</span>
                <div className="text-l font-medium text-gray-600">
                  {new Date(marketScore.lastUpdated).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabbed Container */}
        <div className="mb-6">
          <div className="border-b border-gray-200 mb-4">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('analysis')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analysis'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Market Analysis
              </button>
              <button
                onClick={() => setActiveTab('factors')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'factors'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Key Factors
              </button>
            </nav>
          </div>

          <div className="tab-content">
            {activeTab === 'analysis' && (
              <div>
                <p className="text-gray-700 leading-relaxed">
                  {marketScore.summary}
                </p>
              </div>
            )}
            
            {activeTab === 'factors' && (
              <div className="grid gap-2 h-60 overflow-y-auto">
                {marketScore.keyFactors.slice(0, 5).map((factor, index) => (
                  <div key={index} className="flex items-start gap-2 p-2 bg-blue-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-sm text-blue-800 mt-0.5">{factor}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 mt-4">
            <button
            onClick={generateScore}
            disabled={isLoading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faRefresh} className={`text-xs ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          </div>
          
        </div>
      </div>
    </div>
  );
} 