import { MarketDaum } from "@/types/UnitDataType";
import { formatCurrency, formatMarketDate } from "@/utils/formatters";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faEye, faInfoCircle, faQuestionCircle, faSort, faSortUp, faSortDown, faChartBar, faImage, faBed, faBath } from "@fortawesome/free-solid-svg-icons";
import React from "react";

// Define the SortColumn type to match the one in PropertyUnits.tsx
type SortColumn = 'unit' | 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price' | 'document_recorded_date';
type SortDirection = 'asc' | 'desc';

interface PropertyUnitsTableProps {
    units: any[];
    marketData: MarketDaum[] | null;
    addressId: string | null;
    sortConfig: {
        column: SortColumn | null;
        direction: SortDirection;
    };
    requestSort: (column: SortColumn) => void;
    getSortIcon: (column: SortColumn) => React.ReactElement;
    infoMode: 'unit' | 'market';
    recordDate: Date;
    selectedUnitId?: string | null;
    onUnitClick?: (unitId: string) => void;
}

export default function PropertyUnitsTable({ 
    units, 
    marketData, 
    addressId, 
    sortConfig, 
    requestSort, 
    getSortIcon,
    infoMode,
    recordDate,
    selectedUnitId,
    onUnitClick
}: PropertyUnitsTableProps) {
    // Prepare table headers based on infoMode
    const getTableHeaders = () => {
        if (infoMode === 'unit') {
            return (
                <tr>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-20"
                        onClick={() => requestSort('unit')}
                    >
                        <div className="flex items-center">
                            Unit {getSortIcon('unit')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-16"
                        onClick={() => requestSort('beds')}
                    >
                        <div className="flex items-center">
                            Beds {getSortIcon('beds')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-16"
                        onClick={() => requestSort('baths')}
                    >
                        <div className="flex items-center">
                            Baths {getSortIcon('baths')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-20"
                        onClick={() => requestSort('sqft')}
                    >
                        <div className="flex items-center">
                            Sq Ft {getSortIcon('sqft')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-24"
                        onClick={() => requestSort('rent')}
                    >
                        <div className="flex items-center">
                            Rent {getSortIcon('rent')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-24"
                        onClick={() => requestSort('price')}
                    >
                        <div className="flex items-center">
                            Price {getSortIcon('price')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-24"
                        onClick={() => requestSort('hoa_fee')}
                    >
                        <div className="flex items-center">
                            HOA Fee {getSortIcon('hoa_fee')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-28"
                        onClick={() => requestSort('document_recorded_date')}
                    >
                        <div className="flex items-center">
                            Record Date {getSortIcon('document_recorded_date')}
                        </div>
                    </th>
                </tr>
            );
        } else {
            // Market data headers
            return (
                <tr>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-20"
                        onClick={() => requestSort('unit')}
                    >
                        <div className="flex items-center">
                            Unit {getSortIcon('unit')}
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-28"
                    >
                        <div className="flex items-center">
                            Property Type
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-24"
                    >
                        <div className="flex items-center">
                            Year Built
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-28"
                    >
                        <div className="flex items-center">
                            Last Sale Date
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-28"
                    >
                        <div className="flex items-center">
                            Last Sale Price
                        </div>
                    </th>
                    <th 
                        scope="col" 
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer w-28"
                        onClick={() => requestSort('document_recorded_date')}
                    >
                        <div className="flex items-center">
                            Record Date {getSortIcon('document_recorded_date')}
                        </div>
                    </th>
                </tr>
            );
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Compact header */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-3 py-2 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
                        <FontAwesomeIcon icon={faChartBar} className="text-gray-600 h-4 w-4" />
                        <span>Property Units Overview</span>
                        <FontAwesomeIcon 
                            icon={faInfoCircle} 
                            className="text-gray-400 h-3 w-3" 
                            title="Comprehensive view of all property units with market data and specifications" 
                        />
                    </h3>
                    <div className="text-xs text-gray-600 flex items-center gap-1">
                        <span>{units.length} units</span>
                        <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-3 w-3" title={`Total of ${units.length} property units`} />
                    </div>
                </div>
            </div>

            {/* Compact table */}
            <div className="overflow-x-auto">
                <table className="w-full table-fixed">
                    <thead className="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th className="w-20 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Unit number or identifier">
                                <div className="flex items-center gap-1">
                                    <span>Unit</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                            <th className="w-16 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Number of bedrooms">
                                <div className="flex items-center gap-1">
                                    <FontAwesomeIcon icon={faBed} className="text-gray-500 h-3 w-3" />
                                    <span>Beds</span>
                                </div>
                            </th>
                            <th className="w-16 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Number of bathrooms">
                                <div className="flex items-center gap-1">
                                    <FontAwesomeIcon icon={faBath} className="text-gray-500 h-3 w-3" />
                                    <span>Baths</span>
                                </div>
                            </th>
                            <th className="w-20 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Unit area in square feet">
                                <div className="flex items-center gap-1">
                                    <span>Sq Ft</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                            <th className="w-24 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Monthly rental amount">
                                <div className="flex items-center gap-1">
                                    <span>Rent</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                            <th className="w-24 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Current listing or sale price">
                                <div className="flex items-center gap-1">
                                    <span>Price</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                            <th className="w-24 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Monthly HOA fee">
                                <div className="flex items-center gap-1">
                                    <span>HOA</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                            <th className="w-28 px-3 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider" title="Date when unit information was recorded">
                                <div className="flex items-center gap-1">
                                    <span>Record Date</span>
                                    <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                        {units.map((unit, index) => {
                            const unitMarketData = marketData?.find((md: MarketDaum) => md.unit_id === unit.id) || null;
                            const isSelected = selectedUnitId === unit.id;
                            
                            return (
                                <tr 
                                    key={unit.id || index}
                                    onClick={() => onUnitClick && onUnitClick(unit.id)}
                                    className={`cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
                                        isSelected 
                                            ? 'bg-indigo-50 border-l-4 border-l-indigo-500' 
                                            : 'border-l-4 border-l-transparent'
                                    }`}
                                    title={`Click to select and view details for unit ${unit.unit}`}
                                >
                                    <td className="w-20 px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div className="flex items-center gap-1">
                                            <span>{unit.unit}</span>
                                            {isSelected && (
                                                <div className="w-2 h-2 rounded-full bg-indigo-500 animate-pulse" title="Currently selected unit" />
                                            )}
                                        </div>
                                    </td>
                                    <td className="w-16 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`${unit.beds || '0'} bedrooms`}>
                                        {unit.beds || '0'}
                                    </td>
                                    <td className="w-16 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`${unit.baths || '0'} bathrooms`}>
                                        {unit.baths || '0'}
                                    </td>
                                    <td className="w-20 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`${unit.sqft ? `${unit.sqft} sq ft` : 'N/A'}`}>
                                        {unit.sqft ? `${unit.sqft} sq ft` : 'N/A'}
                                    </td>
                                    <td className="w-24 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`Monthly rent: ${unit.rent ? formatCurrency(Number(unit.rent)) : 'N/A'}`}>
                                        {unit.rent ? formatCurrency(Number(unit.rent)) : 'N/A'}
                                    </td>
                                    <td className="w-24 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`Sale price: ${unit.price ? formatCurrency(Number(unit.price)) : 'N/A'}`}>
                                        {unit.price ? formatCurrency(Number(unit.price)) : 'N/A'}
                                    </td>
                                    <td className="w-24 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`Monthly HOA fee: ${unit.hoa_fee ? formatCurrency(Number(unit.hoa_fee)) : 'N/A'}`}>
                                        {unit.hoa_fee ? formatCurrency(Number(unit.hoa_fee)) : 'N/A'}
                                    </td>
                                    <td className="w-28 px-3 py-2 whitespace-nowrap text-sm text-gray-700" title={`Recorded on: ${unit.document_recorded_date ? formatMarketDate(unit.document_recorded_date) : 'N/A'}`}>
                                        {unit.document_recorded_date ? formatMarketDate(unit.document_recorded_date) : 'N/A'}
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
            
            {units.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                    <FontAwesomeIcon icon={faChartBar} className="h-12 w-12 text-gray-300 mb-3" />
                    <p className="text-sm font-medium mb-1">No units available</p>
                    <p className="text-xs text-gray-400">Start by adding unit information to see data here</p>
                </div>
            )}
        </div>
    );
} 