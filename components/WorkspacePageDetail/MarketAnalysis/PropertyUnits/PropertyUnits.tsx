import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useSearchParams, usePathname } from "next/navigation";
import ProppertyUnitsItem from "./PropertyUnitsItem";
import ProprtyUnitsItemEdittable from "./ProprtyUnitsItemEdittable";
import PropertyUnitsTable from "./PropertyUnitsTable";
import { getUnitData } from "@/actions/marketAnalysisActions/unitDataActions";
import { addUnit, updateUnits } from "@/actions/propetyUnitsActions";
import { MarketDaum } from "@/types/UnitDataType";
import { addUnit as addMarketUnit } from "@/actions/propertyMarketDataActions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSort, faSortUp, faSortDown, faPlus, faChevronRight, faChevronLeft, faTable, faIdCard, faCalendarAlt, faRefresh, faInfoCircle, faQuestionCircle, faBuilding, faUsers, faExpand, faCompress, faMaximize } from "@fortawesome/free-solid-svg-icons";
import { formatDate } from "@/utils/formatters";
import React, { useState, useMemo, useRef, useEffect } from "react";
import Spinner from "@/components/UI/Spinner";
import PropertyUnitsImages from "../PropertyUnitsProperty/PropertyUnitsImages/PropertyUnitsImages";
import PropertyUnitsTags from "../PropertyUnitsProperty/PropertyUnitsTags";
import ProperyUnitsInfo from "../PropertyUnitsProperty/ProperyUnitsInfo";
import PropertyUnitsMarketDataItemEditable from "./PropertyUnitsMarketDataItemEditable";

type SortColumn = 'unit' | 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price' | 'document_recorded_date';
type SortDirection = 'asc' | 'desc';
type ViewMode = 'cards' | 'table' | 'bootstrap';
type InfoMode = 'unit' | 'market';

export default function PropertyUnits() {
    const { unitData, marketData, isLoadingUnitData, updateMarketAnalysisState } = useMarketAnalysis();
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const addressId = searchParams.get('addressId');
    const marketAddress = searchParams.get('marketAddress');
    const containerRef = useRef<HTMLDivElement>(null);
    const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);
    const [viewMode, setViewMode] = useState<ViewMode>('bootstrap');
    const [infoMode, setInfoMode] = useState<InfoMode>('unit');
    const [showCalendar, setShowCalendar] = useState(false);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [isMarketDataCollapsed, setIsMarketDataCollapsed] = useState(false);
    const [showFullscreenGallery, setShowFullscreenGallery] = useState(false);
    const calendarRef = useRef<HTMLDivElement>(null);
    
    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        column: SortColumn | null;
        direction: SortDirection;
    }>({
        column: 'unit',
        direction: 'asc'
    });

    // Close calendar when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
                setShowCalendar(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleRefresh = () => {
        updateMarketAnalysisState({isLoadingUnitData: true})

        getUnitData(marketAddress as string || '').then((data) => {
            const unitData = data?.data?.data?.address?.cherre_address__address?.parcel_boundary_v2__address_point

            updateUnits(addressId as string, unitData).then((data) => {
                updateMarketAnalysisState({unitData: data, isLoadingUnitData: false})
            })
        })
    }

    const handleAddUnit = () => {
        if (!addressId) return;
        
        addUnit(addressId).then((data) => {
            updateMarketAnalysisState({
                unitData: data
            });
            
            // Also add market data for the new unit
            addMarketUnit(addressId).then((marketData) => {
                updateMarketAnalysisState({
                    marketData: marketData
                });
            });
        });
    }
    
    // Sort function for the table
    const requestSort = (column: SortColumn) => {
        let direction: SortDirection = 'asc';
        
        if (sortConfig.column === column) {
            direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
        }
        
        setSortConfig({ column, direction });
    };
    
    // Get sorting icon for a column - make it accept any string but internally validate
    const getSortIcon = (column: any) => {
        // Ensure column is a valid SortColumn before using it
        const validColumn = column as SortColumn;
        
        if (sortConfig.column !== validColumn) {
            return <FontAwesomeIcon icon={faSort} className="ml-1 text-gray-400 h-3 w-3" />;
        }
        
        return sortConfig.direction === 'asc' 
            ? <FontAwesomeIcon icon={faSortUp} className="ml-1 text-indigo-600 h-3 w-3" />
            : <FontAwesomeIcon icon={faSortDown} className="ml-1 text-indigo-600 h-3 w-3" />;
    };
    
    // Apply sorting to the unit data
    const sortedUnits = useMemo(() => {
        if (!unitData || !sortConfig.column) return unitData;
        
        return [...unitData].sort((a, b) => {
            // Handle numbers and strings differently
            let aValue = a[sortConfig.column as keyof typeof a];
            let bValue = b[sortConfig.column as keyof typeof b];
            
            // Convert to numbers for numerical fields
            if (['beds', 'baths', 'hoa_fee', 'sqft', 'rent', 'price'].includes(sortConfig.column as string)) {
                aValue = parseFloat(aValue as string) || 0;
                bValue = parseFloat(bValue as string) || 0;
            } else if (sortConfig.column === 'document_recorded_date') {
                // Handle dates
                aValue = aValue ? new Date(aValue as string).getTime() : 0;
                bValue = bValue ? new Date(bValue as string).getTime() : 0;
            } else {
                // Convert to strings for string comparison
                aValue = String(aValue || '').toLowerCase();
                bValue = String(bValue || '').toLowerCase();
            }
            
            if (aValue < bValue) {
                return sortConfig.direction === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortConfig.direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }, [unitData, sortConfig.column, sortConfig.direction]);

    // Auto-select first unit when unitData loads or changes
    useEffect(() => {
        if (sortedUnits && sortedUnits.length > 0 && !selectedUnitId) {
            setSelectedUnitId(sortedUnits[0].id);
        }
    }, [sortedUnits, selectedUnitId]);

    // Handle dropdown selection
    const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const unitId = e.target.value;
        setSelectedUnitId(unitId);
        
        // Scroll to the selected unit
        if (unitId && containerRef.current && viewMode === 'cards') {
            const unitElement = document.getElementById(`unit-${unitId}`);
            if (unitElement) {
                containerRef.current.scrollTo({
                    left: unitElement.offsetLeft - 20,
                    behavior: 'smooth'
                });
            }
        }
    };

    // Handle unit card click
    const handleUnitClick = (unitId: string) => {
        setSelectedUnitId(unitId);
    };

    // Handle scroll to next/previous unit
    const scrollToUnit = (direction: 'next' | 'prev') => {
        if (!containerRef.current || !sortedUnits || sortedUnits.length === 0) return;
        
        const container = containerRef.current;
        const scrollPosition = container.scrollLeft;
        const containerWidth = container.clientWidth;
        
        if (direction === 'next') {
            container.scrollBy({ left: containerWidth - 40, behavior: 'smooth' });
        } else {
            container.scrollBy({ left: -(containerWidth - 40), behavior: 'smooth' });
        }
    };

    // Toggle between card and table view
    const toggleViewMode = () => {
        setViewMode(viewMode === 'cards' ? 'table' : 'cards');
    };

    // Handle date selection from calendar
    const handleDateSelect = (date: Date) => {
        setSelectedDate(date);
        setShowCalendar(false);
        // Here you would update the record date in your data
        // This would depend on your specific implementation
    };

    // Get the selected unit data
    const selectedUnit = sortedUnits?.find(unit => unit.id === selectedUnitId);

    // Update unit helper
    const handleUpdateUnit = (updatedUnit: any) => {
        const updatedUnits = sortedUnits?.map(unit => 
            unit.id === updatedUnit.id ? updatedUnit : unit
        );
        updateMarketAnalysisState({ unitData: updatedUnits });
    };

    // Determine if we're on the search page (hide Details view) or portfolio page (keep both views)
    const isSearchPage = pathname.includes('/search')

    if(!addressId){
        return(
            <div className="backdrop-blur-sm rounded-xl p-4 space-y-3">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faBuilding} className="text-indigo-600 h-4 w-4" />
                        </div>
                        <div>
                            <h3 className="text-sm font-semibold text-gray-800">Building Units</h3>
                            <p className="text-xs text-gray-500">Property unit management</p>
                        </div>
                    </div>
                    
                    <div className="ml-auto flex items-center gap-2">
                        <div className="bg-gray-100 text-gray-600 px-2.5 py-1 rounded-full text-xs font-medium">
                            {unitData?.length || 0} units
                        </div>
                        {isLoadingUnitData && (
                            <Spinner size="sm" />
                        )}
                    </div>
                </div>
            </div>  
        )
    }
    
    return (
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            {/* Calendar Modal */}
            {showCalendar && (
                <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                <div 
                    ref={calendarRef}
                        className="bg-white rounded-xl shadow-2xl p-6 max-w-sm w-full"
                    >
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <FontAwesomeIcon icon={faCalendarAlt} className="text-indigo-600 h-4 w-4" />
                                </div>
                                <h3 className="text-sm font-semibold text-gray-800">Select Record Date</h3>
                            </div>
                        <button 
                            onClick={() => setShowCalendar(false)}
                                className="w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
                        >
                                ×
                        </button>
                    </div>
                        
                        <input 
                            type="date" 
                            className="w-full p-3 rounded-lg border border-gray-200 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                            value={selectedDate.toISOString().split('T')[0]}
                            onChange={(e) => {
                                const newDate = new Date(e.target.value);
                                setSelectedDate(newDate);
                                handleDateSelect(newDate);
                            }}
                        />
                        
                        <div className="mt-4 flex justify-end">
                        <button 
                            onClick={() => handleDateSelect(selectedDate)}
                                className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
                        >
                            Apply Date
                        </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Header Section - Redesigned */}
            <div className="bg-gradient-to-r from-gray-50 to-indigo-50/30 px-4 py-3 sm:px-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                            <FontAwesomeIcon icon={faBuilding} className="text-indigo-600 h-5 w-5" />
                        </div>
                        <div className="flex-1">
                        <div className="flex items-center gap-2">
                                <h3 className="text-base font-semibold text-gray-800">Building Units</h3>
                                <FontAwesomeIcon 
                                    icon={faInfoCircle} 
                                    className="text-gray-400 h-3.5 w-3.5 cursor-help" 
                                    title="Manage property units, market data, and specifications" 
                                />
                            </div>
                            {sortedUnits && sortedUnits.length > 0 && (
                                <p className="text-sm text-gray-500 mt-0.5">
                                    {sortedUnits.length} unit{sortedUnits.length !== 1 ? 's' : ''} configured
                                </p>
                            )}
                        </div>
                        </div>
                        
                    <div className="flex items-center gap-2 flex-wrap">
                        {/* Unit Selection Dropdown */}
                        {sortedUnits && sortedUnits.length > 0 && (
                                    <select 
                                className="px-3 py-2 text-sm bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 min-w-[140px]"
                                        value={selectedUnitId || ''}
                                        onChange={handleUnitChange}
                                    >
                                        <option value="">Jump to unit...</option>
                                        {sortedUnits.map((unit) => (
                                            <option key={unit.id} value={unit.id}>
                                                Unit {unit.unit}
                                            </option>
                                        ))}
                                    </select>
                        )}
                    
                        {/* Action Buttons */}
                    <div className="flex items-center gap-1.5">
                        <button
                            onClick={handleRefresh}
                            disabled={isLoadingUnitData}
                                className="w-9 h-9 flex items-center justify-center text-gray-600 bg-white rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-all shadow-sm border border-gray-200"
                                title="Refresh unit data"
                        >
                            <FontAwesomeIcon 
                                icon={faRefresh} 
                                    className={`h-4 w-4 ${isLoadingUnitData ? 'animate-spin' : ''}`} 
                                />
                            </button>

                            <button
                                onClick={() => setShowCalendar(true)}
                                className="px-3 py-2 flex items-center gap-2 text-sm text-indigo-600 bg-white rounded-lg hover:bg-indigo-50 transition-all shadow-sm border border-gray-200"
                                title={`Record Date: ${formatDate(selectedDate)}`}
                            >
                                <FontAwesomeIcon icon={faCalendarAlt} className="h-3.5 w-3.5" />
                                <span className="hidden sm:inline text-xs">{formatDate(selectedDate)}</span>
                            </button>

                            {/* View Mode Toggle */}
                        {isSearchPage ? (
                                <button
                                    onClick={() => setViewMode('table')}
                                    className="px-3 py-2 flex items-center gap-1.5 text-xs font-medium bg-indigo-50 text-indigo-700 rounded-lg shadow-sm border border-gray-200"
                                >
                                    <FontAwesomeIcon icon={faTable} className="h-3 w-3" />
                                    <span className="hidden sm:inline">View</span>
                                </button>
                        ) : (
                                <div className="flex bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                                <button
                                        onClick={() => setViewMode('bootstrap')}
                                        className={`px-3 py-2 flex items-center gap-1.5 text-xs font-medium transition-all ${
                                            viewMode === 'bootstrap' 
                                                ? 'bg-indigo-50 text-indigo-700' 
                                                : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                    >
                                        <FontAwesomeIcon icon={faIdCard} className="h-3 w-3" />
                                    <span className="hidden sm:inline">Details</span>
                                </button>
                                <button
                                    onClick={() => setViewMode('table')}
                                        className={`px-3 py-2 flex items-center gap-1.5 text-xs font-medium transition-all ${
                                        viewMode === 'table' 
                                                ? 'bg-indigo-50 text-indigo-700' 
                                                : 'text-gray-600 hover:bg-gray-50'
                                    }`}
                                >
                                        <FontAwesomeIcon icon={faTable} className="h-3 w-3" />
                                    <span className="hidden sm:inline">View</span>
                                </button>
                            </div>
                        )}
                        
                        <button 
                            onClick={handleAddUnit}
                                className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all shadow-sm flex items-center gap-2"
                        >
                                <FontAwesomeIcon icon={faPlus} className="h-3.5 w-3.5" />
                            <span className="hidden sm:inline">Add Unit</span>
                            <span className="inline sm:hidden">Add</span>
                        </button>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Content Section */}
            <div className="bg-white">
            {isLoadingUnitData ? (
                    <div className="flex flex-col items-center justify-center py-12 px-4">
                        <Spinner size="lg" className="mb-4" />
                        <p className="text-sm font-medium text-gray-600 mb-1">Loading unit data...</p>
                        <p className="text-xs text-gray-500">Fetching latest market information</p>
                </div>
            ) : (
                <>
                    {sortedUnits && sortedUnits.length > 0 ? (
                            viewMode === 'table' ? (
                                <div className="p-4">
                                <PropertyUnitsTable 
                                    units={sortedUnits} 
                                    marketData={marketData} 
                                    addressId={addressId}
                                    sortConfig={sortConfig}
                                    requestSort={requestSort}
                                    getSortIcon={getSortIcon}
                                    infoMode={infoMode}
                                    recordDate={selectedDate}
                                    selectedUnitId={selectedUnitId}
                                    onUnitClick={handleUnitClick}
                                />
                            </div>
                            ) : viewMode === 'bootstrap' ? (
                                /* Bootstrap Layout */
                                <div className="flex flex-col lg:flex-row min-h-[calc(100vh-300px)] relative">
                                    {/* Units Sidebar - 2/12 on desktop, full width on mobile */}
                                    <div className="w-full lg:w-1/6 lg:h-full h-80 lg:border-r border-b lg:border-b-0 border-gray-200 bg-gray-50 overflow-hidden">
                                        <div className="p-3 h-full overflow-y-auto">
                                            <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                                <FontAwesomeIcon icon={faBuilding} className="h-4 w-4 text-indigo-600" />
                                                Units ({sortedUnits.length})
                                            </h4>
                                            <div className="space-y-2">
                                                {sortedUnits.map((unit: any) => (
                                                    <div
                                                        key={unit.id}
                                                        onClick={() => handleUnitClick(unit.id)}
                                                        className={`p-3 rounded-lg cursor-pointer transition-all duration-200 h-72 ${
                                                            selectedUnitId === unit.id
                                                                ? 'bg-indigo-100 border-2 border-indigo-300 shadow-sm'
                                                                : 'bg-white border border-gray-200 hover:border-indigo-200 hover:shadow-sm'
                                                        }`}
                                                    >
                                                        <div className="flex flex-col h-full">
                                                            <div className="flex items-center justify-between mb-2">
                                                                <h5 className="font-semibold text-gray-800">Unit {unit.unit}</h5>
                                                                <span className={`px-2 py-1 rounded-full text-xs ${
                                                                    selectedUnitId === unit.id ? 'bg-indigo-200 text-indigo-800' : 'bg-gray-200 text-gray-600'
                                                                }`}>
                                                                    {unit.beds}BR/{unit.baths}BA
                                                                </span>
                                                            </div>
                                                            <div className="text-xs text-gray-600 space-y-1 flex-1">
                                                                <div>📐 {unit.sqft || 'N/A'} sqft</div>
                                                                <div>💰 ${unit.rent || 'N/A'}/mo</div>
                                                                <div>🏷️ ${unit.price || 'N/A'}</div>
                                                                <div className="mt-2">
                                                                    {unit.feature_tags?.slice(0, 3).map((tag: string, idx: number) => (
                                                                        <span key={idx} className="inline-block bg-gray-100 text-gray-700 px-1.5 py-0.5 rounded-md text-xs mr-1 mb-1">
                                                                            {tag}
                                                                        </span>
                                                                    ))}
                                                                    {unit.feature_tags?.length > 3 && (
                                                                        <span className="text-xs text-gray-500">+{unit.feature_tags.length - 3} more</span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Data Area - 10/12 on desktop */}
                                    <div className="w-full lg:w-5/6 lg:h-full flex-1 flex flex-col lg:flex-row relative">
                                        {selectedUnit ? (
                                            <>
                                                {/* Images Column - 4/10 of data area */}
                                                <div className={`w-full ${isMarketDataCollapsed ? 'lg:w-4/7' : 'lg:w-4/10'} lg:h-full h-96 border-b lg:border-b-0 lg:border-r border-gray-200 p-4 transition-all duration-300`}>
                                                    <div className="h-full">
                                                        <div className="flex items-center justify-between mb-3">
                                                            <h4 className="text-sm font-semibold text-gray-700">Unit Photos</h4>
                                                            <button
                                                                onClick={() => setShowFullscreenGallery(true)}
                                                                className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                                                                title="Open fullscreen gallery"
                                                            >
                                                                <FontAwesomeIcon icon={faMaximize} className="h-4 w-4 text-gray-600" />
                                                            </button>
                                                        </div>
                                                        <div className="h-full overflow-y-auto">
                                                            <PropertyUnitsImages
                                                                unitValue={selectedUnit}
                                                                setUnitValue={(unit) => handleUpdateUnit(unit)}
                                                                handleUpdateUnit={handleUpdateUnit}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Unit Info + Tags Column - 3/10 of data area */}
                                                <div className={`w-full ${isMarketDataCollapsed ? 'lg:w-3/7' : 'lg:w-3/10'} lg:h-full h-96 ${!isMarketDataCollapsed ? 'border-b lg:border-b-0 lg:border-r border-gray-200' : ''} p-4 transition-all duration-300`}>
                                                    <h4 className="text-sm font-semibold text-gray-700 mb-3">Unit Information</h4>
                                                    <div className="space-y-4 overflow-y-auto">
                                                        <ProperyUnitsInfo
                                                            unitValue={selectedUnit}
                                                            setUnitValue={(unit) => handleUpdateUnit(unit)}
                                                            handleUpdateUnit={handleUpdateUnit}
                                                        />
                                                        <PropertyUnitsTags
                                                            unitValue={selectedUnit}
                                                            setUnitValue={(unit) => handleUpdateUnit(unit)}
                                                            handleUpdateUnit={handleUpdateUnit}
                                                        />
                                                    </div>
                                                </div>

                                                {/* Market Data Column - 3/10 of data area (Collapsible) */}
                                                {!isMarketDataCollapsed && (
                                                    <div className="w-[250px] p-4">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <h3 className="text-sm font-medium text-gray-800 mb-2 h-8">Market Data</h3>
                                                            <button
                                                                onClick={() => setIsMarketDataCollapsed(true)}
                                                                className="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                                                                title="Collapse market data"
                                                            >
                                                                <FontAwesomeIcon icon={faCompress} className="h-3.5 w-3.5 text-gray-600" />
                                                            </button>
                                                        </div>
                                                        <div className="overflow-y-auto">
                                                            {marketData?.filter((data: MarketDaum) => data.unit_id === selectedUnit.id).map((data: MarketDaum, index: number) => (
                                                                <PropertyUnitsMarketDataItemEditable
                                                                    key={index}
                                                                    unit={data as any}
                                                                    unitKey={`market-${index}`}
                                                                    unitId={selectedUnit.id}
                                                                    indexData={index}
                                                                    unitNumber={selectedUnit.unit}
                                                                    marketId={data.id}
                                                                />
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        ) : (
                                            <div className="w-full h-full flex items-center justify-center">
                                                <div className="text-center text-gray-500">
                                                    <FontAwesomeIcon icon={faBuilding} className="h-12 w-12 text-gray-300 mb-3" />
                                                    <p className="text-sm">Choose a unit from the sidebar to see photos, info, and market data</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Expand Market Data Button */}
                                    {isMarketDataCollapsed && selectedUnit && (
                                        <button
                                            onClick={() => setIsMarketDataCollapsed(false)}
                                            className="fixed right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-indigo-600 text-white rounded-l-lg hover:bg-indigo-700 transition-colors shadow-lg"
                                            title="Expand market data"
                                        >
                                            <FontAwesomeIcon icon={faExpand} className="h-4 w-4" />
                                        </button>
                                    )}
                                </div>
                            ) : (
                                /* Original Cards View */
                                <div className="relative">
                                    {/* Navigation Buttons - Improved */}
                                <button 
                                    onClick={() => scrollToUnit('prev')}
                                        className="absolute left-2 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-indigo-600 hover:text-indigo-800 hover:bg-white hover:shadow-xl transition-all duration-200"
                                    aria-label="Previous unit"
                                >
                                        <FontAwesomeIcon icon={faChevronLeft} className="h-4 w-4" />
                                </button>
                                
                                <div 
                                    ref={containerRef}
                                        className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide py-4"
                                        style={{ scrollSnapType: 'x mandatory' }}
                                    >
                                        <div className="flex gap-4 px-6 min-w-full">
                                        {sortedUnits.map((unit: any) => (
                                            <div 
                                                key={unit.id}
                                                id={`unit-${unit.id}`}
                                                    className={`min-w-[320px] sm:min-w-[400px] lg:min-w-[450px] max-w-[450px] snap-start cursor-pointer transition-all duration-300 ${
                                                    selectedUnitId === unit.id 
                                                            ? 'ring-2 ring-indigo-500 ring-offset-2 shadow-xl scale-[1.02] bg-white rounded-xl' 
                                                            : 'hover:shadow-lg hover:scale-[1.01] bg-white rounded-xl'
                                                    } rounded-xl overflow-hidden`}
                                                onClick={() => handleUnitClick(unit.id)}
                                            >
                                                {addressId ? 
                                                    <ProprtyUnitsItemEdittable 
                                                        unit={unit} 
                                                        infoMode={infoMode} 
                                                        recordDate={selectedDate}
                                                        isSelected={selectedUnitId === unit.id}
                                                    />
                                                :
                                                    <ProppertyUnitsItem 
                                                        unit={unit} 
                                                        marketData={marketData?.filter((marketData: MarketDaum) => marketData.unit_id === unit.id) || []} 
                                                        infoMode={infoMode}
                                                        recordDate={selectedDate}
                                                        isSelected={selectedUnitId === unit.id}
                                                    />
                                                }
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                
                                <button 
                                    onClick={() => scrollToUnit('next')}
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 z-20 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-indigo-600 hover:text-indigo-800 hover:bg-white hover:shadow-xl transition-all duration-200"
                                    aria-label="Next unit"
                                >
                                        <FontAwesomeIcon icon={faChevronRight} className="h-4 w-4" />
                                </button>
                            </div>
                        )
                    ) : (
                            <div className="py-12 px-4">
                                <div className="max-w-md mx-auto text-center">
                                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <FontAwesomeIcon icon={faBuilding} className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-gray-800 mb-2">No Units Available</h4>
                                    <p className="text-sm text-gray-500 mb-6">
                                    Start by adding unit information to manage this property's rental or sales units.
                                </p>
                                {addressId && (
                                    <button 
                                        onClick={handleAddUnit}
                                            className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all shadow-sm"
                                    >
                                            <FontAwesomeIcon icon={faPlus} className="mr-2 h-4 w-4" />
                                        Add First Unit
                                    </button>
                                )}
                                    <div className="mt-4 text-xs text-gray-400">
                                        <p>💡 Units help you track individual apartments, condos, or rental spaces</p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Fullscreen Gallery Modal */}
            {showFullscreenGallery && selectedUnit && (
                <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
                    <div className="w-full h-full p-4 flex flex-col">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-white text-xl font-semibold">Unit {selectedUnit.unit} - Photo Gallery</h2>
                            <button
                                onClick={() => setShowFullscreenGallery(false)}
                                className="text-white hover:text-gray-300 p-2"
                            >
                                <FontAwesomeIcon icon={faCompress} className="h-6 w-6" />
                            </button>
                        </div>
                        <div className="flex-1">
                            <PropertyUnitsImages
                                unitValue={selectedUnit}
                                setUnitValue={(unit) => handleUpdateUnit(unit)}
                                handleUpdateUnit={handleUpdateUnit}
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
