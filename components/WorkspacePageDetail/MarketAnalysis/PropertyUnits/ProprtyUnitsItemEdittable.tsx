import { useState, useEffect } from "react"
import { useDebounce } from "@/helpers/hooks/useDebounce"
import { removeUnit, updateUnit } from "@/actions/propetyUnitsActions"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faTrash, faPlus, faMinus, faImage, faBed, faBath, faRulerCombined, faMoneyBill, faCalendarAlt, faTags, faCouch, faPhotoVideo, faChevronDown, faChevronUp, faChartBar, faPencilAlt, faRobot, faInfoCircle, faQuestionCircle } from "@fortawesome/free-solid-svg-icons"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext"
import { Calendar } from "primereact/calendar"
import { Daum, MarketDaum } from "@/types/UnitDataType"
import PropertyUnitsMarketDataItemEditable from "./PropertyUnitsMarketDataItemEditable"
import FeatureTagInput from "./PropertyUnitsEvents/FeatureTagInput"
import AmenitiesTagInput from "./PropertyUnitsEvents/AmenitiesTagInput"
import PhotoUpload from "./PropertyUnitsEvents/PhotoUpload"
import { addUnitMarketData, removeUnit as removeMarketData, updateUnit as updateMarketData } from "@/actions/propertyMarketDataActions"
import { useSearchParams } from "next/navigation"
import { formatCurrency, formatNumber } from "@/utils/formatters"
import { createClient } from "@/utils/supabase/client"
import { publicUrlToPath } from "@/utils/supabase/paths"

interface Props {
    unit: { [key: string]: any }
    infoMode: 'unit' | 'market'
    recordDate: Date
    isSelected?: boolean
}

interface UnitValues {
    id: any;
    unit: any;
    beds: any;
    baths: any;
    hoa_fee: any;
    sqft: any;
    rent: any;
    price: any;
    document_recorded_date: any;
    feature_tags?: string[];
    amenities_tags?: string[];
    photos?: any[];
    img_urls?: any[];
    [key: string]: any; // Add index signature
}

export default function ProprtyUnitsItemEdittable({unit, infoMode, recordDate, isSelected}: Props) {
    const {unitData, updateMarketAnalysisState} = useMarketAnalysis()
    const [showMarketData, setShowMarketData] = useState(infoMode === 'market')
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    
    // Watch for changes to infoMode and update showMarketData
    useEffect(() => {
        setShowMarketData(infoMode === 'market')
    }, [infoMode])
    
    const [unitValue, setUnitValue] = useState<UnitValues>({
        id: unit?.id || '',
        unit: unit?.unit || '',
        beds: unit?.beds || 0,
        baths: unit?.baths || 0,
        hoa_fee: unit?.hoa_fee || 0,
        sqft: unit?.sqft || 0,
        rent: unit?.rent || 0,
        price: unit?.price || 0,
        document_recorded_date: recordDate ? recordDate.toISOString().split('T')[0] : (unit?.document_recorded_date || ''),
        feature_tags: unit?.feature_tags || [],
        amenities_tags: unit?.amenities_tags || [],
        img_urls: unit?.img_urls || []
    })

    // Update unitValue when recordDate changes
    useEffect(() => {
        if (recordDate) {
            setUnitValue(prevValue => ({
                ...prevValue,
                document_recorded_date: recordDate.toISOString().split('T')[0]
            }));
            
            // Only update if we have a valid addressId
            if (addressId) {
                // Update the unit with the new record date
                const updatedUnit = { 
                    ...unitValue, 
                    document_recorded_date: recordDate.toISOString().split('T')[0] 
                };
                handleUpdateUnit(updatedUnit);
            }
        }
    }, [recordDate, addressId]);

    const handleUpdateUnit = useDebounce(async (unit) => {
        if (!addressId) return; // Don't update if addressId is undefined
        await updateUnit(unit)
    }, 500)

    const handleUnitChange = (e: React.ChangeEvent<HTMLInputElement>, key: string) => {
        // For numeric fields, only accept numeric input
        if (['beds', 'baths', 'hoa_fee', 'sqft', 'rent', 'price'].includes(key)) {
            // Remove any non-numeric characters except decimal point for input
            const rawValue = e.target.value.replace(/[^0-9.]/g, '');
            
            // Allow empty value or valid number
            if (rawValue === '' || !isNaN(Number(rawValue))) {
                const updatedUnit = { ...unitValue, [key]: rawValue };
                setUnitValue(updatedUnit);
                handleUpdateUnit(updatedUnit);
            }
        } else {
            const updatedUnit = { ...unitValue, [key]: e.target.value };
            setUnitValue(updatedUnit);
            handleUpdateUnit(updatedUnit);
        }
    }

    const handleIncrement = (key: 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price', incrementAmount: number) => {
        const currentValue = unitValue[key] === '' ? 0 : Number(unitValue[key]);
        const updatedUnit = { ...unitValue, [key]: String(currentValue + incrementAmount) };
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    }

    const handleDecrement = (key: 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price', decrementAmount: number) => {
        const currentValue = unitValue[key] === '' ? 0 : Number(unitValue[key]);
        if (currentValue > 0) {
            const updatedUnit = { ...unitValue, [key]: String(currentValue - decrementAmount) };
            setUnitValue(updatedUnit);
            handleUpdateUnit(updatedUnit);
        }
    }

    const handleDateChange = (e: any) => {
        const date = e.value;
        let formattedDate = '';
        
        if (date) {
            // Format date as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            formattedDate = `${year}-${month}-${day}`;
        }
        
        const updatedUnit = { 
            ...unitValue, 
            document_recorded_date: formattedDate 
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    }

    const handleRemoveUnit = async (id: string) => {
        updateMarketAnalysisState({unitData: unitData?.filter((unit: any) => unit.id !== id)})
        await removeUnit(id)
    }

    const handleFeatureTagsUpdate = (tags: string[]) => {
        const updatedUnit = {
            ...unitValue,
            feature_tags: tags
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    };

    const handleAmenitiesTagsUpdate = (tags: string[]) => {
        const updatedUnit = {
            ...unitValue,
            amenities_tags: tags
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    };

    const handlePhotosUpdate = (photos: any[]) => {
        const updatedUnit = {
            ...unitValue,
            img_urls: photos
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    };

    const handleAddMarketData = async () => {
        if (!addressId || !unitValue.id) return;
        
        try {
            const marketData = await addUnitMarketData(addressId, unitValue.id, unitValue.unit);
            updateMarketAnalysisState({ 
                marketData: marketData
            });
            
            // Refresh the unit data to show the new market data
            const updatedUnitData = unitData?.map((u: any) => {
                if (u.id === unitValue.id) {
                    const unitMarketData = marketData?.filter((md: any) => md.unit_id === unitValue.id) || [];
                    return {
                        ...u,
                        prop_market_data: unitMarketData
                    };
                }
                return u;
            }) || [];
            
            updateMarketAnalysisState({
                unitData: updatedUnitData
            });
        } catch (error) {
            console.error("Error adding market data to unit:", error);
        }
    };

    // Helper function to get formatted display values with helpful text
    const getFormattedValue = (field: string, value: any) => {
        if (!value || value === '' || value === '0' || value === 0) {
            switch (field) {
                case 'hoa_fee':
                    return 'No HOA fee';
                case 'rent':
                    return 'Not for rent';
                case 'price':
                    return 'Price not set';
                case 'sqft':
                    return 'Area not specified';
                default:
                    return 'Not specified';
            }
        }
        
        switch (field) {
            case 'hoa_fee':
            case 'rent':
            case 'price':
                return `${formatCurrency(Number(value))}/month`;
            case 'sqft':
                return `${formatNumber(Number(value))} sq ft`;
            default:
                return value;
        }
    };

    const extractImageUrl = (img: any): string => {
        if(!img) return ''
        return typeof img === 'string' ? img : img.url || img.publicUrl || img.signedUrl || img.file_path || ''
    }

    const [mainImage, setMainImage] = useState<string | null>(null)

    useEffect(() => {
        async function resolveMain(){
            if(!unitValue.img_urls || unitValue.img_urls.length === 0){
                setMainImage(null)
                return
            }
            const first = unitValue.img_urls[0]
            const url = extractImageUrl(first)
            if(url){
                const supabase = createClient()
                let pathToSign = url
                if(url.startsWith('http')){
                    const maybe=publicUrlToPath(url,'propimages')
                    if(!maybe){ setMainImage(url); return }
                    pathToSign = maybe
                }
                const { data: signed, error } = await supabase.storage.from('propimages').createSignedUrl(pathToSign, 60*60)
                if(!error && signed?.signedUrl){
                    setMainImage(signed.signedUrl)
                } else {
                    const { data: pub } = await supabase.storage.from('propimages').getPublicUrl(pathToSign)
                    setMainImage(pub?.publicUrl || null)
                }
            }
        }
        resolveMain()
    }, [unitValue.img_urls])

    return (
        <div className={`bg-white border rounded-lg shadow-sm overflow-hidden h-full transition-all duration-200 ${
            isSelected 
                ? 'border-indigo-500 shadow-md ring-1 ring-indigo-500' 
                : 'border-gray-200'
        }`}>
            {/* Header - Compact spacing */}
            <div className={`px-2 py-2 border-b ${
                isSelected 
                    ? 'bg-indigo-50 border-indigo-200' 
                    : 'bg-gray-50 border-gray-200'
            }`}>
                <div className="grid grid-cols-7 gap-1.5">
                    {/* Unit Number - 2 columns */}
                    <div className="col-span-2">
                        <label className="block mb-0.5 text-xs font-medium text-gray-700" title="Enter the unit number or identifier">
                            Unit #
                        </label>
                        <input 
                            type="text" 
                            className="w-full h-7 border border-gray-300 rounded-md px-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-colors" 
                            value={unitValue?.unit || ''} 
                            onChange={(e) => handleUnitChange(e, 'unit')}
                            placeholder="e.g., 101A"
                            title="Unit number or identifier"
                        />
                    </div>
                    
                    {/* Record Date - 3 columns */}
                    <div className="col-span-3">
                        <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1" title="Date when this unit information was recorded">
                            <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-500 h-2.5 w-2.5" />
                            <span>Record Date</span>
                            <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                        </label>
                        <div className="flex">
                            <Calendar 
                                value={unitValue?.document_recorded_date ? new Date(unitValue.document_recorded_date) : null} 
                                onChange={(e) => handleDateChange(e)} 
                                dateFormat="M/d/yy"
                                className="w-full"
                                inputClassName="w-full h-7 border border-gray-300 rounded-md px-2 text-sm"
                                monthNavigator
                                yearNavigator
                                yearRange="2000:2030"
                                appendTo={document.body}
                                showButtonBar
                                placeholder="Select date"
                            />
                        </div>
                    </div>
                    
                    {/* Actions - 2 columns */}
                    <div className="col-span-2">
                        <label className="block mb-0.5 text-xs font-medium text-gray-700">
                            Actions
                        </label>
                        <div className="flex space-x-1">
                            <button
                                onClick={() => handleRemoveUnit(unit.id)}
                                className="py-1 px-1.5 h-7 flex items-center gap-1 text-xs text-red-600 hover:text-red-800 bg-white border border-red-200 rounded-md hover:bg-red-50 transition-all"
                                title="Delete this unit permanently"
                            >
                                <FontAwesomeIcon icon={faTrash} className="h-2.5 w-2.5" />
                            </button>
                            <button 
                                onClick={() => setShowMarketData(!showMarketData)}
                                className="py-1 px-2 h-7 flex items-center gap-1 text-xs text-white bg-indigo-600 border border-indigo-700 rounded-md hover:bg-indigo-700 flex-grow justify-center transition-all"
                                title={showMarketData ? "Switch to unit information view" : "Switch to market data view"}
                            >
                                {showMarketData ? "Unit Info" : "Market Data"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main content - reduced padding */}
            {showMarketData ? (
                <div className="p-2 bg-gray-50">
                    <div className="flex justify-between items-center mb-2">
                        <h4 className="text-xs font-semibold text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faChartBar} className="text-gray-500 h-3 w-3" />
                            <span>Market Data</span>
                            <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Historical pricing and listing information" />
                        </h4>
                        <button 
                            onClick={handleAddMarketData}
                            className="py-1 px-2 bg-indigo-600 rounded text-white text-xs font-medium hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500 flex items-center gap-1 transition-all"
                            title="Add new market data entry for this unit"
                        >
                            <FontAwesomeIcon icon={faPlus} className="h-2.5 w-2.5" />
                            <span>Add Market Data</span>
                        </button>
                    </div>
                    
                    {(!unit?.prop_market_data || unit.prop_market_data.length === 0) ? (
                        <div className="p-3 text-center text-gray-500 bg-white border border-gray-200 rounded-md">
                            <FontAwesomeIcon icon={faChartBar} className="h-8 w-8 text-gray-300 mb-2" />
                            <p className="text-sm">No market data available</p>
                            <p className="text-xs text-gray-400 mt-1">Click "Add Market Data" to start tracking this unit's market information</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 gap-1.5">
                            {unit?.prop_market_data?.map((marketData: MarketDaum) => (
                                marketData.data.map((data: Daum, index: number) => (
                                    <div key={`${marketData.id || marketData.unit}-${index}`} className="bg-white border border-gray-200 rounded-md p-2 shadow-sm">
                                        <div className="flex justify-between items-center mb-2">
                                            <h5 className="text-sm font-medium">Unit {marketData.unit || 'N/A'}</h5>
                                            <div className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">
                                                {typeof data.listing_type === 'string' ? data.listing_type.replace(/_/g, ' ') : 'N/A'}
                                            </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-3 gap-3 text-xs">
                                            <div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Beds:</span>
                                                    <input 
                                                        type="text" 
                                                        className="w-full h-8 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0" 
                                                        value={typeof data.description?.beds === 'number' ? data.description.beds : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseInt(e.target.value);
                                                            const updatedData = {...data};
                                                            
                                                            if (!updatedData.description) {
                                                                updatedData.description = {
                                                                    type: '',
                                                                    beds: 0,
                                                                    baths_full: 0,
                                                                    lot_sqft: null,
                                                                    text: '',
                                                                    year_built: null,
                                                                    name: null
                                                                };
                                                            }
                                                            
                                                            updatedData.description.beds = newValue ?? 0;
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Baths:</span>
                                                    <input 
                                                        type="text" 
                                                        className="w-full h-8 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0" 
                                                        value={typeof data.description?.baths_full === 'number' ? data.description.baths_full : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseFloat(e.target.value);
                                                            const updatedData = {...data};
                                                            
                                                            if (!updatedData.description) {
                                                                updatedData.description = {
                                                                    type: '',
                                                                    beds: 0,
                                                                    baths_full: 0,
                                                                    lot_sqft: null,
                                                                    text: '',
                                                                    year_built: null,
                                                                    name: null
                                                                };
                                                            }
                                                            
                                                            updatedData.description.baths_full = newValue ?? 0;
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">Area:</span>
                                                    <input 
                                                        type="number" 
                                                        className="w-12 text-right bg-white border border-gray-300 rounded p-1 text-xs"
                                                        value={typeof data.description?.sqft === 'number' ? data.description.sqft : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseInt(e.target.value);
                                                            const updatedData = {...data};
                                                            
                                                            if (!updatedData.description) {
                                                                updatedData.description = {
                                                                    type: '',
                                                                    beds: 0,
                                                                    baths_full: 0,
                                                                    lot_sqft: null,
                                                                    text: '',
                                                                    year_built: null,
                                                                    name: null
                                                                };
                                                            }
                                                            
                                                            updatedData.description.sqft = newValue ?? 0;
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                            
                                            <div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Price:</span>
                                                    <input 
                                                        type="number" 
                                                        className="w-16 text-right bg-white border border-gray-300 rounded p-1 text-xs"
                                                        value={typeof data.list_price === 'number' ? data.list_price : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseInt(e.target.value);
                                                            const updatedData = {...data, list_price: newValue};
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Sold:</span>
                                                    <input 
                                                        type="number" 
                                                        className="w-16 text-right bg-white border border-gray-300 rounded p-1 text-xs"
                                                        value={typeof data.last_sold_price === 'number' ? data.last_sold_price : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseInt(e.target.value);
                                                            const updatedData = {...data, last_sold_price: newValue};
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">HOA:</span>
                                                    <input 
                                                        type="number" 
                                                        className="w-16 text-right bg-white border border-gray-300 rounded p-1 text-xs"
                                                        value={typeof data.hoa?.fee === 'number' ? data.hoa.fee : 0}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value === '' ? undefined : parseInt(e.target.value);
                                                            const updatedData = {...data};
                                                            if (!updatedData.hoa) updatedData.hoa = {};
                                                            updatedData.hoa.fee = newValue;
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                            
                                            <div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Date:</span>
                                                    <Calendar 
                                                        value={data.last_sold_date ? new Date(data.last_sold_date) : null} 
                                                        onChange={(e) => {
                                                            const date = e.value;
                                                            let formattedDate = '';
                                                            
                                                            if (date) {
                                                                // Format date as YYYY-MM-DD
                                                                const year = date.getFullYear();
                                                                const month = String(date.getMonth() + 1).padStart(2, '0');
                                                                const day = String(date.getDate()).padStart(2, '0');
                                                                formattedDate = `${year}-${month}-${day}`;
                                                            }
                                                            
                                                            const updatedData = {...data, last_sold_date: formattedDate};
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                        dateFormat="M/d/yy"
                                                        className="w-20"
                                                        inputClassName="text-right bg-white border border-gray-300 rounded p-1 text-xs h-6 w-full"
                                                    />
                                                </div>
                                                <div className="flex justify-between mb-1">
                                                    <span className="text-gray-500">Type:</span>
                                                    <select
                                                        className="w-20 text-right bg-white border border-gray-300 rounded p-1 text-xs"
                                                        value={data.listing_type || ''}
                                                        onChange={(e) => {
                                                            const updatedData = {...data, listing_type: e.target.value};
                                                            
                                                            // Update the market data
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            updatedMarketData[updatedMarketData.indexOf(marketData)].data[index] = updatedData;
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        }}
                                                    >
                                                        <option value="sold">Sold</option>
                                                        <option value="for_sale">For Sale</option>
                                                        <option value="for_rent">For Rent</option>
                                                        <option value="rented">Rented</option>
                                                        <option value="pending">Pending</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="mt-2 pt-2 border-t border-gray-100 flex justify-end gap-2">
                                            <button 
                                                onClick={async () => {
                                                    // Implement delete functionality
                                                    if (confirm(`Are you sure you want to delete this market data entry for Unit ${marketData.unit}?`)) {
                                                        try {
                                                            // Remove the item from the data array
                                                            const updatedMarketData = [...unit.prop_market_data];
                                                            const currentMarketData = updatedMarketData[updatedMarketData.indexOf(marketData)];
                                                            currentMarketData.data.splice(index, 1);
                                                            
                                                            // If data array is empty, remove the entire market data entry
                                                            if (currentMarketData.data.length === 0) {
                                                                // Call the database delete function to persist the deletion
                                                                await removeMarketData(marketData.id);
                                                                updatedMarketData.splice(updatedMarketData.indexOf(marketData), 1);
                                                            } else {
                                                                // If we still have data entries, update the record in the database
                                                                await updateMarketData(
                                                                    currentMarketData.data[0], 
                                                                    marketData.unit_id || '', 
                                                                    0, 
                                                                    marketData.unit, 
                                                                    marketData.id
                                                                );
                                                            }
                                                            
                                                            // Update state
                                                            const updatedUnit = { ...unit, prop_market_data: updatedMarketData };
                                                            updateMarketAnalysisState({
                                                                unitData: unitData?.map((u: any) => u.id === unit.id ? updatedUnit : u) || []
                                                            });
                                                        } catch (error) {
                                                            console.error("Error deleting market data:", error);
                                                            alert("Failed to delete market data. Please try again.");
                                                        }
                                                    }
                                                }}
                                                className="text-xs text-red-600 hover:text-red-800 flex items-center gap-1"
                                            >
                                                <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                                                <span>Delete</span>
                                            </button>
                                        </div>
                                    </div>
                                ))
                            ))}
                        </div>
                    )}
                </div>
            ) : (
                <div className="p-2">
                    <div className="grid grid-cols-1 gap-2">
                        {/* Main info grid layout - 2:1:1 ratio with reduced spacing */}
                        <div className="grid grid-cols-4 gap-2">
                            {/* Left column - Property Details - 2 units */}
                            <div className="col-span-2">
                                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-2 border border-blue-100">
                                    <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wider flex items-center gap-1 border-b border-blue-200 pb-1 mb-2">
                                        <FontAwesomeIcon icon={faBed} className="text-blue-600 h-3 w-3" />
                                        <span>Property Details</span>
                                        <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Basic unit specifications" />
                                    </h4>
                                    
                                    <div className="space-y-2">
                                        {/* Bedrooms Control */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <FontAwesomeIcon icon={faBed} className="text-gray-500 h-2.5 w-2.5" />
                                                <span>Bedrooms</span>
                                            </label>
                                            <div className="flex">
                                                <button 
                                                    type="button"
                                                    onClick={() => handleDecrement('beds', 1)}
                                                    className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-l-md p-1 h-7 focus:outline-none transition-colors"
                                                    title="Decrease bedroom count"
                                                >
                                                    <FontAwesomeIcon icon={faMinus} className="w-2.5 h-2.5 text-gray-600" />
                                                </button>
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0 bg-white" 
                                                    value={unitValue?.beds || 0} 
                                                    readOnly={true}
                                                    title="Number of bedrooms"
                                                />
                                                <button 
                                                    type="button" 
                                                    onClick={() => handleIncrement('beds', 1)}
                                                    className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-r-md p-1 h-7 focus:outline-none transition-colors"
                                                    title="Increase bedroom count"
                                                >
                                                    <FontAwesomeIcon icon={faPlus} className="w-2.5 h-2.5 text-gray-600" />
                                                </button>
                                            </div>
                                        </div>

                                        {/* Bathrooms Control */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <FontAwesomeIcon icon={faBath} className="text-gray-500 h-2.5 w-2.5" />
                                                <span>Bathrooms</span>
                                            </label>
                                            <div className="flex">
                                                <button 
                                                    type="button"
                                                    onClick={() => handleDecrement('baths', 0.5)}
                                                    className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-l-md p-1 h-7 focus:outline-none transition-colors"
                                                    title="Decrease bathroom count by 0.5"
                                                >
                                                    <FontAwesomeIcon icon={faMinus} className="w-2.5 h-2.5 text-gray-600" />
                                                </button>
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0 bg-white" 
                                                    value={unitValue?.baths || 0} 
                                                    readOnly={true}
                                                    title="Number of bathrooms (can be fractional)"
                                                />
                                                <button 
                                                    type="button" 
                                                    onClick={() => handleIncrement('baths', 0.5)}
                                                    className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-r-md p-1 h-7 focus:outline-none transition-colors"
                                                    title="Increase bathroom count by 0.5"
                                                >
                                                    <FontAwesomeIcon icon={faPlus} className="w-2.5 h-2.5 text-gray-600" />
                                                </button>
                                            </div>
                                        </div>

                                        {/* Area Control */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <FontAwesomeIcon icon={faRulerCombined} className="text-gray-500 h-2.5 w-2.5" />
                                                <span>Area (sq ft)</span>
                                            </label>
                                            <div className="relative">
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border border-gray-300 rounded-md pl-2 pr-12 text-sm font-medium text-gray-700 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-colors" 
                                                    value={unitValue?.sqft || ''}
                                                    onChange={(e) => handleUnitChange(e, 'sqft')}
                                                    placeholder="1200"
                                                    title="Unit area in square feet"
                                                />
                                                <div className="absolute inset-y-0 right-2 flex items-center pointer-events-none">
                                                    <span className="text-xs font-medium text-gray-500">sq ft</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Middle column - Financial Details - 1 unit */}
                            <div className="col-span-1">
                                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-2 h-full border border-green-100">
                                    <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wider flex items-center gap-1 border-b border-green-200 pb-1 mb-2">
                                        <FontAwesomeIcon icon={faMoneyBill} className="text-green-600 h-3 w-3" />
                                        <span>Financial</span>
                                        <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Pricing and financial information" />
                                    </h4>
                                    
                                    <div className="space-y-2">
                                        {/* HOA Fee */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <span>HOA Fee</span>
                                            </label>
                                            <div className="relative">
                                                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                                    <span className="text-gray-500 text-sm">$</span>
                                                </div>
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border border-gray-300 rounded-md pl-5 pr-2 text-sm focus:ring-green-500 focus:border-green-500 bg-white transition-colors" 
                                                    value={unitValue?.hoa_fee} 
                                                    onChange={(e) => handleUnitChange(e, 'hoa_fee')}
                                                    placeholder="250"
                                                    title="Monthly HOA fee in dollars"
                                                />
                                            </div>
                                            <p className="text-xs text-gray-500 mt-0.5">
                                                {getFormattedValue('hoa_fee', unitValue?.hoa_fee)}
                                            </p>
                                        </div>

                                        {/* Rent */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <span>Monthly Rent</span>
                                            </label>
                                            <div className="relative">
                                                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                                    <span className="text-gray-500 text-sm">$</span>
                                                </div>
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border border-gray-300 rounded-md pl-5 pr-2 text-sm focus:ring-green-500 focus:border-green-500 bg-white transition-colors" 
                                                    value={unitValue?.rent} 
                                                    onChange={(e) => handleUnitChange(e, 'rent')}
                                                    placeholder="2500"
                                                    title="Monthly rental amount"
                                                />
                                            </div>
                                            <p className="text-xs text-gray-500 mt-0.5">
                                                {getFormattedValue('rent', unitValue?.rent)}
                                            </p>
                                        </div>

                                        {/* Price */}
                                        <div>
                                            <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                                <span>Sale Price</span>
                                            </label>
                                            <div className="relative">
                                                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                                    <span className="text-gray-500 text-sm">$</span>
                                                </div>
                                                <input 
                                                    type="text" 
                                                    className="w-full h-7 border border-gray-300 rounded-md pl-5 pr-2 text-sm focus:ring-green-500 focus:border-green-500 bg-white transition-colors" 
                                                    value={unitValue?.price} 
                                                    onChange={(e) => handleUnitChange(e, 'price')}
                                                    placeholder="450000"
                                                    title="Current listing or sale price"
                                                />
                                            </div>
                                            <p className="text-xs text-gray-500 mt-0.5">
                                                {getFormattedValue('price', unitValue?.price)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Right column - Unit Photos - 1 unit */}
                            <div className="col-span-1">
                                <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-2 h-full border border-purple-100">
                                    <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wider flex items-center gap-1 border-b border-purple-200 pb-1 mb-2">
                                        <FontAwesomeIcon icon={faImage} className="text-purple-600 h-3 w-3" />
                                        <span>Photos</span>
                                        <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Unit photos and AI analysis" />
                                    </h4>
                                    
                                    <div className="space-y-2">
                                        <div>
                                            <div className="border border-gray-300 rounded-lg bg-gray-100 h-24 flex flex-col items-center justify-center relative">
                                                {mainImage ? (
                                                    <img 
                                                        src={mainImage} 
                                                        alt={`Unit ${unitValue.unit}`} 
                                                        className="object-cover h-full w-full rounded-lg"
                                                    />
                                                ) : (
                                                    <div className="text-center">
                                                        <FontAwesomeIcon icon={faImage} className="h-6 w-6 text-gray-400 mb-1" />
                                                        <p className="text-xs text-gray-500">No images</p>
                                                    </div>
                                                )}
                                                <div className="absolute bottom-1 right-1">
                                                    <button
                                                        onClick={() => {
                                                            // Create a photo upload dialog
                                                            const modal = document.createElement('div');
                                                            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                                                            modal.id = 'photo-upload-modal';
                                                            
                                                            // Create the content container
                                                            const content = document.createElement('div');
                                                            content.className = 'bg-white p-5 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto';
                                                            content.innerHTML = `
                                                                <h3 class="text-lg font-medium mb-4">Upload Photos for Unit ${unitValue.unit}</h3>
                                                                <div id="photo-upload-container" class="border-2 border-dashed border-gray-300 p-8 text-center rounded-lg">
                                                                    <div class="flex flex-col items-center">
                                                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4h-12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                                        </svg>
                                                                        <p class="mt-2 text-sm text-gray-600">Drag and drop image files here, or click to select files</p>
                                                                        <button id="browse-images-btn" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                                                                            Browse Images
                                                                        </button>
                                                                    </div>
                                                                    <div id="photo-preview" class="mt-4 grid grid-cols-4 gap-2"></div>
                                                                </div>
                                                                <div class="mt-4">
                                                                    <p id="upload-status" class="text-center text-gray-500">No photos added</p>
                                                                </div>
                                                                <div class="mt-4 flex justify-end space-x-2">
                                                                    <button id="cancel-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                                                                        Cancel
                                                                    </button>
                                                                    <button id="upload-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                                                                        Upload
                                                                    </button>
                                                                </div>
                                                            `;
                                                            
                                                            modal.appendChild(content);
                                                            document.body.appendChild(modal);
                                                            
                                                            // Add event listeners with proper null checks
                                                            const cancelBtn = document.getElementById('cancel-btn');
                                                            if (cancelBtn) {
                                                                cancelBtn.addEventListener('click', () => {
                                                                    document.body.removeChild(modal);
                                                                });
                                                            }
                                                            
                                                            const browseBtn = document.getElementById('browse-images-btn');
                                                            if (browseBtn) {
                                                                browseBtn.addEventListener('click', () => {
                                                                    // Create an invisible file input
                                                                    const fileInput = document.createElement('input');
                                                                    fileInput.type = 'file';
                                                                    fileInput.multiple = true;
                                                                    fileInput.accept = 'image/*';
                                                                    fileInput.style.display = 'none';
                                                                    
                                                                    // Add to DOM and trigger click
                                                                    document.body.appendChild(fileInput);
                                                                    fileInput.click();
                                                                    
                                                                    // Handle file selection
                                                                    fileInput.onchange = (e) => {
                                                                        if (!e.target) return;
                                                                        
                                                                        const target = e.target as HTMLInputElement;
                                                                        const files = target.files;
                                                                        
                                                                        if (files && files.length > 0) {
                                                                            const uploadStatus = document.getElementById('upload-status');
                                                                            if (uploadStatus) {
                                                                                uploadStatus.textContent = `${files.length} photo(s) selected`;
                                                                            }
                                                                            
                                                                            // Show previews
                                                                            const previewContainer = document.getElementById('photo-preview');
                                                                            if (previewContainer) {
                                                                                previewContainer.innerHTML = '';
                                                                                
                                                                                Array.from(files).forEach(file => {
                                                                                    const reader = new FileReader();
                                                                                    reader.onload = (e) => {
                                                                                        if (!e.target || !previewContainer) return;
                                                                                        
                                                                                        const img = document.createElement('div');
                                                                                        img.className = 'h-20 bg-gray-100 rounded overflow-hidden';
                                                                                        img.innerHTML = `<img src="${e.target.result}" class="w-full h-full object-cover">`;
                                                                                        previewContainer.appendChild(img);
                                                                                    };
                                                                                    reader.readAsDataURL(file);
                                                                                });
                                                                            }
                                                                        }
                                                                        
                                                                        // Remove the input from DOM
                                                                        document.body.removeChild(fileInput);
                                                                    };
                                                                });
                                                            }
                                                            
                                                            // Handle upload button
                                                            const uploadBtn = document.getElementById('upload-btn');
                                                            if (uploadBtn) {
                                                                uploadBtn.addEventListener('click', () => {
                                                                    // Here you would normally handle the actual upload
                                                                    // For now, we'll just simulate it
                                                                    const uploadStatus = document.getElementById('upload-status');
                                                                    if (uploadStatus) {
                                                                        uploadStatus.textContent = 'Uploading...';
                                                                    }
                                                                    
                                                                    setTimeout(() => {
                                                                        // Update the component state with new images
                                                                        // In a real implementation, you would get URLs from your backend
                                                                        const newImages = ['https://via.placeholder.com/300'];
                                                                        handlePhotosUpdate([...newImages, ...(unitValue?.img_urls || [])]);
                                                                        
                                                                        // Remove modal
                                                                        document.body.removeChild(modal);
                                                                    }, 1000);
                                                                });
                                                            }
                                                        }}
                                                        className="bg-indigo-600 text-white rounded-full p-1 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all shadow-sm"
                                                        title="Upload photos for this unit"
                                                    >
                                                        <FontAwesomeIcon icon={faImage} className="h-2.5 w-2.5" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        {/* AI Condition Analysis */}
                                        <div className="border border-gray-300 rounded-md bg-gradient-to-r from-violet-50 to-purple-50 p-2 text-center">
                                            <div className="flex items-center justify-center gap-1 text-gray-600">
                                                <FontAwesomeIcon icon={faRobot} className="h-3 w-3 text-violet-600" />
                                                <span className="text-xs font-medium">AI Analysis</span>
                                                <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="AI-powered condition assessment from photos" />
                                            </div>
                                            <p className="text-xs text-gray-500 italic mt-1">Coming Soon</p>
                                            <p className="text-xs text-gray-400 mt-0.5">Upload photos to get AI condition insights</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {/* Features & Amenities Section - reduced height */}
                        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-2 border border-orange-100">
                            <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wider flex items-center gap-1 border-b border-orange-200 pb-1 mb-2">
                                <FontAwesomeIcon icon={faTags} className="text-orange-600 h-3 w-3" />
                                <span>Features & Amenities</span>
                                <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Unit features and building amenities" />
                            </h4>
                            
                            <div className="grid grid-cols-2 gap-2">
                                {/* Feature Tags */}
                                <div>
                                    <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                        <FontAwesomeIcon icon={faTags} className="text-gray-500 h-2.5 w-2.5" />
                                        <span>Features</span>
                                    </label>
                                    <div className="border border-gray-300 rounded-md bg-white p-1.5 min-h-[50px] text-xs">
                                        <FeatureTagInput 
                                            tags={unitValue?.feature_tags || []} 
                                            onTagsUpdate={handleFeatureTagsUpdate} 
                                        />
                                    </div>
                                    <p className="text-xs text-gray-400 mt-0.5">e.g., Hardwood floors, Granite counters</p>
                                </div>

                                {/* Amenities Tags */}
                                <div>
                                    <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1">
                                        <FontAwesomeIcon icon={faCouch} className="text-gray-500 h-2.5 w-2.5" />
                                        <span>Amenities</span>
                                    </label>
                                    <div className="border border-gray-300 rounded-md bg-white p-1.5 min-h-[50px] text-xs">
                                        <AmenitiesTagInput 
                                            tags={unitValue?.amenities_tags || []} 
                                            onTagsUpdate={handleAmenitiesTagsUpdate} 
                                        />
                                    </div>
                                    <p className="text-xs text-gray-400 mt-0.5">e.g., Pool, Gym, Concierge</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}