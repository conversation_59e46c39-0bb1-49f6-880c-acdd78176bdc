import { useState, useEffect } from "react"
import { <PERSON><PERSON>, MarketDaum } from "@/types/UnitDataType"
import PropertyUnitsMarketDataItem from "./PropertyUnitsMarketDataItem"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faImage, faBed, faBath, faRulerCombined, faMoneyBill, faCalendarAlt, faTags, faCouch, faChevronDown, faChevronUp, faChartBar, faRobot, faInfoCircle, faQuestionCircle } from "@fortawesome/free-solid-svg-icons"
import { formatCurrency, formatNumber, formatDate } from "@/utils/formatters"
import { createClient } from "@/utils/supabase/client"
import { publicUrlToPath } from "@/utils/supabase/paths"

interface Props {
    unit: { [key: string]: any }
    marketData: MarketDaum[] | []
    infoMode: 'unit' | 'market'
    recordDate: Date
    isSelected?: boolean
}

export default function ProppertyUnitsItem({unit, marketData, infoMode, recordDate, isSelected = false}: Props){
    const [showMarketData, setShowMarketData] = useState(false)
    
    /* --------------------------------------------
       Resolve Supabase storage paths to public URLs
     ---------------------------------------------*/
    const [imageUrls, setImageUrls] = useState<string[]>([])

    useEffect(() => {
        async function resolveUrls(){
            if(!unit?.img_urls || unit.img_urls.length === 0){
                setImageUrls([])
                return
            }

            const supabase = createClient()
            const urls = await Promise.all(
                unit.img_urls.map(async (img: any) => {
                    const rawPath = typeof img === 'string' ? img : img?.file_path || img?.url || ''
                    if(!rawPath) return ''
                    let pathToSign = rawPath
                    if(rawPath.startsWith('http')){
                        const maybePath = publicUrlToPath(rawPath,'propimages')
                        if(maybePath){ pathToSign = maybePath } else { return rawPath }
                    }

                    // Attempt signed URL first (bucket may be private)
                    const { data: signed, error: signedErr } = await supabase.storage.from('propimages').createSignedUrl(pathToSign, 60 * 60)
                    if(!signedErr && signed?.signedUrl) return signed.signedUrl

                    // Fallback to public URL
                    const { data: pub } = await supabase.storage.from('propimages').getPublicUrl(pathToSign)
                    return pub?.publicUrl || ''
                })
            )
            setImageUrls(urls.filter(Boolean))
        }

        resolveUrls()
    }, [unit?.img_urls])
    
    // Watch for changes to infoMode and update showMarketData
    useEffect(() => {
        setShowMarketData(infoMode === 'market')
    }, [infoMode])

    // Extract image URLs and handle different formats
    const extractImageUrl = (img: any): string => {
        if(!img) return ''
        return typeof img === 'string' ? img : img.url || img.publicUrl || img.signedUrl || img.file_path || ''
    }

    const getImageUrl = () => {
        if (!unit?.img_urls || unit.img_urls.length === 0) return null;
        return extractImageUrl(unit.img_urls[0]);
    }

    // Helper function to get formatted display values with helpful text
    const getFormattedValue = (field: string, value: any) => {
        if (!value || value === '' || value === '0' || value === 0) {
            switch (field) {
                case 'hoa_fee':
                    return 'No HOA fee';
                case 'rent':
                    return 'Not for rent';
                case 'price':
                    return 'Price not set';
                case 'sqft':
                    return 'Area not specified';
                default:
                    return 'Not specified';
            }
        }
        
        switch (field) {
            case 'hoa_fee':
            case 'rent':
            case 'price':
                return formatCurrency(Number(value));
            case 'sqft':
                return `${formatNumber(Number(value))} sq ft`;
            default:
                return value;
        }
    };
    
    return(
        <div className={`bg-white border rounded-lg shadow-sm overflow-hidden h-full transition-all duration-200 ${
            isSelected 
                ? 'border-indigo-500 shadow-md ring-1 ring-indigo-500' 
                : 'border-gray-200'
        }`}>
            {/* Header - Compact spacing */}
            <div className={`px-2 py-2 border-b ${
                isSelected 
                    ? 'bg-indigo-50 border-indigo-200' 
                    : 'bg-gray-50 border-gray-200'
            }`}>
                <div className="grid grid-cols-3 gap-1.5">
                    {/* Unit Number */}
                    <div>
                        <label className="block mb-0.5 text-xs font-medium text-gray-700" title="Unit number or identifier">Unit #</label>
                        <div className="h-7 border border-gray-300 rounded-md px-2 bg-gray-50 flex items-center">
                            <span className="text-sm text-gray-700">{unit?.unit}</span>
                        </div>
                    </div>
                    
                    {/* Record Date */}
                    <div>
                        <label className="block mb-0.5 text-xs font-medium text-gray-700 flex items-center gap-1" title="Date when this unit information was recorded">
                            <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-500 h-2.5 w-2.5" />
                            <span>Record Date</span>
                            <FontAwesomeIcon icon={faQuestionCircle} className="text-gray-400 h-2.5 w-2.5" />
                        </label>
                        <div className="flex h-7">
                            <div className="w-full h-7 border border-gray-300 rounded-md px-2 bg-gray-50 flex items-center">
                                <span className="text-sm text-gray-700">{formatDate(recordDate)}</span>
                            </div>
                        </div>
                    </div>
                    
                    {/* Market Data button */}
                    <div>
                        <label className="block mb-0.5 text-xs font-medium text-gray-700">
                            View Type
                        </label>
                        {marketData.length > 0 && (
                            <button 
                                onClick={() => setShowMarketData(!showMarketData)}
                                className="py-1 px-2 h-7 w-full flex items-center gap-1 text-xs text-white bg-indigo-600 border border-indigo-700 rounded-md hover:bg-indigo-700 justify-center transition-all"
                                title={showMarketData ? "Switch to unit information view" : "Switch to market data view"}
                            >
                                {showMarketData ? "Unit Info" : "Market Data"}
                            </button>
                        )}
                    </div>
                </div>
            </div>

            {/* Main content - reduced padding */}
            {showMarketData && marketData.length > 0 ? (
                <div className="p-2 bg-gray-50">
                    <div className="mb-2">
                        <h4 className="text-xs font-semibold text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faChartBar} className="text-gray-500 h-3 w-3" />
                            <span>Market Data</span>
                            <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Historical pricing and listing information" />
                        </h4>
                    </div>
                    
                    <div className="grid grid-cols-1 gap-1.5">
                        {marketData.map((marketData: MarketDaum) => (
                            marketData.data.map((data: Daum, index: number) => (
                                <div key={index} className="bg-white border border-gray-200 rounded-md p-2 shadow-sm">
                                    <div className="flex justify-between items-center mb-2">
                                        <h5 className="text-sm font-medium">Unit {marketData.unit}</h5>
                                        <div className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">
                                            {data.listing_type?.replace(/_/g, ' ') || 'N/A'}
                                        </div>
                                    </div>
                                    
                                    <div className="grid grid-cols-3 gap-2 text-xs">
                                        <div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Beds:</span>
                                                <span className="font-medium">{data.description?.beds || 'N/A'}</span>
                                            </div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Baths:</span>
                                                <span className="font-medium">{data.description?.baths_full || 'N/A'}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">Area:</span>
                                                <span className="font-medium">{data.description?.sqft || 'N/A'}</span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Price:</span>
                                                <span className="font-medium">{data.list_price ? formatCurrency(data.list_price) : 'N/A'}</span>
                                            </div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Sold:</span>
                                                <span className="font-medium">{data.last_sold_price ? formatCurrency(data.last_sold_price) : 'N/A'}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">HOA:</span>
                                                <span className="font-medium">{data.hoa?.fee ? formatCurrency(data.hoa.fee) : 'N/A'}</span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Date:</span>
                                                <span className="font-medium">{data.last_sold_date ? new Date(data.last_sold_date).toLocaleDateString() : 'N/A'}</span>
                                            </div>
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">Tags:</span>
                                                <span className="font-medium">{data.tags?.length || 0}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">Photos:</span>
                                                <span className="font-medium">{data.photos?.length || 0}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {data.photos && data.photos.length > 0 && (
                                        <div className="mt-2 pt-2 border-t border-gray-100">
                                            <div className="flex gap-1 overflow-hidden">
                                                {data.photos.slice(0, 3).map((photo: any, photoIndex: number) => (
                                                    <div key={photoIndex} className="h-12 w-12 rounded overflow-hidden flex-shrink-0">
                                                        <img 
                                                            src={photo.href} 
                                                            alt={`Unit ${marketData.unit} - Image ${photoIndex + 1}`} 
                                                            className="h-full w-full object-cover"
                                                        />
                                                    </div>
                                                ))}
                                                {data.photos.length > 3 && (
                                                    <div className="h-12 w-12 bg-gray-200 rounded flex items-center justify-center flex-shrink-0">
                                                        <span className="text-xs text-gray-600">+{data.photos.length - 3}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))
                        ))}
                    </div>
                </div>
            ) : (
                <div className="p-2">
                    {/* Main grid layout - reduced spacing */}
                    <div className="grid grid-cols-3 gap-2">
                        {/* Left column - Property Details */}
                        <div className="col-span-3 lg:col-span-1">
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-2 h-full border border-blue-100">
                                <h4 className="text-xs font-semibold text-gray-700 uppercase flex items-center gap-1 border-b border-blue-200 pb-1 mb-2">
                                    <FontAwesomeIcon icon={faBed} className="text-blue-600 h-3 w-3" />
                                    <span>Property Details</span>
                                    <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Basic unit specifications" />
                                </h4>
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <FontAwesomeIcon icon={faBed} className="text-gray-500 h-2.5 w-2.5" />
                                            <span className="text-xs font-medium text-gray-700">Bedrooms</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`${unit?.bed_count || unit?.beds || '0'} bedrooms`}>
                                            {unit?.bed_count || unit?.beds || '0'}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <FontAwesomeIcon icon={faBath} className="text-gray-500 h-2.5 w-2.5" />
                                            <span className="text-xs font-medium text-gray-700">Bathrooms</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`${unit?.bath_count || unit?.baths || '0'} bathrooms`}>
                                            {unit?.bath_count || unit?.baths || '0'}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <FontAwesomeIcon icon={faRulerCombined} className="text-gray-500 h-2.5 w-2.5" />
                                            <span className="text-xs font-medium text-gray-700">Area</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`${getFormattedValue('sqft', unit?.sqft)} living space`}>
                                            {getFormattedValue('sqft', unit?.sqft)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Center column - Financial Details */}
                        <div className="col-span-3 lg:col-span-1">
                            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-2 h-full border border-green-100">
                                <h4 className="text-xs font-semibold text-gray-700 uppercase flex items-center gap-1 border-b border-green-200 pb-1 mb-2">
                                    <FontAwesomeIcon icon={faMoneyBill} className="text-green-600 h-3 w-3" />
                                    <span>Financial Details</span>
                                    <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Pricing and financial information" />
                                </h4>
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <span className="text-xs font-medium text-gray-700">HOA Fee</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`Monthly HOA fee: ${getFormattedValue('hoa_fee', unit?.hoa_fee)}`}>
                                            {getFormattedValue('hoa_fee', unit?.hoa_fee)}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <span className="text-xs font-medium text-gray-700">Monthly Rent</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`Monthly rental amount: ${getFormattedValue('rent', unit?.rent)}`}>
                                            {getFormattedValue('rent', unit?.rent)}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-1">
                                            <span className="text-xs font-medium text-gray-700">Sale Price</span>
                                        </div>
                                        <span className="text-xs font-medium text-gray-900" title={`Current listing or sale price: ${getFormattedValue('price', unit?.price)}`}>
                                            {getFormattedValue('price', unit?.price)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right column - Unit Photos */}
                        <div className="col-span-3 lg:col-span-1">
                            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-2 h-full border border-purple-100">
                                <h4 className="text-xs font-semibold text-gray-700 uppercase flex items-center gap-1 border-b border-purple-200 pb-1 mb-2">
                                    <FontAwesomeIcon icon={faImage} className="text-purple-600 h-3 w-3" />
                                    <span>Unit Photos</span>
                                    <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Unit photos and AI analysis" />
                                </h4>
                                
                                <div className="space-y-2">
                                    {imageUrls.length > 0 ? (
                                        <div className="flex gap-1 overflow-hidden">
                                            {imageUrls.slice(0, 3).map((url: string, index: number) => (
                                                <div key={index} className="h-16 w-16 rounded overflow-hidden flex-shrink-0" title={`Unit photo ${index + 1}`}>
                                                    <img 
                                                        src={url} 
                                                        alt={`Unit ${unit.unit} - Image ${index + 1}`} 
                                                        className="h-full w-full object-cover"
                                                    />
                                                </div>
                                            ))}
                                            {imageUrls.length > 3 && (
                                                <div className="h-16 w-16 bg-gray-200 rounded flex items-center justify-center flex-shrink-0" title={`${imageUrls.length - 3} more photos`}>
                                                    <span className="text-xs text-gray-600">+{imageUrls.length - 3}</span>
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="border border-gray-300 rounded-lg bg-gray-200 h-16 flex items-center justify-center">
                                            <div className="text-center">
                                                <FontAwesomeIcon icon={faImage} className="h-6 w-6 text-gray-400 mb-1" />
                                                <p className="text-xs text-gray-500">No images</p>
                                            </div>
                                        </div>
                                    )}
                                    
                                    {/* AI Condition Analysis */}
                                    <div className="border border-gray-300 rounded-md bg-gradient-to-r from-violet-50 to-purple-50 p-2 text-center">
                                        <div className="flex items-center justify-center gap-1 text-gray-600">
                                            <FontAwesomeIcon icon={faRobot} className="h-3 w-3 text-violet-600" />
                                            <span className="text-xs font-medium">AI Analysis</span>
                                            <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="AI-powered condition assessment from photos" />
                                        </div>
                                        <p className="text-xs text-gray-500 italic mt-1">Coming Soon</p>
                                        <p className="text-xs text-gray-400 mt-0.5">Intelligent property condition assessment powered by AI technology</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Features & Amenities Section - reduced height */}
                    <div className="mt-2 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-2 border border-orange-100">
                        <h4 className="text-xs font-semibold text-gray-700 uppercase flex items-center gap-1 border-b border-orange-200 pb-1 mb-2">
                            <FontAwesomeIcon icon={faTags} className="text-orange-600 h-3 w-3" />
                            <span>Features & Amenities</span>
                            <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-2.5 w-2.5" title="Unit features and building amenities" />
                        </h4>
                        
                        <div className="grid grid-cols-2 gap-2">
                            {/* Features */}
                            <div>
                                <div className="flex items-center gap-1 mb-1">
                                    <FontAwesomeIcon icon={faTags} className="text-gray-500 h-2.5 w-2.5" />
                                    <span className="text-xs font-medium text-gray-700">Features</span>
                                </div>
                                <div className="min-h-10 border border-gray-300 rounded-md bg-white p-1.5">
                                    {unit?.feature_tags && unit.feature_tags.length > 0 ? (
                                        <div className="flex flex-wrap gap-1">
                                            {unit.feature_tags.map((tag: string, index: number) => (
                                                <span key={index} className="px-1.5 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-full" title={`Feature: ${tag.replace(/_/g, ' ')}`}>
                                                    {tag.replace(/_/g, ' ')}
                                                </span>
                                            ))}
                                        </div>
                                    ) : (
                                        <span className="text-xs text-gray-500">No features listed</span>
                                    )}
                                </div>
                                <p className="text-xs text-gray-400 mt-0.5">Unit-specific features and finishes</p>
                            </div>
                            
                            {/* Amenities */}
                            <div>
                                <div className="flex items-center gap-1 mb-1">
                                    <FontAwesomeIcon icon={faCouch} className="text-gray-500 h-2.5 w-2.5" />
                                    <span className="text-xs font-medium text-gray-700">Amenities</span>
                                </div>
                                <div className="min-h-10 border border-gray-300 rounded-md bg-white p-1.5">
                                    {unit?.amenities_tags && unit.amenities_tags.length > 0 ? (
                                        <div className="flex flex-wrap gap-1">
                                            {unit.amenities_tags.map((tag: string, index: number) => (
                                                <span key={index} className="px-1.5 py-0.5 bg-green-50 text-green-600 text-xs rounded-full" title={`Amenity: ${tag.replace(/_/g, ' ')}`}>
                                                    {tag.replace(/_/g, ' ')}
                                                </span>
                                            ))}
                                        </div>
                                    ) : (
                                        <span className="text-xs text-gray-500">No amenities listed</span>
                                    )}
                                </div>
                                <p className="text-xs text-gray-400 mt-0.5">Building and community amenities</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}