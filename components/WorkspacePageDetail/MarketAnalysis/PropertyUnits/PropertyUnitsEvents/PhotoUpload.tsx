import { useState, useEffect, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faCloudArrowUp, faXmark } from "@fortawesome/free-solid-svg-icons";
import { createClient } from "@/utils/supabase/client";

interface Photo {
    id: string;
    url: string;
    file_path?: string;
}

interface PhotoUploadProps {
    unitId: string;
    photos: Photo[];
    onPhotosUpdate: (photos: Photo[]) => void;
}

export default function PhotoUpload({ unitId, photos = [], onPhotosUpdate }: PhotoUploadProps) {
    const [showTooltip, setShowTooltip] = useState(false);
    const [showPhotoPopup, setShowPhotoPopup] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    
    const photoPopupRef = useRef<HTMLDivElement>(null);
    const photoButtonRef = useRef<HTMLDivElement>(null);

    // Handle clicks outside of the photos popup
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                photoPopupRef.current && 
                !photoPopupRef.current.contains(event.target as Node) &&
                photoButtonRef.current && 
                !photoButtonRef.current.contains(event.target as Node)
            ) {
                setShowPhotoPopup(false);
            }
        }

        // Add event listener when popup is shown
        if (showPhotoPopup) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        
        // Clean up
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showPhotoPopup]);

    // Handle file drag and drop
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const isImageFile = (file: File): boolean => {
        return file.type.startsWith('image/');
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        setUploadError(null);
        
        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            const droppedFiles = Array.from(e.dataTransfer.files);
            const imageFiles = droppedFiles.filter(isImageFile);
            
            if (imageFiles.length === 0) {
                setUploadError("Only image files are accepted");
                return;
            }
            
            if (imageFiles.length !== droppedFiles.length) {
                setUploadError("Some files were ignored. Only image files are accepted");
            }
            
            setSelectedFiles([...selectedFiles, ...imageFiles]);
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setUploadError(null);
        if (e.target.files && e.target.files.length > 0) {
            const newFiles = Array.from(e.target.files);
            const imageFiles = newFiles.filter(isImageFile);
            
            if (imageFiles.length === 0) {
                setUploadError("Only image files are accepted");
                return;
            }
            
            if (imageFiles.length !== newFiles.length) {
                setUploadError("Some files were ignored. Only image files are accepted");
            }
            
            setSelectedFiles([...selectedFiles, ...imageFiles]);
        }
    };

    const removeSelectedFile = (index: number) => {
        setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
    };

    const uploadPhotos = async () => {
        if (selectedFiles.length === 0) {
            setUploadError("Please select at least one image to upload");
            return;
        }
        
        setIsUploading(true);
        setUploadError(null);
        
        const supabase = createClient();
        
        try {
            let uploadSuccess = true;
            const newPhotos = [];
            
            for (const file of selectedFiles) {
                // Construct the path
                const path = `unit-${unitId}`;
                
                // Create unique filename with timestamp
                const timestamp = new Date().getTime();
                const randomSuffix = Math.floor(Math.random() * 1000);
                const fileName = `${timestamp}-${randomSuffix}`;
                
                // Get file extension from the original filename
                const fileExtension = file.name.split('.').pop();
                const fullPath = `${path}/${fileName}.${fileExtension}`;
                
                const { data: fileData, error: fileError } = await supabase.storage
                    .from('propimages')
                    .upload(fullPath, file);
                
                if (fileError) {
                    console.error("Error uploading file:", fileError);
                    uploadSuccess = false;
                    setUploadError(`Error uploading ${file.name}: ${fileError.message}`);
                    break;
                } else {
                    const { data: { publicUrl } } = await supabase.storage
                        .from('propimages')
                        .getPublicUrl(fullPath);
                        
                    newPhotos.push({
                        id: `${timestamp}-${randomSuffix}`,
                        url: publicUrl,
                        file_path: fullPath
                    });
                }
            }
            
            if (uploadSuccess && newPhotos.length > 0) {
                const updatedPhotos = [...photos, ...newPhotos];
                onPhotosUpdate(updatedPhotos);
                setSelectedFiles([]);
            }
        } catch (error) {
            console.error("Error in upload process:", error);
            setUploadError("An unexpected error occurred during upload");
        } finally {
            setIsUploading(false);
        }
    };

    const handleDeletePhoto = async (photoId: string, filePath?: string) => {
        const photoToDelete = photos.find(photo => photo.id === photoId);
        
        if (photoToDelete && photoToDelete.file_path) {
            // Delete from Supabase storage
            const supabase = createClient();
            const { error } = await supabase.storage
                .from('propimages')
                .remove([photoToDelete.file_path]);
                
            if (error) {
                console.error("Error deleting file from storage:", error);
            }
        }
        
        // Filter out the photo to be deleted
        const updatedPhotos = photos.filter(photo => photo.id !== photoId);
        onPhotosUpdate(updatedPhotos);
    };

    return (
        <div className="flex items-center justify-center relative">
            <div 
                ref={photoButtonRef}
                className="px-2.5 py-0.5 inline-flex text-xs rounded-full bg-gray-100 text-gray-600 cursor-pointer"
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
                onClick={() => setShowPhotoPopup(!showPhotoPopup)}
            >
                {photos.length || 0}
                {showTooltip && photos.length > 0 && (
                    <div className="absolute z-10 left-1/2 -translate-x-1/2 bottom-full mb-2 bg-white shadow-lg rounded-md p-2 w-max max-w-xs text-left">
                        <div className="grid grid-cols-3 gap-1">
                            {photos.slice(0, 6).map((photo: any, index: number) => (
                                <div key={index} className="w-16 h-16 rounded overflow-hidden">
                                    <img src={photo.url} alt="Property" className="w-full h-full object-cover" />
                                </div>
                            ))}
                            {photos.length > 6 && (
                                <div className="w-16 h-16 rounded bg-gray-200 flex items-center justify-center text-gray-600">
                                    +{photos.length - 6}
                                </div>
                            )}
                        </div>
                        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                    </div>
                )}
            </div>
            
            {showPhotoPopup && (
                <div 
                    ref={photoPopupRef}
                    className="absolute z-20 bg-white shadow-lg rounded-md p-3 mt-2 top-full left-1/2 -translate-x-1/2 w-[400px]"
                >
                    {selectedFiles.length === 0 && (
                        <div
                            className={`border-2 border-dashed rounded-lg p-4 mb-4 text-center transition-colors ${
                                isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                        >
                            <FontAwesomeIcon
                                icon={faCloudArrowUp}
                                className="h-6 w-6 text-gray-400 mb-2"
                            />
                            <p className="mb-2 text-sm">Drag and drop image files here or</p>
                            <label className="bg-blue-500 text-white px-3 py-1 rounded text-sm cursor-pointer hover:bg-blue-600 transition-colors">
                                Browse Images
                                <input
                                    type="file"
                                    className="hidden"
                                    onChange={handleFileChange}
                                    accept="image/*"
                                    multiple
                                />
                            </label>
                        </div>
                    )}

                    {selectedFiles.length > 0 && (
                        <div className="mt-2 mb-4">
                            <h3 className="font-medium text-sm mb-2">Selected Images:</h3>
                            <ul className="max-h-32 overflow-y-auto">
                                {selectedFiles.map((file, index) => (
                                    <li key={index} className="flex justify-between items-center py-1 px-2 border-b text-sm">
                                        <span className="truncate mr-2">{file.name}</span>
                                        <button
                                            onClick={() => removeSelectedFile(index)}
                                            className="text-red-500 hover:text-red-700"
                                        >
                                            <FontAwesomeIcon icon={faXmark} className="h-4 w-4" />
                                        </button>
                                    </li>
                                ))}
                            </ul>
                            <button 
                                onClick={uploadPhotos}
                                disabled={isUploading}
                                className={`mt-3 w-full ${isUploading ? 'bg-blue-300' : 'bg-blue-500 hover:bg-blue-600'} text-white px-3 py-1 rounded text-sm transition-colors`}
                            >
                                {isUploading ? 'Uploading...' : 'Upload Images'}
                            </button>
                        </div>
                    )}
                    
                    {uploadError && (
                        <p className="mt-1 mb-2 text-red-500 text-xs text-center">{uploadError}</p>
                    )}
                    
                    {/* Display existing photos with delete option */}
                    {photos.length > 0 ? (
                        <div className="mt-2">
                            <div className="text-xs text-gray-500 mb-2 flex justify-between items-center">
                                <span>Property photos:</span>
                                {selectedFiles.length === 0 && (
                                    <label className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs cursor-pointer hover:bg-gray-200 transition-colors">
                                        Add Images
                                        <input
                                            type="file"
                                            className="hidden"
                                            onChange={handleFileChange}
                                            accept="image/*"
                                            multiple
                                        />
                                    </label>
                                )}
                            </div>
                            <div className="max-h-[240px] overflow-y-auto">
                                <div className="flex flex-wrap gap-2">
                                    {photos.map((photo: any, index: number) => (
                                        <div key={index} className="relative group">
                                            <div className="w-20 h-20 rounded overflow-hidden">
                                                <img src={photo.url} alt="Property" className="w-full h-full object-cover" />
                                            </div>
                                            <button 
                                                className="absolute top-1 right-1 bg-red-500 text-white rounded-full px-1 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                                                onClick={() => handleDeletePhoto(photo.id, photo.file_path)}
                                            >
                                                <FontAwesomeIcon icon={faTrash} className="text-[10px]" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-sm text-gray-500 py-2 text-center">
                            No photos added yet
                        </div>
                    )}
                    
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                </div>
            )}
        </div>
    );
} 