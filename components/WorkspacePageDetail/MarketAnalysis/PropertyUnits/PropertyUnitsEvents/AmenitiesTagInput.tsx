import { useState, useEffect, useRef } from "react"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faMinus } from "@fortawesome/free-solid-svg-icons"

interface AmenitiesTagInputProps {
    tags: string[]
    onTagsUpdate: (tags: string[]) => void
}

export default function AmenitiesTagInput({ tags = [], onTagsUpdate }: AmenitiesTagInputProps) {
    const [showTooltip, setShowTooltip] = useState(false)
    const [showTagInput, setShowTagInput] = useState(false)
    const [newTag, setNewTag] = useState("")
    
    const tagPopupRef = useRef<HTMLDivElement>(null)
    const tagButtonRef = useRef<HTMLDivElement>(null)

    // <PERSON>le clicks outside of the tag popup
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                tagPopupRef.current && 
                !tagPopupRef.current.contains(event.target as Node) &&
                tagButtonRef.current && 
                !tagButtonRef.current.contains(event.target as Node)
            ) {
                setShowTagInput(false);
            }
        }

        // Add event listener when popup is shown
        if (showTagInput) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        
        // Clean up
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showTagInput]);

    const handleAddTag = () => {
        if (!newTag.trim()) return;
        
        // Create a clean tag string (lowercase, underscores instead of spaces)
        const formattedTag = newTag.trim().toLowerCase().replace(/\s+/g, '_');
        
        // Check if tag already exists
        if (tags.includes(formattedTag)) {
            setNewTag("");
            return;
        }
        
        // Add the new tag
        const updatedTags = [...tags, formattedTag];
        onTagsUpdate(updatedTags);
        setNewTag("");
    };

    const handleDeleteTag = (tagToDelete: string) => {
        // Filter out the tag to be deleted
        const updatedTags = tags.filter(tag => tag !== tagToDelete);
        onTagsUpdate(updatedTags);
    };

    return (
        <div className="flex items-center justify-center relative">
            <div 
                ref={tagButtonRef}
                className="px-2.5 py-0.5 inline-flex text-xs rounded-full bg-gray-100 text-gray-600 cursor-pointer"
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
                onClick={() => setShowTagInput(!showTagInput)}
            >
                {tags.length || 0}
                {showTooltip && tags.length > 0 && (
                    <div className="absolute z-10 left-1/2 -translate-x-1/2 bottom-full mb-2 bg-white shadow-lg rounded-md p-2 w-max max-w-xs text-left">
                        <div className="flex flex-wrap gap-1">
                            {tags.map((tag: string, index: number) => (
                                <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs">
                                    {tag.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                </span>
                            ))}
                        </div>
                        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                    </div>
                )}
            </div>
            
            {showTagInput && (
                <div 
                    ref={tagPopupRef}
                    className="absolute z-20 bg-white shadow-lg rounded-md p-3 mt-2 top-full left-1/2 -translate-x-1/2"
                >
                    <div className="flex items-center mb-2">
                        <input
                            type="text"
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Enter amenity tag"
                            className="border border-gray-300 rounded-l-md px-2 py-1 text-sm outline-none focus:border-indigo-500"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') handleAddTag();
                                if (e.key === 'Escape') {
                                    setShowTagInput(false);
                                    setNewTag("");
                                }
                            }}
                        />
                        <button
                            className="bg-indigo-500 text-white px-2 py-1 rounded-r-md text-sm hover:bg-indigo-600"
                            onClick={handleAddTag}
                        >
                            Add
                        </button>
                    </div>
                    
                    {/* Display existing tags with delete option */}
                    {tags.length > 0 && (
                        <div className="mt-2">
                            <div className="text-xs text-gray-500 mb-1">Existing amenities:</div>
                            <div className="max-h-28 overflow-y-auto">
                                <div className="flex flex-wrap gap-1">
                                    {tags.map((tag: string, index: number) => (
                                        <div key={index} className="flex items-center bg-gray-100 text-gray-700 rounded-full px-2 py-1 text-xs">
                                            <span>
                                                {tag.replace(/_/g, ' ').split(' ').map((word: string) => 
                                                    word.charAt(0).toUpperCase() + word.slice(1)
                                                ).join(' ')}
                                            </span>
                                            <button 
                                                className="ml-1 text-gray-500 hover:text-red-500"
                                                onClick={() => handleDeleteTag(tag)}
                                            >
                                                <FontAwesomeIcon icon={faMinus} className="text-xs" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                    
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                </div>
            )}
        </div>
    )
} 