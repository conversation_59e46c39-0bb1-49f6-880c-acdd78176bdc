import { useState } from "react"
import Fancybox from "@/helpers/fancyBox/fancyBox"
import { Fancybox as NativeFancybox } from "@fancyapps/ui"
import { Daum, Photo2 } from "@/types/UnitDataType"
import { formatMarketDate } from "@/utils/formatters"

interface Props {
    unit: Daum
    unitKey: string
}

export default function PropertyUnitsMarketDataItem({ unit, unitKey }: Props) {
    const [showTagTooltip, setShowTagTooltip] = useState(false);
    
    return (
        <>
            <tr  className="hover:bg-gray-50">
                <td className="px-3 py-2 text-left">{unitKey}</td>
                <td className="px-3 py-2 text-center">{unit?.description?.beds || 'N/A'}</td>
                <td className="px-3 py-2 text-center">{unit?.description?.baths_full || 'N/A'}</td>
                <td className="px-3 py-2 text-center">{unit?.description?.sqft || 'N/A'}</td>
                <td className="px-3 py-2 text-center">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                        unit?.listing_type === 'sold' ? 'bg-blue-100 text-blue-800' :
                        unit?.listing_type === 'for_sale' || unit?.listing_type === 'forsale' ? 'bg-purple-100 text-purple-800' :
                        unit?.listing_type === 'for_rent' || unit?.listing_type === 'forrent' || unit?.listing_type === 'rented' ? 'bg-green-100 text-green-800' :
                        unit?.listing_type === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                        }`}>
                        {unit?.listing_type.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                    </span>
                </td>
                <td className="px-3 py-2 text-center">{unit?.list_price || 'N/A'}</td>
                <td className="px-3 py-2 text-center">{unit?.last_sold_price || 'N/A'}</td>
                <td className="px-3 py-2 text-center">{unit?.last_sold_date ? formatMarketDate(unit.last_sold_date) : 'N/A'}</td>
                <td className="px-3 py-2 text-center">{unit?.hoa?.fee || 'N/A'}</td>
                <td className="px-3 py-2 text-center">
                    <div 
                        className="px-2.5 py-0.5 inline-flex text-xs rounded-full bg-gray-100 text-gray-600 cursor-pointer  relative"
                        onMouseEnter={() => setShowTagTooltip(true)}
                        onMouseLeave={() => setShowTagTooltip(false)}
                    >
                        {unit?.tags?.length || 'N/A'}
                        {showTagTooltip && unit?.tags?.length > 0 && (
                            <div className="absolute z-10 left-1/2 -translate-x-1/2 bottom-full mb-2 bg-white shadow-lg rounded-md p-2 w-max max-w-xs text-left">
                                <div className="flex flex-wrap gap-1">
                                    {unit?.tags?.map((tag: string, index: number) => (
                                        <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs">
                                            {tag.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                        </span>
                                    ))}
                                </div>
                                <div className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                            </div>
                        )}
                    </div>
                </td>
                <td className="px-3 py-2 text-center">
                    <Fancybox>
                        <button 
                            className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-md text-xs hover:bg-indigo-200 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                            data-fancybox="gallery"
                            data-src={unit?.photos?.[0]?.href}
                        >
                            {unit?.photos?.length} Photos
                        </button>
                        
                        {/* Hidden gallery items */}
                        {unit?.photos?.slice(1).map((img: Photo2, index: number) => (
                            <a 
                                key={index} 
                                data-fancybox="gallery" 
                                href={img.href} 
                                className="hidden"
                            />
                        ))}
                    </Fancybox>
                </td>
            </tr>
            
        </>
        
)
}