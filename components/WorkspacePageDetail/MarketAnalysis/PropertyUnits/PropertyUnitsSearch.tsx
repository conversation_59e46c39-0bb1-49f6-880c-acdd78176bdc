import Spinner from "@/components/UI/Spinner"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext"

export default function PropertryUnitsSearch() {
    const {unitData, isLoadingUnitData} = useMarketAnalysis()
    
    return (
        <div className="bg-white shadow-sm rounded-lg p-3 relative">
            <div className="flex items-center gap-x-2 m-4">
                <h3 className="text-lg font-medium text-gray-800">Building Units: {unitData?.length}</h3>
                {
                    isLoadingUnitData ? (
                        <div className="flex flex-col items-center justify-center">
                            <Spinner size="sm" />
                        </div>
                    ) : null
                }
            </div>
        </div>  
    )
}