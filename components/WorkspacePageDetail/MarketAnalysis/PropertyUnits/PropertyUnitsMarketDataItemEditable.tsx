import { useState, useRef, useEffect, use } from "react"
import Fancybox from "@/helpers/fancyBox/fancyBox"
import { Fancybox as NativeFancybox } from "@fancyapps/ui"
import { Daum, Photo2 } from "@/types/UnitDataType"
import { useDebounce } from "@/helpers/hooks/useDebounce"
import { updateUnit } from "@/actions/propertyMarketDataActions"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons"
import { Calendar } from "primereact/calendar"
import { formatMarketDate } from "@/utils/formatters"

interface Props {
    unit: Daum
    unitKey: string
    unitId: string
    indexData: number
    unitNumber: string
    marketId: string
}

// Extend Daum type locally to handle document_recorded_date property
interface ExtendedDaum extends Daum {
    document_recorded_date?: string;
}

export default function PropertyUnitsMarketDataItemEditable({ unit, unitKey, unitId, indexData, unitNumber, marketId }: Props) {
    const [showTagTooltip, setShowTagTooltip] = useState(false);
    const [unitValue, setUnitValue] = useState<ExtendedDaum>(unit as ExtendedDaum)
    const [showTagInput, setShowTagInput] = useState(false);
    const [newTag, setNewTag] = useState("");
    const tagPopupRef = useRef<HTMLDivElement>(null);
    const tagButtonRef = useRef<HTMLButtonElement>(null);
    const [unitNumberValue, setUnitNumberValue] = useState(unitNumber);
    // Handle clicks outside of the tag popup
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                tagPopupRef.current && 
                !tagPopupRef.current.contains(event.target as Node) &&
                tagButtonRef.current && 
                !tagButtonRef.current.contains(event.target as Node)
            ) {
                setShowTagInput(false);
            }
        }

        // Add event listener when popup is shown
        if (showTagInput) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        
        // Clean up
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showTagInput]);

    const handleUpdateUnit = useDebounce(async (unit, unitId, indexData, unitNumberValue = '') => {
        await updateUnit(unit, unitId, indexData, unitNumberValue || unitNumber, marketId)
    }, 500)

    const handleIncrement = (key: string, step: number = 1) => {
        if (key === 'beds' || key === 'baths_full') {
            const currentValue = key === 'beds' ? unitValue?.description?.beds : unitValue?.description?.baths_full;
            const newValue = (currentValue || 0) + step;
            
            const updatedUnit = {
                ...unitValue,
                description: {
                    ...unitValue.description,
                    [key]: newValue
                }
            }
            setUnitValue(updatedUnit);
            
            handleUpdateUnit(updatedUnit, unitId, indexData);
        }
    }
    
    const handleDecrement = (key: string, step: number = 1) => {
        if (key === 'beds' || key === 'baths_full') {
            const currentValue = key === 'beds' ? unitValue?.description?.beds : unitValue?.description?.baths_full;
            const newValue = Math.max(0, (currentValue || 0) - step);
            
            const updatedUnit = {
                ...unitValue,
                description: {
                    ...unitValue.description,
                    [key]: newValue
                }
            };
            setUnitValue(updatedUnit);
            
            handleUpdateUnit(updatedUnit, unitId, indexData);
        }
    }

    const handleDateChange = (e: any) => {
        const date = e.value;
        let formattedDate = '';
        
        if (date) {
            // Format date as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            formattedDate = `${year}-${month}-${day}`;
        }
        
        const updatedUnit = { 
            ...unitValue, 
            last_sold_date: formattedDate 
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit, unitId, indexData);
    }

    const handleUnitChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, key: string) => {
        // For numeric fields, only accept numeric input
        if (['beds', 'baths_full', 'sqft', 'hoa_fee', 'list_price', 'last_sold_price'].includes(key)) {
            // Use parseFloat to convert to number
            const value = e.target.value;
            // Allow empty value or valid number
            const numericValue = value === '' ? undefined : parseFloat(value);
            
            let updatedUnit;
            
            if (key === 'beds' || key === 'baths_full' || key === 'sqft') {
                updatedUnit = {
                    ...unitValue, 
                    description: {
                        ...unitValue.description,
                        [key]: numericValue
                    }
                };
            } else if (key === 'hoa_fee') {
                updatedUnit = {
                    ...unitValue,
                    hoa: {
                        ...unitValue.hoa,
                        fee: numericValue
                    }
                };
            } else {
                updatedUnit = {...unitValue, [key]: numericValue};
            }
            
            setUnitValue(updatedUnit);
            // Call the debounced update function with the updated unit
            handleUpdateUnit(updatedUnit, unitId, indexData);
        } else {
            const updatedUnit = {...unitValue, [key]: e.target.value};
            setUnitValue(updatedUnit);
            // Call the debounced update function with the updated unit
            handleUpdateUnit(updatedUnit, unitId, indexData);
        }
    }

    const handleAddTag = () => {
        if (!newTag.trim()) return;
        
        // Create a clean tag string (lowercase, underscores instead of spaces)
        const formattedTag = newTag.trim().toLowerCase().replace(/\s+/g, '_');
        
        // Check if tag already exists
        const existingTags = unitValue.tags || [];
        if (existingTags.includes(formattedTag)) {
            setNewTag("");
            return;
        }
        
        // Add the new tag
        const updatedUnit = {
            ...unitValue,
            tags: [...existingTags, formattedTag]
        };
        
        setUnitValue(updatedUnit);
        setNewTag("");
        
        // Update in the database
        handleUpdateUnit(updatedUnit, unitId, indexData);
    };

    const handleDeleteTag = (tagToDelete: string) => {
        const existingTags = unitValue.tags || [];
        
        // Filter out the tag to be deleted
        const updatedUnit = {
            ...unitValue,
            tags: existingTags.filter(tag => tag !== tagToDelete)
        };
        
        setUnitValue(updatedUnit);
        
        // Update in the database
        handleUpdateUnit(updatedUnit, unitId, indexData);
    };

    return (
        <>
            <tr className="hover:bg-gray-50">
                <td className="px-1 py-2 text-left">
                    <input type="text" className="w-20 bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500" value={unitNumberValue} onChange={(e) => {
                        setUnitNumberValue(e.target.value)
                        handleUpdateUnit(unit, unitId, indexData, e.target.value)
                    }} />
                </td>
                <td className="px-1 py-2 text-center">
                    <div className="flex items-center justify-center">
                        <button 
                            type="button" 
                            className="px-2 py-1 bg-gray-200 rounded-l-md hover:bg-gray-300 cursor-pointer" 
                            onClick={() => handleDecrement('beds', 1)}
                        >
                            <FontAwesomeIcon icon={faMinus} className="text-gray-700" />
                        </button>
                        <input type="number" className="w-10 bg-white border-y border-gray-300 px-2 py-1 outline-none focus:border-indigo-500 text-center" value={unitValue?.description?.beds} onChange={(e) => handleUnitChange(e, 'beds')} />
                        <button 
                            type="button" 
                            className="px-2 py-1 bg-gray-200 rounded-r-md hover:bg-gray-300 cursor-pointer" 
                            onClick={() => handleIncrement('beds', 1)}
                        >
                            <FontAwesomeIcon icon={faPlus} className="text-gray-700" />
                        </button>
                    </div>
                </td>
                <td className="px-1 py-2 text-center">
                    <div className="flex items-center justify-center">
                        <button 
                            type="button" 
                            className="px-2 py-1 bg-gray-200 rounded-l-md hover:bg-gray-300 cursor-pointer" 
                            onClick={() => handleDecrement('baths_full', 1)}
                        >
                            <FontAwesomeIcon icon={faMinus} className="text-gray-700" />
                        </button>
                        <input type="number" className="w-10 bg-white border-y border-gray-300 px-2 py-1 outline-none focus:border-indigo-500 text-center" value={unitValue?.description?.baths_full} onChange={(e) => handleUnitChange(e, 'baths_full')} />
                        <button 
                            type="button" 
                            className="px-2 py-1 bg-gray-200 rounded-r-md hover:bg-gray-300 cursor-pointer" 
                            onClick={() => handleIncrement('baths_full', 0.5)}
                        >
                            <FontAwesomeIcon icon={faPlus} className="text-gray-700" />
                        </button>
                    </div>
                </td>
                <td className="px-1 py-2 text-center">
                    <input type="number" className="w-14 text-center bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500" value={unitValue?.description?.sqft} onChange={(e) => handleUnitChange(e, 'sqft')} />
                </td>
                <td className="px-1 py-2 text-center">
                    <select 
                        className="w-21 bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500"
                        value={unitValue?.listing_type || ''}
                        onChange={(e) => handleUnitChange(e, 'listing_type')}
                    >
                        <option value="sold">Sold</option>
                        <option value="for_sale">For Sale</option>
                        <option value="forsale">For Sale</option>
                        <option value="for_rent">For Rent</option>
                        <option value="forrent">For Rent</option>
                        <option value="rented">Rented</option>
                        <option value="pending">Pending</option>
                    </select>
                </td>
                <td className="px-1 py-2 text-center">
                    <input type="number" className="w-18 text-center bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500" value={unitValue?.list_price || ''} onChange={(e) => handleUnitChange(e, 'list_price')} />
                </td>
                <td className="px-1 py-2 text-center">
                    <input type="number" className="w-18 text-center bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500" value={unitValue?.last_sold_price || ''} onChange={(e) => handleUnitChange(e, 'last_sold_price')} />
                </td>
                <td className="px-1 py-2 text-center">
                    <Calendar 
                        value={unitValue?.last_sold_date ? new Date(unitValue.last_sold_date) : null} 
                        onChange={handleDateChange} 
                        className="w-24 text-center bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500"
                        panelClassName="w-[300px] text-sm bg-white text-gray-800 shadow-md rounded-lg p-2 border border-[#E6E1E5] shadow-[0px_4px_10px_0px_rgba(175,175,161,0.41)] market-data-calendar"
                        dateFormat="yy-mm-dd"
                    />
                </td>
                <td className="px-1 py-2 text-center">
                    <input type="number" className="w-14 text-center bg-white border border-gray-300 rounded-md px-2 py-1 outline-none focus:border-indigo-500" value={unitValue?.hoa?.fee || ''} onChange={(e) => handleUnitChange(e, 'hoa_fee')} />
                </td>
                <td className="px-1 py-2 text-center">
                    <div className="flex items-center justify-center relative">
                        <div 
                            className="px-2.5 py-0.5 inline-flex text-xs rounded-full bg-gray-100 text-gray-600 cursor-pointer relative"
                            onMouseEnter={() => setShowTagTooltip(true)}
                            onMouseLeave={() => setShowTagTooltip(false)}
                        >
                            {unitValue?.tags?.length || 'N/A'}
                            {showTagTooltip && unitValue?.tags?.length > 0 && (
                                <div className="tag-tooltip bg-white shadow-lg rounded-md p-2 w-max max-w-xs text-left">
                                    <div className="flex flex-wrap gap-1">
                                        {unitValue?.tags?.map((tag: string, index: number) => (
                                            <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs">
                                                {tag.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                            </span>
                                        ))}
                                    </div>
                                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                                </div>
                            )}
                        </div>
                        <button 
                            ref={tagButtonRef}
                            className="ml-1 text-indigo-500 hover:text-indigo-700"
                            onClick={(e) => {
                                e.stopPropagation();
                                setShowTagInput(!showTagInput);
                            }}
                        >
                            <FontAwesomeIcon icon={faPlus} className="text-xs" />
                        </button>
                        
                        {showTagInput && (
                            <div 
                                ref={tagPopupRef}
                                className="tag-popup bg-white shadow-lg rounded-md p-3 mt-2"
                            >
                                <div className="flex items-center mb-2">
                                    <input
                                        type="text"
                                        value={newTag}
                                        onChange={(e) => setNewTag(e.target.value)}
                                        placeholder="Enter tag"
                                        className="border border-gray-300 rounded-l-md px-2 py-1 text-sm outline-none focus:border-indigo-500"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') handleAddTag();
                                            if (e.key === 'Escape') {
                                                setShowTagInput(false);
                                                setNewTag("");
                                            }
                                        }}
                                    />
                                    <button
                                        className="bg-indigo-500 text-white px-2 py-1 rounded-r-md text-sm hover:bg-indigo-600"
                                        onClick={handleAddTag}
                                    >
                                        Add
                                    </button>
                                </div>
                                
                                {/* Display existing tags with delete option */}
                                {unitValue?.tags && unitValue.tags.length > 0 && (
                                    <div className="mt-2">
                                        <div className="text-xs text-gray-500 mb-1">Existing tags:</div>
                                        <div className="max-h-28 overflow-y-auto">
                                            <div className="flex flex-wrap gap-1">
                                                {unitValue.tags.map((tag: string, index: number) => (
                                                    <div key={index} className="flex items-center bg-gray-100 text-gray-700 rounded-full px-2 py-1 text-xs">
                                                        <span>
                                                            {tag.replace(/_/g, ' ').split(' ').map((word: string) => 
                                                                word.charAt(0).toUpperCase() + word.slice(1)
                                                            ).join(' ')}
                                                        </span>
                                                        <button 
                                                            className="ml-1 text-gray-500 hover:text-red-500"
                                                            onClick={() => handleDeleteTag(tag)}
                                                        >
                                                            <FontAwesomeIcon icon={faMinus} className="text-xs" />
                                                        </button>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}
                                
                                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-45 w-2 h-2 bg-white"></div>
                            </div>
                        )}
                    </div>
                </td>
                <td className="px-1 py-2 text-center">
                    <Fancybox>
                        <button 
                            className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-md text-xs hover:bg-indigo-200 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                            data-fancybox="gallery"
                            data-src={unit?.photos?.[0]?.href}
                        >
                            {unit?.photos?.length} Photos
                        </button>
                        
                        {/* Hidden gallery items */}
                        {unit?.photos?.slice(1).map((img: Photo2, index: number) => (
                            <a 
                                key={index} 
                                data-fancybox="gallery" 
                                href={img.href} 
                                className="hidden"
                            />
                        ))}
                    </Fancybox>
                </td>
            </tr>
            
        </>
        
)
}