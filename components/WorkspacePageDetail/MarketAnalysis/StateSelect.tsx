import { useState, useRef, useEffect } from "react";
import { states } from "@/helpers/states";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useAuth } from "@/context/AuthContext";
import { useParams } from "next/navigation";
import Image from "next/image";

export default function StateSelect({ isSearchPageStart }: { isSearchPageStart: boolean }) {
    const { selectedStates, updateMarketAnalysisState, searchPlace } = useMarketAnalysis();
    const { workspaceSubscriptionDetails } = useAuth();
    const params = useParams();
    const [subscribedStates, setSubscribedStates] = useState<{name: string, abbreviation: string}[]>([])

    // Get subscribed states from workspace subscription details
    useEffect(() => {
        if (workspaceSubscriptionDetails && params?.id) {
            const currentWorkspace = workspaceSubscriptionDetails.find(w => w.id === params.id);
            if (currentWorkspace?.active_states && currentWorkspace.active_states.length > 0) {
                const mappedStates = currentWorkspace.active_states
                    .map(abbr => states.find(state => state.abbreviation === abbr))
                    .filter((state): state is {name: string, abbreviation: string} => state !== undefined);
                
                setSubscribedStates(mappedStates);
                
                // Auto-select all subscribed states - always keep them selected
                updateMarketAnalysisState({ selectedStates: mappedStates });
            }
        }
    }, [workspaceSubscriptionDetails, params?.id]);

    const handleStateClick = (state: {name: string, abbreviation: string}) => {
        const stateExists = selectedStates.some(s => s.abbreviation === state.abbreviation);
        if (stateExists) {
            updateMarketAnalysisState({ selectedStates: selectedStates.filter(s => s.abbreviation !== state.abbreviation) })
        } else {
            updateMarketAnalysisState({ selectedStates: [...selectedStates, state] })
        }
        updateMarketAnalysisState({ errorState: null })
        updateMarketAnalysisState({ searchPlace: '' })
    }

    // Clean State Cards Layout - No Borders, Always Selected
    return (
        <div className="flex flex-wrap items-center justify-center gap-2 max-w-4xl mx-auto">
            {subscribedStates.map((state) => {
                return (
                    <div
                        key={state.abbreviation}
                        className="group relative flex items-center rounded-lg overflow-hidden transition-all duration-300 ease-out transform hover:scale-105 shadow-sm hover:shadow-md"
                    >
                        {/* Flag Section - Left Side - Taller */}
                        <div className="w-13 h-14 relative overflow-hidden">
                            <Image 
                                src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${state.abbreviation.toLowerCase()}.webp`}
                                alt={`${state.abbreviation} flag`}
                                width={52}
                                height={56}
                                className="object-cover"
                                quality={95}
                                priority={true}
                                sizes="52px"
                                style={{
                                    width: '100%',
                                    height: '100%'
                                }}
                            />
                        </div>
                        
                        {/* Text Section - Right Side */}
                        <div className="px-3 py-3 bg-white transition-colors duration-300 group-hover:bg-gray-50">
                            <div className="flex flex-col items-start min-w-0">
                                <span className="font-semibold text-xs leading-tight text-gray-900" style={{ fontSmooth: 'always'}}>
                                    {state.abbreviation}
                                </span>
                                <span className="text-[10px] leading-tight truncate max-w-16 text-gray-500" style={{ fontSmooth: 'always'}}>
                                    {state.name}
                                </span>
                            </div>
                        </div>
                    </div>
                );
            })}
            
            {subscribedStates.length === 0 && (
                <div className="text-center py-8 antialiased">
                    <div className="text-gray-400 mb-2">
                        <svg className="w-12 h-12 mx-auto image-rendering-smooth" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <p className="text-sm text-gray-500 font-medium antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased' }}>No subscribed states found</p>
                    <p className="text-xs text-gray-400 mt-1 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased' }}>Please check your subscription</p>
                </div>
            )}
        </div>
    )
}