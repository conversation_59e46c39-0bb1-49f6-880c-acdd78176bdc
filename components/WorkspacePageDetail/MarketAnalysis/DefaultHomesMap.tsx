'use client'
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useJsApi<PERSON>oader } from '@react-google-maps/api';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import { createGlobalStyle } from 'styled-components';

const containerStyle = {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: '0',
    left: '0',
    backgroundColor: 'transparent',
} as React.CSSProperties;

// State center coordinates (approximate)
const stateCoordinates: { [key: string]: { lat: number; lng: number; zoom: number } } = {
    'AL': { lat: 32.806671, lng: -86.791130, zoom: 7 },
    'AK': { lat: 61.370716, lng: -152.404419, zoom: 4 },
    'AZ': { lat: 33.729759, lng: -111.431221, zoom: 7 },
    'AR': { lat: 34.969704, lng: -92.373123, zoom: 7 },
    'CA': { lat: 36.116203, lng: -119.681564, zoom: 6 },
    'CO': { lat: 39.059811, lng: -105.311104, zoom: 7 },
    'CT': { lat: 41.597782, lng: -72.755371, zoom: 8 },
    'DE': { lat: 39.318523, lng: -75.507141, zoom: 8 },
    'FL': { lat: 27.766279, lng: -81.686783, zoom: 6 },
    'GA': { lat: 33.040619, lng: -83.643074, zoom: 7 },
    'HI': { lat: 21.094318, lng: -157.498337, zoom: 7 },
    'ID': { lat: 44.240459, lng: -114.478828, zoom: 6 },
    'IL': { lat: 40.349457, lng: -88.986137, zoom: 7 },
    'IN': { lat: 39.849426, lng: -86.258278, zoom: 7 },
    'IA': { lat: 42.011539, lng: -93.210526, zoom: 7 },
    'KS': { lat: 38.526600, lng: -96.726486, zoom: 7 },
    'KY': { lat: 37.668140, lng: -84.670067, zoom: 7 },
    'LA': { lat: 31.169546, lng: -91.867805, zoom: 7 },
    'ME': { lat: 44.693947, lng: -69.381927, zoom: 7 },
    'MD': { lat: 39.063946, lng: -76.802101, zoom: 8 },
    'MA': { lat: 42.230171, lng: -71.530106, zoom: 8 },
    'MI': { lat: 43.326618, lng: -84.536095, zoom: 7 },
    'MN': { lat: 45.694454, lng: -93.900192, zoom: 6 },
    'MS': { lat: 32.741646, lng: -89.678696, zoom: 7 },
    'MO': { lat: 38.456085, lng: -92.288368, zoom: 7 },
    'MT': { lat: 46.921925, lng: -110.454353, zoom: 6 },
    'NE': { lat: 41.125370, lng: -98.268082, zoom: 7 },
    'NV': { lat: 38.313515, lng: -117.055374, zoom: 6 },
    'NH': { lat: 43.452492, lng: -71.563896, zoom: 8 },
    'NJ': { lat: 40.298904, lng: -74.521011, zoom: 8 },
    'NM': { lat: 34.840515, lng: -106.248482, zoom: 6 },
    'NY': { lat: 42.165726, lng: -74.948051, zoom: 7 },
    'NC': { lat: 35.630066, lng: -79.806419, zoom: 7 },
    'ND': { lat: 47.528912, lng: -99.784012, zoom: 7 },
    'OH': { lat: 40.388783, lng: -82.764915, zoom: 7 },
    'OK': { lat: 35.565342, lng: -96.928917, zoom: 7 },
    'OR': { lat: 44.572021, lng: -122.070938, zoom: 7 },
    'PA': { lat: 40.590752, lng: -77.209755, zoom: 7 },
    'RI': { lat: 41.680893, lng: -71.511780, zoom: 9 },
    'SC': { lat: 33.856892, lng: -80.945007, zoom: 7 },
    'SD': { lat: 44.299782, lng: -99.438828, zoom: 7 },
    'TN': { lat: 35.747845, lng: -86.692345, zoom: 7 },
    'TX': { lat: 31.054487, lng: -97.563461, zoom: 6 },
    'UT': { lat: 40.150032, lng: -111.862434, zoom: 7 },
    'VT': { lat: 44.045876, lng: -72.710686, zoom: 8 },
    'VA': { lat: 37.769337, lng: -78.169968, zoom: 7 },
    'WA': { lat: 47.400902, lng: -121.490494, zoom: 7 },
    'WV': { lat: 38.491226, lng: -80.954453, zoom: 7 },
    'WI': { lat: 44.268543, lng: -89.616508, zoom: 7 },
    'WY': { lat: 42.755966, lng: -107.302490, zoom: 7 },
    'DC': { lat: 38.897438, lng: -77.026817, zoom: 11 }
};

interface DefaultHome {
    id: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    price: number;
    lat: number;
    lng: number;
}

function DefaultHomesMap() {
    const {isLoaded} = useJsApiLoader({
        id: 'google-map-script',
        googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string,
    })

    const { selectedStates } = useMarketAnalysis();
    const [defaultHomes, setDefaultHomes] = useState<DefaultHome[]>([]);
    const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>({ lat: 39.8283, lng: -98.5795 });
    const [zoomLevel, setZoomLevel] = useState<number>(4);
    const [map, setMap] = useState<google.maps.Map | null>(null);

    

    useEffect(() => {
        if (selectedStates.length > 0) {
            // For now, just center the map on the first selected state
            // In a real implementation, you would fetch homes from the API
            const stateAbbr = selectedStates[0].abbreviation;
            if (stateCoordinates[stateAbbr]) {
                setMapCenter({
                    lat: stateCoordinates[stateAbbr].lat,
                    lng: stateCoordinates[stateAbbr].lng
                });
                setZoomLevel(stateCoordinates[stateAbbr].zoom);
            }

            // This would be replaced with a real API call
            // For demonstration, we'll create some dummy homes
            const dummyHomes: DefaultHome[] = [];
            selectedStates.forEach(state => {
                const stateCenter = stateCoordinates[state.abbreviation];
                if (stateCenter) {
                    // Generate 5 random homes around the state center
                    for (let i = 0; i < 5; i++) {
                        const latOffset = (Math.random() - 0.5) * 2;
                        const lngOffset = (Math.random() - 0.5) * 2;
                        dummyHomes.push({
                            id: `${state.abbreviation}-${i}`,
                            address: `${i+100} Main St`,
                            city: `${state.name} City`,
                            state: state.abbreviation,
                            zip: `${10000 + Math.floor(Math.random() * 90000)}`,
                            price: 300000 + Math.floor(Math.random() * 700000),
                            lat: stateCenter.lat + latOffset,
                            lng: stateCenter.lng + lngOffset
                        });
                    }
                }
            });
            setDefaultHomes(dummyHomes);

            // TODO: In the future, replace with actual API call:
            // fetch('/api/search-default-homes?state=' + selectedStates.map(s => s.abbreviation).join(','))
            //   .then(res => res.json())
            //   .then(data => setDefaultHomes(data));
        }
    }, [selectedStates]);

    const onLoad = React.useCallback(function callback(map: google.maps.Map) {
        setMap(map);
    }, []);

    const onUnmount = React.useCallback(function callback() {
        setMap(null);
    }, []);

    const StyledMarker = createGlobalStyle`
        .home-marker {
            background: #ff5722;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.3));
            box-sizing: border-box;
        }
    `;

    const fitMapToMarkers = () => {
        if (map && defaultHomes.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            defaultHomes.forEach(home => {
                bounds.extend(new google.maps.LatLng(home.lat, home.lng));
            });
            
            // Only fit bounds if we have multiple homes
            if (defaultHomes.length > 1) {
                map.fitBounds(bounds);
            }
        }
    };

    useEffect(() => {
        if (map && defaultHomes.length > 0) {
            fitMapToMarkers();
        }
    }, [map, defaultHomes]);

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            maximumFractionDigits: 0
        }).format(price);
    };

    return isLoaded ? (
        <div className="h-[600px] w-full relative rounded-lg overflow-hidden">
            <StyledMarker />
            <GoogleMap
                mapContainerStyle={containerStyle}
                center={mapCenter}
                zoom={zoomLevel}
                onLoad={onLoad}
                onUnmount={onUnmount}
                options={{
                    disableDefaultUI: false,
                    zoomControl: true,
                    mapTypeControl: false,
                    scaleControl: false,
                    streetViewControl: true,
                    rotateControl: true,
                    fullscreenControl: true
                }}
            >
                {defaultHomes.map((home) => (
                    <Marker
                        key={home.id}
                        position={{ lat: home.lat, lng: home.lng }}
                        icon={{
                            url: ' ',
                            scaledSize: new google.maps.Size(16, 16),
                            anchor: new google.maps.Point(8, 8),
                        }}
                        label={{
                            text: ' ',
                            className: 'home-marker'
                        }}
                        title={`${home.address}, ${home.city}, ${home.state} - ${formatPrice(home.price)}`}
                    />
                ))}
            </GoogleMap>
        </div>
    ) : (
        <div className="h-[600px] w-full bg-gray-100 animate-pulse rounded-lg flex items-center justify-center">
            <p className="text-gray-400">Loading map...</p>
        </div>
    );
}

export default DefaultHomesMap; 