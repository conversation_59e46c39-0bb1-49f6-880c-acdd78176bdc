import { useState, useEffect } from 'react';
import { fetchFredCharts, generateFredChartsSummary, Fred<PERSON><PERSON> } from '@/actions/fredActions';
import { getFredChartPages } from '@/utils/fredUtils';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine, faChevronLeft, faChevronRight, faRobot } from '@fortawesome/free-solid-svg-icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Spinner from '@/components/UI/Spinner';

export default function FredChartsWithSummary() {
    const [charts, setCharts] = useState<FredChart[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingSummary, setIsLoadingSummary] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [totalPages, setTotalPages] = useState(1);
    const [aiSummary, setAiSummary] = useState<string>('');
    
    const chartsPerPage = 3;

    useEffect(() => {
        // Calculate total pages synchronously
        const pages = getFredChartPages(chartsPerPage);
        setTotalPages(pages);
    }, [chartsPerPage]);

    useEffect(() => {
        if (totalPages > 0) {
            fetchCharts();
        }
    }, [currentPage, totalPages]);

    const fetchCharts = async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const fredCharts = await fetchFredCharts(currentPage, chartsPerPage);
            setCharts(fredCharts);
            
            // Generate AI summary for these charts
            if (fredCharts.length > 0) {
                setIsLoadingSummary(true);
                try {
                    const summary = await generateFredChartsSummary(fredCharts);
                    setAiSummary(summary);
                } catch (summaryError) {
                    console.error('Error generating summary:', summaryError);
                    setAiSummary('Economic analysis not available');
                } finally {
                    setIsLoadingSummary(false);
                }
            }
        } catch (err) {
            console.error('Error fetching FRED charts:', err);
            setError('Failed to load economic data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const formatValue = (value: number, units: string) => {
        if (units.toLowerCase().includes('percent')) {
            return `${value.toFixed(2)}%`;
        }
        if (units.toLowerCase().includes('dollar')) {
            return `$${value.toLocaleString()}`;
        }
        if (units.toLowerCase().includes('thousand')) {
            return `${value.toLocaleString()}K`;
        }
        return value.toLocaleString();
    };

    const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short' 
        });
    };

    if (isLoading) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="flex flex-col items-center justify-center py-10">
                    <Spinner size="lg" className="mb-3" />
                    <p className="text-sm text-gray-600">Loading economic data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="text-center py-10">
                    <FontAwesomeIcon icon={faChartLine} className="text-gray-400 text-4xl mb-4" />
                    <p className="text-red-600 mb-2">{error}</p>
                    <button 
                        onClick={fetchCharts}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            {/* Header with Pagination */}
            <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <FontAwesomeIcon icon={faChartLine} className="text-indigo-600 text-xl" />
                        <h3 className="text-lg font-semibold text-gray-900">
                            Economic Indicators
                        </h3>
                        <span className="text-sm text-gray-500">
                            Page {currentPage} of {totalPages}
                        </span>
                    </div>
                    
                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                        <div className="flex items-center gap-2">
                            <button
                                onClick={goToPrevPage}
                                disabled={currentPage === 1}
                                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faChevronLeft} className="text-sm" />
                            </button>
                            <button
                                onClick={goToNextPage}
                                disabled={currentPage === totalPages}
                                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faChevronRight} className="text-sm" />
                            </button>
                        </div>
                    )}
                </div>
            </div>

            {/* Charts Grid */}
            <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    {charts.map((chart) => (
                        <div key={chart.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="mb-4">
                                <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                                    {chart.title}
                                </h4>
                                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                                    {chart.description}
                                </p>
                                <div className="text-xs text-gray-500">
                                    {chart.units || 'Index'} • {chart.data.length} points
                                </div>
                            </div>
                            
                            <div className="h-48">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={chart.data}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                        <XAxis 
                                            dataKey="date" 
                                            tick={{ fontSize: 10 }}
                                            tickFormatter={formatDate}
                                        />
                                        <YAxis 
                                            tick={{ fontSize: 10 }}
                                            tickFormatter={(value) => formatValue(value, chart.units)}
                                        />
                                        <Tooltip 
                                            labelFormatter={(label) => formatDate(label)}
                                            formatter={(value: number) => [
                                                formatValue(value, chart.units), 
                                                chart.units || 'Value'
                                            ]}
                                            contentStyle={{
                                                backgroundColor: '#f9fafb',
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px',
                                                fontSize: '12px'
                                            }}
                                        />
                                        <Line 
                                            type="monotone" 
                                            dataKey="value" 
                                            stroke="#4f46e5" 
                                            strokeWidth={2}
                                            dot={false}
                                            activeDot={{ r: 3, fill: '#4f46e5' }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    ))}
                </div>

                {/* AI Summary */}
                {aiSummary && (
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <div className="flex items-center gap-2 mb-3">
                            <FontAwesomeIcon icon={faRobot} className="text-blue-600" />
                            <h4 className="text-sm font-medium text-blue-900">AI Economic Analysis</h4>
                            {isLoadingSummary && <Spinner size="sm" />}
                        </div>
                        <p className="text-sm text-blue-800 leading-relaxed">
                            {aiSummary}
                        </p>
                    </div>
                )}

                {charts.length === 0 && !isLoading && (
                    <div className="text-center py-10">
                        <FontAwesomeIcon icon={faChartLine} className="text-gray-400 text-4xl mb-4" />
                        <p className="text-gray-600">No economic data available for this page.</p>
                    </div>
                )}
            </div>
        </div>
    );
} 