import React from 'react';
import GoogleMapComponent from './GoogleMap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGlobe, faXmark } from '@fortawesome/free-solid-svg-icons';

interface MapCardProps {
  latitude: number;
  longitude: number;
  selectedLocation: { [key: string]: any }[];
  colorPrimary: string[];
  handleSelectedLocation: (current: { [key: string]: any }[], address: string, lat: number, lng: number) => void;
  setColorPrimary: React.Dispatch<React.SetStateAction<string[]>>;
}

export default function MapCard({
  latitude,
  longitude,
  selectedLocation,
  colorPrimary,
  handleSelectedLocation,
  setColorPrimary,
}: MapCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex flex-col">
      {/* Card Header */}
      <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
            <FontAwesomeIcon icon={faGlobe} className="h-4 w-4 text-indigo-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800">Map</h3>
        </div>
      </div>
      {/* Card Content */}
      <div className="p-6 flex-1 flex flex-col overflow-hidden">
        {selectedLocation.length > 0 ? (
          <div className="flex flex-wrap gap-2 mb-4">
            {selectedLocation.map((loc, idx) => (
              <div
                key={idx}
                className="flex items-center gap-x-2 px-3 py-1 rounded-full text-sm text-white"
                style={{ backgroundColor: colorPrimary[idx] }}
              >
                <p>{loc.address}</p>
                <FontAwesomeIcon
                  icon={faXmark}
                  className="text-white cursor-pointer"
                  onClick={() =>
                    handleSelectedLocation(
                      selectedLocation,
                      loc.address,
                      loc.latitude,
                      loc.longitude
                    )
                  }
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500 mb-4">No places selected yet</div>
        )}
        <div className="relative w-full flex-1 rounded-lg overflow-hidden">
          <GoogleMapComponent
            latitude={latitude}
            longitude={longitude}
            selectedLocation={selectedLocation}
            setColorPrimary={setColorPrimary}
          />
        </div>
      </div>
    </div>
  );
} 