import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFile, faPlus, faCheck, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { useEffect, useRef } from "react";
import { useState } from "react";
import { useParams } from "next/navigation";
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import { createClient } from "@/utils/supabase/client";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { getPortfolios } from "@/actions/portfolioActions";
import Link from "next/link";
import pathName from "@/constants/pathName";
import modalTriggerType from "@/constants/modalTriggerType";
import { cachePropData } from "@/actions/propertyActions";
import { documentGenerateSlides } from "@/actions/documentGenerateSlides";
import { useAuth } from "@/context/AuthContext";

import Spinner from "@/components/UI/Spinner";
import { fetchNearbyPlacesWithGoogleMaps } from "@/actions/marketAnalysisActions/googleMapsActions";
import { fetchSchoolData } from "@/actions/marketAnalysisActions/cherreActions";
import { getTransitData } from "@/actions/marketAnalysisActions/unitDataActions";

interface SaveToPortfolioProps {
    portfolios: any[]
    setPortfolios: React.Dispatch<React.SetStateAction<any[]>>
}

export default function SaveToPortfolio({portfolios, setPortfolios}: SaveToPortfolioProps){
    const { searchPlace, unitData, marketData, mainImage, aiSummary, nearbyLocations, schoolData, myLocations, transitData, demographicsData, solarData, taxHistory, propertyDetails, allImageUrls } = useMarketAnalysis();
    const {showModal, modalData, updateModalData, updateModalTrigger, modalTrigger} = useModal()   
    const { user } = useAuth();
    const params = useParams();
    const [isOpen, setIsOpen] = useState<boolean>(false)
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [existingAddresses, setExistingAddresses] = useState<{ [key: string]: any }[] | null>(null);
    const [isSaved, setIsSaved] = useState<boolean>(false)
    const [isSavingLoading, setIsSavingLoading] = useState<boolean>(false)
    

    const addressSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('street_number'))?.long_name + ' ' + searchPlace?.address_components?.find((component: any) => component.types.includes('route'))?.long_name
    const displayAddressSearch = typeof searchPlace === 'object' && searchPlace?.formatted_address
    const citySearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('locality'))?.short_name
    const stateSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('administrative_area_level_1'))?.short_name
    const zipcodeSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('postal_code'))?.short_name
    const latSearch = typeof searchPlace === 'object' && searchPlace?.geometry?.location?.lat()
    const lonSearch = typeof searchPlace === 'object' && searchPlace?.geometry?.location?.lng()

    const displayAddress = propertyDetails?.address?.display_address || displayAddressSearch
    const address = propertyDetails?.address?.street_address || addressSearch
    const city = propertyDetails?.address?.city || citySearch
    const state = propertyDetails?.address?.state || stateSearch
    const zipcode = propertyDetails?.address?.zip || zipcodeSearch
    const lat = propertyDetails?.address?.latitude || latSearch
    const lon = propertyDetails?.address?.longitude || lonSearch

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        }

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isOpen]);



    useEffect(() => {
        if(typeof searchPlace === 'string' || portfolios.length === 0) return;

        const fetchExistingAddresses = async () => {
           
            
            const supabase = createClient();
            
            const { data: existingAddresses } = await supabase
              .from('prop_addresses')
              .select('*')
              .eq('address', address || '')
              .eq('city', city || '')
              .eq('state', state || '')
              .eq('zip', zipcode || '')
              .eq('lat', lat || '')
              .eq('lon', lon || '');
            
            if(existingAddresses && existingAddresses?.length > 0) {
                const portfolioAddressIds = existingAddresses.map(portfolio => portfolio.id);
                const portfolioIds = portfolios.map(portfolio => portfolio.id);
                const { data: existingProp } = await supabase
                    .from('prop')
                    .select('*')
                    .eq('is_deleted', false)
                    .in('address_id', portfolioAddressIds)
                    .in('portfolio_id', portfolioIds);

                    
                   
                setExistingAddresses(existingProp);
            }
        };

        fetchExistingAddresses();
    }, [searchPlace, isSaved, portfolios]);

    const handleSaveToPortfolio = async (portfolioId: string) => {
        if(typeof searchPlace === 'string') return;
        //setIsSavingLoading(true)
        

        const supabase = createClient();
        
        
        if(!existingAddresses?.find((prop: any) => prop.portfolio_id === portfolioId)) {
            const { data: newAddress, error: newAddressError } = await supabase
                .from('prop_addresses')
                .insert({
                    address: address || '',
                    city: city || '',
                    state: state || '',
                    zip: zipcode || '',
                    lat: lat || '',
                    lon: lon || ''
                })
                .select()

            if(newAddressError) {
                console.error('Error getting address:', newAddressError);
            }

            if(newAddress && newAddress.length > 0) {
                const { data: newProp } = await supabase
                    .from('prop')
                    .insert({
                        address_id: newAddress[0].id,
                        portfolio_id: portfolioId,
                        main_img_url: mainImage,
                        img_urls: allImageUrls || [],
                    })
                    .select()

                if(newProp && newProp.length > 0) {
                    fetch('/api/save-portfolio/ai-summary', {
                        method: 'POST',
                        body: JSON.stringify({
                            aiSummary: aiSummary,
                            displayAddress: displayAddress,
                            newPropId: newProp[0].id
                        })
                    })

                    const unitsDataInsert = unitData?.map((unit: any) => ({
                        id: unit?.id,
                        prop_id: newProp[0].id,
                        unit: unit?.unit || 'N/A',
                        beds: unit?.bed_count || 0,
                        baths: unit?.bath_count || 0,
                        hoa_fee: unit?.hoa_fee || 0,
                        document_recorded_date: unit?.document_recorded_date || null,
                    }))

                    const marketDataInsert = marketData?.map((market: any) => ({
                        prop_id: newProp[0].id,
                        unit: market.unit,
                        data: market.data,
                        unit_id: market.unit_id
                    }))

                    fetch('/api/save-portfolio/units', { 
                        method: 'POST',
                        body: JSON.stringify({
                            unitData: unitsDataInsert,
                            marketData: marketDataInsert,
                            newPropId: newProp[0].id,
                            address: address,
                            city: city,
                            state: state,
                            zipcode: zipcode,
                        })
                    })

                    const demographicsDataInsert = {
                        prop_id: newProp[0].id,
                        vintage: demographicsData?.demographics?.vintage,
                        population_2020_count: demographicsData?.demographics?.population_2020_count,
                        population_median_age: demographicsData?.demographics?.population_median_age,
                        median_household_income: demographicsData?.demographics?.median_household_income,
                        average_household_income: demographicsData?.demographics?.average_household_income,
                        crime_total_risk: demographicsData?.demographics?.crime_total_risk,
                    }

                    fetch('/api/save-portfolio/demographics', {
                        method: 'POST',
                        body: JSON.stringify({
                            demographicsData: demographicsDataInsert,
                            newPropId: newProp[0].id,
                            address: address,
                            city: city,
                            state: state,
                            zipcode: zipcode,
                        })
                    })

                    const taxHistoryInsert = taxHistory?.tax_records?.map((tax: any) => ({
                        prop_id: newProp[0].id,
                        assessed_tax_year: tax?.assessed_tax_year,
                        assessed_value_land: tax?.assessed_value_land,
                        assessed_value_total: tax?.assessed_value_total,
                        tax_bill_amount: tax?.tax_bill_amount,
                    }))

                    fetch('/api/save-portfolio/tax-history', {
                        method: 'POST',
                        body: JSON.stringify({
                            taxHistory: taxHistoryInsert,
                            newPropId: newProp[0].id,
                            address: address,
                            city: city,
                            state: state,
                            zipcode: zipcode,
                        })
                    })

                    fetch('/api/save-portfolio/property-details', {
                        method: 'POST',
                        body: JSON.stringify({
                            propertyDetails: propertyDetails,
                            newPropId: newProp[0].id,
                            address: address,
                            city: city,
                            state: state,
                            zipcode: zipcode,
                        })
                    })

                    const nearbyLocationsData = nearbyLocations || await fetchNearbyPlacesWithGoogleMaps(displayAddress, lat, lon)
                    

                    fetch('/api/save-portfolio/nearby-locations', {
                        method: 'POST',
                        body: JSON.stringify({
                            nearbyLocations: nearbyLocationsData,
                            schoolData: schoolData,
                            myLocations: myLocations,
                            transitData: transitData,
                            newPropId: newProp[0].id,
                            address: address,
                            zipcode: zipcode,
                            displayAddress: displayAddress,
                            lat: lat,
                            lon: lon,
                        })
                    })

                    fetch('/api/save-portfolio/solar-potential', {
                        method: 'POST',
                        body: JSON.stringify({
                            solarData: solarData,
                            newPropId: newProp[0].id,
                            lat: lat,
                            lon: lon,
                        })
                    })

                    supabase.from('prop_financials').insert(
                        Array.from({ length: 5 }, (_, i) => ({
                            prop_id: newProp[0].id,
                            year: i + 1,
                        }))
                    )
                        
                    cachePropData(portfolioId, newProp[0].id)

                    documentGenerateSlides(portfolioId, user?.user?.id as string);

                    setIsSavingLoading(false)
                    setIsSaved(true)

                    setTimeout(() => {
                        setIsSaved(false)
                    }, 1000)

                }
                
            }

            /*const { data: newAddress, error: newAddressError } = await supabase
                .from('prop_addresses')
                .insert({
                    address: address || '',
                    city: city || '',
                    state: state || '',
                    zip: zipcode || '',
                    lat: lat || '',
                    lon: lon || ''
                })
                .select()
            
            if(newAddressError) {
                console.error('Error getting address:', newAddressError);
            }

            if (newAddress && newAddress.length > 0) {
                const { data: newProp } = await supabase
                    .from('prop')
                    .insert({
                        address_id: newAddress[0].id,
                        portfolio_id: portfolioId,
                        main_img_url: mainImage,
                        img_urls: allImageUrls || [],
                        ai_summary: aiSummary
                    })
                    .select()
                
                if (newProp && newProp.length > 0) {
                    const unitsDataInsert = unitData?.map((unit: any) => ({
                        id: unit?.id,
                        prop_id: newProp[0].id,
                        unit: unit?.unit || 'N/A',
                        beds: unit?.bed_count || 0,
                        baths: unit?.bath_count || 0,
                        hoa_fee: unit?.hoa_fee || 0,
                        document_recorded_date: unit?.document_recorded_date || null,
                    }))

                    const { data: newUnits, error: newUnitsError } = await supabase
                        .from('prop_units')
                        .insert(unitsDataInsert)
                        .select()

                    if (newUnitsError) {
                        console.error('Error saving units data:', newUnitsError);
                    }
                    
                    // Create a mapping of original unit IDs to new unit IDs
                    const unitIdMapping: {[key: string]: string} = {};
                    if (newUnits && newUnits.length > 0) {
                        unitData?.forEach((unit: any, index: number) => {
                            if (index < newUnits.length) {
                                unitIdMapping[unit.id] = newUnits[index].id;
                            }
                        });
                    }

                    const marketDataInsert = marketData?.map((market: any) => ({
                        prop_id: newProp[0].id,
                        unit: market.unit,
                        data: market.data,
                        unit_id: market.unit_id//unitIdMapping[market.unit_id] || null
                    }))

                    const { data: newMarketData, error: newMarketDataError } = await supabase
                        .from('prop_market_data')
                        .insert(marketDataInsert)
                        .select()

                    if (newMarketDataError) {
                        console.error('Error saving market data:', newMarketDataError);
                    }

                    const {data: newDemographics, error: newDemographicsError} = await supabase
                        .from('prop_demographics')
                        .insert({
                            prop_id: newProp[0].id,
                            vintage: demographicsData?.demographics?.vintage,
                            population_2020_count: demographicsData?.demographics?.population_2020_count,
                            population_median_age: demographicsData?.demographics?.population_median_age,
                            median_household_income: demographicsData?.demographics?.median_household_income,
                            average_household_income: demographicsData?.demographics?.average_household_income,
                            crime_total_risk: demographicsData?.demographics?.crime_total_risk,
                        })
                    
                    if (newDemographicsError) {
                        console.error('Error saving demographics data:', newDemographicsError);
                    }


                    const taxHistoryInsert = taxHistory?.tax_records?.map((tax: any) => ({
                        prop_id: newProp[0].id,
                        assessed_tax_year: tax?.assessed_tax_year,
                        assessed_value_land: tax?.assessed_value_land,
                        assessed_value_total: tax?.assessed_value_total,
                        tax_bill_amount: tax?.tax_bill_amount,
                    }))

                    const {data: newTaxHistory, error: newTaxHistoryError} = await supabase
                        .from('prop_tax_history')
                        .insert(taxHistoryInsert)

                    if (newTaxHistoryError) {
                        console.error('Error saving tax history data:', newTaxHistoryError);
                    }

                    const {data: newPropertyDetails, error: newPropertyDetailsError} = await supabase
                        .from('prop_details')
                        .insert({
                            prop_id: newProp[0].id,
                            ...propertyDetails?.data
                        })

                    if (newPropertyDetailsError) {
                        console.error('Error saving property details data:', newPropertyDetailsError);
                    }


                    const {data: newNearbyLocations, error: newNearbyLocationsError} = await supabase
                        .from('pois')
                        .insert([
                            {
                                prop_id: newProp[0].id,
                                data: nearbyLocations || [],
                                type: 'nearby_locations'
                            },
                            {
                                prop_id: newProp[0].id,
                                data: schoolData || [],
                                type: 'schools'
                            },
                            {
                                prop_id: newProp[0].id,
                                data: myLocations || [],
                                type: 'my_locations'
                            },
                            {
                                prop_id: newProp[0].id,
                                data: transitData || [],
                                type: 'transit'
                            }
                        ])
                        

                    const {data: newSolarData, error: newSolarDataError} = await supabase
                        .from('prop_solar_potential')
                        .insert({
                            prop_id: newProp[0].id,
                            data: solarData || [],
                        })

                    const {data: newFinancials, error: newFinancialsError} = await supabase
                        .from('prop_financials')
                        .insert(
                            Array.from({ length: 5 }, (_, i) => ({
                                prop_id: newProp[0].id,
                                year: i + 1,
                            }))
                        )
                        

                    cachePropData(portfolioId, newProp[0].id).then((res) => {
                        console.log(res)
                    })

                    documentGenerateSlides(portfolioId, user?.user?.id as string);

                    setIsSavingLoading(false)
                    setIsSaved(true)

                    setTimeout(() => {
                        setIsSaved(false)
                    }, 1000)
                }
            }*/
        }
        
    }

    return (
        <>
            {
                !isSavingLoading && !isSaved ? (
                    <button 
                        onClick={() => setIsOpen(!isOpen)}
                        className="flex items-center gap-x-2 px-3 py-1.5 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-md cursor-pointer hover:bg-indigo-100 transition">
                            <div className="flex items-center gap-x-2">
                                <FontAwesomeIcon icon={faPlus} className="text-indigo-600" />
                                Save Property
                            </div>
                            {existingAddresses && existingAddresses?.length > 0 && <span className="px-1.5 py-0.5 text-xs rounded-full bg-green-100 text-green-800">{existingAddresses?.length}</span>}
                    </button>
                ) : null
            }
            {
                isSaved ? (
                    <button 
                        onClick={() => setIsOpen(!isOpen)}
                        className="flex items-center gap-x-2 px-3 py-1.5 text-sm font-medium text-green-600 bg-green-50 rounded-md cursor-pointer hover:bg-green-100 transition">
                        <FontAwesomeIcon icon={faCheck} className="text-green-600" />Property Saved
                    </button>
                ) : null
            }
            {
                isSavingLoading ? (
                    <div className="flex items-center gap-2">
                        <Spinner size="sm" />
                        <span>Saving...</span>
                    </div>
                ) : null
            }
            
            {
                isOpen && (
                    <div ref={dropdownRef} className="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg z-[9999] border border-gray-200">
                        <div className="p-2 border-b border-gray-200">
                            <h3 className="text-sm font-medium">Save to Portfolio</h3>
                        </div>
                        {
                            portfolios.length > 0 ? (
                            <div className="max-h-60 overflow-y-auto">
                                {
                                portfolios.map((portfolio) => {
                                    const existingProp = existingAddresses?.find((prop: any) => prop.portfolio_id === portfolio.id)
                                    
                                    return (
                                        <div 
                                            onClick={() => !existingProp ? handleSaveToPortfolio(portfolio.id) : null} 
                                            key={portfolio.id} className={`w-full text-left px-4 py-2 text-sm flex items-center justify-between text-gray-700 hover:bg-gray-100 cursor-pointer ${existingProp ? 'bg-green-50' : ''}`}>
                                                <div className="flex items-center gap-x-2">
                                                    {existingProp ? <FontAwesomeIcon icon={faCheck} className="text-green-600" /> : <FontAwesomeIcon icon={faFile} className="text-indigo-600" />}
                                                    <span className={`${existingProp ? 'text-green-600' : 'text-gray-700'}`}>{portfolio.name}</span>
                                                </div>
                                                {
                                                    existingProp && (
                                                        <>
                                                            <Link href={`${pathName.workspace}/${params?.id}?portfolioId=${portfolio.id}`}>
                                                                <div className="flex items-center gap-x-2">
                                                                    <span className="text-xs text-green-600">Saved</span>
                                                                    <FontAwesomeIcon icon={faChevronRight} className="text-green-600 text-xs" />
                                                                </div>
                                                            </Link>
                                                        </>
                                                    )
                                                }
                                                
                                        </div>
                                    )
                                })
                                }
                            </div>
                            ): <div className="p-3 text-sm text-gray-500 text-center">No portfolios found</div>
                        }
                        
                        <div className="border-t border-gray-200">
                            <button 
                                onClick={() => {
                                    showModal(modalType.createPortfolio)
                                    updateModalData({
                                        workspaceId: params?.id as string,
                                        saveToPortfolio: true
                                    })
                                }}
                                className="w-full text-left px-4 py-2 text-sm text-indigo-600 hover:bg-gray-100 flex items-center cursor-pointer">
                            <FontAwesomeIcon icon={faPlus} className="text-indigo-600 mr-2" />Create New Portfolio
                            </button>
                        </div>
                    </div>
                )
            }
        </>
    )
}