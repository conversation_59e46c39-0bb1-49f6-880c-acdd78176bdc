import { useState, useEffect } from 'react';
import { fetchFredCharts, Fred<PERSON>hart } from '@/actions/fredActions';
import { getFredChartPages } from '@/utils/fredUtils';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine, faChevronLeft, faChevronRight, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Spinner from '@/components/UI/Spinner';

export default function FredCharts() {
    const [charts, setCharts] = useState<FredChart[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [totalPages, setTotalPages] = useState(1);
    
    const chartsPerPage = 3;

    useEffect(() => {
        // Calculate total pages synchronously
        const pages = getFredChartPages(chartsPerPage);
        setTotalPages(pages);
    }, [chartsPerPage]);

    useEffect(() => {
        if (totalPages > 0) {
            fetchCharts();
        }
    }, [currentPage, totalPages]);

    const fetchCharts = async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const fredCharts = await fetchFredCharts(currentPage, chartsPerPage);
            setCharts(fredCharts);
        } catch (err) {
            console.error('Error fetching FRED charts:', err);
            setError('Failed to load economic data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const formatValue = (value: number, units: string) => {
        if (units.toLowerCase().includes('percent')) {
            return `${value.toFixed(2)}%`;
        }
        if (units.toLowerCase().includes('dollar')) {
            return `$${value.toLocaleString()}`;
        }
        if (units.toLowerCase().includes('thousand')) {
            return `${value.toLocaleString()}K`;
        }
        return value.toLocaleString();
    };

    const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short' 
        });
    };

    if (isLoading) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="flex flex-col items-center justify-center py-10">
                    <Spinner size="lg" className="mb-3" />
                    <p className="text-sm text-gray-600">Loading economic data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="text-center py-10">
                    <FontAwesomeIcon icon={faChartLine} className="text-gray-400 text-4xl mb-4" />
                    <p className="text-red-600 mb-2">{error}</p>
                    <button 
                        onClick={fetchCharts}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                        <div className="flex items-center gap-2">
                            <button
                                onClick={goToPrevPage}
                                disabled={currentPage === 1}
                                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faChevronLeft} className="text-sm" />
                            </button>
                            <span className="text-sm text-gray-600 px-3">
                                {currentPage} of {totalPages}
                            </span>
                            <button
                                onClick={goToNextPage}
                                disabled={currentPage === totalPages}
                                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faChevronRight} className="text-sm" />
                            </button>
                        </div>
                    )}
                </div>
                <p className="text-gray-600 mt-2">Real estate and housing market indicators from the Federal Reserve Economic Data.</p>
            </div>

            <div className="p-6">
                <div className="grid gap-6 lg:grid-cols-1">
                    {charts.map((chart) => (
                        <div key={chart.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="mb-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-1">
                                    {chart.title}
                                </h3>
                                <p className="text-sm text-gray-600 mb-2">
                                    {chart.description}
                                </p>
                                <div className="flex items-center gap-4 text-xs text-gray-500">
                                    <span>Units: {chart.units || 'Index'}</span>
                                    <span>Last Updated: {formatDate(chart.lastUpdated)}</span>
                                    <span>{chart.data.length} data points</span>
                                </div>
                            </div>
                            
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={chart.data}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                        <XAxis 
                                            dataKey="date" 
                                            tick={{ fontSize: 12 }}
                                            tickFormatter={formatDate}
                                        />
                                        <YAxis 
                                            tick={{ fontSize: 12 }}
                                            tickFormatter={(value) => formatValue(value, chart.units)}
                                        />
                                        <Tooltip 
                                            labelFormatter={(label) => formatDate(label)}
                                            formatter={(value: number) => [
                                                formatValue(value, chart.units), 
                                                chart.units || 'Value'
                                            ]}
                                            contentStyle={{
                                                backgroundColor: '#f9fafb',
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px',
                                                fontSize: '12px'
                                            }}
                                        />
                                        <Line 
                                            type="monotone" 
                                            dataKey="value" 
                                            stroke="#4f46e5" 
                                            strokeWidth={2}
                                            dot={false}
                                            activeDot={{ r: 4, fill: '#4f46e5' }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    ))}
                </div>

                {charts.length === 0 && !isLoading && (
                    <div className="text-center py-10">
                        <FontAwesomeIcon icon={faChartLine} className="text-gray-400 text-4xl mb-4" />
                        <p className="text-gray-600">No economic data available for this page.</p>
                    </div>
                )}
            </div>
        </div>
    );
} 