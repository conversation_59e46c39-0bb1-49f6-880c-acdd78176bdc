'use client';
import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { 
    getSearchHistory, 
    clearSearchHistory, 
    SearchHistoryItem, 
    formatSearchDate 
} from '@/utils/searchHistory';
import { faHome } from '@fortawesome/free-solid-svg-icons';
import { useRecentSearch } from '@/helpers/hooks/marketAnalysis/useRecentSearch';


export default function LocalSearchHistory() {
    const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);
    const { handleRecentSearchSelect } = useRecentSearch();

    // Load search history from cookies on component mount
    useEffect(() => {
        const history = getSearchHistory();
        setSearchHistory(history);
    }, []);

    // Truncate address for display
    const truncateAddress = (address: string, maxLength: number = 50) => {
        if (address.length <= maxLength) return address;
        return address.substring(0, maxLength) + '...';
    };

    // Only show history on search page start and when there are items
    if (searchHistory.length === 0) {
        return null;
    }

    return (
        <div className="w-full max-w-2xl mx-auto">
            {/* Modern Header */}
            {/*<div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-1 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>Recently Searched</h3>
                <p className="text-sm text-gray-500 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>Pick up where you left off</p>
            </div>*/}

            {/* Modern Search History Cards */}
            <div className="grid gap-3">
                {searchHistory.slice(0, 4).map((item) => (
                    <button
                        key={item.id}
                        onClick={() => handleRecentSearchSelect(item.address)}
                        onMouseEnter={() => setHoveredItem(item.id)}
                        onMouseLeave={() => setHoveredItem(null)}
                        className={`
                            group w-full p-4 bg-white rounded-xl border border-gray-200 
                            transition-all duration-300 text-left
                            hover:border-purple-300 hover:shadow-lg hover:shadow-purple-500/10
                            ${hoveredItem === item.id ? 'transform scale-[1.02] border-purple-400' : ''}
                        `}
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                                {/* Location Icon */}
                                <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-purple-100 transition-colors duration-300">
                                    <svg className={`w-5 h-5 transition-colors duration-300 ${hoveredItem === item.id ? 'text-purple-600' : 'text-gray-500'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                
                                {/* Address Info */}
                                <div className="flex-1 min-w-0">
                                    <p className="font-medium text-gray-900 truncate mb-1 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>
                                        {truncateAddress(item.address, 60)}
                                    </p>
                                    <p className="text-xs text-gray-500 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>
                                        Searched {formatSearchDate(item.searchedAt)}
                                    </p>
                                </div>
                            </div>
                            
                            {/* Arrow Icon */}
                            <div className={`
                                w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300
                                ${hoveredItem === item.id ? 'bg-purple-100 text-purple-600 transform translate-x-1' : 'bg-gray-50 text-gray-400'}
                            `}>
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </button>
                ))}
            </div>

            {/* More Items Indicator */}
            {searchHistory.length > 4 && (
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-500 antialiased mb-5" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>
                        {searchHistory.length - 4}+ more searches available
                    </p>
                </div>
            )}
        </div>
    );
} 