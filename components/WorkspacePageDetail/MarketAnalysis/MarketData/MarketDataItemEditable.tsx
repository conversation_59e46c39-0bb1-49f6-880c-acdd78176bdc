import { updateUnit, removeUnit } from "@/actions/propertyMarketDataActions"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext"
import Fancybox from "@/helpers/fancyBox/fancyBox"
import { useDebounce } from "@/helpers/hooks/useDebounce"
import { faTrash } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { useState } from "react"
import { InputMask, InputMaskChangeEvent } from 'primereact/inputmask';
import { Calendar } from 'primereact/calendar';

interface Props {
    unit: { [key: string]: any }
    showUnitInfo?: boolean
}

export default function MarketDataItemEditable({ unit, showUnitInfo = true }: Props) {
    const { marketData, updateMarketAnalysisState } = useMarketAnalysis()
    const [showHistory, setShowHistory] = useState(false)

    const [unitValue, setUnitValue] = useState(unit)

    const handleUpdateUnit = useDebounce(async (unit) => {
        await updateUnit(unit, 'sd', 0, 'sd', '')
    }, 500)

    const handleUnitChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement> | InputMaskChangeEvent, key: string) => {
        const value = e.target.value;
        let isValid = true;
        let processedValue = value;
        
        // Validation logic based on field type
        if (['beds', 'sqft'].includes(key)) {
            // Integer validation
            isValid = value === '' || !isNaN(Number(value));
        } else if (key === 'baths') {
            // Decimal validation (allow period or comma)
            processedValue = value ? value.replace(',', '.') : '';
            isValid = value === '' || !isNaN(Number(processedValue));
        } else if (key === 'data.price') {
            // Price validation
            isValid = value === '' || !isNaN(Number(value));
        }
        
        // Only update if valid or not a validated field
        if (isValid) {
            let updatedUnit;
            
            if (key.includes('.')) {
                // Handle nested properties
                const [parent, child] = key.split('.');
                updatedUnit = { 
                    ...unitValue, 
                    [parent]: { 
                        ...unitValue[parent],
                        [child]: processedValue 
                    } 
                };
            } else {
                // Handle top-level properties
                updatedUnit = { ...unitValue, [key]: processedValue };
            }
            
            setUnitValue(updatedUnit);
            handleUpdateUnit(updatedUnit);
        }
    }

    // Handle Calendar date change separately
    const handleDateChange = (e: any) => {
        const date = e.value;
        let formattedDate = '';
        
        if (date) {
            // Format date as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            formattedDate = `${year}-${month}-${day}`;
        }
        
        const updatedUnit = { 
            ...unitValue, 
            data: { 
                ...unitValue.data,
                date: formattedDate 
            } 
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    }

    const handleRemoveUnit = async (id: string) => {
        updateMarketAnalysisState({marketData: marketData?.filter((unit: any) => unit.id !== id)})
        await removeUnit(id)
    }
    
    // Define different input fields based on view mode
    const renderUnitInfoFields = (
        <>
            <td className="px-3 py-2 font-medium">
                <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.unit} onChange={(e) => handleUnitChange(e, 'unit')} />
            </td>
            <td className="px-3 py-2 text-center">
                <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.beds} onChange={(e) => handleUnitChange(e, 'beds')} />
            </td>
            <td className="px-3 py-2 text-center">
                <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.sqft} onChange={(e) => handleUnitChange(e, 'sqft')} />
            </td>
        </>
    )
    
    const renderMarketDataFields = (
        <>
            <td className="px-3 py-2 font-medium">
                <select className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.data?.propertyType || ''} onChange={(e) => handleUnitChange(e, 'data.propertyType')}>
                    <option value="">Type</option>
                    <option value="Condo">Condo</option>
                    <option value="Single Family">Single Family</option>
                    <option value="Multi Family">Multi Family</option>
                    <option value="Townhouse">Townhouse</option>
                </select>
            </td>
            <td className="px-3 py-2 text-center">
                <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" 
                       placeholder="Year Built" value={unitValue?.data?.yearBuilt || ''} onChange={(e) => handleUnitChange(e, 'data.yearBuilt')} />
            </td>
            <td className="px-3 py-2 text-center">
                <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" 
                       placeholder="Lot Size" value={unitValue?.data?.lotSize || ''} onChange={(e) => handleUnitChange(e, 'data.lotSize')} />
            </td>
        </>
    )
    
    return (
        <>
            <tr className="hover:bg-gray-50">
                {showUnitInfo ? renderUnitInfoFields : renderMarketDataFields}
                <td className="px-3 py-2 text-center">
                    <select className="w-[100px] border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.data?.lastEvent} onChange={(e) => handleUnitChange(e, 'data.lastEvent')}>
                        <option value="Sold">Sold</option>
                        <option value="For Rent">For Rent</option>
                        <option value="For Sale">For Sale</option>
                        <option value="Off Market">Off Market</option>
                    </select>
                </td>
                <td className="px-3 py-2 text-right">
                    <input type="text" className="w-full border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500" value={unitValue?.data?.price} onChange={(e) => handleUnitChange(e, 'data.price')} />
                </td>
                <td className="px-3 py-2 text-right">
                    <Calendar 
                        value={unitValue?.data?.date ? new Date(unitValue.data.date) : null} 
                        onChange={handleDateChange} 
                        className={'w-[150px] border border-gray-300 rounded-md p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500'}
                        panelClassName="w-[300px] text-sm bg-white text-gray-800 shadow-md rounded-lg p-2 border border-[#E6E1E5] shadow-[0px_4px_10px_0px_rgba(175,175,161,0.41)] market-data-calendar"
                        dateFormat="yy-mm-dd"
                    />
                </td>
                <td className="px-3 py-2 text-right">
                    <Fancybox>
                        <button 
                            className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-md text-xs hover:bg-indigo-200 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                            data-fancybox="gallery"
                            data-src={unit?.data?.images[0]}
                        >
                            {unit?.data?.images?.length} Photos
                        </button>
                        
                        {/* Hidden gallery items */}
                        {unit?.data?.images?.slice(1).map((img: string, index: number) => (
                            <a 
                                key={index} 
                                data-fancybox="gallery" 
                                href={img} 
                                className="hidden"
                            />
                        ))}
                    </Fancybox>
                </td>
                <td className="px-3 py-2 font-medium text-center">
                    <FontAwesomeIcon icon={faTrash} className="text-red-500 cursor-pointer text-center" onClick={() => handleRemoveUnit(unit?.id)} />
                </td>
            </tr>
        </>
    )
}