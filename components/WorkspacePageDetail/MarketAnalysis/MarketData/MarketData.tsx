import MarketDataItem from "./MarketDataItem"
import { Fragment, useState, useEffect } from "react"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useSearchParams } from "next/navigation";
import MarketDataItemEditable from "./MarketDataItemEditable";
import { addUnit } from "@/actions/propertyMarketDataActions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faExclamationTriangle, faXmark, faBroom } from "@fortawesome/free-solid-svg-icons";
import { createClient } from "@/utils/supabase/client";

interface DuplicateGroup {
    key: string;
    items: any[];
    year: string;
    price: number;
    status: string;
}

export default function MarketData(){
    const { marketData, isLoadingMarketData, updateMarketAnalysisState } = useMarketAnalysis();
    const [eventFilter, setEventFilter] = useState("all");
    const [isToggled, setIsToggled] = useState(false);
    const [duplicates, setDuplicates] = useState<DuplicateGroup[]>([]);
    const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);
    const [showCleanupModal, setShowCleanupModal] = useState(false);
    const [isCleaningUp, setIsCleaningUp] = useState(false);
    const searchParams = useSearchParams();
    const addressId = searchParams.get('addressId');
    const marketAddress = searchParams.get('marketAddress');
    
    // Filter data based on selected event type
    const filteredMarketData = eventFilter === "all"
        ? marketData
        : marketData?.filter((unit: any) => unit.lastEvent === eventFilter);

    // Check for duplicates whenever marketData changes
    useEffect(() => {
        if (marketData && marketData.length > 0) {
            checkForDuplicates();
        }
    }, [marketData]);

    const checkForDuplicates = () => {
        if (!marketData) return;

        const duplicateMap = new Map<string, any[]>();
        
        marketData.forEach((item: any) => {
            // Create a key based on year, price, and status
            const year = item.date ? new Date(item.date).getFullYear().toString() : 'unknown';
            const price = item.price || 0;
            const status = item.lastEvent || 'unknown';
            const key = `${year}-${price}-${status}`;
            
            if (!duplicateMap.has(key)) {
                duplicateMap.set(key, []);
            }
            duplicateMap.get(key)!.push(item);
        });

        // Find groups with more than one item (duplicates)
        const duplicateGroups: DuplicateGroup[] = [];
        duplicateMap.forEach((items, key) => {
            if (items.length > 1) {
                const [year, price, status] = key.split('-');
                duplicateGroups.push({
                    key,
                    items,
                    year,
                    price: parseFloat(price),
                    status
                });
            }
        });

        setDuplicates(duplicateGroups);
        
        // Show warning if duplicates found
        if (duplicateGroups.length > 0) {
            setShowDuplicateWarning(true);
        }
    };

    const handleCleanupDuplicates = async () => {
        if (duplicates.length === 0) return;
        
        setIsCleaningUp(true);
        const supabase = createClient();
        
        try {
            // For each duplicate group, keep the first item and delete the rest
            const itemsToDelete: string[] = [];
            
            duplicates.forEach(group => {
                // Keep the first item, mark the rest for deletion
                const itemsToRemove = group.items.slice(1);
                itemsToRemove.forEach(item => {
                    if (item.id) {
                        itemsToDelete.push(item.id);
                    }
                });
            });

            // Delete from Supabase
            if (itemsToDelete.length > 0) {
                const { error } = await supabase
                    .from('unit_market_data')
                    .delete()
                    .in('id', itemsToDelete);

                if (error) {
                    console.error('Error deleting duplicates:', error);
                    alert('Failed to delete duplicates. Please try again.');
                    return;
                }
            }

            // Update local state by removing duplicates
            const updatedMarketData = marketData?.filter((item: any) => 
                !itemsToDelete.includes(item.id)
            );
            
            updateMarketAnalysisState({ marketData: updatedMarketData });
            
            // Reset duplicate states
            setDuplicates([]);
            setShowDuplicateWarning(false);
            setShowCleanupModal(false);
            
            alert(`Successfully removed ${itemsToDelete.length} duplicate items.`);
            
        } catch (error) {
            console.error('Error during cleanup:', error);
            alert('An error occurred during cleanup. Please try again.');
        } finally {
            setIsCleaningUp(false);
        }
    };
    
    const handleAddUnit = () => {
        if (!addressId) return;
        
        addUnit(addressId).then((data) => {
            updateMarketAnalysisState({ marketData: data });
        });
    };

    const toggleMarketData = () => {
        setIsToggled(!isToggled);
    };

    const getTotalDuplicateCount = () => {
        return duplicates.reduce((total, group) => total + (group.items.length - 1), 0);
    };

    return (
        <div className="bg-white shadow-sm rounded-lg p-4">

            <div className="flex justify-between items-center m-4">
                <div className="flex items-center gap-3">
                    <h3 className="text-lg font-medium text-gray-800">Market Data</h3>
                    
                    {/* Toggle Switch */}
                    <div className="flex items-center">
                        <span className={`mr-2 text-sm ${!isToggled ? 'font-medium text-indigo-600' : 'text-gray-500'}`}>
                            Unit Info
                        </span>
                        
                        <button 
                            onClick={toggleMarketData}
                            className="relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none"
                            role="switch"
                            aria-checked={isToggled}
                        >
                            <span 
                                className={`${isToggled ? 'bg-indigo-600' : 'bg-gray-200'} 
                                           inline-block h-6 w-11 rounded-full transition-colors duration-200 ease-in-out`}
                            />
                            <span 
                                className={`${isToggled ? 'translate-x-6' : 'translate-x-1'} 
                                           inline-block h-4 w-4 transform rounded-full bg-white transition duration-200 ease-in-out shadow-md`}
                            />
                        </button>
                        
                        <span className={`ml-2 text-sm ${isToggled ? 'font-medium text-indigo-600' : 'text-gray-500'}`}>
                            Market Data
                        </span>
                    </div>
                </div>
                
                <div className="flex items-end gap-x-4">
                    {addressId && (
                        <button 
                            onClick={handleAddUnit}
                            className="cursor-pointer rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                            Add Market Data
                        </button>
                    )}
                    <div>
                        <label htmlFor="event-filter" className="block text-xs text-gray-500 mb-1">Filter by Event Type</label>
                        <select 
                            id="event-filter" 
                            className="block w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            value={eventFilter}
                            onChange={(e) => setEventFilter(e.target.value)}
                        >
                            <option value="all">All Events</option>
                            <option value="sold">Sold</option>
                            <option value="for_sale">For Sale</option>
                            <option value="for_rent">For Rent</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>
            </div>        
            
            <>
                {
                    isLoadingMarketData ? 
                        <>
                            <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                            <div className="space-y-2">
                                {[...Array(4)].map((_, i) => (
                                    <div key={i} className="h-3 bg-gray-200 rounded"></div>
                                ))}
                            </div>
                        </>
                    :
                    <div className="overflow-x-auto max-h-[445px] overflow-y-auto border border-gray-200 rounded-lg shadow-sm">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50 sticky top-0 z-10">
                                <tr className="text-xs text-gray-500">
                                    <th className="px-3 py-2 text-left">
                                        {isToggled ? 'Property Type' : 'Unit'} 
                                    </th>
                                    <th className="px-3 py-2 text-center">
                                        {isToggled ? 'Year Built' : 'Beds'}
                                    </th>
                                    <th className="px-3 py-2 text-center">
                                        {isToggled ? 'Lot Size' : 'Sq Ft'} 
                                    </th>
                                    <th className="px-3 py-2 text-center">Last Event</th>
                                    <th className={`px-3 py-2  ${addressId ? 'text-center' : 'text-right'}`}>Price</th>
                                    <th className={`px-3 py-2  ${addressId ? 'text-center' : 'text-right'}`}>Date</th>
                                    <th className={`px-3 py-2  ${addressId ? 'text-center' : 'text-right'}`}>Images</th>
                                    {
                                        !addressId ?
                                            <th className="px-3 py-2 text-right">More</th>
                                        :
                                        <th className="px-3 py-2 text-center">Remove</th>
                                    }
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200 text-sm bg-white">
                                {filteredMarketData?.map((unit: any) => (
                                    <Fragment key={unit.id}>
                                        {
                                            addressId ? 
                                                <MarketDataItemEditable unit={unit} showUnitInfo={!isToggled} /> 
                                                : 
                                                <MarketDataItem unit={unit} showUnitInfo={!isToggled} />
                                        }
                                    </Fragment>
                                ))}
                            </tbody>
                        </table>
                    </div>
                }

                {
                    filteredMarketData?.length === 0 && !isLoadingMarketData ?
                        <>
                            <div className="text-center py-6">
                                <svg className="w-12 h-12 text-gray-300 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <p className="text-gray-500">No market data available</p>
                            </div>
                        </>
                    :
                    null
                }
            </>

            {/* Cleanup Confirmation Modal */}
            {showCleanupModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                        <div className="p-6">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-600 h-6 w-6" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-800">Clean Up Duplicate Data</h3>
                                    <p className="text-sm text-gray-500">Review and confirm deletion of duplicate market data</p>
                                </div>
                            </div>

                            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-sm text-red-700 font-medium mb-2">⚠️ Warning: This action cannot be undone!</p>
                                <p className="text-sm text-red-600">
                                    {getTotalDuplicateCount()} duplicate items will be permanently deleted from the database.
                                    Only the first occurrence of each duplicate group will be kept.
                                </p>
                            </div>

                            <div className="mb-6">
                                <h4 className="text-sm font-semibold text-gray-800 mb-3">Duplicate Groups Found:</h4>
                                <div className="space-y-3 max-h-60 overflow-y-auto">
                                    {duplicates.map((group, index) => (
                                        <div key={group.key} className="p-3 bg-gray-50 rounded-lg border">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-800">
                                                    Group {index + 1}
                                                </span>
                                                <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                                                    {group.items.length - 1} duplicates
                                                </span>
                                            </div>
                                            <div className="text-xs text-gray-600 space-y-1">
                                                <div>Year: {group.year}</div>
                                                <div>Price: ${group.price.toLocaleString()}</div>
                                                <div>Status: {group.status}</div>
                                                <div className="text-green-600">✓ Keeping: {group.items[0].unit || 'N/A'}</div>
                                                {group.items.slice(1).map((item, idx) => (
                                                    <div key={idx} className="text-red-600">✗ Deleting: {item.unit || 'N/A'}</div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="flex gap-3 justify-end">
                                <button
                                    onClick={() => setShowCleanupModal(false)}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                    disabled={isCleaningUp}
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleCleanupDuplicates}
                                    disabled={isCleaningUp}
                                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                                >
                                    {isCleaningUp ? (
                                        <>
                                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                            Cleaning...
                                        </>
                                    ) : (
                                        <>
                                            <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                                            Delete {getTotalDuplicateCount()} Duplicates
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}