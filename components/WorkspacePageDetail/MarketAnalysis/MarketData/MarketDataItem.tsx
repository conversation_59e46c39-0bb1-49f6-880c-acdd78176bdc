import { useState } from "react"
import Fancybox from "@/helpers/fancyBox/fancyBox"
import { Fancybox as NativeFancybox } from "@fancyapps/ui"
interface Props {
    unit: any
    showUnitInfo?: boolean
}

export default function MarketDataItem({ unit, showUnitInfo = true }: Props) {
    const [showHistory, setShowHistory] = useState(false)
    
    // Determine which data to display based on the toggle
    const displayData = showUnitInfo ? {
        // Unit Info view
        column1: unit.unitNumber,
        column2: unit.bedrooms || 'N/A',
        column3: unit.squareFeet || 'N/A',
    } : {
        // Market Data view
        column1: unit.marketData?.propertyType || 'N/A',
        column2: unit.marketData?.yearBuilt || 'N/A',
        column3: unit.marketData?.lotSize || 'N/A',
    }
    
    return (
        <>
            <tr key={unit.unitNumber} className="hover:bg-gray-50" onClick={() => setShowHistory(!showHistory)}>
                <td className="px-3 py-2 font-medium">{displayData.column1}</td>
                <td className="px-3 py-2 text-center">{displayData.column2}</td>
                <td className="px-3 py-2 text-center">{displayData.column3}</td>
                <td className="px-3 py-2 text-center">
                <span className={`px-2 py-1 rounded-full text-xs ${
                    unit.lastEvent === 'sold' ? 'bg-blue-100 text-blue-800' :
                    unit.lastEvent === 'for_sale' || unit.lastEvent === 'forsale' ? 'bg-purple-100 text-purple-800' :
                    unit.lastEvent === 'for_rent' || unit.lastEvent === 'forrent' || unit.lastEvent === 'rented' ? 'bg-green-100 text-green-800' :
                    unit.lastEvent === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                    }`}>
                    {unit.lastEvent.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
                </td>
                <td className="px-3 py-2 text-right">{unit.price || 'N/A'}</td>
                <td className="px-3 py-2 text-right">{unit.date || 'N/A'}</td>
                <td className="px-3 py-2 text-right">
                    <Fancybox>
                        <button 
                            className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-md text-xs hover:bg-indigo-200 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                            data-fancybox="gallery"
                            data-src={unit.images[0]}
                        >
                            {unit.images.length} Photos
                        </button>
                        
                        {/* Hidden gallery items */}
                        {unit.images.slice(1).map((img: string, index: number) => (
                            <a 
                                key={index} 
                                data-fancybox="gallery" 
                                href={img} 
                                className="hidden"
                            />
                        ))}
                    </Fancybox>
                </td>
                <td className="px-4 py-2">
                    <div className="flex items-center justify-end">
                        {!showHistory ? (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        ) : (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                            </svg>
                        )}
                    </div>
                </td>
            </tr>
            {showHistory && (
                <tr className="bg-gray-50">
                    <td colSpan={9} className="px-4 py-3">
                        <div className="text-sm space-y-4">
                            {/* Property Details Section */}
                            {(unit.bedrooms || unit.bathrooms || unit.squareFeet || unit.unitNumber) && (
                                <div>
                                    <h4 className="font-semibold mb-2 text-gray-800">Property Details</h4>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                                        {unit.bedrooms && (
                                            <div className="bg-white p-2 rounded border">
                                                <span className="text-gray-500">Bedrooms:</span>
                                                <div className="font-medium">{unit.bedrooms}</div>
                                            </div>
                                        )}
                                        {unit.bathrooms && (
                                            <div className="bg-white p-2 rounded border">
                                                <span className="text-gray-500">Bathrooms:</span>
                                                <div className="font-medium">{unit.bathrooms}</div>
                                            </div>
                                        )}
                                        {unit.squareFeet && (
                                            <div className="bg-white p-2 rounded border">
                                                <span className="text-gray-500">Square Feet:</span>
                                                <div className="font-medium">{unit.squareFeet.toLocaleString()}</div>
                                            </div>
                                        )}
                                        {unit.unitNumber && (
                                            <div className="bg-white p-2 rounded border">
                                                <span className="text-gray-500">Unit:</span>
                                                <div className="font-medium">{unit.unitNumber}</div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Images Section */}
                            {unit.images && unit.images.length > 0 && (
                                <div>
                                    <h4 className="font-semibold mb-2 text-gray-800">Property Images</h4>
                                    <Fancybox>
                                        <div className="grid grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-2">
                                            {unit.images.slice(0, 10).map((img: string, index: number) => (
                                                <div key={index} className="aspect-square bg-gray-100 rounded border overflow-hidden">
                                                    <img 
                                                        src={img} 
                                                        alt={`Property ${index + 1}`}
                                                        className="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
                                                        data-fancybox="property-gallery"
                                                        data-src={img}
                                                    />
                                                </div>
                                            ))}
                                            {unit.images.length > 10 && (
                                                <div className="aspect-square bg-gray-200 rounded border flex items-center justify-center text-xs text-gray-600">
                                                    +{unit.images.length - 10} more
                                                </div>
                                            )}
                                        </div>
                                        {/* Hidden gallery items for full image view */}
                                        {unit.images.slice(10).map((img: string, index: number) => (
                                            <a 
                                                key={index + 10} 
                                                data-fancybox="property-gallery" 
                                                href={img} 
                                                className="hidden"
                                            />
                                        ))}
                                    </Fancybox>
                                </div>
                            )}

                            {/* Transaction History Section */}
                            <div>
                                <h4 className="font-semibold mb-2 text-gray-800">Transaction History</h4>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200 text-xs">
                                        <thead>
                                            <tr className="bg-gray-100">
                                                <th className="px-2 py-1 text-left">Event</th>
                                                <th className="px-2 py-1 text-right">Price</th>
                                                <th className="px-2 py-1 text-center">Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {unit.history && unit.history.length > 0 ? unit.history.map((event: any) => (
                                                <tr key={`${event.date}-${event.type}`}>
                                                    <td className="px-2 py-1 text-left">
                                                        <span className={`px-1.5 py-0.5 rounded text-xs ${
                                                            event.type === 'sold' ? 'bg-blue-100 text-blue-800' :
                                                            event.type === 'for_sale' || event.type === 'forsale' ? 'bg-purple-100 text-purple-800' :
                                                            event.type === 'for_rent' || event.type === 'forrent' || event.type === 'rented' ? 'bg-green-100 text-green-800' :
                                                            event.type === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                            'bg-gray-100 text-gray-800'
                                                            }`}>
                                                            {event.type.replace(/_/g, ' ').split(' ').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                                        </span>
                                                        {event.isLatest && (
                                                            <span className="ml-1 text-xs text-indigo-600">(Latest)</span>
                                                        )}
                                                    </td>
                                                    <td className="px-2 py-1 text-right font-medium">
                                                        ${typeof event.price === 'number' ? event.price.toLocaleString() : event.price}
                                                    </td>
                                                    <td className="px-2 py-1 text-center">{event.date}</td>
                                                </tr>
                                            )) : (
                                                <tr>
                                                    <td colSpan={3} className="px-2 py-1 text-center text-gray-500">
                                                        No transaction history available
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            {/* Source Information */}
                            {unit.sourceUrl && (
                                <div>
                                    <h4 className="font-semibold mb-2 text-gray-800">Data Source</h4>
                                    <div className="bg-blue-50 border border-blue-200 rounded p-3">
                                        <a 
                                            href={unit.sourceUrl} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-blue-700 hover:text-blue-900 underline text-xs break-all flex items-center gap-1"
                                        >
                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                            </svg>
                                            View Original Source
                                        </a>
                                        <p className="text-xs text-blue-600 mt-1">Data scraped from: {new URL(unit.sourceUrl).hostname}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </td>
                </tr>
            )}
        </>
        
)
}