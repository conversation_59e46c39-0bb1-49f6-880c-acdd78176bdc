'use client'

import { useState, useEffect } from 'react';
import { getSearchHistory, deleteSearchHistory, SearchHistoryItem } from '@/actions/searchHistoryActions';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faXmark, faHistory, faClock } from '@fortawesome/free-solid-svg-icons';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';

interface Props {
  onSearchSelect: (address: string) => void;
}

export default function RecentSearches({ onSearchSelect }: Props) {
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const { searchPlace } = useMarketAnalysis();

  // Load search history on component mount
  useEffect(() => {
    loadSearchHistory(true);
  }, []);

  // Reload search history when searchPlace changes from object to string (user cleared search)
  useEffect(() => {
    if (typeof searchPlace === 'string' && searchPlace === '') {
      loadSearchHistory(true);
    }
  }, [searchPlace]);

  const loadSearchHistory = async (reset: boolean = false) => {
    if (reset) {
      setLoading(true);
      setSearchHistory([]);
      setHasMore(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const offset = reset ? 0 : searchHistory.length;
      const result = await getSearchHistory(offset, 20);
      
      if (result.success && result.data) {
        if (reset) {
          setSearchHistory(result.data);
        } else {
          setSearchHistory(prev => [...prev, ...(result.data || [])]);
        }
        
        // If we got less than 20 items, we've reached the end
        if (result.data.length < 20) {
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleDeleteSearch = async (e: React.MouseEvent, id: string) => {
    e.stopPropagation(); // Prevent triggering the search select
    
    try {
      const result = await deleteSearchHistory(id);
      if (result.success) {
        // Remove from local state with smooth animation
        setSearchHistory(prev => prev.filter(item => item.id !== id));
      }
    } catch (error) {
      console.error('Error deleting search:', error);
    }
  };

  const handleSearchClick = (address: string) => {
    onSearchSelect(address);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = { 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
  };

  const truncateAddress = (address: string, maxLength: number = 60) => {
    if (address.length <= maxLength) return address;
    return address.substring(0, maxLength) + '...';
  };

  // Handle scroll to load more
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    // Load more when user scrolls to within 100px of bottom
    if (scrollHeight - scrollTop <= clientHeight + 100 && hasMore && !loadingMore) {
      loadSearchHistory(false);
    }
  };

  // Show recent searches only when there's no active search place selected
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
          <span className="ml-2 text-gray-600">Loading search history...</span>
        </div>
      </div>
    );
  }

  // Don't show if there's currently a search active (object means a place is selected)
  if (typeof searchPlace === 'object' && searchPlace !== null) {
    return null;
  }

  // Don't show if we have no search history
  if (searchHistory.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
      <div className="flex items-center gap-2 mb-4">
        <FontAwesomeIcon icon={faHistory} className="text-gray-600 w-4 h-4" />
        <h3 className="text-lg font-semibold text-gray-800">Recent Searches</h3>
      </div>
      
      <div 
        className="space-y-2 max-h-96 overflow-y-auto"
        onScroll={handleScroll}
      >
        {searchHistory.map((item) => (
          <div
            key={item.id}
            className={`
              group relative p-3 rounded-lg border transition-all duration-200 cursor-pointer
              ${hoveredItem === item.id 
                ? 'border-indigo-300 bg-indigo-50 shadow-md transform scale-[1.01]' 
                : 'border-gray-200 bg-gray-50 hover:border-gray-300 hover:bg-gray-100'
              }
            `}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
            onClick={() => handleSearchClick(item.address_string)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <FontAwesomeIcon 
                    icon={faClock} 
                    className="text-gray-400 w-3 h-3 flex-shrink-0" 
                  />
                  <span className="text-xs text-gray-500 font-medium">
                    {formatDate(item.updated_at)}
                  </span>
                </div>
                <p className="text-sm font-medium text-gray-800 leading-relaxed">
                  {truncateAddress(item.address_string)}
                </p>
              </div>
              
              {/* Delete button - only visible on hover */}
              <button
                onClick={(e) => handleDeleteSearch(e, item.id)}
                className={`
                  ml-3 p-1.5 rounded-full transition-all duration-200 flex-shrink-0
                  ${hoveredItem === item.id 
                    ? 'bg-red-100 hover:bg-red-200 text-red-600 opacity-100' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-500 opacity-0 group-hover:opacity-100'
                  }
                `}
                title="Remove from history"
              >
                <FontAwesomeIcon icon={faXmark} className="w-3 h-3" />
              </button>
            </div>
            
            {/* Subtle gradient overlay on hover */}
            {hoveredItem === item.id && (
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 rounded-lg pointer-events-none" />
            )}
          </div>
        ))}
        
        {/* Loading more indicator */}
        {loadingMore && (
          <div className="flex items-center justify-center py-3">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading more...</span>
          </div>
        )}
        
        {/* End of results indicator */}
        {!hasMore && searchHistory.length > 0 && (
          <div className="text-center py-3">
            <p className="text-xs text-gray-500 italic">
              You've reached the end of your search history
            </p>
          </div>
        )}
      </div>
      
      {searchHistory.length >= 20 && hasMore && (
        <p className="text-xs text-gray-500 mt-3 text-center italic">
          Scroll down to load more searches
        </p>
      )}
    </div>
  );
} 