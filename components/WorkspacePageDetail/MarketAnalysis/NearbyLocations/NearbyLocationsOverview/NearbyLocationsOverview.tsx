import NerbyOverviewLocation from "./NerbyOverviewLocation"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext"
import Spinner from "@/components/UI/Spinner";
interface Props {
    nearbyLocations: any
    setActiveTab: React.Dispatch<React.SetStateAction<string>>
}

export default function NerbyLocationsOverview({ nearbyLocations, setActiveTab }: Props) {
    const { isLoadingNearbyLocations } = useMarketAnalysis()        

    if (isLoadingNearbyLocations) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading overview data..." />
            </div>
        );
    }
    
    return (
        <div className="overflow-y-auto h-full py-2">
            <NerbyOverviewLocation 
                icon="🛒" 
                title="Groceries" 
                locations={nearbyLocations?.groceries || []}
                name="groceries"
                setActiveTab={setActiveTab}
            />
            
            <NerbyOverviewLocation 
                icon="🌳" 
                title="Parks" 
                locations={nearbyLocations?.parks || []}
                name="services"
                setActiveTab={setActiveTab}
            />

            <NerbyOverviewLocation 
                icon="🛍️" 
                title="Shopping" 
                locations={nearbyLocations?.shopping || []}
                name="shopping"
                setActiveTab={setActiveTab}
            />

            <NerbyOverviewLocation 
                icon="🍽️" 
                title="Restaurants" 
                locations={nearbyLocations?.restaurants || []} 
                name="groceries"
                setActiveTab={setActiveTab}
            />
        </div>
    )
}