interface Props {
    icon: string
    title: string
    locations: any[]
    name: string
    setActiveTab: React.Dispatch<React.SetStateAction<string>>
}

export default function NerbyOverviewLocation({ icon, title, locations, name, setActiveTab }: Props) {
    // Calculate average rating from locations
    const calculateAverageRating = () => {
        if (!locations || locations.length === 0) return null;
        
        const ratingsWithValues = locations.filter(location => location.rating && location.rating > 0);
        if (ratingsWithValues.length === 0) return null;
        
        const totalRating = ratingsWithValues.reduce((sum, location) => sum + location.rating, 0);
        const averageRating = totalRating / ratingsWithValues.length;
        
        return Math.round(averageRating * 10) / 10; // Round to 1 decimal place
    };

    const averageRating = calculateAverageRating();
    
    // Determine rating color based on value
    const getRatingColor = (rating: number) => {
        if (rating >= 4.0) return 'bg-green-100 text-green-800 border-green-300';
        if (rating >= 3.0) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
        return 'bg-red-100 text-red-800 border-red-300';
    };

    return (
        <div className="flex gap-x-4 border-b border-gray-200 py-4">
            <div>
                {averageRating ? (
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRatingColor(averageRating)} border w-16 inline-flex justify-center`}>
                        {averageRating}/5
                    </span>
                ) : (
                    <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-500 border-gray-300 border w-16 inline-flex justify-center">
                        N/A
                    </span>
                )}
            </div>
            <div className="flex-1">
                <div className="flex gap-x-2 items-center">
                    <span>{icon}</span>
                    <span className="text-xl font-semibold">{title}</span>
                    <span className="text-sm text-gray-500">({locations?.length})</span>
                </div>
                <div className="overflow-hidden relative h-5">
                    <div className="truncate text-sm text-gray-500 absolute top-0 left-0 w-full h-5">
                        {
                            locations?.map((location: any, index: number) => (
                                <span key={index}>
                                    {location?.business_name}{index < (locations?.length - 1) ? ', ' : ''}
                                </span>
                            ))
                        }
                    </div>
                </div>
                <div className="text-sm text-indigo-600 hover:text-indigo-800 mt-2 flex items-center cursor-pointer" onClick={() => setActiveTab(name)}>
                    <div>
                        <span>View all {locations?.length}</span>
                    </div>
                    <div>
                        <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    );
}