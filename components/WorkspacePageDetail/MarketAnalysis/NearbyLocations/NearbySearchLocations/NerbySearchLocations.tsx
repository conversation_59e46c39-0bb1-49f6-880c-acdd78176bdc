import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import useSearchPlaces from "@/helpers/hooks/marketAnalysis/useSearchPlaces"
import { useState } from "react"
import { Location } from "@/types/NearbyLocationType"
interface Props {
    setActiveTabNearby: React.Dispatch<React.SetStateAction<string>>
}

export default function NerbySearchLocations({setActiveTabNearby }: Props) {
    const { searchPlace, myLocations,updateMarketAnalysisState } = useMarketAnalysis();
    const [searchQuery, setSearchQuery] = useState<string>('')
    const { searchPlaces, searchResults, setSearchResults } = useSearchPlaces(searchQuery, searchPlace as { [key: string]: any })

    const handleSearchPlaces = (value: string) => {
        setSearchQuery(value)
        
        if(value.length > 2){
            searchPlaces()
        }else{
            setSearchResults([])
        }
    }

    const handleAddToMyLocations = (result: Location) => {
        const locationsArray = Array.isArray(myLocations) ? myLocations : [];
        updateMarketAnalysisState({
            myLocations: locationsArray.some((location: any) => location.place_id === result.place_id) ? locationsArray : [...locationsArray, result]
        })
        setSearchResults([])
        setSearchQuery('')
        setActiveTabNearby('my_locations')
    }

    return (
        <div className='flex gap-x-2 my-4'>
            <div className="relative flex-grow">
                <input
                    type="text"
                    placeholder="Search for places nearby..."
                    className="w-full px-3 py-2 bg-gray-100 border-0 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:bg-gray-50 transition-all duration-200"
                    value={searchQuery}
                    onChange={(e) => handleSearchPlaces(e.target.value)}
                />

                    {searchResults.length > 0 && (
                        <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md overflow-hidden border border-gray-200">
                            <ul className="max-h-60 overflow-auto">
                            {searchResults.map((result, index) => (
                                <li 
                                    key={index}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleAddToMyLocations(result as Location)}
                                    >
                                    <div className="font-medium">{result.business_name}</div>
                                    <div className="text-sm text-gray-500">{result.street}</div>
                                </li>
                            ))}
                            </ul>
                        </div>
                    )}
            </div>
            <button className="bg-indigo-600 text-white px-4 rounded-md hover:bg-indigo-700 cursor-pointer" onClick={searchPlaces}>
                Search
            </button>
        </div>
    )
}