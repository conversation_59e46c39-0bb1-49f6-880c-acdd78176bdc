import { useState } from "react"
import NerbyLocationsOverview from "./NearbyLocationsOverview/NearbyLocationsOverview"
import NerbyLocationsTab from "./NerbyLocationsTab/NerbyLocationsTab"
import NerbyLocationsSchoolTab from "./NerbyLocationsTab/NerbyLocationsSchoolTab"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import NerbySearchLocations from "./NearbySearchLocations/NerbySearchLocations";
import { Location } from "@/types/NearbyLocationType";
import NerbyLocationsTransportTab from "./NerbyLocationsTab/NerbyLocationsTransportTab/NerbyLocationsTransportTab";
import { useSearchParams } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { fetchNearbyPlacesWithGoogleMaps } from "@/actions/marketAnalysisActions/googleMapsActions";
import { fetchSchoolData } from "@/actions/marketAnalysisActions/cherreActions";
import { getTransitData } from "@/actions/marketAnalysisActions/unitDataActions";

import Spinner from "@/components/UI/Spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons";

export default function NearbyLocations() {
    const { nearbyLocations, handleSelectedLocation, searchPlace, myLocations, isLoadingNearbyLocations, isLoadingSchoolData, isLoadingTransitData, updateMarketAnalysisState} = useMarketAnalysis();
    const [activeTab, setActiveTab] = useState<string>('nearby')
    const [activeTabNearby, setActiveTabNearby] = useState<string>('overview')
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const marketAddress = searchParams.get('marketAddress')
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false)

    const handleRefresh = async () => {
        setIsRefreshing(true)
        updateMarketAnalysisState({ isLoadingNearbyLocations: true, isLoadingSchoolData: true, isLoadingTransitData: true })
        const supabase = createClient();
        const {data: propAddressData, error: propAddressError} = await supabase.from('prop_addresses').select('*').eq('id', addressId as string).single();
        const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId as string).single();
        
        const nearbyLocations = await fetchNearbyPlacesWithGoogleMaps(marketAddress as string || '', propAddressData?.lat as number, propAddressData?.lon as number)
        const schoolData = await fetchSchoolData(`${marketAddress}`)
        const transitData = await getTransitData(propAddressData?.lat as number, propAddressData?.lon as number)

        updateMarketAnalysisState({ nearbyLocations, isLoadingNearbyLocations: false, schoolData, isLoadingSchoolData: false, transitData, isLoadingTransitData: false })

        const {data: newNearbyLocations, error: newNearbyLocationsError} = await supabase.from('pois')
            .update({ data: nearbyLocations || [] }).eq('prop_id', propData.id).eq('type', 'nearby_locations');
            
        const {data: newSchoolData, error: newSchoolDataError} = await supabase.from('pois')
            .update({ data: schoolData || [] }).eq('prop_id', propData.id).eq('type', 'schools');

        const {data: newTransitData, error: newTransitDataError} = await supabase.from('pois')
            .update({ data: transitData || [] }).eq('prop_id', propData.id).eq('type', 'transit');
        
    }
    
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faMapMarkerAlt} className="h-4 w-4 text-indigo-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Nearby Locations</h3>
                    </div>
                    {addressId && !isLoadingNearbyLocations && !isLoadingSchoolData && !isLoadingTransitData && (
                        <button 
                            onClick={handleRefresh}
                            disabled={isRefreshing}
                            className="cursor-pointer rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                            {isRefreshing ? (
                                <div className="flex items-center gap-x-1">
                                    <Spinner size="sm" />
                                    Refreshing...
                                </div>
                            ) : 'Refresh'}
                        </button>
                    )}
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6 h-full max-h-[800px] flex flex-col">
            <div className="flex items-center gap-x-2">
                <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTab === 'nearby' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTab('nearby')}>Places</button>
                <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTab === 'transport' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTab('transport')}>Public Transport</button>
                <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTab === 'school' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTab('school')}>Schools</button>
            </div>
            {
                activeTab === 'nearby' && <NerbySearchLocations setActiveTabNearby={setActiveTabNearby} />
            }
            
            <div className={` pb-2 -mx-2 px-2 ${activeTab !== 'nearby' ? 'hidden' : ''}`}>
                <div className="flex min-w-max border-b">
                    <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTabNearby === 'overview' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTabNearby('overview')}>Overview</button>
                    <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTabNearby === 'groceries' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTabNearby('groceries')}>Food &amp; Drink</button>
                    <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTabNearby === 'shopping' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTabNearby('shopping')}>Shopping</button>
                    <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTabNearby === 'services' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTabNearby('services')}>Services</button>
                    <button className={`px-4 py-2 whitespace-nowrap border-b-2 cursor-pointer ${activeTabNearby === 'my_locations' ? 'border-indigo-500 font-medium' : 'border-transparent'}`} onClick={() => setActiveTabNearby('my_locations')}>My Locations</button>
                </div>
            </div>
            
            {/* Scrollable content area with fixed height */}
            <div className="flex-1 overflow-y-auto min-h-0">
                {
                    activeTab === 'school' ?
                        <NerbyLocationsSchoolTab /> :
                        activeTab === 'transport' ?
                            <NerbyLocationsTransportTab /> :
                            <>
                                {
                                    activeTabNearby === 'overview' ? 
                                    <NerbyLocationsOverview nearbyLocations={nearbyLocations} setActiveTab={setActiveTabNearby} /> : 
                                    activeTabNearby === 'groceries' ? 
                                            <NerbyLocationsTab data={nearbyLocations?.groceries} /> : 
                                            activeTabNearby === 'shopping' ? 
                                                <NerbyLocationsTab data={nearbyLocations?.shopping}  /> : 
                                                activeTabNearby === 'services' ? 
                                                    <NerbyLocationsTab data={nearbyLocations?.services} /> : 
                                                    activeTabNearby === 'my_locations' ? 
                                                        <NerbyLocationsTab data={myLocations as Location[]}  /> : null
                                }
                            </>
                }
            </div>
            </div>
        </div>
    )
}
