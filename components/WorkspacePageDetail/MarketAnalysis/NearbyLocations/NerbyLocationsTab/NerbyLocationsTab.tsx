import React, { useEffect } from 'react';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import { Location } from '@/types/NearbyLocationType';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashCan } from '@fortawesome/free-solid-svg-icons';
import Spinner from "@/components/UI/Spinner";

interface Props {
    data: Location[]
}

export default function NerbyLocationsTab({ data }: Props) {
    const { selectedLocation, handleSelectedLocation, myLocations, isLoadingNearbyLocations, updateMarketAnalysisState } = useMarketAnalysis();
    const [locationsWithImages, setLocationsWithImages] = React.useState<Record<string, string>>({});
    const [failedImages, setFailedImages] = React.useState<Set<string>>(new Set());

    // Group data by line_of_business (with null safety)
    const groupedLocations = (data || []).reduce((acc, location: Location) => {
        const category = location.line_of_business || 'Other';
        if (!acc[category]) {
            acc[category] = [];
        }
        acc[category].push(location);
        return acc;
    }, {} as Record<string, Location[]>);

    // Get emoji based on business type (as fallback)
    const getBusinessEmoji = (lineOfBusiness: string): string => {
        const type = lineOfBusiness.toLowerCase();
        
        if (type.includes('grocery') || type.includes('supermarket') || type.includes('market')) {
            return '🛒';
        } else if (type.includes('restaurant') || type.includes('cafe')) {
            return '🍽️';
        } else if (type.includes('bar') || type.includes('pub')) {
            return '🍺';
        } else if (type.includes('park') || type.includes('recreation')) {
            return '🌳';
        } else if (type.includes('theater') || type.includes('cinema')) {
            return '🎬';
        } else if (type.includes('hospital') || type.includes('medical')) {
            return '🏥';
        } else if (type.includes('school') || type.includes('education')) {
            return '🏫';
        } else if (type.includes('gym') || type.includes('fitness')) {
            return '💪';
        } else if (type.includes('bank') || type.includes('financial')) {
            return '🏦';
        } else if (type.includes('shop') || type.includes('store')) {
            return '🛍️';
        }
        
        return '📍'; // Default
    };

    useEffect(() => {
        if (!window.google || !window.google.maps || !data || data.length === 0) return;

        const loadPlaceImage = async (location: Location) => {
            try {
                const placesService = new google.maps.places.PlacesService(document.createElement('div'));
                
                return new Promise<string>((resolve) => {
                    placesService.getDetails(
                        {
                            placeId: location.place_id,
                            fields: ['photos', 'icon']
                        },
                        (place, status) => {
                            if (status === google.maps.places.PlacesServiceStatus.OK && place) {
                                // First try to use the photo
                                if (place.photos && place.photos.length > 0) {
                                    const photoUrl = place.photos[0].getUrl({ maxWidth: 100, maxHeight: 100 });
                                    resolve(photoUrl);
                                    return;
                                }
                                
                                // Fall back to icon
                                if (place.icon) {
                                    resolve(place.icon);
                                    return;
                                }
                            }
                            resolve(''); // No image found
                        }
                    );
                });
            } catch (error) {
                console.error('Error fetching place image:', error);
                return '';
            }
        };

        const loadAllImages = async () => {
            const imagePromises = data.map(async (location) => {
                const imageUrl = await loadPlaceImage(location);
                return { placeId: location.place_id, imageUrl };
            });

            const results = await Promise.all(imagePromises);
            
            const imageMap = results.reduce((acc, { placeId, imageUrl }) => {
                if (imageUrl) {
                    acc[placeId] = imageUrl;
                }
                return acc;
            }, {} as Record<string, string>);
            
            setLocationsWithImages(imageMap);
        };

        loadAllImages();
    }, [data]); // Run when data changes


    if (isLoadingNearbyLocations) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading location data..." />
            </div>
        );
    }

    // Handle null/undefined data after all hooks
    if (!data || !Array.isArray(data) || data.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <p className="text-gray-500">No locations available.</p>
            </div>
        );
    }

    return (
        <div className="flex flex-col h-full mt-4 space-y-6">
            {Object.entries(groupedLocations).map(([category, locations]: [string, Location[]]) => {
                return (
                    <div key={category} className="border-b border-gray-200 pb-4 last:border-b-0">
                        <div className="mb-4 mt-2">
                            <p className="text-lg font-semibold">{category}</p>
                        </div>
                        
                        <div className="space-y-3">
                            {locations.map((location: Location, index: number) => (
                                <div key={index} className="flex items-start p-3 hover:bg-gray-50 rounded-md relative group cursor-pointer" onClick={() => handleSelectedLocation(selectedLocation, location.business_name, location.latitude, location.longitude)}>
                                    <div className="w-10 h-10 bg-gray-100 rounded-md mr-3 flex items-center justify-center overflow-hidden flex-shrink-0">
                                        {locationsWithImages[location.place_id] && !failedImages.has(location.place_id) ? (
                                            <img 
                                                src={locationsWithImages[location.place_id]} 
                                                alt={location.business_name}
                                                className="w-full h-full object-cover"
                                                onError={() => {
                                                    setFailedImages(prev => new Set([...prev, location.place_id]));
                                                }}
                                            />
                                        ) : (
                                            <div className="text-lg">{getBusinessEmoji(category)}</div>
                                        )}
                                    </div>
                                    
                                    <div className="flex-1 min-w-0">
                                        <h3 className="font-medium text-gray-900 truncate">{location.business_name}</h3>
                                        <p className="text-sm text-gray-600 truncate">{location.street}</p>
                                        <div className="flex items-center mt-1">
                                            <div className="flex items-center text-yellow-500 mr-3">
                                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                                <span className="text-sm ml-1">{location.rating || 'N/A'}</span>
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {location.distance.toFixed(1)} miles away
                                            </div>
                                        </div>
                                    </div>

                                    {myLocations && (
                                        <button className="absolute top-1/2 -translate-y-1/2 right-2 opacity-0 group-hover:opacity-100 cursor-pointer p-1" onClick={(e) => {
                                            e.preventDefault()
                                            e.stopPropagation()
                                            updateMarketAnalysisState({
                                                myLocations: myLocations.filter((item: any) => item.place_id !== location.place_id)
                                            })
                                        }}>
                                            <FontAwesomeIcon icon={faTrashCan} className='hover:text-red-500 cursor-pointer h-4 w-4' />
                                        </button>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                );
            })}
        </div>
    );
}