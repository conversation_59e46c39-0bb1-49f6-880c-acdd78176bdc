interface TransitRouteIconProps {
    routeType: number;
    routeName: string;
    routeColor: string;
    textColor: string;
}

export default function TransitRouteIcon({ routeType, routeName, routeColor, textColor }: TransitRouteIconProps) {
    // Route types based on General Transit Feed Specification (GTFS)
    // 0: Tram/Light Rail, 1: Subway/Metro, 2: Rail, 3: Bus, 4: Ferry, etc.

    // For subway lines, we render a circular icon
    if (routeType === 1) {
        return (
            <div 
                className="p-2 min-w-[48px] rounded-full flex items-center justify-center text-2xl font-bold text-nowrap"
                style={{ 
                    backgroundColor: `#${routeColor}`, 
                    color: `#${textColor}`,
                    fontFamily: "'Helvetica Neue', Arial, sans-serif",
                    letterSpacing: '-0.5px'
                }}
            >
                {routeName}
            </div>
        );
    }

    // For buses, we render a rectangular icon
    if (routeType === 3) {
        return (
            <div 
                className="p-2 min-w-[48px] rounded-md flex items-center justify-center text-xl font-bold text-nowrap"
                style={{ 
                    backgroundColor: `#${routeColor}`, 
                    color: `#${textColor}`,
                    fontFamily: "'Helvetica Neue', Arial, sans-serif",
                    letterSpacing: '-0.5px'
                }}
            >
                {routeName}
            </div>
        );
    }

    // For all other types, we render a generic pill shaped icon
    return (
        <div 
            className="p-2 min-w-[48px] rounded-full flex items-center justify-center text-xl font-bold text-nowrap"
            style={{ 
                backgroundColor: `#${routeColor}`, 
                color: `#${textColor}`,
                fontFamily: "'Helvetica Neue', Arial, sans-serif",
                letterSpacing: '-0.5px'
            }}
        >
            {routeName}
        </div>
    );
} 