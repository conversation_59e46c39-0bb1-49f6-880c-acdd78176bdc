'use client';
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";
import TransitRouteIcon from "./TransitRouteIcon";
import Image from "next/image";
import Spinner from "@/components/UI/Spinner";

// Function to calculate distance between two points using Haversine formula
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const distanceInMeters = R * c;
    const distanceInMiles = distanceInMeters * 0.000621371; // Convert meters to miles
    return Number(distanceInMiles.toFixed(2)); // Distance in miles, rounded to 2 decimal places
};


export default function NerbyLocationsTransportTab() {
    const { transitData, isLoadingTransitData, searchPlace, selectedLocation, handleSelectedLocation } = useMarketAnalysis();
    const [hoveredStop, setHoveredStop] = useState<string | null>(null);

    
    // Take a maximum of 4 routes
    const nearbyRoutes = transitData?.routes.slice(0, 8) || [];

    // Get property location from search
    const propertyLat = typeof searchPlace !== 'string' ? searchPlace?.geometry?.location?.lat() : null;
    const propertyLng = typeof searchPlace !== 'string' ? searchPlace?.geometry?.location?.lng() : null;

    // Calculate distance in miles from the closest stop
    const getStopDistance = (route: any) => {
        if (!route.itineraries?.[0]?.closest_stop) return null;
        const stopLat = route.itineraries[0].closest_stop.stop_lat;
        const stopLon = route.itineraries[0].closest_stop.stop_lon;

        if (propertyLat && propertyLng) {
            return calculateDistance(propertyLat, propertyLng, stopLat, stopLon);
        }

        // Fallback if we don't have property coordinates
        return Number((Math.floor(Math.random() * 400) + 100) * 0.000621371).toFixed(2); // Convert random meters to miles
    };

    const getStopName = (route: any) => {
        return route.itineraries?.[0]?.closest_stop?.stop_name || 'Unknown Stop';
    };

    // Get stop coordinates for plotting on map
    const getStopCoordinates = (route: any) => {
        if (!route.itineraries?.[0]?.closest_stop) return null;
        return {
            latitude: route.itineraries[0].closest_stop.stop_lat,
            longitude: route.itineraries[0].closest_stop.stop_lon
        };
    };

    // Handle clicking on a transit route to add to map
    const handleTransitRouteClick = (route: any) => {
        const stopCoords = getStopCoordinates(route);
        const stopName = getStopName(route);
        
        if (stopCoords) {
            handleSelectedLocation(
                selectedLocation, 
                `${route.route_short_name} - ${stopName}`, 
                stopCoords.latitude, 
                stopCoords.longitude
            );
        }
    };

    // Check if a route is selected on the map
    const isRouteSelected = (route: any) => {
        const stopCoords = getStopCoordinates(route);
        const stopName = getStopName(route);
        
        if (!stopCoords) return false;
        
        // Create the same address format used when adding to the map
        const routeAddress = `${route.route_short_name} - ${stopName}`;
        
        return selectedLocation.some(
            loc => loc.latitude === stopCoords.latitude && 
                  loc.longitude === stopCoords.longitude &&
                  loc.address === routeAddress
        );
    };

    // Loading state
    if (isLoadingTransitData) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading transportation data..." />
            </div>
        );
    }

    // If there's no data or routes, show a message
    if (!isLoadingTransitData && (transitData && transitData?.routes && transitData?.routes.length === 0)) {
        return (
            <div className="flex items-center justify-center h-64">
                <p className="text-gray-500">No public transportation options found nearby.</p>
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto py-4">
                <div className="grid grid-cols-2 gap-4">
                    {nearbyRoutes.map((route: any, index: number) => {
                        const distance = getStopDistance(route);
                        const stopName = getStopName(route);
                        const selected = isRouteSelected(route);

                        return (
                            <div 
                                key={index} 
                                className={`bg-gray-50 rounded-lg p-3 flex flex-col items-center cursor-pointer hover:bg-gray-100 transition-colors ${selected ? 'ring-2 ring-indigo-500 shadow-sm' : ''}`}
                                onClick={() => handleTransitRouteClick(route)}
                            >
                                <TransitRouteIcon
                                    routeType={route.route_type}
                                    routeName={route.route_short_name}
                                    routeColor={route.route_color}
                                    textColor={route.route_text_color}
                                />

                                <div className="mt-2 flex items-center text-sm text-gray-600">
                                    <span>{distance} mi</span>
                                    <div 
                                        className="ml-1 relative"
                                        onMouseEnter={() => setHoveredStop(route.global_route_id)}
                                        onMouseLeave={() => setHoveredStop(null)}
                                    >
                                        <FontAwesomeIcon 
                                            icon={faInfoCircle} 
                                            className="text-gray-400 hover:text-gray-700 cursor-pointer"
                                        />
                                        {hoveredStop === route.global_route_id && (
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded shadow-lg z-10 text-center whitespace-nowrap">
                                                {stopName}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="flex justify-end mt-4">
                <a 
                    href="https://transitapp.com/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="opacity-80 hover:opacity-100 transition-opacity"
                >
                    <Image 
                        src="/transit-api-badge.svg" 
                        alt="Powered by Transit API" 
                        width={112} 
                        height={37} 
                    />
                </a>
            </div>
        </div>
    );
} 