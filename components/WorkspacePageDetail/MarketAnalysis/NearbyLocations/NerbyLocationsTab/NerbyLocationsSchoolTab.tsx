import { useEffect, useState } from "react";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import Spinner from "@/components/UI/Spinner";

export default function NerbyLocationsSchoolTab() {
    const { handleSelectedLocation, schoolData, searchPlace, selectedLocation, isLoadingSchoolData } = useMarketAnalysis();

    const [mockSchoolsData, setMockSchoolsData] = useState<any[]>([]);
    const getScoreColor = (score: string) => {
        if (score === 'A') return 'bg-green-100 text-green-800 border-green-300';
        if (score === 'B') return 'bg-blue-100 text-blue-800 border-blue-300';
        if (score === 'C') return 'bg-yellow-100 text-yellow-800 border-yellow-300';
        return 'bg-red-100 text-red-800 border-red-300';
    };

    // Function to geocode an address using Google Maps API
    const geocodeAddress = async (address: string) => {
        try {
            // Replace with your actual API key
            const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
            const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`;
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.status === 'OK' && data.results.length > 0) {
                const result = data.results[0];
                const location = result.geometry.location;
                
                // Extract county from address components
                let county = '';
                for (const component of result.address_components) {
                    if (component.types.includes('administrative_area_level_2')) {
                        county = component.long_name.replace(' County', '');
                        break;
                    }
                }
                
                return {
                    lat: location.lat,
                    lon: location.lng,
                    county_name: county
                };
            }
            
            return null;
        } catch (error) {
            console.error('Error geocoding address:', error);
            return null;
        }
    };

    useEffect(() => {
        const result: any[] = [];

        function deepSearchInstitutionAddress(node: any) {
            if (Array.isArray(node)) {
                for (const item of node) {
                    deepSearchInstitutionAddress(item);
                }
            } else if (node && typeof node === 'object') {
                if ('institution_address' in node) {
                    result.push(node);
                }

                for (const key in node) {
                    deepSearchInstitutionAddress(node[key]);
                }
            }
        }

        const processSchools = async () => {
            deepSearchInstitutionAddress(schoolData);
            
            // Geocode addresses for schools that don't have lat/lon
            const enhancedResults = await Promise.all(
                result.map(async (school) => {
                    if ((!school.lat || !school.lon) && typeof searchPlace !== 'string') {
                        const city = searchPlace?.address_components?.find((component: any) => component.types.includes('locality'))?.short_name || 'N/A'
                        const state = searchPlace?.address_components?.find((component: any) => component.types.includes('administrative_area_level_1'))?.short_name || 'N/A'
                        const geocodeResult = await geocodeAddress(`${school.institution_address}, ${city}, ${state}`);
                        if (geocodeResult) {
                            return {
                                ...school,
                                lat: school.lat || geocodeResult.lat,
                                lon: school.lon || geocodeResult.lon,
                                county_name: school.county_name || geocodeResult.county_name
                            };
                        }
                    }
                    return school;
                })
            );
            
            setMockSchoolsData(enhancedResults);
            console.log("schoolsData:", enhancedResults);
        };
        
        if (schoolData) {
            processSchools();
        }
    }, [schoolData]);

    if (isLoadingSchoolData) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading school data..." />
            </div>
        );
    }
    
    return (
        <div className="h-full flex flex-col overflow-hidden">
            <div className="flex justify-between gap-x-2 mt-4 rounded-lg bg-gray-100 p-1">
                <button className="px-3 py-1.5 text-sm font-medium rounded-md transition-all 
                    text-gray-500 hover:text-gray-700 hover:bg-gray-200 cursor-pointer
                ">Elementary</button>
                <button className="px-3 py-1.5 text-sm font-medium rounded-md transition-all 
                    text-gray-500 hover:text-gray-700 hover:bg-gray-200 cursor-pointer
                ">Middle</button>
                <button className="px-3 py-1.5 text-sm font-medium rounded-md transition-all 
                    text-gray-500 hover:text-gray-700 hover:bg-gray-200 cursor-pointer
                ">High</button>
                <button className="px-3 py-1.5 text-sm font-medium rounded-md transition-all 
                    text-gray-500 hover:text-gray-700 hover:bg-gray-200 cursor-pointer
                ">Private</button>
            </div>
            
            <div className="flex-1 overflow-y-auto mt-4">
                {
                    mockSchoolsData?.map((school, index) => (
                        <div 
                            key={index} 
                            className="flex gap-x-4 border-b border-gray-200 py-4 cursor-default"
                        >
                            <div>
                                {
                                    school?.test_rating ? (
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(school?.test_rating)} border`}>{school?.test_rating}</span>
                                    ) : (
                                        <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-500 border">N/A</span>
                                    )
                                }
                            </div>
                            <div className="flex-1">
                                <div className="">
                                    <p className="text-lg font-semibold text-indigo-800 hover:underline">{school?.institution_name}</p>
                                    <p className="text-sm text-gray-500 mt-1">Grades {school?.grade_level_range_low}-{school?.grade_level_range_high} | {school?.student_count} students</p>
                                    <p className="text-sm text-gray-500 mt-1">{school?.institution_address}</p>
                                </div>
                            </div>
                        </div>
                    ))        
                }
            </div>
        </div>
    )
}