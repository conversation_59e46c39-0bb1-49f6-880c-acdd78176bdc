'use client';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark, faMagnifyingGlass, faGlobe } from "@fortawesome/free-solid-svg-icons";
import PropertyInformation from "@/components/WorkspacePageDetail/MarketAnalysis/PropertyInformation/PropertyInformation";
import BuildingAnalysis from "@/components/WorkspacePageDetail/MarketAnalysis/BuildingAnalysis";
import NearbyLocations from "@/components/WorkspacePageDetail/MarketAnalysis/NearbyLocations/NearbyLocations";
import MapCard from "@/components/WorkspacePageDetail/MarketAnalysis/MapCard";
import SolarPotential from "@/components/WorkspacePageDetail/MarketAnalysis/SolarPotential";
import Demographics from "@/components/WorkspacePageDetail/MarketAnalysis/Demographics";
import SaveToPortfolio from "@/components/WorkspacePageDetail/MarketAnalysis/SaveToPortfolio";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useSearchPara<PERSON>, useRouter, useParams } from "next/navigation";
import TaxHistory from "./TaxHistory";
import { useEffect, useState, useRef } from "react";
import Spinner from "@/components/UI/Spinner";
import { useAuth } from "@/context/AuthContext";
import { useCheckPay } from "@/helpers/hooks/useCheckPay";
import PropertyUnits from "./PropertyUnitsProperty/PropertyUnits";
import TableOfContents from "./TableOfContents";
import { getPortfoliosWithProperties } from "@/actions/portfolioActions";
import { useSidebarNavigation } from "@/helpers/hooks/marketAnalysis/useSidebarNavigation";
import MarketAnalysisHeader from "./MarketAnalysisHeader/MarketAnalysisHeader";
import Link from "next/link";

export default function MarketAnalysisContent() {
    const searchParams = useSearchParams()
    const router = useRouter()
    const addressId = searchParams.get('addressId')
    const [colorPrimary, setColorPrimary] = useState<string[]>([]);
    const {workspaceSubscriptionDetails} = useAuth()
    const {id} = useParams()
    const {isPay} = useCheckPay()
    const [portfolios, setPortfolios] = useState<{ [key: string]: any }[]>([]);
    const { 
        selectedLocation, 
        searchPlace, 
        handleSelectedLocation,
    } = useMarketAnalysis();

    // Use sidebar navigation hook
    const {
        activeSection,
        sectionRefs,
        scrollToSection
    } = useSidebarNavigation(addressId, searchPlace);



    const handleGetPortfolios = () => {
        getPortfoliosWithProperties(id as string || '').then(data => {
            setPortfolios(data || [])
        });
    }

    useEffect(() => {
        handleGetPortfolios()
    }, [id]);

    
    useEffect(() => {
        if(!isPay){
            router.push(`/workspace/${id}/pricing`);
        }
    }, [isPay]);

    
    if (!workspaceSubscriptionDetails) {
        return (
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" text="Loading search data..." />
            </div>
        );
    }

    if(!isPay){
        return (
            <div className="min-h-screen flex flex-col">
                <div className="flex-1 overflow-auto">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
                            <div className="text-center">
                                <div className="text-6xl mb-4">
                                    <FontAwesomeIcon icon={faMagnifyingGlass} className="text-indigo-600" />
                                </div>
                                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Property Search </h2>
                                <p className="text-gray-600 mb-4">
                                    Subscribe to states to access our property search tool.
                                </p>
                                <Link href={`/workspace/${id}/pricing`}
                                    className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                                >
                                    Manage Subscriptions
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className={'w-full h-full overflow-y-auto'}>
            {addressId && typeof searchPlace !== 'string' && (
                <TableOfContents 
                    activeSection={activeSection} 
                    onNavigate={scrollToSection} 
                />
            )}
            
            <MarketAnalysisHeader portfolios={portfolios} handleGetPortfolios={handleGetPortfolios} />

            {
                typeof searchPlace !== 'string' ? (
                    <>
                        <div className={`space-y-6 m-4 pb-20 ${addressId ? 'ml-20' : ''}`}>
                            <div className={`${addressId ? 'hidden' : ''}`}>
                                <div className="flex justify-between items-center m-4 ml-0">
                                    <div className="relative">
                                        <SaveToPortfolio portfolios={portfolios} setPortfolios={setPortfolios} />
                                    </div>
                                </div>
                            </div>
                            {/* Building Information Section */}
                            <div id="building"  className="scroll-mt-20">
                                <div ref={sectionRefs.building}>
                                    <PropertyInformation />
                                </div>
                                <div className="mt-6" ref={sectionRefs.aiSummary}>
                                    <BuildingAnalysis  />
                                </div>
                            </div>
                            
                            {/* Tax History Section */}
                            <div id="tax-history" ref={sectionRefs.taxHistory} className="scroll-mt-20 pt-6">
                                <TaxHistory />
                            </div>
                            
                            {/* Building Units Section */}
                            <div id="units" ref={sectionRefs.units} className="scroll-mt-20 pt-6">
                                <PropertyUnits />
                            </div>
                            
                            {/* Solar & Demographics Section */}
                            <div id="solar" ref={sectionRefs.solar} className="scroll-mt-20 pt-6">
                                                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <SolarPotential />
                                <Demographics />
                            </div>
                            </div>

                            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                                {/* Nearby Locations Section */}
                                <div id="locations" ref={sectionRefs.locations} className="scroll-mt-20 h-[800px]">
                                    <NearbyLocations />
                                </div>

                                {/* Map Section */}
                                <div id="map" className="scroll-mt-20 h-[800px]">
                                    <MapCard
                                        latitude={searchPlace?.geometry.location.lat()}
                                        longitude={searchPlace?.geometry.location.lng()}
                                        selectedLocation={selectedLocation}
                                        colorPrimary={colorPrimary}
                                        handleSelectedLocation={handleSelectedLocation}
                                        setColorPrimary={setColorPrimary}
                                    />
                                </div>
                            </div>
                        </div>
                    </>
                ) : null
            }
        </div>
    );
}
