import Spinner from "@/components/UI/Spinner";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { updateUnit, addUnit, addUnitMarketData, getUnitDataDB, removeUnit, deleteUnitMarketDataItem } from "@/actions/propetyUnitsActions";
import { useDebounce } from "@/helpers/hooks/useDebounce"
import { useEffect, useState, useMemo } from "react";
import PropertyUnitSelect from "./PropertyUnitSelect";
import PropertyUnitsInfo from "./ProperyUnitsInfo";
import PropertyUnitsTags from "./PropertyUnitsTags";
import PropertyUnitsImages from "./PropertyUnitsImages/PropertyUnitsImages";
import PropertyAIConditionAnalysis from "./PropertyAIConditionAnalysis";
import { useSearchParams, usePathname } from "next/navigation";
import PropertyUnitsMarketItem from "./PropertyUnitsMarketItem";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faExclamationTriangle, faPlus, faFilePdf, faInfoCircle, faXmark, faMagicWandSparkles, faChevronDown, faChevronUp, faChartSimple, faRobot, faBroom, faSearch, faCheckCircle, faExternalLinkAlt, faBuilding, faCheck, faEdit } from "@fortawesome/free-solid-svg-icons";
import ModalBase from "@/components/UI/ModalBase";
import ModalAddMarketData from "@/components/Modals/ModalAddMarketData";
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import { getListingTypeDisplayName, getListingTypeBadgeClasses } from "@/utils/formatters";
import { createClient } from "@/utils/supabase/client";
import { useStatus } from "@/context/StatusContext";

interface DuplicateInfo {
    type: 'amenity' | 'feature';
    items: string[];
    count: number;
}

function PropertyUnits() {
    const { unitData, marketData, isLoadingUnitData, updateMarketAnalysisState, searchPlace } = useMarketAnalysis();
    const { showModal, updateModalData } = useModal();
    const { addStatus, updateStatus } = useStatus();
    const [unitValue, setUnitValue] = useState<{ [key: string]: any } | null>(null)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [unitToDelete, setUnitToDelete] = useState<{ [key: string]: any } | null>(null)
    const [isDeleting, setIsDeleting] = useState(false)
    const [isSearchingAgentically, setIsSearchingAgentically] = useState(false)
    const [showAgenticResults, setShowAgenticResults] = useState(false)
    const [agenticResults, setAgenticResults] = useState<any>(null)
    const [isSearchingMarketData, setIsSearchingMarketData] = useState(false)
    const [marketDataResults, setMarketDataResults] = useState<any>(null)
    const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({})
    const [isCalculatingWithAI, setIsCalculatingWithAI] = useState(false)
    const [aiCalculationProgress, setAiCalculationProgress] = useState<{
        currentUnit: number;
        totalUnits: number;
        currentTask: string;
        unitName: string;
    } | null>(null)
    const [showProgressModal, setShowProgressModal] = useState(false)
    const [aiJobId, setAiJobId] = useState<string | null>(null)
    const [showStatusPopup, setShowStatusPopup] = useState(false)
    const [abortController, setAbortController] = useState<AbortController | null>(null)
    const [showConfirmationModal, setShowConfirmationModal] = useState(false)
    const [duplicateInfo, setDuplicateInfo] = useState<DuplicateInfo[]>([])
    const [showDuplicateWarning, setShowDuplicateWarning] = useState(false)
    const [showDuplicateCleanupModal, setShowDuplicateCleanupModal] = useState(false)
    const [isCleaningDuplicates, setIsCleaningDuplicates] = useState(false)
    const [globalDuplicates, setGlobalDuplicates] = useState<{
        amenities: string[];
        features: string[];
        count: number;
    } | null>(null)
    const [showGlobalDuplicateWarning, setShowGlobalDuplicateWarning] = useState(false)
    // Responsive tab state for smaller screens
    const [activeResponsiveTab, setActiveResponsiveTab] = useState<'images' | 'info'>('images')
    // Unit name editing state
    const [isEditingUnitName, setIsEditingUnitName] = useState(false)
    const [editingUnitName, setEditingUnitName] = useState('')
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const addressId = searchParams.get('addressId');
    const marketAddress = searchParams.get('marketAddress');

    // Add search state for unit filtering
    const [unitSearchQuery, setUnitSearchQuery] = useState('');
    
    // Market data modal states
    const [showMarketDataModal, setShowMarketDataModal] = useState(false);
    const [marketDataModalResults, setMarketDataModalResults] = useState<{
        success: boolean;
        title: string;
        message: string;
        details: any[];
        type: 'success' | 'warning' | 'info' | 'error';
    } | null>(null);

    // Manual market data modal states
    const [showManualMarketDataModal, setShowManualMarketDataModal] = useState(false);
    const [isSubmittingManualData, setIsSubmittingManualData] = useState(false);

    // Determine if we're on the search page - use pathname-based detection
    // Search page typically has '/search' in the path or no addressId
    const isSearchPage = pathname.includes('/search') || !addressId

    // Check for global duplicates across all units when component mounts
    useEffect(() => {
        if (unitData && unitData.length > 0 && !isSearchPage) {
            checkGlobalDuplicates();
        }
    }, [unitData, isSearchPage]);

    // Auto-select first unit when unitData loads and no unit is currently selected
    useEffect(() => {
        if (unitData && unitData.length > 0 && !unitValue && !isSearchPage) {
            console.log('Auto-selecting first unit:', unitData[0]);
            handleSelectUnit(unitData[0]);
        }
    }, [unitData, unitValue, isSearchPage]);

    const checkGlobalDuplicates = () => {
        if (!unitData || unitData.length === 0) return;

        const allAmenities: string[] = [];
        const allFeatures: string[] = [];
        
        // Collect all amenities and features from all units
        unitData.forEach((unit: any, unitIndex: number) => {
            console.log(`DEBUG: Unit ${unitIndex} amenities_tags:`, unit.amenities_tags);
            console.log(`DEBUG: Unit ${unitIndex} feature_tags:`, unit.feature_tags);
            
            if (unit.amenities_tags && Array.isArray(unit.amenities_tags)) {
                // Flatten any nested arrays and filter out empty/null values
                const flatAmenities = unit.amenities_tags.flat().filter((tag: any) => tag && typeof tag === 'string');
                allAmenities.push(...flatAmenities);
            }
            if (unit.feature_tags && Array.isArray(unit.feature_tags)) {
                // Flatten any nested arrays and filter out empty/null values
                const flatFeatures = unit.feature_tags.flat().filter((tag: any) => tag && typeof tag === 'string');
                allFeatures.push(...flatFeatures);
            }
        });

        console.log('DEBUG: All amenities collected:', allAmenities);
        console.log('DEBUG: All features collected:', allFeatures);

        // Find duplicates
        const duplicateAmenities = findDuplicatesInArray(allAmenities);
        const duplicateFeatures = findDuplicatesInArray(allFeatures);
        
        console.log('DEBUG: Duplicate amenities found:', duplicateAmenities);
        console.log('DEBUG: Duplicate features found:', duplicateFeatures);
        
        const totalDuplicates = duplicateAmenities.length + duplicateFeatures.length;
        
        console.log('DEBUG: Total duplicates count:', totalDuplicates);
        
        if (totalDuplicates >= 3) {
            setGlobalDuplicates({
                amenities: duplicateAmenities,
                features: duplicateFeatures,
                count: totalDuplicates
            });
            setShowGlobalDuplicateWarning(true);
        }
    };

    const findDuplicatesInArray = (arr: string[]): string[] => {
        const counts = new Map<string, string[]>();
        
        // Count occurrences of each normalized item
        arr.forEach(item => {
            const normalized = item.toLowerCase().trim();
            if (!counts.has(normalized)) {
                counts.set(normalized, []);
            }
            counts.get(normalized)!.push(item);
        });
        
        // Return only the extra instances (not the first occurrence)
        const duplicates: string[] = [];
        counts.forEach((items, normalized) => {
            if (items.length > 1) {
                // Only include the duplicates (skip the first occurrence)
                duplicates.push(...items.slice(1));
            }
        });
        
        return duplicates;
    };

    const checkUnitDuplicates = (unit: any) => {
        const duplicates: DuplicateInfo[] = [];
        
        // Check amenities for duplicates
        if (unit.amenities_tags && unit.amenities_tags.length > 0) {
            const amenityDuplicates = findDuplicatesInArray(unit.amenities_tags);
            if (amenityDuplicates.length > 0) {
                duplicates.push({
                    type: 'amenity',
                    items: amenityDuplicates,
                    count: amenityDuplicates.length
                });
            }
        }
        
        // Check features for duplicates
        if (unit.feature_tags && unit.feature_tags.length > 0) {
            const featureDuplicates = findDuplicatesInArray(unit.feature_tags);
            if (featureDuplicates.length > 0) {
                duplicates.push({
                    type: 'feature',
                    items: featureDuplicates,
                    count: featureDuplicates.length
                });
            }
        }
        
        setDuplicateInfo(duplicates);
        setShowDuplicateWarning(duplicates.length > 0);
    };

    const handleCleanupUnitDuplicates = async () => {
        if (!unitValue || duplicateInfo.length === 0) return;
        
        setIsCleaningDuplicates(true);
        const supabase = createClient();
        
        try {
            let updatedAmenities = [...(unitValue.amenities_tags || [])];
            let updatedFeatures = [...(unitValue.feature_tags || [])];
            
            // Remove duplicates from amenities
            const amenityDuplicates = duplicateInfo.find(d => d.type === 'amenity');
            if (amenityDuplicates) {
                updatedAmenities = removeDuplicatesFromArray(updatedAmenities);
            }
            
            // Remove duplicates from features
            const featureDuplicates = duplicateInfo.find(d => d.type === 'feature');
            if (featureDuplicates) {
                updatedFeatures = removeDuplicatesFromArray(updatedFeatures);
            }
            
            // Update in database
            const { error } = await supabase
                .from('units')
                .update({
                    amenities_tags: updatedAmenities,
                    feature_tags: updatedFeatures
                })
                .eq('id', unitValue.id);
                
            if (error) {
                console.error('Error cleaning duplicates:', error);
                alert('Failed to clean duplicates. Please try again.');
                return;
            }
            
            // Update local state
            const updatedUnitValue = {
                ...unitValue,
                amenities_tags: updatedAmenities,
                feature_tags: updatedFeatures
            };
            
            setUnitValue(updatedUnitValue);
            
            // Update unit data in context
            const updatedUnitData = unitData?.map((unit: any) => 
                unit.id === unitValue.id ? updatedUnitValue : unit
            );
            updateMarketAnalysisState({ unitData: updatedUnitData });
            
            // Clear duplicate states
            setDuplicateInfo([]);
            setShowDuplicateWarning(false);
            setShowDuplicateCleanupModal(false);
            
            const totalRemoved = (amenityDuplicates?.count || 0) + (featureDuplicates?.count || 0);
            alert(`Successfully removed ${totalRemoved} duplicate items.`);
            
        } catch (error) {
            console.error('Error during cleanup:', error);
            alert('An error occurred during cleanup. Please try again.');
        } finally {
            setIsCleaningDuplicates(false);
        }
    };

    const handleGlobalCleanup = async () => {
        if (!globalDuplicates || !unitData) return;
        
        setIsCleaningDuplicates(true);
        const supabase = createClient();
        
        try {
            const updates = unitData.map((unit: any) => {
                const cleanedAmenities = removeDuplicatesFromArray(unit.amenities_tags || []);
                const cleanedFeatures = removeDuplicatesFromArray(unit.feature_tags || []);
                
                return {
                    id: unit.id,
                    amenities_tags: cleanedAmenities,
                    feature_tags: cleanedFeatures
                };
            });
            
            // Update all units in database
            for (const update of updates) {
                const { error } = await supabase
                    .from('units')
                    .update({
                        amenities_tags: update.amenities_tags,
                        feature_tags: update.feature_tags
                    })
                    .eq('id', update.id);
                    
                if (error) {
                    console.error('Error updating unit:', update.id, error);
                }
            }
            
            // Update local state
            const updatedUnitData = unitData.map((unit: any) => {
                const update = updates.find(u => u.id === unit.id);
                return update ? { ...unit, ...update } : unit;
            });
            
            updateMarketAnalysisState({ unitData: updatedUnitData });
            
            // Update current unit if selected
            if (unitValue) {
                const updatedCurrentUnit = updates.find(u => u.id === unitValue.id);
                if (updatedCurrentUnit) {
                    setUnitValue({ ...unitValue, ...updatedCurrentUnit });
                }
            }
            
            // Clear global duplicate states
            setGlobalDuplicates(null);
            setShowGlobalDuplicateWarning(false);
            
            alert(`Successfully cleaned duplicates across all units.`);
            
        } catch (error) {
            console.error('Error during global cleanup:', error);
            alert('An error occurred during cleanup. Please try again.');
        } finally {
            setIsCleaningDuplicates(false);
        }
    };

    const removeDuplicatesFromArray = (arr: string[]): string[] => {
        const seen = new Set<string>();
        return arr.filter(item => {
            const normalized = item.toLowerCase().trim();
            if (seen.has(normalized)) {
                return false;
            } else {
                seen.add(normalized);
                return true;
            }
        });
    };

    // Calculate market data statistics
    const getMarketDataStats = () => {
        if (!unitValue?.prop_market_data?.length) return null;
        
        const allItems = unitValue.prop_market_data.flatMap((group: any) => group.data || []);
        const rentalItems = allItems.filter((item: any) => 
            item.listing_type === 'for_rent' || 
            item.listing_type === 'rented' || 
            item.listing_type === 'forrent'
        );
        
        // Filter for past 3 years
        const threeYearsAgo = new Date();
        threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
        
        const recentRentals = rentalItems.filter((item: any) => {
            if (!item.list_date) return false;
            const itemDate = new Date(item.list_date);
            return itemDate >= threeYearsAgo;
        });
        
        const totalCount = allItems.length;
        const rentalCount = rentalItems.length;
        
        // Calculate average rental price
        const avgRental = recentRentals.length > 0 
            ? recentRentals.reduce((sum: number, item: any) => sum + (Number(item.list_price) || 0), 0) / recentRentals.length
            : 0;
            
        // Price distribution data for plot
        const priceRanges = [
            { range: '<$1K', min: 0, max: 1000, count: 0 },
            { range: '$1K-2K', min: 1000, max: 2000, count: 0 },
            { range: '$2K-3K', min: 2000, max: 3000, count: 0 },
            { range: '$3K-4K', min: 3000, max: 4000, count: 0 },
            { range: '$4K+', min: 4000, max: Infinity, count: 0 }
        ];
        
        rentalItems.forEach((item: any) => {
            const price = Number(item.list_price) || 0;
            const range = priceRanges.find(r => price >= r.min && price < r.max);
            if (range) range.count++;
        });
        
        return {
            totalCount,
            rentalCount,
            avgRental,
            priceRanges,
            recentRentalsCount: recentRentals.length
        };
    };

    const toggleItemExpansion = (itemKey: string) => {
        setExpandedItems(prev => ({
            ...prev,
            [itemKey]: !prev[itemKey]
        }));
    };

    const handleUpdateUnit = useDebounce(async (unit) => {
        await updateUnit(unit)
        getUnitDataDB(addressId as string).then((data) => {
            updateMarketAnalysisState({
                unitData: data
            })
        })
    }, 500);

    const handleSelectUnit = (unit: { [key: string]: any }) => {
        if (isSearchPage) {
            // On search page, don't set the unit value to prevent showing details
            return;
        }
        
        const selectedUnit = {
            id: unit?.id || '',
            unit: unit?.unit || '',
            beds: unit?.beds || 0,
            baths: unit?.baths || 0,
            hoa_fee: unit?.hoa_fee || 0,
            sqft: unit?.sqft || 0,
            rent: unit?.rent || 0,
            price: unit?.price || 0,
            document_recorded_date: unit?.document_recorded_date || '',
            feature_tags: unit?.feature_tags || [],
            amenities_tags: unit?.amenities_tags || [],
            img_urls: unit?.img_urls || [],
            prop_market_data: unit?.prop_market_data || []
        };
        
        setUnitValue(selectedUnit);
        
        // Check for duplicates in the selected unit
        checkUnitDuplicates(selectedUnit);
    };

    const handleAddUnit = () => {
        if (isSearchPage) {
            // On search page, don't allow adding units
            return;
        }
        
        addUnit(addressId as string).then((data) => {
            updateMarketAnalysisState({
                unitData: data
            })
        })
    };
    
    const handleAddUnitMarketData = () => {
        if (!addressId || !unitValue) return;
        
        setShowManualMarketDataModal(true);
    };

    const handleManualMarketDataSubmit = async (marketDataEntry: any) => {
        if (!addressId || !unitValue) return;
        
        setIsSubmittingManualData(true);
        
        // Add loading status
        const statusId = addStatus({
            title: 'Adding Market Data',
            message: 'Saving manual market data entry...',
            type: 'loading'
        });
        
        try {
            const response = await fetch('/api/add-manual-market-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    addressId: addressId,
                    unitId: unitValue.id,
                    unitNumber: unitValue.unit,
                    marketDataEntry: marketDataEntry
                })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to add market data');
            }

            // Update status to success
            updateStatus(statusId, {
                title: 'Market Data Added',
                message: 'Manual market data entry saved successfully!',
                type: 'success'
            });

            // Close modal and refresh data
            setShowManualMarketDataModal(false);
            
            // Refresh unit data to show the new market data
            const updatedData = await getUnitDataDB(addressId as string);
            updateMarketAnalysisState({
                unitData: updatedData
            });
            setUnitValue(updatedData?.find((unit: any) => unit.id === unitValue?.id));
            
        } catch (error) {
            console.error('Failed to add manual market data:', error);
            
            // Update status to error
            updateStatus(statusId, {
                title: 'Save Failed',
                message: `Could not save market data: ${error instanceof Error ? error.message : 'Unknown error'}`,
                type: 'error'
            });
        } finally {
            setIsSubmittingManualData(false);
        }
    };

    const handleDeleteUnit = (unit: { [key: string]: any }) => {
        if (isSearchPage) {
            // On search page, don't allow deleting units
            return;
        }
        
        setUnitToDelete(unit)
        setShowDeleteModal(true)
    };

    const handleUploadDocuments = () => {
        showModal(modalType.documentUpload)
        updateModalData({
            propId: addressId,
            selectedPortfolioId: null,
            individual: true,
            unitId: unitValue?.id,
            unitNumber: unitValue?.unit
        })
    };

    const confirmDeleteUnit = async () => {
        if (!unitToDelete) return
        
        setIsDeleting(true)
        try {
            // Remove unit from database
            await removeUnit(unitToDelete.id)
            
            // Update local state
            const updatedUnits = unitData?.filter((unit: any) => unit.id !== unitToDelete.id) || []
            updateMarketAnalysisState({
                unitData: updatedUnits
            })
            
            // If deleted unit was selected, clear selection or select first available unit
            if (unitValue?.id === unitToDelete.id) {
                if (updatedUnits.length > 0) {
                    handleSelectUnit(updatedUnits[0])
                } else {
                    setUnitValue(null)
                }
            }
            
            // Close modal and reset state
            setShowDeleteModal(false)
            setUnitToDelete(null)
        } catch (error) {
            console.error('Error deleting unit:', error)
            // You could add a toast notification here for error handling
        } finally {
            setIsDeleting(false)
        }
    };

    const cancelDeleteUnit = () => {
        setShowDeleteModal(false)
        setUnitToDelete(null)
    };

    const handleAgenticSearch = async () => {
        if (!unitValue || isSearchPage) return;
        
        setIsSearchingAgentically(true);
        
        // Add loading status
        const statusId = addStatus({
            title: 'AI Market Search',
            message: 'Searching for market data online...',
            type: 'loading'
        });
        
        try {
            // Get the address for search
            let address = '';
            if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                address = searchPlace.formatted_address;
            } else if (typeof searchPlace === 'string') {
                address = searchPlace;
            } else if (marketAddress) {
                address = marketAddress;
            }

            // Call our agentic search API
            const response = await fetch('/api/agentic-market-search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address,
                    unit: unitValue.unit,
                    beds: unitValue.beds,
                    baths: unitValue.baths,
                    sqft: unitValue.sqft
                })
            });

            if (!response.ok) {
                throw new Error('Search failed');
            }

            const results = await response.json();
            setAgenticResults(results);
            setShowAgenticResults(true);
            
            // Update status to success
            updateStatus(statusId, {
                title: 'Market Data Found!',
                message: `Found ${results.data?.length || 0} market data points. Review and confirm to save.`,
                type: 'success'
            });
            
        } catch (error) {
            console.error('Agentic search failed:', error);
            
            // Update status to error
            updateStatus(statusId, {
                title: 'Search Failed',
                message: 'Unable to search for market data. Please try again.',
                type: 'error'
            });
        } finally {
            setIsSearchingAgentically(false);
        }
    };

    const handleFindMoreData = async () => {
        if (!unitValue || isSearchPage) return;
        
        setIsSearchingMarketData(true);
        console.log('🔍 Starting market data search for unit:', unitValue.unit);
        
        // Add loading status
        const statusId = addStatus({
            title: 'Market Data Search',
            message: 'Clearing old data and searching for new market data...',
            type: 'loading'
        });
        
        try {
            // Get the address for search
            let address = '';
            if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                address = searchPlace.formatted_address;
            } else if (typeof searchPlace === 'string') {
                address = searchPlace;
            } else if (marketAddress) {
                address = marketAddress;
            }

            console.log('🏠 Using address for search:', address);

            // Always clear existing market data before fetching new data
            console.log('🧹 Clearing existing market data for unit:', unitValue.id);
            try {
                const clearResponse = await fetch('/api/clear-unit-market-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        unitId: unitValue.id,
                        addressId: addressId
                    })
                });

                if (!clearResponse.ok) {
                    const clearError = await clearResponse.json().catch(() => ({}));
                    console.error('❌ Failed to clear market data:', clearError);
                    throw new Error(clearError.error || 'Failed to clear existing market data');
                }

                console.log('✅ Successfully cleared existing market data');
                
                // Small delay to ensure database operation completes
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Immediately refresh the UI to show cleared state
                const updatedDataAfterClear = await getUnitDataDB(addressId as string);
                console.log('📊 Data after clear:', updatedDataAfterClear?.find((unit: any) => unit.id === unitValue?.id)?.prop_market_data);
                updateMarketAnalysisState({
                    unitData: updatedDataAfterClear
                });
                setUnitValue(updatedDataAfterClear?.find((unit: any) => unit.id === unitValue?.id));
                
            } catch (clearError) {
                console.error('❌ Error clearing market data:', clearError);
                console.log('🎯 Setting error modal and showing it...');
                
                // Update status to error
                updateStatus(statusId, {
                    title: 'Clear Failed',
                    message: `Could not clear existing market data: ${clearError instanceof Error ? clearError.message : 'Unknown error'}`,
                    type: 'error'
                });
                
                setMarketDataModalResults({
                    success: false,
                    title: 'Failed to Clear Data',
                    message: `Could not clear existing market data: ${clearError instanceof Error ? clearError.message : 'Unknown error'}`,
                    details: [],
                    type: 'error'
                });
                setShowMarketDataModal(true);
                console.log('Modal state set:', { showMarketDataModal: true, marketDataModalResults: 'error type' });
                return;
            }

            // Call our comprehensive market data search API
            console.log('🚀 Calling agentic market data API...');
            const response = await fetch('/api/add-agentic-market-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address,
                    unit: unitValue.unit,
                    addressId: addressId,
                    unitId: unitValue.id
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('❌ API response not ok:', errorData);
                throw new Error(errorData.error || 'Search failed');
            }

            const results = await response.json();
            console.log('📈 API Results received:', results);
            setMarketDataResults(results);
            
            if (results.data && results.data.length > 0 && results.saved) {
                console.log('✅ Market data found and saved. Refreshing UI...');
                
                // Small delay to ensure database save operation completes
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Force refresh unit data to show the new market data
                const updatedData = await getUnitDataDB(addressId as string);
                console.log('📊 Updated unit data after save:', updatedData);
                
                updateMarketAnalysisState({
                    unitData: updatedData
                });
                
                const updatedUnit = updatedData?.find((unit: any) => unit.id === unitValue?.id);
                console.log('🏠 Updated unit after refresh:', updatedUnit);
                console.log('📈 New market data count:', updatedUnit?.prop_market_data?.length);
                setUnitValue(updatedUnit);
                
                // Update status to success and set modal results
                updateStatus(statusId, {
                    title: 'Market Data Updated!',
                    message: `Added ${results.data.length} new market data points for Unit ${unitValue.unit}`,
                    type: 'success'
                });
                
                console.log('🎯 Setting SUCCESS modal and showing it...');
                setMarketDataModalResults({
                    success: true,
                    title: 'Market Data Re-Fetched Successfully!',
                    message: `Cleared old data and added ${results.data.length} new market data points for Unit ${unitValue.unit}`,
                    details: results.data,
                    type: 'success'
                });
                setShowMarketDataModal(true);
                console.log('✅ Modal state set to show SUCCESS');
            } else if (results.data && results.data.length > 0) {
                console.log('⚠️ Data found but not saved...');
                updateStatus(statusId, {
                    title: 'Save Failed',
                    message: `Found ${results.data.length} data points but could not save them.`,
                    type: 'error'
                });
                setMarketDataModalResults({
                    success: false,
                    title: 'Market Data Found but Not Saved',
                    message: `Found ${results.data.length} market data points but could not save them.`,
                    details: results.data,
                    type: 'warning'
                });
                setShowMarketDataModal(true);
                console.log('⚠️ Modal state set to show WARNING');
            } else {
                console.log('ℹ️ No market data found');
                updateStatus(statusId, {
                    title: 'No Data Found',
                    message: 'No market data found for this property. Try a different search.',
                    type: 'info'
                });
                setMarketDataModalResults({
                    success: false,
                    title: 'No Market Data Found',
                    message: 'No market data was found for this property and unit. Try searching for a different property or check if the address is correct.',
                    details: [],
                    type: 'info'
                });
                setShowMarketDataModal(true);
                console.log('ℹ️ Modal state set to show INFO');
            }
            
        } catch (error) {
            console.error('❌ Market data search failed:', error);
            console.log('🎯 Setting ERROR modal and showing it...');
            
            // Update status to error
            updateStatus(statusId, {
                title: 'Search Failed',
                message: `Unable to search for market data: ${error instanceof Error ? error.message : 'Please try again later.'}`,
                type: 'error'
            });
            
            setMarketDataModalResults({
                success: false,
                title: 'Search Failed',
                message: `Unable to search for market data: ${error instanceof Error ? error.message : 'Please try again later.'}`,
                details: [],
                type: 'error'
            });
            setShowMarketDataModal(true);
            console.log('❌ Modal state set to show ERROR');
        } finally {
            setIsSearchingMarketData(false);
            console.log('🏁 Market data search completed, isSearchingMarketData set to false');
        }
    };

    const handleAcceptAgenticData = async () => {
        if (!agenticResults || !unitValue) return;
        
        try {
            // Add the agentic data as market data
            const response = await fetch('/api/add-agentic-market-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    addressId: addressId,
                    unitId: unitValue.id,
                    marketData: agenticResults.data,
                    source: agenticResults.source,
                    searchQuery: agenticResults.searchQuery
                })
            });

            if (response.ok) {
                // Refresh unit data
                const updatedData = await getUnitDataDB(addressId as string);
                updateMarketAnalysisState({
                    unitData: updatedData
                });
                setUnitValue(updatedData?.find((unit: any) => unit.id === unitValue?.id));
                
                setShowAgenticResults(false);
                setAgenticResults(null);
            }
        } catch (error) {
            console.error('Failed to save agentic data:', error);
            alert('Failed to save the market data. Please try again.');
        }
    };

    const handleRejectAgenticData = () => {
        setShowAgenticResults(false);
        setAgenticResults(null);
    };

    const handleClearAllMarketData = async () => {
        if (!unitValue || isSearchPage) return;
        
        // Add loading status
        const statusId = addStatus({
            title: 'Clearing Market Data',
            message: 'Removing all market data for this unit...',
            type: 'loading'
        });
        
        try {
            const response = await fetch('/api/clear-all-market-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    unitId: unitValue.id,
                    addressId: addressId
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || 'Failed to clear market data');
            }

            // Update status to success
            updateStatus(statusId, {
                title: 'Data Cleared',
                message: 'All market data has been removed successfully.',
                type: 'success'
            });

            // Force refresh unit data to show the cleared state
            const updatedData = await getUnitDataDB(addressId as string);
            updateMarketAnalysisState({
                unitData: updatedData
            });
            setUnitValue(updatedData?.find((unit: any) => unit.id === unitValue?.id));
            
        } catch (error) {
            console.error('Clear all market data failed:', error);
            
            // Update status to error
            updateStatus(statusId, {
                title: 'Clear Failed',
                message: `Unable to clear market data: ${error instanceof Error ? error.message : 'Please try again.'}`,
                type: 'error'
            });
        }
    };

    const handleDeleteMarketDataItem = async (marketId: string, index: number) => {
        if (!unitValue || isSearchPage) return;
        
        try {
            const updatedData = await deleteUnitMarketDataItem(unitValue.id, index, marketId, addressId as string);
            updateMarketAnalysisState({
                unitData: updatedData
            });
            setUnitValue(updatedData?.find((unit: any) => unit.id === unitValue?.id));
        } catch (error) {
            console.error('Failed to delete market data item:', error);
            alert('Failed to delete the market data item. Please try again.');
        }
    };

    // Check for existing AI job on component mount
    useEffect(() => {
        const checkExistingJob = () => {
            const jobData = localStorage.getItem(`ai-calc-job-${addressId}`);
            if (jobData) {
                try {
                    const { jobId, progress, isActive } = JSON.parse(jobData);
                    if (isActive) {
                        setAiJobId(jobId);
                        setAiCalculationProgress(progress);
                        setIsCalculatingWithAI(true);
                        setShowStatusPopup(true);
                        // Resume the job
                        resumeAICalculation(jobId, progress);
                    }
                } catch (error) {
                    console.error('Failed to parse existing AI job:', error);
                    localStorage.removeItem(`ai-calc-job-${addressId}`);
                }
            }
        };

        if (addressId) {
            checkExistingJob();
        }
    }, [addressId]);

    // Save job state to localStorage
    const saveJobState = (jobId: string, progress: any, isActive: boolean) => {
        if (!addressId) return;
        localStorage.setItem(`ai-calc-job-${addressId}`, JSON.stringify({
            jobId,
            progress,
            isActive,
            timestamp: Date.now()
        }));
    };

    // Clear job state from localStorage
    const clearJobState = () => {
        if (!addressId) return;
        localStorage.removeItem(`ai-calc-job-${addressId}`);
    };

    const resumeAICalculation = async (jobId: string, lastProgress: any) => {
        if (!unitData || unitData.length === 0) return;
        
        const controller = new AbortController();
        setAbortController(controller);

        try {
            // Get the address for searches
            let address = '';
            if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                address = searchPlace.formatted_address;
            } else if (typeof searchPlace === 'string') {
                address = searchPlace;
            } else if (marketAddress) {
                address = marketAddress;
            }

            // Resume from where we left off
            for (let i = lastProgress.currentUnit; i < unitData.length; i++) {
                if (controller.signal.aborted) return;

                const unit = unitData[i];
                await processUnit(unit, i, unitData.length, address, controller.signal);
            }

            // Completion
            const completionProgress = {
                currentUnit: unitData.length,
                totalUnits: unitData.length,
                currentTask: 'Complete!',
                unitName: 'All units processed'
            };

            setAiCalculationProgress(completionProgress);
            saveJobState(jobId, completionProgress, false);

            // Refresh data
            const updatedData = await getUnitDataDB(addressId as string);
            updateMarketAnalysisState({ unitData: updatedData });
            if (unitValue) {
                const refreshedUnit = updatedData?.find((unit: any) => unit.id === unitValue.id);
                if (refreshedUnit) setUnitValue(refreshedUnit);
            }

            // Auto-hide after completion
            setTimeout(() => {
                setShowStatusPopup(false);
                setIsCalculatingWithAI(false);
                setAiCalculationProgress(null);
                clearJobState();
            }, 3000);

                 } catch (error: any) {
             if (error.name !== 'AbortError') {
                 console.error('AI calculation failed:', error);
             }
         }
    };

    const processUnit = async (unit: any, index: number, total: number, address: string, signal: AbortSignal) => {
        const unitProgress = {
            currentUnit: index + 1,
            totalUnits: total,
            currentTask: 'Searching unit info...',
            unitName: `Unit ${unit.unit}`
        };

        setAiCalculationProgress(unitProgress);
        saveJobState(aiJobId!, unitProgress, true);

        // Unit info search
        if (!signal.aborted) {
            try {
                const unitInfoResponse = await fetch('/api/agentic-unit-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address, unit: unit.unit }),
                    signal
                });

                if (unitInfoResponse.ok) {
                    const unitInfoResults = await unitInfoResponse.json();
                    const updatedUnit = {
                        ...unit,
                        beds: unitInfoResults.data.beds || (unit as any).beds || 0,
                        baths: unitInfoResults.data.baths || (unit as any).baths || 0,
                        sqft: unitInfoResults.data.sqft || (unit as any).sqft || 0,
                        rent: unitInfoResults.data.rent || (unit as any).rent || 0,
                        price: unitInfoResults.data.price || (unit as any).price || 0,
                        hoa_fee: unitInfoResults.data.hoa_fee || (unit as any).hoa_fee || 0
                    };
                    await updateUnit(updatedUnit);
                }
            } catch (error: any) {
                if (error.name !== 'AbortError') {
                    console.error(`Failed to get AI unit info for unit ${unit.unit}:`, error);
                }
            }
        }

        if (signal.aborted) return;
        await new Promise(resolve => setTimeout(resolve, 500));

        // Image analysis
        const imageProgress = {
            currentUnit: index + 1,
            totalUnits: total,
            currentTask: 'Analyzing existing images...',
            unitName: `Unit ${unit.unit}`
        };

        setAiCalculationProgress(imageProgress);
        saveJobState(aiJobId!, imageProgress, true);

        if ((unit as any).img_urls?.length > 0 && !signal.aborted) {
            try {
                for (const imageUrl of (unit as any).img_urls) {
                    if (signal.aborted) return;
                    
                    await fetch('/api/ai-photo-analysis', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            imageUrl,
                            unitId: unit.id,
                            addressId: addressId
                        }),
                        signal
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            } catch (error: any) {
                if (error.name !== 'AbortError') {
                    console.error(`Failed to analyze images for unit ${unit.unit}:`, error);
                }
            }
        }

        if (signal.aborted) return;
        await new Promise(resolve => setTimeout(resolve, 300));

        // Market data search
        const marketProgress = {
            currentUnit: index + 1,
            totalUnits: total,
            currentTask: 'Searching market data...',
            unitName: `Unit ${unit.unit}`
        };

        setAiCalculationProgress(marketProgress);
        saveJobState(aiJobId!, marketProgress, true);

        if (!signal.aborted) {
            try {
                const marketSearchResponse = await fetch('/api/agentic-market-search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        address,
                        unit: unit.unit,
                        beds: (unit as any).beds,
                        baths: (unit as any).baths,
                        sqft: (unit as any).sqft
                    }),
                    signal
                });

                if (marketSearchResponse.ok) {
                    const marketResults = await marketSearchResponse.json();
                    await fetch('/api/add-agentic-market-data', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            addressId: addressId,
                            unitId: unit.id,
                            marketData: marketResults.data,
                            source: marketResults.source,
                            searchQuery: marketResults.searchQuery
                        }),
                        signal
                    });
                }
            } catch (error: any) {
                if (error.name !== 'AbortError') {
                    console.error(`Failed to get AI market data for unit ${unit.unit}:`, error);
                }
            }
        }

        await new Promise(resolve => setTimeout(resolve, 500));
    };

    const handleCalculateWithAI = async () => {
        if (!unitData || unitData.length === 0 || isSearchPage) return;
        
        // Show confirmation modal first
        setShowConfirmationModal(true);
    };

    const handleConfirmAICalculation = async () => {
        setShowConfirmationModal(false);
        
        const jobId = `ai-calc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        setAiJobId(jobId);
        setIsCalculatingWithAI(true);
        setShowProgressModal(true);
        setShowStatusPopup(false);

        const initialProgress = { currentUnit: 0, totalUnits: unitData?.length || 0, currentTask: 'Starting...', unitName: '' };
        setAiCalculationProgress(initialProgress);
        saveJobState(jobId, initialProgress, true);

        // Start background processing after a short delay to show modal
        setTimeout(() => {
            setShowProgressModal(false);
            setShowStatusPopup(true);
            resumeAICalculation(jobId, initialProgress);
        }, 2000);
    };

    const handleCancelConfirmation = () => {
        setShowConfirmationModal(false);
    };

    const handleCancelAICalculation = () => {
        if (abortController) {
            abortController.abort();
        }
        setIsCalculatingWithAI(false);
        setShowProgressModal(false);
        setShowStatusPopup(false);
        setAiCalculationProgress(null);
        setAbortController(null);
        clearJobState();
    };

    const handleCloseStatusPopup = () => {
        setShowStatusPopup(false);
    };

    // Unit name editing handlers
    const handleEditUnitName = () => {
        setIsEditingUnitName(true);
        setEditingUnitName(unitValue?.unit || '');
    };

    const handleSaveUnitName = async () => {
        if (editingUnitName.trim() && editingUnitName !== unitValue?.unit) {
            try {
                const updatedUnit = { ...unitValue, unit: editingUnitName.trim() };
                setUnitValue(updatedUnit);
                handleUpdateUnit(updatedUnit);
            } catch (error) {
                console.error('Failed to update unit name:', error);
            }
        }
        setIsEditingUnitName(false);
    };

    const handleCancelEditUnitName = () => {
        setIsEditingUnitName(false);
        setEditingUnitName(unitValue?.unit || '');
    };

    const handleUnitNameKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSaveUnitName();
        } else if (e.key === 'Escape') {
            e.preventDefault();
            handleCancelEditUnitName();
        }
    };

    const handleOpenProgressModal = () => {
        setShowProgressModal(true);
    };

    useEffect(() => {
        console.log(unitValue)
    }, [unitValue]);

    const marketStats = getMarketDataStats();

    // Filter and sort units for PropertyUnitSelect
    const filteredAndSortedUnits = useMemo(() => {
        if (!unitData) return [];
        
        // Filter units based on search query
        const filtered = unitData.filter((unit: any) => {
            if (!unitSearchQuery) return true;
            const searchLower = unitSearchQuery.toLowerCase();
            return (
                unit.unit?.toString().toLowerCase().includes(searchLower) ||
                unit.beds?.toString().includes(searchLower) ||
                unit.baths?.toString().includes(searchLower) ||
                unit.sqft?.toString().includes(searchLower)
            );
        });
        
        // Sort alphabetically by unit number
        return filtered.sort((a: any, b: any) => {
            const unitA = a.unit?.toString() || '';
            const unitB = b.unit?.toString() || '';
            return unitA.localeCompare(unitB, undefined, { numeric: true });
        });
    }, [unitData, unitSearchQuery]);

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faBuilding} className="h-4 w-4 text-blue-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Property Units</h3>
                    </div>
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
                {
                    isLoadingUnitData ? (
                        <div className="flex flex-col items-center justify-center py-10">
                            <Spinner size="lg" className="mb-3" />
                            <p className="text-sm text-gray-600">Loading unit data...</p>
                        </div>
                    ) : (
                        <div className="flex gap-4">
                            <div className="w-90">
                                <div className="mb-4 group relative h-10 flex items-center">
                                    {/* Search bar for units */}
                                    <div className="w-full">
                                        <div className="relative">
                                            <FontAwesomeIcon 
                                                icon={faSearch} 
                                                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 z-10" 
                                            />
                                            <input
                                                type="text"
                                                placeholder="Search units by number, beds, baths..."
                                                value={unitSearchQuery}
                                                onChange={(e) => setUnitSearchQuery(e.target.value)}
                                                className="w-full pl-12 pr-10 py-2.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm shadow-sm bg-white/95 backdrop-blur-sm transition-all duration-200"
                                            />
                                            {unitSearchQuery && (
                                                <button
                                                    onClick={() => setUnitSearchQuery('')}
                                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
                                                >
                                                    <FontAwesomeIcon icon={faXmark} className="h-3.5 w-3.5" />
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="units-rolodex-container">
                                    <div className="units-rolodex-scroll">
                                        {filteredAndSortedUnits?.map((unit: any) => (
                                            <PropertyUnitSelect 
                                                key={unit.id} 
                                                unit={unit} 
                                                handleSelectUnit={handleSelectUnit}
                                                handleDeleteUnit={isSearchPage ? undefined : handleDeleteUnit}
                                            />
                                        ))}
                                        {filteredAndSortedUnits?.length === 0 && unitSearchQuery && (
                                            <div className="text-center py-8 text-gray-500">
                                                <FontAwesomeIcon icon={faSearch} className="h-8 w-8 mb-2" />
                                                <p>No units found matching "{unitSearchQuery}"</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                
                                {!isSearchPage && (
                                    <div className="flex gap-2 mt-4">
                                        <button 
                                            className="add-unit-btn flex-1" 
                                            onClick={handleAddUnit}
                                        >
                                            <FontAwesomeIcon icon={faPlus} className="mr-2" />
                                            Add New Unit
                                        </button>
                                        <button 
                                            className="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                            onClick={(e) => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                handleCalculateWithAI();
                                            }}
                                            disabled={isCalculatingWithAI || !unitData || unitData.length === 0}
                                        >
                                            {isCalculatingWithAI ? (
                                                <>
                                                    <Spinner size="sm" />
                                                    <span className="hidden sm:inline">AI Working</span>
                                                    <span className="sm:hidden">🤖</span>
                                                </>
                                            ) : (
                                                <>
                                                    <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                    <span className="hidden sm:inline">AI Calculate</span>
                                                    <span className="sm:hidden">AI</span>
                                                </>
                                            )}
                                        </button>
                                    </div>
                                )}
                            </div>
                            {
                                isSearchPage ? (
                                    <div className="flex-1">
                                        <div className="p-8 rounded-xl h-[575px] bg-gradient-to-br from-indigo-50 to-blue-50 flex flex-col items-center justify-center text-center">
                                            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                                                <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-600 h-8 w-8" />
                                            </div>
                                            <h3 className="text-xl font-semibold text-gray-800 mb-3">
                                                Add Property to Portfolio to see Unit Details
                                            </h3>
                                            <p className="text-gray-600 mb-6 max-w-md">
                                                To view and edit detailed unit information, photos, tags, and market data, 
                                                save this property to one of your portfolios.
                                            </p>
                                            <div className="flex items-center gap-2 text-sm text-indigo-600 bg-indigo-100 px-4 py-2 rounded-lg">
                                                <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4" />
                                                <span>Look for the "Save to Portfolio" button above</span>
                                            </div>
                                        </div>
                                    </div>
                                ) : unitValue ? (
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between h-10 mb-4">
                                            <h3 className="text-s font-medium text-gray-800 flex items-center gap-2">
                                                {(() => {
                                                    const getAddressComponents = () => {
                                                        if (typeof searchPlace === 'object' && searchPlace?.address_components) {
                                                            const streetNumber = searchPlace.address_components.find((component: any) => component.types.includes('street_number'))?.long_name;
                                                            const route = searchPlace.address_components.find((component: any) => component.types.includes('route'))?.long_name;
                                                            const locality = searchPlace.address_components.find((component: any) => component.types.includes('locality'))?.short_name;
                                                            const administrativeArea = searchPlace.address_components.find((component: any) => component.types.includes('administrative_area_level_1'))?.short_name;
                                                            const postalCode = searchPlace.address_components.find((component: any) => component.types.includes('postal_code'))?.short_name;

                                                            const streetAddress = [streetNumber, route].filter(Boolean).join(' ');
                                                            return [
                                                                streetAddress,
                                                                locality,
                                                                administrativeArea,
                                                                postalCode
                                                            ].filter(Boolean);
                                                        } else if (typeof searchPlace === 'string') {
                                                            return [searchPlace];
                                                        } else if (marketAddress) {
                                                            return [marketAddress];
                                                        }
                                                        return [];
                                                    };

                                                    const addressComponents = getAddressComponents();

                                                    return (
                                                        <>  
                                                            <span className="ml-2 px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full group relative cursor-pointer hover:bg-indigo-200 transition-colors"
                                                                  onMouseEnter={() => {/* hover logic handled by CSS */}}
                                                            >
                                                                {isEditingUnitName ? (
                                                                    <div className="flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
                                                                        <span>Unit </span>
                                                                        <input
                                                                            type="text"
                                                                            value={editingUnitName}
                                                                            onChange={(e) => setEditingUnitName(e.target.value)}
                                                                            onKeyDown={handleUnitNameKeyDown}
                                                                            className="w-16 bg-white border border-indigo-300 rounded px-1 py-0.5 text-indigo-700 font-medium text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                                                            autoFocus
                                                                        />
                                                                        <div className="flex items-center gap-0.5 ml-1">
                                                                            <button
                                                                                onClick={handleSaveUnitName}
                                                                                className="w-4 h-4 bg-green-100 hover:bg-green-200 text-green-600 rounded flex items-center justify-center transition-colors"
                                                                                title="Save"
                                                                            >
                                                                                <FontAwesomeIcon icon={faCheck} className="h-2 w-2" />
                                                                            </button>
                                                                            <button
                                                                                onClick={handleCancelEditUnitName}
                                                                                className="w-4 h-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded flex items-center justify-center transition-colors"
                                                                                title="Cancel"
                                                                            >
                                                                                <FontAwesomeIcon icon={faXmark} className="h-2 w-2" />
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                ) : (
                                                                    <div className="flex items-center gap-1">
                                                                        <span>Unit {unitValue.unit}</span>
                                                                        <button
                                                                            onClick={handleEditUnitName}
                                                                            className="w-3 h-3 bg-indigo-200 hover:bg-indigo-300 text-indigo-700 rounded flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100"
                                                                            title="Edit unit name"
                                                                        >
                                                                            <FontAwesomeIcon icon={faEdit} className="h-2 w-2" />
                                                                        </button>
                                                                    </div>
                                                                )}
                                                            </span>
                                                        </>
                                                    );
                                                })()}
                                            </h3>
                                            <button 
                                                onClick={handleUploadDocuments}
                                                className="cursor-pointer flex items-center px-3 py-2 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors"
                                            >
                                                <FontAwesomeIcon icon={faFilePdf} className="mr-2 h-4 w-4" />
                                                Upload Documents
                                            </button>
                                        </div>
                                        {/* Desktop Layout (1200px+) */}
                                        <div className="hidden xl:flex flex-col lg:flex-row gap-4 p-4 border border-gray-200 shadow-sm rounded-xl min-h-[545px]">
                                            <div className="flex-1 min-w-0">
                                                <div className="flex flex-col xl:flex-row gap-4 h-full">
                                                    <div className="flex flex-col gap-4 xl:w-[60%]">
                                                        <div className="flex-1 min-h-[300px] lg:min-h-[400px]">
                                                            <PropertyUnitsImages 
                                                                unitValue={unitValue} 
                                                                setUnitValue={setUnitValue} 
                                                                handleUpdateUnit={handleUpdateUnit}
                                                                address={(() => {
                                                                    if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                                                                        return searchPlace.formatted_address;
                                                                    } else if (typeof searchPlace === 'string') {
                                                                        return searchPlace;
                                                                    } else if (marketAddress) {
                                                                        return marketAddress;
                                                                    }
                                                                    return '';
                                                                })()}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="flex flex-col gap-4 xl:w-[40%] min-w-[150px]">
                                                        <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm" style={{ height: '260px' }}>
                                                            <PropertyUnitsInfo unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                        </div>
                                                        <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm flex-1 max-h-[240px] overflow-y-auto">
                                                            <PropertyUnitsTags unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex flex-col w-full max-w-[350px] min-w-[280px] xl:max-w-[350px] bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200" style={{ height: '513px' }}>
                                                {/* Header */}
                                                <div className="px-6 py-4 border-b border-gray-50">
                                                    <div className="flex items-center justify-between">
                                                        <div>
                                                            <h3 className="text-lg font-semibold text-gray-900">Market Data</h3>
                                                            <p className="text-xs text-gray-500 mt-1">Rental & sales history</p>
                                                        </div>
                                                        {marketStats && (
                                                            <div className="text-right">
                                                                <div className="text-xs font-semibold text-indigo-600">{marketStats.totalCount} items</div>
                                                                <div className="text-xs text-gray-500">{marketStats.rentalCount} rentals</div>
                                                            </div>
                                                        )}
                                                    </div>

                                                    {/* Market Statistics */}
                                                    {marketStats && marketStats.recentRentalsCount > 0 && (
                                                        <div className="mt-4 p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                                                            <div className="flex items-center gap-2 mb-2">
                                                                <FontAwesomeIcon icon={faChartSimple} className="h-3 w-3 text-indigo-600" />
                                                                <span className="text-xs font-semibold text-indigo-700">3-Year Rental Stats</span>
                                                            </div>
                                                            <div className="text-sm text-indigo-800 font-semibold mb-2">
                                                                Avg: ${marketStats.avgRental.toLocaleString()}
                                                            </div>
                                                            
                                                            {/* Price Distribution Mini Chart */}
                                                            <div className="space-y-1">
                                                                {marketStats.priceRanges.filter(range => range.count > 0).map((range, index) => (
                                                                    <div key={index} className="flex items-center justify-between text-xs">
                                                                        <span className="text-indigo-600 font-medium w-14">{range.range}</span>
                                                                        <div className="flex-1 mx-2 bg-indigo-100 rounded-full h-1.5 overflow-hidden">
                                                                            <div 
                                                                                className="h-full bg-indigo-400 rounded-full transition-all duration-300"
                                                                                style={{ 
                                                                                    width: `${(range.count / Math.max(...marketStats.priceRanges.map(r => r.count))) * 100}%` 
                                                                                }}
                                                                            />
                                                                        </div>
                                                                        <span className="text-indigo-700 font-semibold w-4 text-right">{range.count}</span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Content */}
                                                <div className="flex-1 overflow-y-auto max-h-[400px] p-4">
                                                    {unitValue?.prop_market_data?.length > 0 ? (
                                                        <div className="space-y-3">
                                                            {unitValue.prop_market_data.map((marketData: any) => (
                                                                <div key={marketData?.id} className="space-y-3">
                                                                    {marketData?.data?.map((item: any, index: number) => {
                                                                        const itemKey = `${marketData?.id}-${index}`;
                                                                        const isExpanded = expandedItems[itemKey];
                                                                        
                                                                        return (
                                                                            <div key={index} className="bg-white rounded-xl p-4 border border-gray-200 hover:border-gray-300 transition-colors shadow-sm">
                                                                                {/* Collapsed View */}
                                                                                <div className="flex items-center justify-between">
                                                                                    <div 
                                                                                        className="flex items-center gap-3 cursor-pointer flex-1"
                                                                                        onClick={() => toggleItemExpansion(itemKey)}
                                                                                    >
                                                                                        <div className="text-sm font-semibold text-gray-800">
                                                                                            ${item.list_price?.toLocaleString() || 'N/A'}
                                                                                        </div>
                                                                                        <div className="text-xs text-gray-500">
                                                                                            {item.list_date ? new Date(item.list_date).toLocaleDateString('en-US', { 
                                                                                                month: 'short', 
                                                                                                day: 'numeric',
                                                                                                year: 'numeric'
                                                                                            }) : 'No date'}
                                                                                        </div>
                                                                                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getListingTypeBadgeClasses(item.listing_type || 'unknown')}`}>
                                                                                            {getListingTypeDisplayName(item.listing_type || 'unknown')}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div className="flex items-center gap-2">
                                                                                        <button
                                                                                            onClick={(e) => {
                                                                                                e.stopPropagation();
                                                                                                handleDeleteMarketDataItem(marketData?.id, index);
                                                                                            }}
                                                                                            className="w-7 h-7 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg flex items-center justify-center transition-colors"
                                                                                            title="Delete item"
                                                                                        >
                                                                                            <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                                                                                        </button>
                                                                                        <button
                                                                                            onClick={() => toggleItemExpansion(itemKey)}
                                                                                            className="w-7 h-7 text-gray-400 hover:text-gray-600 transition-colors flex items-center justify-center"
                                                                                        >
                                                                                            <FontAwesomeIcon 
                                                                                                icon={isExpanded ? faChevronUp : faChevronDown} 
                                                                                                className="h-4 w-4" 
                                                                                            />
                                                                                        </button>
                                                                                    </div>
                                                                                </div>

                                                                                {/* Expanded View - Edit Form */}
                                                                                {isExpanded && (
                                                                                    <div className="mt-4 pt-4 border-t border-gray-100">
                                                                                        <PropertyUnitsMarketItem 
                                                                                            indexData={index} 
                                                                                            marketData={item} 
                                                                                            marketId={marketData?.id} 
                                                                                            unitValue={unitValue} 
                                                                                            setUnitValue={setUnitValue} 
                                                                                            addressId={addressId as string} 
                                                                                        />
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        )
                                                                    })}
                                                                </div>
                                                            ))}

                                                        </div>
                                                    ) : (
                                                        <div className="flex flex-col items-center justify-center py-8 text-center h-full">
                                                            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4 shadow-sm">
                                                                <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-7 w-7" />
                                                            </div>
                                                            <h4 className="text-sm font-semibold text-gray-800 mb-2">No Market Data</h4>
                                                            <p className="text-xs text-gray-500 mb-6 leading-relaxed max-w-[200px]">
                                                                Add market data manually or let AI find rental history
                                                            </p>
                                                            <div className="flex gap-2">
                                                                <button 
                                                                    onClick={handleAgenticSearch}
                                                                    disabled={isSearchingAgentically}
                                                                    className="flex items-center gap-2 px-4 py-2.5 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-md hover:shadow-lg"
                                                                >
                                                                    {isSearchingAgentically ? (
                                                                        <>
                                                                            <Spinner size="sm" />
                                                                            <span>Searching...</span>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                                            <span>Search Now</span>
                                                                        </>
                                                                    )}
                                                                </button>
                                                                <button 
                                                                    onClick={handleAddUnitMarketData}
                                                                    className="flex items-center gap-2 px-4 py-2.5 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-all duration-200 text-sm font-medium shadow-md hover:shadow-lg"
                                                                >
                                                                    <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                                    <span>Add Manually</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Footer */}
                                                {unitValue?.prop_market_data?.length > 0 && (
                                                    <div className="px-6 py-4 border-t border-gray-50 bg-gray-50/50 rounded-b-2xl">
                                                        <div className="flex gap-2">
                                                            <button 
                                                                onClick={handleFindMoreData}
                                                                disabled={isSearchingMarketData}
                                                                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-purple-50 border border-purple-200 text-purple-700 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-sm"
                                                            >
                                                                {isSearchingMarketData ? (
                                                                    <>
                                                                        <Spinner size="sm" />
                                                                        <span>Searching...</span>
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                                        <span>Re-Fetch Data</span>
                                                                    </>
                                                                )}
                                                            </button>
                                                            <button 
                                                                onClick={handleAddUnitMarketData}
                                                                className="px-4 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                                                                title="Add market data manually"
                                                            >
                                                                <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                            </button>
                                                            <button 
                                                                onClick={handleClearAllMarketData}
                                                                disabled={isSearchingMarketData}
                                                                className="px-4 py-3 bg-red-50 border border-red-200 text-red-700 rounded-xl hover:bg-red-100 hover:border-red-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-sm"
                                                                title="Clear all market data"
                                                            >
                                                                <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                                                            </button>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Tablet/Small Desktop Layout (768px - 1199px) - Tabbed */}
                                        <div className="hidden md:block xl:hidden">
                                            {/* Tab Navigation */}
                                            <div className="flex border-b border-gray-200 mb-4">
                                                <button
                                                    onClick={() => setActiveResponsiveTab('images')}
                                                    className={`px-6 py-3 text-sm font-medium transition-colors relative ${
                                                        activeResponsiveTab === 'images'
                                                            ? 'text-indigo-600 border-b-2 border-indigo-500 -mb-[1px]'
                                                            : 'text-gray-500 hover:text-gray-700'
                                                    }`}
                                                >
                                                    Images & Condition
                                                    {activeResponsiveTab === 'images' && (
                                                        <span className="absolute bottom-0 left-0 w-full h-[2px] bg-indigo-500"></span>
                                                    )}
                                                </button>
                                                <button
                                                    onClick={() => setActiveResponsiveTab('info')}
                                                    className={`px-6 py-3 text-sm font-medium transition-colors relative ${
                                                        activeResponsiveTab === 'info'
                                                            ? 'text-indigo-600 border-b-2 border-indigo-500 -mb-[1px]'
                                                            : 'text-gray-500 hover:text-gray-700'
                                                    }`}
                                                >
                                                    Info & Market Data
                                                    {activeResponsiveTab === 'info' && (
                                                        <span className="absolute bottom-0 left-0 w-full h-[2px] bg-indigo-500"></span>
                                                    )}
                                                </button>
                                            </div>

                                            {/* Tab Content */}
                                            <div className="p-4 border border-gray-200 shadow-sm rounded-xl min-h-[545px]">
                                                {activeResponsiveTab === 'images' && (
                                                    <div className="h-full">
                                                        <PropertyUnitsImages 
                                                            unitValue={unitValue} 
                                                            setUnitValue={setUnitValue} 
                                                            handleUpdateUnit={handleUpdateUnit}
                                                            address={(() => {
                                                                if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                                                                    return searchPlace.formatted_address;
                                                                } else if (typeof searchPlace === 'string') {
                                                                    return searchPlace;
                                                                } else if (marketAddress) {
                                                                    return marketAddress;
                                                                }
                                                                return '';
                                                            })()}
                                                        />
                                                    </div>
                                                )}
                                                
                                                {activeResponsiveTab === 'info' && (
                                                    <div className="flex gap-4 h-full">
                                                        <div className="flex flex-col gap-4 flex-1">
                                                            <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm" style={{ height: '260px' }}>
                                                                <PropertyUnitsInfo unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                            </div>
                                                            <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm flex-1 max-h-[240px] overflow-y-auto">
                                                                <PropertyUnitsTags unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                            </div>
                                                        </div>
                                                        <div className="flex flex-col w-full max-w-[350px] min-w-[280px] bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200" style={{ height: '513px' }}>
                                                            {/* Market Data Header */}
                                                            <div className="px-6 py-4 border-b border-gray-50">
                                                                <div className="flex items-center justify-between">
                                                                    <div>
                                                                        <h3 className="text-lg font-semibold text-gray-900">Market Data</h3>
                                                                        <p className="text-xs text-gray-500 mt-1">Rental & sales history</p>
                                                                    </div>
                                                                    {marketStats && (
                                                                        <div className="text-right">
                                                                            <div className="text-xs font-semibold text-indigo-600">{marketStats.totalCount} items</div>
                                                                            <div className="text-xs text-gray-500">{marketStats.rentalCount} rentals</div>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {/* Market Data Content */}
                                                            <div className="flex-1 overflow-y-auto max-h-[400px] p-4">
                                                                {unitValue?.prop_market_data?.length > 0 ? (
                                                                    <div className="space-y-3">
                                                                        {unitValue.prop_market_data.map((marketData: any) => (
                                                                            <div key={marketData?.id} className="space-y-3">
                                                                                {marketData?.data?.map((item: any, index: number) => {
                                                                                    const itemKey = `${marketData?.id}-${index}`;
                                                                                    const isExpanded = expandedItems[itemKey];
                                                                                    
                                                                                    return (
                                                                                        <div key={index} className="bg-white rounded-xl p-4 border border-gray-200 hover:border-gray-300 transition-colors shadow-sm">
                                                                                            <div className="flex items-center justify-between">
                                                                                                <div className="flex items-center gap-3 cursor-pointer flex-1" onClick={() => toggleItemExpansion(itemKey)}>
                                                                                                    <div className="text-sm font-semibold text-gray-800">
                                                                                                        ${item.list_price?.toLocaleString() || 'N/A'}
                                                                                                    </div>
                                                                                                    <div className="text-xs text-gray-500">
                                                                                                        {item.list_date ? new Date(item.list_date).toLocaleDateString('en-US', { 
                                                                                                            month: 'short', 
                                                                                                            day: 'numeric',
                                                                                                            year: 'numeric'
                                                                                                        }) : 'No date'}
                                                                                                    </div>
                                                                                                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getListingTypeBadgeClasses(item.listing_type || 'unknown')}`}>
                                                                                                        {getListingTypeDisplayName(item.listing_type || 'unknown')}
                                                                                                    </span>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                })}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                ) : (
                                                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                                                        <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4 shadow-sm">
                                                                            <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-7 w-7" />
                                                                        </div>
                                                                        <h4 className="text-sm font-semibold text-gray-800 mb-2">No Market Data</h4>
                                                                        <p className="text-xs text-gray-500 mb-6 leading-relaxed max-w-[200px]">
                                                                            Add market data manually or let AI find rental history
                                                                        </p>
                                                                        <div className="flex gap-2">
                                                                            <button 
                                                                                onClick={handleAgenticSearch}
                                                                                disabled={isSearchingAgentically}
                                                                                className="flex items-center gap-2 px-4 py-2.5 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-md hover:shadow-lg"
                                                                            >
                                                                                {isSearchingAgentically ? (
                                                                                    <>
                                                                                        <Spinner size="sm" />
                                                                                        <span>Searching...</span>
                                                                                    </>
                                                                                ) : (
                                                                                    <>
                                                                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                                                        <span>Search Now</span>
                                                                                    </>
                                                                                )}
                                                                            </button>
                                                                            <button 
                                                                                onClick={handleAddUnitMarketData}
                                                                                className="flex items-center gap-2 px-4 py-2.5 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-all duration-200 text-sm font-medium shadow-md hover:shadow-lg"
                                                                            >
                                                                                <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                                                <span>Add Manually</span>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Mobile Layout (< 768px) - Single Column */}
                                        <div className="block md:hidden">
                                            <div className="space-y-4 p-4 border border-gray-200 shadow-sm rounded-xl">
                                                {/* Images Gallery */}
                                                <div className="min-h-[300px]">
                                                    <PropertyUnitsImages 
                                                        unitValue={unitValue} 
                                                        setUnitValue={setUnitValue} 
                                                        handleUpdateUnit={handleUpdateUnit}
                                                        address={(() => {
                                                            if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                                                                return searchPlace.formatted_address;
                                                            } else if (typeof searchPlace === 'string') {
                                                                return searchPlace;
                                                            } else if (marketAddress) {
                                                                return marketAddress;
                                                            }
                                                            return '';
                                                        })()}
                                                    />
                                                </div>

                                                {/* Unit Info */}
                                                <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm">
                                                    <PropertyUnitsInfo unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                </div>

                                                {/* Unit Tags & Amenities */}
                                                <div className="p-4 rounded-xl border border-gray-200 bg-white shadow-sm">
                                                    <PropertyUnitsTags unitValue={unitValue} setUnitValue={setUnitValue} handleUpdateUnit={handleUpdateUnit} />
                                                </div>

                                                {/* Market Data */}
                                                <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
                                                    <div className="px-6 py-4 border-b border-gray-50">
                                                        <div className="flex items-center justify-between">
                                                            <div>
                                                                <h3 className="text-lg font-semibold text-gray-900">Market Data</h3>
                                                                <p className="text-xs text-gray-500 mt-1">Rental & sales history</p>
                                                            </div>
                                                            {marketStats && (
                                                                <div className="text-right">
                                                                    <div className="text-xs font-semibold text-indigo-600">{marketStats.totalCount} items</div>
                                                                    <div className="text-xs text-gray-500">{marketStats.rentalCount} rentals</div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="p-4 max-h-[400px] overflow-y-auto">
                                                        {unitValue?.prop_market_data?.length > 0 ? (
                                                            <div className="space-y-3">
                                                                {unitValue.prop_market_data.map((marketData: any) => (
                                                                    <div key={marketData?.id} className="space-y-3">
                                                                        {marketData?.data?.map((item: any, index: number) => (
                                                                            <div key={index} className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                                                                                <div className="flex flex-col gap-2">
                                                                                    <div className="flex items-center justify-between">
                                                                                        <div className="text-sm font-semibold text-gray-800">
                                                                                            ${item.list_price?.toLocaleString() || 'N/A'}
                                                                                        </div>
                                                                                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getListingTypeBadgeClasses(item.listing_type || 'unknown')}`}>
                                                                                            {getListingTypeDisplayName(item.listing_type || 'unknown')}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div className="text-xs text-gray-500">
                                                                                        {item.list_date ? new Date(item.list_date).toLocaleDateString('en-US', { 
                                                                                            month: 'short', 
                                                                                            day: 'numeric',
                                                                                            year: 'numeric'
                                                                                        }) : 'No date'}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            <div className="flex flex-col items-center justify-center py-8 text-center">
                                                                <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-4 shadow-sm">
                                                                    <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 h-7 w-7" />
                                                                </div>
                                                                <h4 className="text-sm font-semibold text-gray-800 mb-2">No Market Data</h4>
                                                                <p className="text-xs text-gray-500 mb-6 leading-relaxed max-w-[200px]">
                                                                    Add market data manually or let AI find rental history
                                                                </p>
                                                                <div className="flex gap-2">
                                                                    <button 
                                                                        onClick={handleAgenticSearch}
                                                                        disabled={isSearchingAgentically}
                                                                        className="flex items-center gap-2 px-4 py-2.5 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-md hover:shadow-lg"
                                                                    >
                                                                        {isSearchingAgentically ? (
                                                                            <>
                                                                                <Spinner size="sm" />
                                                                                <span>Searching...</span>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                                                <span>Search Now</span>
                                                                            </>
                                                                        )}
                                                                    </button>
                                                                    <button 
                                                                        onClick={handleAddUnitMarketData}
                                                                        className="flex items-center gap-2 px-4 py-2.5 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-all duration-200 text-sm font-medium shadow-md hover:shadow-lg"
                                                                    >
                                                                        <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                                        <span>Add Manually</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>

                                                    {/* Mobile Market Data Footer */}
                                                    {unitValue?.prop_market_data?.length > 0 && (
                                                        <div className="px-6 py-4 border-t border-gray-50 bg-gray-50/50 rounded-b-2xl">
                                                            <div className="flex gap-2">
                                                                <button 
                                                                    onClick={handleFindMoreData}
                                                                    disabled={isSearchingMarketData}
                                                                    className="flex-1 flex items-center justify-center gap-2 px-3 py-3 bg-purple-50 border border-purple-200 text-purple-700 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-sm"
                                                                >
                                                                    {isSearchingMarketData ? (
                                                                        <>
                                                                            <Spinner size="sm" />
                                                                            <span>Searching...</span>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                                            <span>Re-Fetch</span>
                                                                        </>
                                                                    )}
                                                                </button>
                                                                <button 
                                                                    className="flex items-center justify-center gap-2 px-3 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md" 
                                                                    onClick={handleAddUnitMarketData}
                                                                >
                                                                    <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                                    Add
                                                                </button>
                                                                <button 
                                                                    onClick={handleClearAllMarketData}
                                                                    disabled={isSearchingMarketData}
                                                                    className="px-3 py-3 bg-red-50 border border-red-200 text-red-700 rounded-xl hover:bg-red-100 hover:border-red-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium shadow-sm"
                                                                    title="Clear all market data"
                                                                >
                                                                    <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex-1">
                                        <div className="p-4 rounded-xl border border-gray-200 bg-grey shadow-md h-[500px] mt-17">
                                            <div className="flex flex-col gap-2 items-center justify-center h-full">
                                                <p className="text-sm text-gray-500">Select a unit to view data</p>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                        </div>
                    )
                }

                {/* Delete Confirmation Modal */}
                {showDeleteModal && (
                    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 h-5 w-5" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-800">Delete Unit</h3>
                                    <p className="text-sm text-gray-500">This action cannot be undone</p>
                                </div>
                            </div>
                            
                            <p className="text-sm text-gray-600 mb-6">
                                Are you sure you want to delete Unit <strong>{unitToDelete?.unit}</strong>? 
                                This will permanently remove all unit data, photos, tags, and market information.
                            </p>
                            
                            <div className="flex justify-end gap-3">
                                <button
                                    onClick={cancelDeleteUnit}
                                    disabled={isDeleting}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={confirmDeleteUnit}
                                    disabled={isDeleting}
                                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                                >
                                    {isDeleting && <Spinner size="sm" />}
                                    {isDeleting ? 'Deleting...' : 'Delete Unit'}
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Agentic Search Results Modal */}
                {showAgenticResults && agenticResults && (
                    <ModalBase
                        isOpen={showAgenticResults}
                        onClose={handleRejectAgenticData}
                        title="Market Data Search Results"
                        subtitle="AI found rental & sales data for this property"
                        icon={faMagicWandSparkles}
                        iconBgColor="bg-purple-100"
                        iconColor="text-purple-600"
                        maxWidth="max-w-3xl"
                        actions={
                            <div className="flex justify-end gap-3">
                                <button
                                    onClick={handleRejectAgenticData}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleAcceptAgenticData}
                                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors flex items-center gap-2"
                                >
                                    <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4" />
                                    Accept & Save Data ({agenticResults.data?.length || 0} items)
                                </button>
                            </div>
                        }
                    >

                            {/* Search Query */}
                            <div className="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 className="text-sm font-semibold text-gray-700 mb-2">Search Query Used:</h4>
                                <p className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
                                    {agenticResults.searchQuery}
                                </p>
                            </div>

                            {/* Found Data */}
                            <div className="mb-6">
                                <h4 className="text-lg font-semibold text-gray-800 mb-4">Found Market Data:</h4>
                                <div className="space-y-4">
                                                                                                    {agenticResults.data?.map((item: any, index: number) => (
                                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Date:</span>
                                                <p className="text-sm text-gray-800">{item.date || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Price:</span>
                                                <p className="text-sm text-gray-800 font-semibold">${item.price?.toLocaleString() || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Type:</span>
                                                <p className="text-sm text-gray-800">{item.listingType || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Confidence:</span>
                                                <p className="text-sm text-gray-800">{item.confidence || 'Medium'}%</p>
                                            </div>
                                        </div>
                                        {item.description && (
                                            <div className="mt-3">
                                                <span className="text-sm font-medium text-gray-600">Details:</span>
                                                <p className="text-sm text-gray-700 mt-1">{item.description}</p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>

                            {/* Source Information */}
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <h4 className="text-sm font-semibold text-yellow-800 mb-2">🔍 Source Information:</h4>
                                <div className="space-y-2">
                                    {agenticResults.sources?.map((source: string, index: number) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <span className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></span>
                                            <a 
                                                href={source} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="text-sm text-yellow-700 hover:text-yellow-900 underline break-all"
                                            >
                                                {source}
                                            </a>
                                        </div>
                                    ))}
                                </div>
                                <p className="text-xs text-yellow-600 mt-3">
                                    ⚠️ Please verify this information independently. AI-generated data may contain inaccuracies.
                                </p>
                            </div>


                        </div>
                    </ModalBase>
                )}

                {/* AI Calculation Confirmation Modal */}
                {showConfirmationModal && (
                    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-amber-600 h-6 w-6" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-800">Confirm AI Calculation</h3>
                                    <p className="text-sm text-gray-500">This will process all units automatically</p>
                                </div>
                            </div>

                            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
                                <h4 className="text-sm font-semibold text-amber-800 mb-3">⚠️ Important Warning</h4>
                                <ul className="text-sm text-amber-700 space-y-2">
                                    <li>• <strong>Existing data may be overridden</strong> with AI-found information</li>
                                    <li>• <strong>Market data might be duplicated</strong> if you've run AI calculation before</li>
                                    <li>• <strong>Unit specifications</strong> (beds, baths, sqft, rent, price) will be updated</li>
                                    <li>• <strong>New market data</strong> will be added to existing records</li>
                                    <li>• <strong>Images will be analyzed</strong> and tagged automatically</li>
                                </ul>
                            </div>

                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <h4 className="text-sm font-semibold text-blue-800 mb-2">🤖 What AI Will Do:</h4>
                                <div className="text-sm text-blue-700 space-y-1">
                                    <div>1. Search for unit specifications online</div>
                                    <div>2. Analyze existing property images</div>
                                    <div>3. Find rental & sales history data</div>
                                    <div>4. Update unit information automatically</div>
                                </div>
                            </div>

                            <div className="flex justify-end gap-3">
                                <button
                                    onClick={handleCancelConfirmation}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleConfirmAICalculation}
                                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors flex items-center gap-2"
                                >
                                    <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                    Yes, Start AI Calculation
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* AI Calculation Progress Modal */}
                {showProgressModal && isCalculatingWithAI && aiCalculationProgress && (
                    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full">
                            <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center gap-3">
                                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="text-purple-600 h-6 w-6" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-800">AI Calculation Started</h3>
                                        <p className="text-sm text-gray-500">Processing all units automatically</p>
                                    </div>
                                </div>
                                <button
                                    onClick={handleCancelAICalculation}
                                    className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
                                >
                                    <FontAwesomeIcon icon={faXmark} className="text-gray-600 h-4 w-4" />
                                </button>
                            </div>

                            {/* Progress Bar */}
                            <div className="mb-4">
                                <div className="flex justify-between text-sm text-gray-600 mb-2">
                                    <span>Progress</span>
                                    <span>{aiCalculationProgress.currentUnit} of {aiCalculationProgress.totalUnits}</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                    <div 
                                        className="h-full bg-purple-600 transition-all duration-300 ease-out"
                                        style={{ 
                                            width: `${(aiCalculationProgress.currentUnit / aiCalculationProgress.totalUnits) * 100}%` 
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Current Status */}
                            <div className="bg-purple-50 rounded-lg p-4 border border-purple-200 mb-4">
                                <div className="flex items-center gap-2 mb-2">
                                    <Spinner size="sm" />
                                    <span className="text-sm font-medium text-purple-700">
                                        {aiCalculationProgress.unitName}
                                    </span>
                                </div>
                                <p className="text-sm text-purple-600">
                                    {aiCalculationProgress.currentTask}
                                </p>
                            </div>

                            {/* Background Notice */}
                            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <p className="text-sm text-blue-700 mb-3">
                                    🚀 This job will continue in the background. You can:
                                </p>
                                <ul className="text-xs text-blue-600 space-y-1 mb-3">
                                    <li>• Close this modal and keep working</li>
                                    <li>• Refresh the page - job will continue</li>
                                    <li>• Check progress in the bottom-left status</li>
                                </ul>
                                <button
                                    onClick={() => setShowProgressModal(false)}
                                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                                >
                                    Continue in Background
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Bottom-left Status Popup */}
                {showStatusPopup && isCalculatingWithAI && aiCalculationProgress && (
                    <div className="fixed bottom-20 left-4 z-[60] bg-white rounded-xl shadow-2xl border border-gray-200 p-4 max-w-sm w-full">
                        <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <FontAwesomeIcon icon={faMagicWandSparkles} className="text-purple-600 h-4 w-4" />
                                </div>
                                <div>
                                    <h4 className="text-sm font-semibold text-gray-800">AI Calculation</h4>
                                    <p className="text-xs text-gray-500">Running in background</p>
                                </div>
                            </div>
                            <div className="flex items-center gap-1">
                                <button
                                    onClick={handleOpenProgressModal}
                                    className="w-6 h-6 bg-gray-100 hover:bg-gray-200 rounded-md flex items-center justify-center transition-colors"
                                    title="View details"
                                >
                                    <FontAwesomeIcon icon={faInfoCircle} className="text-gray-600 h-3 w-3" />
                                </button>
                                <button
                                    onClick={handleCancelAICalculation}
                                    className="w-6 h-6 bg-red-100 hover:bg-red-200 rounded-md flex items-center justify-center transition-colors"
                                    title="Cancel job"
                                >
                                    <FontAwesomeIcon icon={faXmark} className="text-red-600 h-3 w-3" />
                                </button>
                            </div>
                        </div>

                        {/* Mini Progress Bar */}
                        <div className="mb-2">
                            <div className="flex justify-between text-xs text-gray-600 mb-1">
                                <span>{aiCalculationProgress.unitName}</span>
                                <span>{aiCalculationProgress.currentUnit}/{aiCalculationProgress.totalUnits}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
                                <div 
                                    className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 ease-out"
                                    style={{ 
                                        width: `${(aiCalculationProgress.currentUnit / aiCalculationProgress.totalUnits) * 100}%` 
                                    }}
                                />
                            </div>
                        </div>

                        <p className="text-xs text-gray-600">{aiCalculationProgress.currentTask}</p>
                    </div>
                )}

                {/* Unit Duplicate Cleanup Modal */}
                {showDuplicateCleanupModal && duplicateInfo.length > 0 && unitValue && (
                    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[80vh] overflow-y-auto">
                            <div className="p-6">
                                <div className="flex items-center gap-3 mb-6">
                                    <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-orange-600 h-6 w-6" />
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-semibold text-gray-800">Clean Up Unit Duplicates</h3>
                                        <p className="text-sm text-gray-500">Unit {unitValue.unit} - Remove duplicate items</p>
                                    </div>
                                </div>

                                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <p className="text-sm text-red-700 font-medium mb-2">⚠️ Warning: This action cannot be undone!</p>
                                    <p className="text-sm text-red-600">
                                        Duplicate items will be permanently removed from this unit.
                                    </p>
                                </div>

                                <div className="mb-6">
                                    <h4 className="text-sm font-semibold text-gray-800 mb-3">Duplicates Found:</h4>
                                    <div className="space-y-3">
                                        {duplicateInfo.map((info) => (
                                            <div key={info.type} className="p-3 bg-gray-50 rounded-lg border">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="text-sm font-medium text-gray-800 capitalize">
                                                        {info.type === 'amenity' ? 'Amenities' : 'Features'}
                                                    </span>
                                                    <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                                                        {info.count} duplicates
                                                    </span>
                                                </div>
                                                <div className="text-xs text-gray-600 space-y-1">
                                                    {info.items.map((item, idx) => (
                                                        <div key={idx} className="text-red-600">✗ Duplicate: {item}</div>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div className="flex gap-3 justify-end">
                                    <button
                                        onClick={() => setShowDuplicateCleanupModal(false)}
                                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                        disabled={isCleaningDuplicates}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleCleanupUnitDuplicates}
                                        disabled={isCleaningDuplicates}
                                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                                    >
                                        {isCleaningDuplicates ? (
                                            <>
                                                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                                Cleaning...
                                            </>
                                        ) : (
                                            <>
                                                <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                                                Remove Duplicates
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Market Data Results Modal */}
                {showMarketDataModal && marketDataModalResults && (
                    <ModalBase
                        isOpen={showMarketDataModal}
                        onClose={() => setShowMarketDataModal(false)}
                        title="Market Data Re-Fetched Successfully!"
                        icon={
                            marketDataModalResults.type === 'success' ? faCheckCircle :
                            marketDataModalResults.type === 'warning' ? faExclamationTriangle :
                            marketDataModalResults.type === 'error' ? faXmark :
                            faInfoCircle
                        }
                        iconBgColor={
                            marketDataModalResults.type === 'success' ? 'bg-green-100' :
                            marketDataModalResults.type === 'warning' ? 'bg-yellow-100' :
                            marketDataModalResults.type === 'error' ? 'bg-red-100' :
                            'bg-blue-100'
                        }
                        iconColor={
                            marketDataModalResults.type === 'success' ? 'text-green-600' :
                            marketDataModalResults.type === 'warning' ? 'text-yellow-600' :
                            marketDataModalResults.type === 'error' ? 'text-red-600' :
                            'text-blue-600'
                        }
                        subtitle={marketDataModalResults.message}
                        maxWidth="max-w-2xl"
                        actions={
                            <div className="flex justify-end">
                                <button
                                    onClick={() => setShowMarketDataModal(false)}
                                    className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                                >
                                    Close
                                </button>
                            </div>
                        }
                    >
                        <div>
                            {/* Show data details if available */}
                            {marketDataModalResults?.details && marketDataModalResults.details.length > 0 && (
                                <div className="mb-6">
                                    <h4 className="text-sm font-semibold text-gray-800 mb-4">Market Data Found:</h4>
                                    <div className="space-y-3 max-h-60 overflow-y-auto">
                                        {marketDataModalResults.details.slice(0, 5).map((item: any, index: number) => (
                                            <div key={index} className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                                                <div className="p-4">
                                                    <div className="grid grid-cols-2 gap-4 mb-3">
                                                        <div>
                                                            <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Price:</span>
                                                            <p className="text-lg font-bold text-gray-900 mt-1">
                                                                {item.price ? `$${item.price.toLocaleString()}` : 'Not specified'}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Date:</span>
                                                            <p className="text-sm text-gray-800 mt-1 font-medium">
                                                                {item.date || 'Not specified'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Status:</span>
                                                            <p className="text-sm text-gray-800 mt-1">
                                                                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                                                    item.lastEvent?.toLowerCase().includes('rent') ? 'bg-blue-100 text-blue-700' :
                                                                    item.lastEvent?.toLowerCase().includes('sale') || item.lastEvent?.toLowerCase().includes('sold') ? 'bg-green-100 text-green-700' :
                                                                    'bg-gray-100 text-gray-700'
                                                                }`}>
                                                                    {item.lastEvent || 'N/A'}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Source:</span>
                                                            <p className="text-sm text-gray-800 mt-1 font-mono text-xs">
                                                                {item.source || 'AI Search'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        {marketDataModalResults?.details && marketDataModalResults.details.length > 5 && (
                                            <div className="text-center text-sm text-gray-500 py-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                                                ... and {marketDataModalResults.details.length - 5} more items
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </ModalBase>
                )}

                {/* Manual Market Data Modal */}
                <ModalAddMarketData
                    isOpen={showManualMarketDataModal}
                    onClose={() => setShowManualMarketDataModal(false)}
                    onSubmit={handleManualMarketDataSubmit}
                    isLoading={isSubmittingManualData}
                />
            </div>
        </div>
    )
}

export default PropertyUnits;