import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faHome, faBed, faBath, faRulerCombined, faTrash, faDollarSign, faEye, faEdit, faCheck, faXmark } from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";
import { updateUnit } from "@/actions/propetyUnitsActions";

interface PropertyUnitSelectProps {
    unit: { [key: string]: any }
    handleSelectUnit: (unit: { [key: string]: any }) => void
    handleDeleteUnit?: (unit: { [key: string]: any }) => void
}

export default function PropertyUnitSelect({unit, handleSelectUnit, handleDeleteUnit}: PropertyUnitSelectProps) {
    const [isEditingName, setIsEditingName] = useState(false);
    const [editingName, setEditingName] = useState(unit?.unit || '');
    const [isHovering, setIsHovering] = useState(false);
    
    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('Unit clicked:', unit.unit);
        handleSelectUnit(unit);
    };

    const handleDeleteClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('Delete clicked for unit:', unit.unit);
        if (handleDeleteUnit) {
            handleDeleteUnit(unit);
        }
    };

    const handleEditClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsEditingName(true);
        setEditingName(unit?.unit || '');
    };

    const handleSaveName = async (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        
        if (editingName.trim() && editingName !== unit?.unit) {
            try {
                const updatedUnit = { ...unit, unit: editingName.trim() };
                await updateUnit(updatedUnit);
                // Update the local unit data
                unit.unit = editingName.trim();
            } catch (error) {
                console.error('Failed to update unit name:', error);
            }
        }
        
        setIsEditingName(false);
    };

    const handleCancelEdit = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsEditingName(false);
        setEditingName(unit?.unit || '');
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSaveName(e as any);
        } else if (e.key === 'Escape') {
            e.preventDefault();
            handleCancelEdit(e as any);
        }
    };

    return (
        <div 
            className="unit-card-simple group relative cursor-pointer" 
            onClick={handleClick}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
        >
            <div className="unit-card-simple-inner">
                {/* Main content */}
                <div className="flex items-center gap-2 px-2 py-1.5 h-full">
                    {/* Unit icon and number */}
                    <div className="flex items-center gap-2 flex-shrink-0 min-w-0">
                        <div className="w-7 h-7 bg-indigo-600 rounded-lg flex items-center justify-center group-hover:bg-indigo-700 transition-colors flex-shrink-0">
                            <FontAwesomeIcon icon={faHome} className="text-white h-2.5 w-2.5" />
                        </div>
                        <div className="min-w-0 flex-shrink-0 relative">
                            {isEditingName ? (
                                <div className="flex items-center gap-1">
                                    <div className="relative">
                                        <span className="text-xs font-semibold text-gray-800">Unit </span>
                                        <input
                                            type="text"
                                            value={editingName}
                                            onChange={(e) => setEditingName(e.target.value)}
                                            onKeyDown={handleKeyDown}
                                            className="w-16 text-xs font-semibold text-gray-800 bg-white border border-indigo-300 rounded px-1 py-0.5 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            autoFocus
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                    </div>
                                    <div className="flex items-center gap-0.5">
                                        <button
                                            onClick={handleSaveName}
                                            className="w-4 h-4 bg-green-100 hover:bg-green-200 text-green-600 rounded flex items-center justify-center transition-colors"
                                            title="Save"
                                        >
                                            <FontAwesomeIcon icon={faCheck} className="h-2 w-2" />
                                        </button>
                                        <button
                                            onClick={handleCancelEdit}
                                            className="w-4 h-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded flex items-center justify-center transition-colors"
                                            title="Cancel"
                                        >
                                            <FontAwesomeIcon icon={faXmark} className="h-2 w-2" />
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="flex items-center gap-1">
                                    <h3 className="text-xs font-semibold text-gray-800 truncate">
                                        Unit {unit?.unit || 'N/A'}
                                    </h3>
                                    {isHovering && (
                                        <button
                                            onClick={handleEditClick}
                                            className="w-3 h-3 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100"
                                            title="Edit unit name"
                                        >
                                            <FontAwesomeIcon icon={faEdit} className="h-2 w-2" />
                                        </button>
                                    )}
                                </div>
                            )}
                            <p className="text-xs text-gray-500">Property</p>
                        </div>
                    </div>

                    {/* Stats in 2x2 grid - minimal spacing */}
                    <div className="flex-1 min-w-0">
                        <div className="grid grid-cols-2 gap-x-3 gap-y-0.5">
                            {/* Row 1: Beds and Baths */}
                            <div className="flex items-center gap-1">
                                <FontAwesomeIcon icon={faBed} className="text-gray-400 h-2.5 w-2.5" />
                                <span className="text-xs text-gray-700">{unit?.beds || 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <FontAwesomeIcon icon={faBath} className="text-gray-400 h-2.5 w-2.5" />
                                <span className="text-xs text-gray-700">{unit?.baths || 0}</span>
                            </div>
                            
                            {/* Row 2: Sqft and Price */}
                            <div className="flex items-center gap-1">
                                <FontAwesomeIcon icon={faRulerCombined} className="text-gray-400 h-2.5 w-2.5" />
                                <span className="text-xs text-gray-700">{unit?.sqft || 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <FontAwesomeIcon icon={faDollarSign} className="text-gray-400 h-2.5 w-2.5" />
                                <span className="text-xs text-gray-700">
                                    {unit?.rent ? `${Math.round(unit.rent/1000)}k` : '0'}
                                </span>
                            </div>

                        </div>
                    </div>

                    {/* Delete button - show on hover, positioned on the right */}
                    {handleDeleteUnit && (
                        <div className="opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center">
                            <button
                                onClick={handleDeleteClick}
                                className="w-6 h-6 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg flex items-center justify-center transition-colors"
                                title="Delete unit"
                            >
                                <FontAwesomeIcon icon={faTrash} className="h-2.5 w-2.5" />
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}