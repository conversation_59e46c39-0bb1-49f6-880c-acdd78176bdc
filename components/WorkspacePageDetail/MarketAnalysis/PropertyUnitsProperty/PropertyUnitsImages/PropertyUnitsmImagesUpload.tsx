import { useState, useEffect } from "react"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faImage, faPlus, faTrash, faXmark, faCloudArrowUp, faSpinner, faCheck } from "@fortawesome/free-solid-svg-icons"
import { createClient } from "@/utils/supabase/client"
import { publicUrlToPath } from "@/utils/supabase/paths"


interface PropertyUnitsmImagesUploadProps {
    isOpen: boolean
    onClose: () => void
    onUpload: (imageUrls: string[]) => void
    unitId: string
}

export default function PropertyUnitsmImagesUpload({ isOpen, onClose, onUpload, unitId }: PropertyUnitsmImagesUploadProps) {
    const [files, setFiles] = useState<File[]>([])
    const [isDragging, setIsDragging] = useState(false)
    const [uploadStatus, setUploadStatus] = useState<{[key: number]: 'idle' | 'uploading' | 'success' | 'error'}>({})
    const [uploadErrors, setUploadErrors] = useState<{[key: number]: string}>({})


    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragging(true)
    }

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragging(false)
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragging(false)
        const droppedFiles = Array.from(e.dataTransfer.files).filter(file => 
            file.type.startsWith('image/')
        )
        setFiles(prev => [...prev, ...droppedFiles])
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            const selectedFiles = Array.from(e.target.files).filter(file => 
                file.type.startsWith('image/')
            )
            setFiles(prev => [...prev, ...selectedFiles])
        }
    }

    const removeFile = (index: number) => {
        setFiles(prev => prev.filter((_, i) => i !== index))
        setUploadStatus(prev => {
            const newStatus = { ...prev }
            delete newStatus[index]
            // Reindex remaining statuses
            const reindexed: {[key: number]: 'idle' | 'uploading' | 'success' | 'error'} = {}
            let newIndex = 0
            for (let i = 0; i < files.length - 1; i++) {
                if (i < index && prev[i]) {
                    reindexed[newIndex] = prev[i]
                } else if (i > index && prev[i]) {
                    reindexed[newIndex] = prev[i]
                }
                if (i !== index) newIndex++
            }
            return reindexed
        })
        setUploadErrors(prev => {
            const newErrors = { ...prev }
            delete newErrors[index]
            return newErrors
        })
    }

    const uploadFiles = async () => {
        if (!unitId) {
            console.error("Address ID or Unit ID is missing")
            return
        }

        const supabase = createClient()
        
        // Initialize all files as uploading
        const initialStatus: {[key: number]: 'uploading'} = {}
        files.forEach((_, index) => {
            initialStatus[index] = 'uploading'
        })
        setUploadStatus(initialStatus)
        setUploadErrors({})

        const uploadedUrls: string[] = []

        // Process each file individually
        for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
            const file = files[fileIndex]
            
            try {
                // Create filename with timestamp for uniqueness
                const timestamp = new Date().getTime()
                const originalName = file.name.split('.').slice(0, -1).join('.')
                const fileName = `${originalName.toLowerCase().replace(/ /g, '_')}_${timestamp}`
                const fileExtension = file.name.split('.').pop()
                
                // Create path: addressId/unitId/filename
                const fullPath = `${unitId}/${fileName}.${fileExtension}`

                const { data: fileData, error: fileError } = await supabase.storage
                    .from('propimages')
                    .upload(fullPath, file)

                if (fileError) {
                    console.error("Error uploading file:", fileError)
                    setUploadStatus(prev => ({
                        ...prev,
                        [fileIndex]: 'error'
                    }))
                    setUploadErrors(prev => ({
                        ...prev,
                        [fileIndex]: `Upload failed: ${fileError.message}`
                    }))
                    continue
                }

                const { data: { publicUrl } } = await supabase.storage
                    .from('propimages')
                    .getPublicUrl(fullPath)

                uploadedUrls.push(publicUrl)

                // Mark file as successful
                setUploadStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'success'
                }))

            } catch (error) {
                console.error("Error in upload process for file:", file.name, error)
                setUploadStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'error'
                }))
                setUploadErrors(prev => ({
                    ...prev,
                    [fileIndex]: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
                }))
            }
        }

        // Call onUpload with the uploaded URLs
        if (uploadedUrls.length > 0) {
            onUpload(uploadedUrls)
        }

        // Close modal after a short delay
        setTimeout(() => {
            onClose()
            setFiles([])
            setUploadStatus({})
            setUploadErrors({})
        }, 1000)
    }

    const isUploading = Object.values(uploadStatus).some(status => status === 'uploading')
    const isUploadDisabled = files.length === 0 || isUploading

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white w-[640px] rounded-2xl p-6 relative max-h-[90vh] overflow-y-auto">
                <FontAwesomeIcon
                    icon={faXmark}
                    className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors"
                    onClick={onClose}
                />

                <h2 className="text-xl font-semibold mb-4">Upload Unit Images</h2>

                {files.length === 0 && (
                    <div
                        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors flex flex-col items-center justify-center h-[340px] ${
                            isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"
                        }`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <FontAwesomeIcon
                            icon={faCloudArrowUp}
                            className="h-10 w-10 text-gray-400 mb-2"
                        />
                        <p className="mb-2">Drag and drop image files here or</p>
                        <label className="bg-blue-500 text-white px-4 py-2 rounded cursor-pointer hover:bg-blue-600 transition-colors">
                            Browse Images
                            <input
                                type="file"
                                className="hidden"
                                onChange={handleFileChange}
                                accept="image/*"
                                multiple={true}
                            />
                        </label>
                    </div>
                )}

                {files.length > 0 && (
                    <div className="h-[340px] flex flex-col">
                        <h3 className="font-medium mb-2">Selected Images:</h3>
                        <ul className="flex-1 overflow-y-auto">
                            {files.map((file, index) => (
                                <li key={index} className="flex justify-between items-center py-3 border border-gray-300 rounded-lg p-2 mb-2">
                                    <div className="flex items-center gap-3">
                                        <div className="w-12 h-12 bg-gray-100 rounded overflow-hidden">
                                            <img 
                                                src={URL.createObjectURL(file)} 
                                                alt={file.name}
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                        <span className="truncate">{file.name}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {uploadStatus[index] === 'uploading' ? (
                                            <FontAwesomeIcon 
                                                icon={faSpinner} 
                                                className="h-4 w-4 text-blue-500 animate-spin" 
                                            />
                                        ) : uploadStatus[index] === 'success' ? (
                                            <FontAwesomeIcon 
                                                icon={faCheck} 
                                                className="h-4 w-4 text-green-500" 
                                            />
                                        ) : uploadStatus[index] === 'error' ? (
                                            <FontAwesomeIcon 
                                                icon={faXmark} 
                                                className="h-4 w-4 text-red-500" 
                                            />
                                        ) : (
                                            <button
                                                onClick={() => removeFile(index)}
                                                className="text-red-500 hover:text-red-700"
                                            >
                                                <FontAwesomeIcon icon={faXmark} className="h-4 w-4" />
                                            </button>
                                        )}
                                    </div>
                                    {uploadErrors[index] && (
                                        <div className="mt-2 text-red-500 text-sm">
                                            {uploadErrors[index]}
                                        </div>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}

                {files.length > 0 && (
                    <div className="flex items-center justify-end mt-3">
                        <button 
                            className={`px-4 py-2 rounded transition-colors flex items-center gap-2 ${
                                isUploadDisabled 
                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                                    : 'bg-blue-500 text-white cursor-pointer hover:bg-blue-600'
                            }`}
                            disabled={isUploadDisabled}
                            onClick={uploadFiles}
                        >
                            {isUploading && (
                                <FontAwesomeIcon 
                                    icon={faSpinner} 
                                    className="h-4 w-4 animate-spin" 
                                />
                            )}
                            {isUploading ? 'Uploading...' : 'Upload Images'}
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
}