import { useState, useEffect, useRef } from "react"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faImage, faPlus, faTrash, faXmark, faCloudArrowUp, faSpinner, faCheck, faChevronLeft, faChevronRight, faMagicWandSparkles, faRobot, faExclamationTriangle, faClipboardCheck, faChevronDown, faChevronUp, faInfoCircle, faExternalLinkAlt } from "@fortawesome/free-solid-svg-icons"
import { createClient } from "@/utils/supabase/client"
import { publicUrlToPath } from "@/utils/supabase/paths"
import { useStatus } from '@/context/StatusContext'

import PropertyUnitsmImagesUpload from "./PropertyUnitsmImagesUpload"
import Fancybox from "@/helpers/fancyBox/fancyBox"
import ModalBase from "@/components/UI/ModalBase"

interface PropertyUnitsImagesProps {
    unitValue: { [key: string]: any }
    setUnitValue: (unit: { [key: string]: any }) => void
    handleUpdateUnit: (unit: { [key: string]: any }) => void
    address?: string
}

interface PhotoAnalysisResult {
    feature_tags: string[];
    amenities_tags: string[];
    confidence: number;
    analysis_summary: string;
}

interface PropertyConditionResult {
    conditionScore: number;
    overallCondition: string;
    repairItems: string[];
    analysisDetails: {
        exterior: { score: number; issues: string[]; };
        structural: { score: number; issues: string[]; };
        maintenance: { score: number; issues: string[]; };
        aesthetic: { score: number; issues: string[]; };
    };
    confidence: number;
    timestamp: string;
}

export default function PropertyUnitsImages({unitValue, setUnitValue, handleUpdateUnit, address}: PropertyUnitsImagesProps) {
    const { addStatus, removeStatus } = useStatus()
    const [mainImage, setMainImage] = useState<string | null>(null)
    const [allImages, setAllImages] = useState<string[]>([])
    const [showUploadModal, setShowUploadModal] = useState(false)
    const [aiAnalysisStatus, setAiAnalysisStatus] = useState<'idle' | 'analyzing' | 'success' | 'error'>('idle')
    const [analysisResult, setAnalysisResult] = useState<PhotoAnalysisResult | null>(null)
    const [showRegenerateModal, setShowRegenerateModal] = useState(false)
    const [isSearchingImages, setIsSearchingImages] = useState(false)
    const [showAgenticImageResults, setShowAgenticImageResults] = useState(false)
    const [agenticImageResults, setAgenticImageResults] = useState<{
        success: boolean;
        searchQuery: string;
        data: Array<{
            url: string;
            description: string;
            type: string;
            source: string;
            confidence: number;
            dimensions: string;
            thumbnailUrl: string;
            link: string;
            selected: boolean;
        }>;
        openaiAnalysis?: string;
        sources: string[];
        metadata: {
            searchQueriesUsed: string[];
            resultCount: number;
            preSelectedCount?: number;
            timestamp: string;
        };
    } | null>(null)
    const [conditionAnalysis, setConditionAnalysis] = useState<PropertyConditionResult | null>(null)
    const [isAnalyzingCondition, setIsAnalyzingCondition] = useState(false)
    const [isConditionSectionCollapsed, setIsConditionSectionCollapsed] = useState(true)

    const extractImageUrl = (img: any): string => {
        if(!img) return ''
        return typeof img === 'string' ? img : img.url || img.publicUrl || img.signedUrl || img.file_path || ''
    }

    const handlePhotosUpdate = (photos: any[]) => {
        const updatedUnit = {
            ...unitValue,
            img_urls: photos
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    };

    const handleUploadComplete = async (newImageUrls: string[]) => {
        if (!newImageUrls || newImageUrls.length === 0) {
            console.log('No new images to add');
            return;
        }
        
        // Create a copy of existing images and add new ones
        const existingImages = [...(unitValue?.img_urls || [])];
        const updatedImages = [...existingImages, ...newImageUrls];
        
        console.log(`Adding ${newImageUrls.length} new images. Total: ${updatedImages.length}`);
        
        // Update the state with new images
        console.log('Saving images to database via handlePhotosUpdate...');
        handlePhotosUpdate(updatedImages);
        
        // Set the first new image as main if no main image exists
        if (!mainImage && newImageUrls.length > 0) {
            setMainImage(newImageUrls[0]);
        }
        
        // Wait a moment for the database save to process before analyzing
        console.log('Waiting before AI analysis to ensure images are saved...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Auto-analyze the new images if there are any
        if (newImageUrls.length > 0) {
            console.log('Starting AI analysis with', updatedImages.length, 'images');
            await analyzePhotosForTags(updatedImages);
        }
    }

    const analyzePhotosForTags = async (imageUrls?: string[]) => {
        const imagesToAnalyze = imageUrls || allImages
        
        if (imagesToAnalyze.length === 0) {
            return
        }

        setAiAnalysisStatus('analyzing')
        setAnalysisResult(null)

        try {
            const response = await fetch('/api/ai-photo-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    imageUrls: imagesToAnalyze,
                    unitType: `${unitValue.beds || 0}BR/${unitValue.baths || 0}BA`,
                    existingTags: {
                        feature_tags: unitValue.feature_tags || [],
                        amenities_tags: unitValue.amenities_tags || []
                    }
                })
            })

            if (!response.ok) {
                throw new Error('Failed to analyze photos')
            }

            const result: PhotoAnalysisResult = await response.json()
            
            // Validate the response structure
            if (!result || typeof result !== 'object') {
                throw new Error('Invalid response format from AI analysis')
            }
            
            // Ensure feature_tags and amenities_tags are arrays
            const validatedResult = {
                ...result,
                feature_tags: Array.isArray(result.feature_tags) ? result.feature_tags : [],
                amenities_tags: Array.isArray(result.amenities_tags) ? result.amenities_tags : [],
                confidence: typeof result.confidence === 'number' ? result.confidence : 0,
                analysis_summary: typeof result.analysis_summary === 'string' ? result.analysis_summary : 'Analysis completed'
            }
            
            setAnalysisResult(validatedResult)
            setAiAnalysisStatus('success')

            // Auto-apply the detected tags (merge with existing)
            const existingFeatureTags = unitValue.feature_tags || []
            const existingAmenityTags = unitValue.amenities_tags || []
            
            const newFeatureTags = [...new Set([...existingFeatureTags, ...validatedResult.feature_tags])]
            const newAmenityTags = [...new Set([...existingAmenityTags, ...validatedResult.amenities_tags])]

            // Use the images that were passed to the analysis, not unitValue.img_urls
            // This ensures we preserve the images that were just uploaded
            const currentImages = imageUrls || unitValue.img_urls || []
            
            const updatedUnit = {
                ...unitValue,
                feature_tags: newFeatureTags,
                amenities_tags: newAmenityTags,
                img_urls: currentImages // Use the current images from analysis
            }

            console.log('AI Analysis updating unit with images:', currentImages.length)
            setUnitValue(updatedUnit)
            handleUpdateUnit(updatedUnit)

            // Reset success state after 5 seconds
            setTimeout(() => {
                setAiAnalysisStatus('idle')
            }, 5000)

        } catch (error) {
            console.error('AI photo analysis error:', error)
            setAiAnalysisStatus('error')
            
            // Reset error state after 3 seconds
            setTimeout(() => {
                setAiAnalysisStatus('idle')
            }, 3000)
        }
    }

    const handleRegenerateConfirm = async () => {
        setShowRegenerateModal(false)
        await analyzePhotosForTags()
    }

    const handleAgenticImageSearch = async () => {
        setIsSearchingImages(true);
        
        // Add status notification using the standard system
        const statusId = addStatus({
            title: 'Searching for Images...',
            message: 'AI is finding property images',
            type: 'loading',
            autoClose: false
        });
        
        try {
            const response = await fetch('/api/agentic-image-search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address || '',
                    unit: unitValue.unit || '',
                    searchType: 'unit_interior' // Specify this is for unit interior images
                })
            });

            if (!response.ok) {
                throw new Error('Image search failed');
            }

            const results = await response.json();
            console.log('Agentic image search results:', results);
            setAgenticImageResults(results);
            setShowAgenticImageResults(true);
            
            // Remove loading status and add success status
            removeStatus(statusId);
            addStatus({
                title: 'Images Found!',
                message: 'Check the results modal',
                type: 'success',
                autoClose: true,
                duration: 5000
            });
            
        } catch (error) {
            console.error('Agentic image search failed:', error);
            
            // Remove loading status and add error status
            removeStatus(statusId);
            addStatus({
                title: 'Search Failed',
                message: 'Please try again later',
                type: 'error',
                autoClose: true,
                duration: 5000
            });
        } finally {
            setIsSearchingImages(false);
        }
    }

    const handleAcceptAgenticImages = async () => {
        if (!agenticImageResults || !agenticImageResults.data) return;
        
        try {
            // Add the agentic images to existing images
            const selectedImageUrls = agenticImageResults.data
                .filter((img: any) => img.selected)
                .map((img: any) => img.url);
            
            if (selectedImageUrls.length > 0) {
                await handleUploadComplete(selectedImageUrls);
                
                // TODO: Property condition analysis - disabled for now
                // After adding images, automatically analyze property condition
                // await analyzePropertyCondition([...allImages, ...selectedImageUrls]);
            }
            
            setShowAgenticImageResults(false);
            setAgenticImageResults(null);
        } catch (error) {
            console.error('Failed to save agentic images:', error);
            alert('Failed to save the images. Please try again.');
        }
    }

    const handleRejectAgenticImages = () => {
        setShowAgenticImageResults(false);
        setAgenticImageResults(null);
    }

    const toggleImageSelection = (index: number) => {
        if (!agenticImageResults) return;
        
        const updatedResults = {
            ...agenticImageResults,
            data: agenticImageResults.data.map((img: any, i: number) => 
                i === index ? { ...img, selected: !img.selected } : img
            )
        };
        
        setAgenticImageResults(updatedResults);
    }

    const handleAcceptSelectedImages = handleAcceptAgenticImages;

    const analyzePropertyCondition = async (imageUrls?: string[]) => {
        const imagesToAnalyze = imageUrls || allImages;
        
        if (imagesToAnalyze.length === 0) {
            return;
        }

        setIsAnalyzingCondition(true);
        setConditionAnalysis(null);

        try {
            const response = await fetch('/api/ai-photo-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    images: imagesToAnalyze,
                    address: address || ''
                })
            });

            if (!response.ok) {
                throw new Error('Failed to analyze property condition');
            }

            const result = await response.json();
            if (result.success && result.data) {
                setConditionAnalysis(result.data);
            }

        } catch (error) {
            console.error('Property condition analysis error:', error);
        } finally {
            setIsAnalyzingCondition(false);
        }
    }

    useEffect(() => {
        async function resolveImages(){
            if(!unitValue.img_urls || unitValue.img_urls.length === 0){
                setMainImage(null)
                setAllImages([])
                return
            }
            
            const supabase = createClient()
            const resolvedImages: string[] = []
            
            for (const img of unitValue.img_urls) {
                const url = extractImageUrl(img)
                if(url){
                    let pathToSign = url
                    if(url.startsWith('http')){
                        const maybe = publicUrlToPath(url,'propimages')
                        if(!maybe){ 
                            resolvedImages.push(url)
                            continue 
                        }
                        pathToSign = maybe
                    }
                    const { data: signed, error } = await supabase.storage.from('propimages').createSignedUrl(pathToSign, 60*60)
                    if(!error && signed?.signedUrl){
                        resolvedImages.push(signed.signedUrl)
                    } else {
                        const { data: pub } = await supabase.storage.from('propimages').getPublicUrl(pathToSign)
                        if(pub?.publicUrl) {
                            resolvedImages.push(pub.publicUrl)
                        }
                    }
                }
            }
            
            setAllImages(resolvedImages)
            
            // Only update main image if it's null or not in the resolved images
            if (!mainImage || !resolvedImages.includes(mainImage)) {
                setMainImage(resolvedImages[0] || null)
            }
        }
        resolveImages()
    }, [unitValue.img_urls]) // Removed mainImage from dependency to prevent infinite loops

    const handleRemoveImage = (indexToRemove: number) => {
        // Create a copy of the current img_urls array
        const currentImages = [...(unitValue.img_urls || [])];
        
        // Remove only the specific image at the given index
        currentImages.splice(indexToRemove, 1);
        
        // Update the main image if we removed the currently selected one
        if (mainImage === allImages[indexToRemove]) {
            setMainImage(currentImages.length > 0 ? allImages[indexToRemove] || allImages[0] : null);
        }
        
        // Update the state with the new array
        handlePhotosUpdate(currentImages);
        
        console.log(`Removed image at index ${indexToRemove}. Remaining images:`, currentImages.length);
    };

    return (
        <div className="space-y-4">
            {/* Regenerate Tags Confirmation Modal */}
            {showRegenerateModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center">
                                <FontAwesomeIcon icon={faExclamationTriangle} className="text-amber-600 h-5 w-5" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Regenerate Tags from Photos</h3>
                                <p className="text-sm text-gray-500">This will analyze all photos and update tags</p>
                            </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-6">
                            AI will analyze all {allImages.length} photos in this unit and automatically add new relevant tags 
                            and amenities. Existing tags will be preserved and new ones will be added.
                        </p>
                        
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={() => setShowRegenerateModal(false)}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleRegenerateConfirm}
                                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
                            >
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                Analyze Photos
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Main Image Display */}
            <div className="relative group">
                <div className="w-full h-[280px] sm:h-[320px] lg:h-[360px] bg-gray-100 rounded-xl border border-gray-200 shadow-sm flex items-center justify-center overflow-hidden">
                    {mainImage ? (
                        <img 
                            src={mainImage} 
                            alt={`Unit ${unitValue.unit} - Main`} 
                            className="w-full h-full object-cover rounded-xl"
                        />
                    ) : (
                        <div className="text-center text-gray-500 p-8">
                            <FontAwesomeIcon icon={faImage} className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mb-3" />
                            <p className="text-lg font-medium">No images uploaded</p>
                            <p className="text-sm text-gray-400 mt-1">Upload images to get started</p>
                        </div>
                    )}
                </div>
                
                {/* Floating Add Button - appears on hover over main image */}
                <button
                    onClick={() => setShowUploadModal(true)}
                    className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:shadow-xl hover:scale-110"
                >
                    <FontAwesomeIcon icon={faPlus} className="w-4 h-4 text-gray-700" />
                </button>
            </div>

            {/* AI Analysis Status */}
            {aiAnalysisStatus !== 'idle' && (
                <div className={`p-4 rounded-xl border ${
                    aiAnalysisStatus === 'analyzing' ? 'bg-purple-50 border-purple-200' :
                    aiAnalysisStatus === 'success' ? 'bg-emerald-50 border-emerald-200' :
                    'bg-red-50 border-red-200'
                }`}>
                    <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            aiAnalysisStatus === 'analyzing' ? 'bg-purple-100' :
                            aiAnalysisStatus === 'success' ? 'bg-emerald-100' :
                            'bg-red-100'
                        }`}>
                            {aiAnalysisStatus === 'analyzing' && (
                                <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 text-purple-600 animate-spin" />
                            )}
                            {aiAnalysisStatus === 'success' && (
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4 text-emerald-600" />
                            )}
                            {aiAnalysisStatus === 'error' && (
                                <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4 text-red-600" />
                            )}
                        </div>
                        <div className="flex-1">
                            <p className={`text-sm font-medium ${
                                aiAnalysisStatus === 'analyzing' ? 'text-purple-800' :
                                aiAnalysisStatus === 'success' ? 'text-emerald-800' :
                                'text-red-800'
                            }`}>
                                {aiAnalysisStatus === 'analyzing' && 'AI is analyzing your photos...'}
                                {aiAnalysisStatus === 'success' && 'AI analysis complete!'}
                                {aiAnalysisStatus === 'error' && 'AI analysis failed'}
                            </p>
                            {analysisResult && aiAnalysisStatus === 'success' && (
                                <p className="text-xs text-emerald-600 mt-1">
                                    Added {analysisResult.feature_tags.length} features and {analysisResult.amenities_tags.length} amenities 
                                    • {analysisResult.confidence}% confidence
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Thumbnails Section */}
            <div className="space-y-3">
                {allImages.length > 0 ? (
                    <div className="flex flex-col space-y-3">
                        <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-700">
                                Gallery ({allImages.length} image{allImages.length !== 1 ? 's' : ''})
                            </h4>
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={() => setShowRegenerateModal(true)}
                                    disabled={aiAnalysisStatus === 'analyzing'}
                                    className="px-3 py-1.5 text-xs font-medium text-purple-700 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-1.5 disabled:opacity-50"
                                >
                                    <FontAwesomeIcon icon={faMagicWandSparkles} className="w-3 h-3" />
                                    AI Tags
                                </button>
                                <button
                                    onClick={handleAgenticImageSearch}
                                    disabled={isSearchingImages}
                                    className="px-3 py-1.5 text-xs font-medium text-purple-700 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg hover:from-purple-100 hover:to-indigo-100 transition-colors flex items-center gap-1.5 disabled:opacity-50"
                                >
                                    {isSearchingImages ? (
                                        <FontAwesomeIcon icon={faSpinner} className="w-3 h-3 animate-spin" />
                                    ) : (
                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="w-3 h-3" />
                                    )}
                                    {isSearchingImages ? 'Searching...' : 'Find Images'}
                                </button>
                                <button
                                    onClick={() => setShowUploadModal(true)}
                                    className="text-xs text-indigo-600 hover:text-indigo-800 font-medium transition-colors flex items-center gap-1"
                                >
                                    <FontAwesomeIcon icon={faPlus} className="w-3 h-3" />
                                    Add More
                                </button>
                            </div>
                        </div>
                        
                        <Fancybox>
                            <div className="flex flex-wrap">
                                {allImages.map((image, index) => (
                                    <div key={index} className="relative group w-[40px] h-[40px] m-2">
                                        <div className="bg-gray-100 rounded-lg overflow-hidden border border-gray-200 hover:border-indigo-300 transition-all duration-200 w-[40px] h-[40px]">
                                            <img 
                                                src={image} 
                                                alt={`Unit ${unitValue.unit} - ${index + 1}`}
                                                className="w-[40px] h-[40px] object-cover cursor-pointer"
                                                style={{width: '40px', height: '40px', minWidth: '40px', minHeight: '40px', maxWidth: '40px', maxHeight: '40px'}}
                                                data-fancybox="gallery"
                                                data-src={image}
                                                onClick={() => setMainImage(image)}
                                            />
                                        </div>
                                        
                                        {/* Delete button - appears on hover */}
                                        <button
                                            onClick={() => handleRemoveImage(index)}
                                            className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-600 hover:scale-110 shadow-lg"
                                        >
                                            <FontAwesomeIcon icon={faXmark} className="w-2.5 h-2.5" />
                                        </button>
                                        
                                        {/* Selected indicator */}
                                        {mainImage === image && (
                                            <div className="absolute inset-0 rounded-lg ring-2 ring-indigo-500 ring-offset-1"></div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </Fancybox>
                    </div>
                ) : (
                    <div className="text-center py-6">
                        <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
                            <button
                                onClick={() => setShowUploadModal(true)}
                                className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors shadow-sm"
                            >
                                <FontAwesomeIcon icon={faPlus} className="w-4 h-4" />
                                Add Images
                            </button>
                            <button
                                onClick={handleAgenticImageSearch}
                                disabled={isSearchingImages}
                                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                            >
                                {isSearchingImages ? (
                                    <FontAwesomeIcon icon={faSpinner} className="w-4 h-4 animate-spin" />
                                ) : (
                                    <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                )}
                                {isSearchingImages ? 'Searching...' : 'Find Images'}
                            </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">Upload photos, let AI find images, or get property condition analysis for this unit</p>
                    </div>
                )}
            </div>


            {/* Upload Modal */}
            <PropertyUnitsmImagesUpload
                isOpen={showUploadModal}
                onClose={() => setShowUploadModal(false)}
                onUpload={handleUploadComplete}
                unitId={unitValue.id}
            />

            {/* Agentic Image Search Results Modal */}
            {showAgenticImageResults && agenticImageResults && (
                <ModalBase
                    isOpen={showAgenticImageResults}
                    onClose={handleRejectAgenticImages}
                    title="AI Found Property Images"
                    subtitle="Select images to add to your unit"
                    icon={faMagicWandSparkles}
                    iconBgColor="bg-purple-100"
                    iconColor="text-purple-600"
                    maxWidth="max-w-4xl"
                    actions={
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={handleRejectAgenticImages}
                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleAcceptSelectedImages}
                                disabled={!agenticImageResults.data?.some((img: any) => img.selected)}
                                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faCheck} className="h-4 w-4" />
                                Add Selected Images ({agenticImageResults.data?.filter((img: any) => img.selected).length || 0})
                            </button>
                        </div>
                    }
                >
                    {/* Search Query */}
                    {agenticImageResults.searchQuery && (
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-gray-700 mb-2">Search Query Used:</h4>
                            <p className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
                                {agenticImageResults.searchQuery}
                            </p>
                        </div>
                    )}

                    {/* OpenAI Analysis */}
                    {agenticImageResults.openaiAnalysis && (
                        <div className="bg-blue-50 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-blue-700 mb-2 flex items-center gap-2">
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                AI Analysis & Pre-selection:
                            </h4>
                            <p className="text-sm text-blue-800 bg-white p-3 rounded border">
                                {agenticImageResults.openaiAnalysis}
                            </p>
                            {(agenticImageResults.metadata?.preSelectedCount || 0) > 0 && (
                                <p className="text-xs text-blue-600 mt-2">
                                    ✨ {agenticImageResults.metadata?.preSelectedCount || 0} images were automatically pre-selected based on address and unit matching
                                </p>
                            )}
                        </div>
                    )}

                            {/* Found Images */}
                            <div className="mb-6">
                                <h4 className="text-lg font-semibold text-gray-800 mb-4">
                                    Found Images ({agenticImageResults.data?.length || 0}):
                                </h4>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                    {agenticImageResults.data?.map((image: any, index: number) => (
                                        <div key={index} className="relative group">
                                            <div 
                                                className={`aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 cursor-pointer transition-all ${
                                                    image.selected ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200 hover:border-indigo-300'
                                                }`}
                                                onClick={() => toggleImageSelection(index)}
                                            >
                                                <img 
                                                    src={image.url} 
                                                    alt={`Property image ${index + 1}`}
                                                    className="w-full h-full object-cover"
                                                    onError={(e) => {
                                                        (e.target as HTMLImageElement).src = '/default-home-list-img.svg';
                                                    }}
                                                />
                                                
                                                {/* Selection overlay */}
                                                {image.selected && (
                                                    <div className="absolute inset-0 bg-indigo-500/20 flex items-center justify-center">
                                                        <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                                            <FontAwesomeIcon icon={faCheck} className="text-white h-4 w-4" />
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            
                                            {/* Image info */}
                                            {image.description && (
                                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">{image.description}</p>
                                            )}
                                            <div className="flex items-center justify-between mt-1">
                                                {image.source && (
                                                    <p className="text-xs text-gray-400">{image.source}</p>
                                                )}
                                                                                                 {agenticImageResults?.data && 
                                                 agenticImageResults.data[index]?.selected && 
                                                 (agenticImageResults.metadata?.preSelectedCount || 0) > 0 && (
                                                    <span className="text-xs text-blue-600 font-medium flex items-center gap-1">
                                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="h-3 w-3" />
                                                        AI Match
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Source Information */}
                            {agenticImageResults.sources && agenticImageResults.sources.length > 0 && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                    <h4 className="text-sm font-semibold text-yellow-800 mb-2">🔍 Source Information:</h4>
                                    <div className="space-y-2">
                                        {agenticImageResults.sources.map((source: string, index: number) => (
                                            <div key={index} className="flex items-center gap-2">
                                                <span className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></span>
                                                <a 
                                                    href={source} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-yellow-700 hover:text-yellow-900 underline break-all"
                                                >
                                                    {source}
                                                </a>
                                            </div>
                                        ))}
                                    </div>
                                    <p className="text-xs text-yellow-600 mt-3">
                                        ⚠️ Please verify image rights and usage permissions independently.
                                    </p>
                                </div>
                            )}

                </ModalBase>
            )}


        </div>
    )
}