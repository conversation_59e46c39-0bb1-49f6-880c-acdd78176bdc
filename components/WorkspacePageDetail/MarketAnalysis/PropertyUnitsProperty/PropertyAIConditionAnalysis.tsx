import { faRobot, faMagicWandSparkles } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export default function PropertyAIConditionAnalysis() {
    return (
        <div className="flex flex-col items-center justify-center text-center space-y-4 h-full">
            {/* Robot Icon */}
            <FontAwesomeIcon icon={faMagicWandSparkles} className="h-10 w-10 text-indigo-600" />  
            
            {/* Title */}
            <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    AI Condition Analysis
                </h3>
                <p className="text-gray-500 text-sm italic">
                    Coming Soon
                </p>
            </div>
            
            {/* Optional description */}
            <p className="text-xs text-gray-400 max-w-xs">
                Intelligent property condition assessment powered by AI technology
            </p>
        </div>
    )
}