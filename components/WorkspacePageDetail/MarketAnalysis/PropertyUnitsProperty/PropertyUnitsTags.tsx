import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState, useRef, useEffect } from "react";
import { faPlus, faTimes, faRobot, faMagicWandSparkles } from "@fortawesome/free-solid-svg-icons";

interface PropertyUnitsTagsProps {
    unitValue: { [key: string]: any };
    setUnitValue: (unit: { [key: string]: any }) => void;
    handleUpdateUnit: (unit: { [key: string]: any }) => void;
}

export default function PropertyUnitsTags({ unitValue, setUnitValue, handleUpdateUnit }: PropertyUnitsTagsProps) {
    const [selectedTab, setSelectedTab] = useState<'feature_tags' | 'amenities_tags'>('feature_tags');
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const [newTagText, setNewTagText] = useState('');
    const [recentlyAddedTags, setRecentlyAddedTags] = useState<string[]>([]);
    const popoverRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (popoverRef.current && !popoverRef.current.contains(event.target as Node) &&
                buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
                setIsPopoverOpen(false);
                setNewTagText('');
            }
        };

        if (isPopoverOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isPopoverOpen]);

    // Track recently added tags to show AI indicator
    useEffect(() => {
        const currentTags = unitValue[selectedTab] || [];
        const previousTags = JSON.parse(localStorage.getItem(`previousTags_${unitValue.id}_${selectedTab}`) || '[]');
        
        if (previousTags.length > 0 && currentTags.length > previousTags.length) {
            const newTags = currentTags.filter((tag: string) => !previousTags.includes(tag));
            setRecentlyAddedTags(newTags);
            
            // Clear the indicator after 10 seconds
            setTimeout(() => {
                setRecentlyAddedTags([]);
            }, 10000);
        }
        
        // Store current tags for next comparison
        localStorage.setItem(`previousTags_${unitValue.id}_${selectedTab}`, JSON.stringify(currentTags));
    }, [unitValue[selectedTab], unitValue.id, selectedTab]);

    const updateTags = (tags: string[]) => {
        const updatedUnit = {
            ...unitValue,
            [selectedTab]: tags
        };
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    };

    const handleAddTag = () => {
        if (newTagText.trim()) {
            const currentTags = unitValue[selectedTab] || [];
            const newTags = [...currentTags, newTagText.trim()];
            updateTags(newTags);
            setNewTagText('');
            setIsPopoverOpen(false);
        }
    };

    const handleRemoveTag = (tagToRemove: string) => {
        const currentTags = unitValue[selectedTab] || [];
        const newTags = currentTags.filter((tag: string) => tag !== tagToRemove);
        updateTags(newTags);
    };


    const getCurrentTags = () => unitValue[selectedTab] || [];

    const isRecentlyAdded = (tag: string) => recentlyAddedTags.includes(tag);

    const hasAnyImages = unitValue.img_urls && unitValue.img_urls.length > 0;

    return (
        <div className="space-y-3">
            {/* Header with AI Hint */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <h4 className="text-sm font-medium text-gray-700">Unit Tags & Amenities</h4>
                    {hasAnyImages && (
                        <div className="flex items-center gap-1 text-xs text-purple-600 bg-purple-50 px-2 py-0.5 rounded-md">
                            <FontAwesomeIcon icon={faMagicWandSparkles} className="h-2.5 w-2.5" />
                            <span className="hidden sm:inline">AI-powered</span>
                        </div>
                    )}
                </div>
                
                {recentlyAddedTags.length > 0 && (
                    <div className="flex items-center gap-1 text-xs text-emerald-600 bg-emerald-50 px-2 py-0.5 rounded-md animate-pulse">
                        <FontAwesomeIcon icon={faRobot} className="h-2.5 w-2.5" />
                        <span>{recentlyAddedTags.length} new</span>
                    </div>
                )}
            </div>

            {/* Tab Navigation */}
            <div className="flex gap-1">
                {['feature_tags', 'amenities_tags'].map((tab) => (
                    <button
                        key={tab}
                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex-1 ${
                            selectedTab === tab 
                                ? 'bg-indigo-600 text-white shadow-sm' 
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                        onClick={() => setSelectedTab(tab as 'feature_tags' | 'amenities_tags')}
                    >
                        {tab === 'feature_tags' ? 'Features' : 'Amenities'}
                        <span className="ml-1 text-xs opacity-75">
                            ({(unitValue[tab] || []).length})
                        </span>
                    </button>
                ))}
            </div>

            {/* Tags Container - More Compact */}
            <div className="relative bg-gray-50 rounded-lg p-3 h-40 flex flex-col">
                <div className="flex-1 overflow-y-auto mb-3">
                    {getCurrentTags().length > 0 ? (
                        <div className="flex flex-wrap gap-1.5">
                            {getCurrentTags().map((tag: string) => (
                                <div 
                                    key={tag} 
                                    className={`group flex items-center rounded-md px-2 py-1 text-xs transition-all duration-200 ${
                                        isRecentlyAdded(tag)
                                            ? 'bg-emerald-100 border border-emerald-300 text-emerald-800'
                                            : 'bg-white border border-gray-200 text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    {isRecentlyAdded(tag) && (
                                        <FontAwesomeIcon 
                                            icon={faRobot} 
                                            className="h-2.5 w-2.5 text-emerald-600 mr-1" 
                                        />
                                    )}
                                    <span className="font-medium">{tag}</span>
                                    <button
                                        onClick={() => handleRemoveTag(tag)}
                                        className="ml-1 w-3 h-3 rounded-full bg-gray-200 text-gray-500 hover:bg-red-100 hover:text-red-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-150"
                                    >
                                        <FontAwesomeIcon icon={faTimes} className="h-2 w-2" />
                                    </button>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center h-full text-gray-500">
                            <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center mb-2">
                                <FontAwesomeIcon 
                                    icon={selectedTab === 'feature_tags' ? faMagicWandSparkles : faPlus} 
                                    className="h-4 w-4 text-gray-400" 
                                />
                            </div>
                            <p className="text-xs font-medium text-gray-600 text-center">
                                No {selectedTab === 'feature_tags' ? 'features' : 'amenities'} added yet
                            </p>
                            <p className="text-xs text-gray-500 mt-1 text-center leading-tight">
                                {hasAnyImages 
                                    ? 'Upload photos to auto-generate tags with AI' 
                                    : 'Add tags manually or upload photos for AI detection'
                                }
                            </p>
                        </div>
                    )}
                </div>

                {/* Add Tag Button */}
                <div className="flex justify-end">
                    <button
                        ref={buttonRef}
                        onClick={() => setIsPopoverOpen(!isPopoverOpen)}
                        className="w-8 h-8 flex items-center justify-center rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                        <FontAwesomeIcon icon={faPlus} className="w-3.5 h-3.5" />
                    </button>
                </div>

                {/* Add Tag Popover */}
                {isPopoverOpen && (
                    <div
                        ref={popoverRef}
                        className="absolute bottom-full right-0 mb-2 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-3"
                    >
                        <h4 className="text-xs font-semibold text-gray-800 mb-2 flex items-center gap-2">
                            <FontAwesomeIcon 
                                icon={selectedTab === 'feature_tags' ? faMagicWandSparkles : faPlus} 
                                className="h-3 w-3 text-indigo-600" 
                            />
                            Add {selectedTab === 'feature_tags' ? 'Feature' : 'Amenity'}
                        </h4>
                        
                        <input
                            type="text"
                            value={newTagText}
                            onChange={(e) => setNewTagText(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                            placeholder={
                                selectedTab === 'feature_tags' 
                                    ? 'e.g., hardwood floors...' 
                                    : 'e.g., dishwasher...'
                            }
                            className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                            autoFocus
                        />
                        
                        <div className="flex justify-end space-x-2 mt-3">
                            <button
                                onClick={() => { setIsPopoverOpen(false); setNewTagText(''); }}
                                className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleAddTag}
                                disabled={!newTagText.trim()}
                                className="px-2 py-1 text-xs font-medium bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                Add
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}