import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { formatCurrencyNoSymbol, formatNumber } from "@/utils/formatters";
import { faBath, faBed, faMinus, faMoneyBill, faPlus, faRulerCombined, faMagicWandSparkles, faXmark, faInfoCircle, faExternalLinkAlt, faCheckCircle, faImage } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import Spinner from "@/components/UI/Spinner";
import ModalBase from "@/components/UI/ModalBase";
import { useSearchParams } from "next/navigation";
import { updateUnit } from "@/actions/propetyUnitsActions";
import { useStatus } from '@/context/StatusContext';

interface ProperyUnitsItemEditable {
    unitValue: { [key: string]: any }
    setUnitValue: (unit: { [key: string]: any }) => void
    handleUpdateUnit: (unit: { [key: string]: any }) => void
}

export default function PropertyUnitsInfo({unitValue, setUnitValue, handleUpdateUnit}: ProperyUnitsItemEditable) {
    const { searchPlace } = useMarketAnalysis();
    const { addStatus, removeStatus } = useStatus();
    const searchParams = useSearchParams();
    const addressId = searchParams.get('addressId');
    const [isSearchingAI, setIsSearchingAI] = useState(false);
    const [showAIResults, setShowAIResults] = useState(false);
    const [aiResults, setAIResults] = useState<any>(null);


    const handleUnitChange = (e: React.ChangeEvent<HTMLInputElement>, key: string) => {
        // For numeric fields, only accept numeric input
        if (['beds', 'baths', 'hoa_fee', 'sqft', 'rent', 'price'].includes(key)) {
            // Remove any non-numeric characters except decimal point for input
            const rawValue = e.target.value.replace(/[^0-9.]/g, '');
            
            // Allow empty value or valid number
            if (rawValue === '' || !isNaN(Number(rawValue))) {
                const updatedUnit = { ...unitValue, [key]: rawValue };
                setUnitValue(updatedUnit);
                handleUpdateUnit(updatedUnit);
            }
        } else {
            const updatedUnit = { ...unitValue, [key]: e.target.value };
            setUnitValue(updatedUnit);
            handleUpdateUnit(updatedUnit);
        }
    }
    
    const handleDecrement = (key: 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price', decrementAmount: number) => {
        const currentValue = unitValue[key] === '' ? 0 : Number(unitValue[key]);
        if (currentValue > 0) {
            const updatedUnit = { ...unitValue, [key]: String(currentValue - decrementAmount) };
            setUnitValue(updatedUnit);
            handleUpdateUnit(updatedUnit);
        }
    }

    const handleIncrement = (key: 'beds' | 'baths' | 'hoa_fee' | 'sqft' | 'rent' | 'price', incrementAmount: number) => {
        const currentValue = unitValue[key] === '' ? 0 : Number(unitValue[key]);
        const updatedUnit = { ...unitValue, [key]: String(currentValue + incrementAmount) };
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
    }

    const getFormattedValue = (key: string, value: any) => {
        if (!value) return '';
        
        if (key === 'sqft') {
            return `${formatNumber(Number(value))} sq ft`;
        } else if (['hoa_fee', 'rent', 'price'].includes(key)) {
            return formatCurrencyNoSymbol(Number(value));
        }
        return value;
    };

    const handleAISearch = async () => {
        setIsSearchingAI(true);
        
        // Add status notification
        const statusId = addStatus({
            title: 'AI Unit Info Search',
            message: 'Searching for unit information online...',
            type: 'loading',
            autoClose: false
        });
        
        try {
            // Get the address for search
            let address = '';
            if (typeof searchPlace === 'object' && searchPlace?.formatted_address) {
                address = searchPlace.formatted_address;
            } else if (typeof searchPlace === 'string') {
                address = searchPlace;
            }

            // Call our agentic unit info search API with unit and address IDs for image analysis
            const response = await fetch('/api/agentic-unit-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address,
                    unit: unitValue.unit,
                    unitId: unitValue.id,
                    addressId: addressId,
                    currentInfo: {
                        beds: unitValue.beds,
                        baths: unitValue.baths,
                        sqft: unitValue.sqft,
                        rent: unitValue.rent,
                        price: unitValue.price,
                        hoa_fee: unitValue.hoa_fee
                    }
                })
            });

            if (!response.ok) {
                throw new Error('AI search failed');
            }

            const results = await response.json();
            setAIResults(results);
            setShowAIResults(true);
            
            // Remove loading status and add success status
            removeStatus(statusId);
            addStatus({
                title: 'Unit Info Found',
                message: 'AI successfully found unit information. Review and accept changes.',
                type: 'success',
                autoClose: true,
                duration: 5000
            });
            
        } catch (error) {
            console.error('AI unit info search failed:', error);
            
            // Remove loading status and add error status
            removeStatus(statusId);
            addStatus({
                title: 'Unit Info Search Failed',
                message: 'Unable to search for unit information. Please try again later.',
                type: 'error',
                autoClose: true,
                duration: 5000
            });
        } finally {
            setIsSearchingAI(false);
        }
    };

    const handleAcceptAIData = () => {
        if (!aiResults || !aiResults.data) return;
        
        const updatedUnit = {
            ...unitValue,
            beds: aiResults.data.beds || unitValue.beds,
            baths: aiResults.data.baths || unitValue.baths,
            sqft: aiResults.data.sqft || unitValue.sqft,
            rent: aiResults.data.rent || unitValue.rent,
            price: aiResults.data.price || unitValue.price,
            hoa_fee: aiResults.data.hoa_fee || unitValue.hoa_fee
        };
        
        setUnitValue(updatedUnit);
        handleUpdateUnit(updatedUnit);
        setShowAIResults(false);
        setAIResults(null);
    };

    const handleRejectAIData = () => {
        setShowAIResults(false);
        setAIResults(null);
    };
    

    return (
        <>
            <h3 className="text-sm font-medium text-gray-800 mb-2">Info</h3>
            <div className="flex gap-4 max-w-[380px]">
                <div className="flex flex-col gap-2 w-1/2">
                    {/*<div>
                        <label className="mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <span>Unit #</span>
                        </label>
                        <input type="text" onChange={(e) => handleUnitChange(e, 'unit')} className="border border-gray-300 rounded-md p-1 h-8 focus:outline-none" value={unitValue?.unit || ''} />
                    </div>*/}
                    <div>
                        <label className="mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faBed} className="text-gray-500 h-3 w-3" />
                            <span>Bedrooms</span>
                        </label>
                        <div className="flex">
                            <button 
                                type="button"
                                onClick={() => handleDecrement('beds', 1)}
                                className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-l-md p-1 h-8 focus:outline-none"
                            >
                                <FontAwesomeIcon icon={faMinus} className="w-3 h-3 text-gray-600" />
                            </button>
                            <input 
                                type="text" 
                                className="w-full h-8 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0" 
                                value={unitValue?.beds || 0} 
                                readOnly={true}
                            />
                            <button 
                                type="button" 
                                onClick={() => handleIncrement('beds', 1)}
                                className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-r-md p-1 h-8 focus:outline-none"
                            >
                                <FontAwesomeIcon icon={faPlus} className="w-3 h-3 text-gray-600" />
                            </button>
                        </div>
                    </div>
                    <div>
                        <label className="block mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faBath} className="text-gray-500 h-3 w-3" />
                            <span>Bathrooms</span>
                        </label>
                        <div className="flex">
                            <button 
                                type="button"
                                onClick={() => handleDecrement('baths', 0.5)}
                                className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-l-md p-1 h-8 focus:outline-none"
                            >
                                <FontAwesomeIcon icon={faMinus} className="w-3 h-3 text-gray-600" />
                            </button>
                            <input 
                                type="text" 
                                className="w-full h-8 border-y border-gray-300 text-center text-sm font-medium text-gray-700 focus:outline-none py-0" 
                                value={unitValue?.baths || 0} 
                                readOnly={true}
                            />
                            <button 
                                type="button" 
                                onClick={() => handleIncrement('baths', 0.5)}
                                className="bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-r-md p-1 h-8 focus:outline-none"
                            >
                                <FontAwesomeIcon icon={faPlus} className="w-3 h-3 text-gray-600" />
                            </button>
                        </div>
                    </div>
                    <div>
                        <label className="block mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faRulerCombined} className="text-gray-500 h-3 w-3" />
                            <span>Area</span>
                        </label>
                        <div className="relative">
                            <input 
                                type="text" 
                                className="w-full h-8 border border-gray-300 rounded-md pl-2 pr-14 text-sm font-medium text-gray-700 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white" 
                                value={unitValue?.sqft || ''}
                                onChange={(e) => handleUnitChange(e, 'sqft')}
                            />
                            <div className="absolute inset-y-0 right-2 flex items-center pointer-events-none">
                                <span className="text-xs font-medium text-gray-500">sq ft</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex flex-col gap-2 w-1/2">
                    <div>
                        <label className="mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faMoneyBill} className="text-gray-500 h-3 w-3" />
                            <span>HOA Fee</span>
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                                <span className="text-gray-500">$</span>
                            </div>
                            <input 
                                type="text" 
                                className="w-full h-8 border border-gray-300 rounded-md pl-6 pr-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white" 
                                value={getFormattedValue('hoa_fee', unitValue?.hoa_fee)} 
                                onChange={(e) => handleUnitChange(e, 'hoa_fee')} 
                            />
                        </div>
                    </div>

                    
                    <div>
                        <label className="block mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faMoneyBill} className="text-gray-500 h-3 w-3" />
                            <span>Rent</span>
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                                <span className="text-gray-500">$</span>
                            </div>
                            <input 
                                type="text" 
                                className="w-full h-8 border border-gray-300 rounded-md pl-6 pr-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white" 
                                value={getFormattedValue('rent', unitValue?.rent)}
                                onChange={(e) => handleUnitChange(e, 'rent')} 
                            />
                        </div>
                    </div>

                    
                    <div>
                        <label className="block mb-1 text-xs font-medium text-gray-700 flex items-center gap-1">
                            <FontAwesomeIcon icon={faMoneyBill} className="text-gray-500 h-3 w-3" />
                            <span>Price</span>
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                                <span className="text-gray-500">$</span>
                            </div>
                            <input 
                                type="text" 
                                className="w-full h-8 border border-gray-300 rounded-md pl-6 pr-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white" 
                                value={getFormattedValue('price', unitValue?.price)} 
                                onChange={(e) => handleUnitChange(e, 'price')} 
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* AI Search Button */}
            <div className="mt-4 max-w-[380px]">
                <button
                    onClick={handleAISearch}
                    disabled={isSearchingAI}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                >
                    {isSearchingAI ? (
                        <>
                            <Spinner size="sm" />
                            <span>Searching for unit info...</span>
                        </>
                    ) : (
                        <>
                            <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                            <span>AI Search Unit Info</span>
                        </>
                    )}
                </button>
            </div>

            {/* AI Search Results Modal */}
            {showAIResults && aiResults && (
                <ModalBase
                    isOpen={showAIResults}
                    onClose={handleRejectAIData}
                    title="AI Found Unit Information"
                    subtitle="AI searched the internet for unit details"
                    icon={faMagicWandSparkles}
                    iconBgColor="bg-purple-100"
                    iconColor="text-purple-600"
                    maxWidth="max-w-2xl"
                    actions={
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={handleRejectAIData}
                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleAcceptAIData}
                                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors flex items-center gap-2"
                            >
                                <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4" />
                                Accept & Update Unit
                            </button>
                        </div>
                    }
                >
                    {/* Search Query */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <h4 className="text-sm font-semibold text-gray-700 mb-2">Search Query Used:</h4>
                        <p className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
                            {aiResults.searchQuery}
                        </p>
                    </div>

                    {/* Sources */}
                    {aiResults.sources && aiResults.sources.length > 0 && (
                        <div className="bg-blue-50 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-blue-800 mb-3 flex items-center gap-2">
                                <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4" />
                                Data Sources & References
                            </h4>
                            <div className="space-y-2">
                                {aiResults.sources.map((source: string, index: number) => (
                                    <a
                                        key={index}
                                        href={source}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-2 text-sm text-blue-700 hover:text-blue-900 hover:underline transition-colors"
                                    >
                                        <FontAwesomeIcon icon={faExternalLinkAlt} className="h-3 w-3" />
                                        <span className="truncate">{source}</span>
                                    </a>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Image Analysis Notification */}
                    {aiResults.estimatedFromImages && (
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-orange-800 mb-3 flex items-center gap-2">
                                <FontAwesomeIcon icon={faImage} className="h-4 w-4" />
                                Enhanced with Image Analysis
                            </h4>
                            <div className="space-y-2">
                                <p className="text-sm text-orange-700">
                                    {aiResults.imageAnalysisNotes || 'Some room counts were estimated from property images when search data was insufficient.'}
                                </p>
                                <div className="bg-orange-100 rounded p-2">
                                    <p className="text-xs text-orange-600 font-medium">
                                        💡 Data source: AI analyzed property images to estimate bedroom/bathroom counts where internet search results were unclear or missing.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Found Data */}
                    <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4">Found Unit Information:</h4>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center gap-2 mb-1">
                                    <FontAwesomeIcon icon={faBed} className="text-gray-500 h-3 w-3" />
                                    <span className="text-sm font-medium text-gray-600">Bedrooms:</span>
                                </div>
                                <p className="text-lg font-semibold text-gray-800">{aiResults.data?.beds || 'N/A'}</p>
                                <p className="text-xs text-purple-600">Confidence: {aiResults.data?.confidence_beds || 0}%</p>
                            </div>
                            
                            <div className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center gap-2 mb-1">
                                    <FontAwesomeIcon icon={faBath} className="text-gray-500 h-3 w-3" />
                                    <span className="text-sm font-medium text-gray-600">Bathrooms:</span>
                                </div>
                                <p className="text-lg font-semibold text-gray-800">{aiResults.data?.baths || 'N/A'}</p>
                                <p className="text-xs text-purple-600">Confidence: {aiResults.data?.confidence_baths || 0}%</p>
                            </div>
                            
                            <div className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center gap-2 mb-1">
                                    <FontAwesomeIcon icon={faRulerCombined} className="text-gray-500 h-3 w-3" />
                                    <span className="text-sm font-medium text-gray-600">Square Feet:</span>
                                </div>
                                <p className="text-lg font-semibold text-gray-800">{aiResults.data?.sqft?.toLocaleString() || 'N/A'}</p>
                                <p className="text-xs text-purple-600">Confidence: {aiResults.data?.confidence_sqft || 0}%</p>
                            </div>
                            
                            <div className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center gap-2 mb-1">
                                    <FontAwesomeIcon icon={faMoneyBill} className="text-gray-500 h-3 w-3" />
                                    <span className="text-sm font-medium text-gray-600">Est. Rent:</span>
                                </div>
                                <p className="text-lg font-semibold text-gray-800">${aiResults.data?.rent?.toLocaleString() || 'N/A'}</p>
                                <p className="text-xs text-purple-600">Confidence: {aiResults.data?.confidence_rent || 0}%</p>
                            </div>
                        </div>
                    </div>

                    {/* Data Accuracy Disclaimer */}
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                        <h4 className="text-sm font-semibold text-amber-800 mb-2">⚠️ Data Accuracy Disclaimer</h4>
                        <p className="text-sm text-amber-700">
                            This information was extracted from internet search results using AI and may not be 100% accurate. 
                            Please verify this data independently before making important decisions.
                        </p>
                    </div>
                </ModalBase>
            )}
        </>
    )
}