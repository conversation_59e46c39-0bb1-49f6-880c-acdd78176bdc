import { updateUnitMarketData } from "@/actions/propetyUnitsActions";
import { formatCurrencyNoSymbol, canonicalizeListingType, getListingTypeDisplayName } from "@/utils/formatters"
import { useEffect, useState } from "react"
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useDebounce } from "@/helpers/hooks/useDebounce"
import { useMarketAnalysis } from "@/context/MarketAnalysisContext"

interface PropertyUnitsMarketItemProps {
    unitValue: { [key: string]: any }
    setUnitValue: (unit: { [key: string]: any }) => void
    marketData: { [key: string]: any }
    indexData: number
    marketId: string
    addressId: string
}

// Helper function to safely parse date
const parseDate = (dateValue: any): Date | null => {
    if (!dateValue) return null;
    
    // If it's already a formatted string (longer than basic date format), don't process it
    if (typeof dateValue === 'string' && dateValue.length > 10) {
        return null; // Let it display as string
    }
    
    // For short date strings like "2024-01-15", try to parse
    if (typeof dateValue === 'string') {
        try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) return null;
            return date;
        } catch (error) {
            return null;
        }
    }
    
    // For Date objects
    if (dateValue instanceof Date) {
        return isNaN(dateValue.getTime()) ? null : dateValue;
    }
    
    return null;
};

// Helper function to check if date should be displayed as string
const shouldDisplayAsString = (dateValue: any): boolean => {
    // Display as string if it's a formatted string (longer than basic date format)
    // or if it contains text like "January", "Feb", etc.
    if (typeof dateValue === 'string') {
        return dateValue.length > 10 || /[a-zA-Z]/.test(dateValue);
    }
    return false;
};

export default function PropertyUnitsMarketItem({ unitValue, setUnitValue, marketData, marketId, indexData, addressId }: PropertyUnitsMarketItemProps) {
    const {updateMarketAnalysisState} = useMarketAnalysis()
    const [isClient, setIsClient] = useState(false)
    const [isEditingDate, setIsEditingDate] = useState(false)
    const [marketDataValue, setMarketDataValue] = useState({
        list_date: marketData?.list_date,
        list_price: marketData?.list_price || 0,
        listing_type: marketData?.listing_type
    })
    
    // Ensure we're on the client side before rendering DatePicker
    useEffect(() => {
        setIsClient(true)
    }, [])
    
    const handleUpdateUnitMarketData = useDebounce(async (updatedMarketData) => {
        updateUnitMarketData({...marketData, ...updatedMarketData}, unitValue?.id, indexData, marketId, addressId).then((unitData) => {
            updateMarketAnalysisState({
                unitData: unitData
            })
        })
    }, 500)
    
    const handleDateChange = (date: any) => {
        const newMarketData = { ...marketDataValue, list_date: date }
        setMarketDataValue(newMarketData)
        handleUpdateUnitMarketData(newMarketData)
        setIsEditingDate(false)
    }

    const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[^0-9.]/g, '')
        const newMarketData = { ...marketDataValue, list_price: value }
        setMarketDataValue(newMarketData)
        handleUpdateUnitMarketData(newMarketData)
    }

    const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const canonicalType = canonicalizeListingType(e.target.value);
        const newMarketData = { ...marketDataValue, listing_type: canonicalType }
        setMarketDataValue(newMarketData)
        handleUpdateUnitMarketData(newMarketData)
    }

    // Safely get the selected date
    const selectedDate = parseDate(marketDataValue?.list_date);
    const displayAsString = shouldDisplayAsString(marketDataValue?.list_date);

    return (
        <div className="space-y-3">
            {/* Date Field */}
            <div>
                <label className="block text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">
                    Date
                </label>
                {displayAsString && !isEditingDate ? (
                    <div 
                        className="w-full h-10 border border-gray-200 rounded-lg px-3 text-sm bg-white flex items-center cursor-pointer hover:border-gray-300 transition-all duration-200"
                        onClick={() => setIsEditingDate(true)}
                    >
                        {marketDataValue?.list_date}
                    </div>
                ) : isClient ? (
                    <DatePicker 
                        selected={selectedDate} 
                        onChange={(date) => handleDateChange(date)} 
                        dateFormat="MMMM do, yyyy"
                        className="w-full h-10 border border-gray-200 rounded-lg px-3 text-sm focus:ring-2 focus:ring-indigo-100 focus:border-indigo-300 bg-white transition-all duration-200 hover:border-gray-300"
                        popperPlacement="bottom-start"
                        popperProps={{
                            strategy: "fixed",
                            placement: "bottom-start"
                        }}
                        isClearable
                        placeholderText="Select a date"
                        onBlur={() => setIsEditingDate(false)}
                    />
                ) : (
                    <div className="w-full h-10 border border-gray-200 rounded-lg px-3 text-sm bg-gray-50 flex items-center text-gray-500">
                        Loading date picker...
                    </div>
                )}
            </div>

            {/* Price Field */}
            <div>
                <label className="block text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">
                    Price
                </label>
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 font-medium">$</span>
                    </div>
                    <input 
                        type="text" 
                        className="w-full h-10 border border-gray-200 rounded-lg pl-8 pr-3 text-sm focus:ring-2 focus:ring-indigo-100 focus:border-indigo-300 bg-white transition-all duration-200 hover:border-gray-300 font-medium" 
                        value={marketDataValue?.list_price && !isNaN(Number(marketDataValue?.list_price)) ? formatCurrencyNoSymbol(Number(marketDataValue?.list_price)) : marketDataValue?.list_price || ''}
                        placeholder="0" 
                        onChange={handlePriceChange} 
                    />
                </div>
            </div>

            {/* Listing Type Field */}
            <div>
                <label className="block text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">
                    Type
                </label>
                <select 
                    className="w-full h-10 bg-white border border-gray-200 rounded-lg px-3 text-sm font-medium focus:ring-2 focus:ring-indigo-100 focus:border-indigo-300 transition-all duration-200 hover:border-gray-300 cursor-pointer"
                    value={marketDataValue?.listing_type || ''}
                    onChange={handleSelectChange}
                >
                    <option value="for_rent" className="font-medium">For Rent</option>
                    <option value="rented" className="font-medium">Rented</option>
                    <option value="for_sale" className="font-medium">For Sale</option>
                    <option value="sold" className="font-medium">Sold</option>
                    <option value="pending" className="font-medium">Pending</option>
                    <option value="market_analysis" className="font-medium">Market Analysis</option>
                    <option value="off_market" className="font-medium">Off Market</option>
                </select>
            </div>

            {/* Sources Section - Enhanced */}
            {(marketData?.sources && marketData.sources.length > 0) || marketData?.sourceUrl && (
                <div>
                    <label className="block text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">
                        Source{(marketData?.sources && marketData.sources.length > 1) ? 's' : ''}
                    </label>
                    <div className="space-y-2">
                        {/* Multiple sources from array */}
                        {marketData?.sources && marketData.sources.length > 0 && marketData.sources.map((source: string, index: number) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                <a 
                                    href={source.startsWith('http') ? source : `https://${source}`} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-800 font-mono break-all hover:underline flex-1"
                                >
                                    {source}
                                </a>
                                <svg className="w-3 h-3 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </div>
                        ))}
                        
                        {/* Single sourceUrl field */}
                        {marketData?.sourceUrl && (
                            <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                                <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                                <a 
                                    href={marketData.sourceUrl.startsWith('http') ? marketData.sourceUrl : `https://${marketData.sourceUrl}`} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-xs text-green-800 font-mono break-all hover:underline flex-1"
                                >
                                    {marketData.sourceUrl}
                                </a>
                                <svg className="w-3 h-3 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Legacy Source URL Section (for backwards compatibility) */}
            {marketData?.source?.url && !marketData?.sourceUrl && !marketData?.sources && (
                <div>
                    <label className="block text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">
                        Source
                    </label>
                    <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></div>
                        <a 
                            href={marketData.source.url.startsWith('http') ? marketData.source.url : `https://${marketData.source.url}`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-xs text-yellow-800 font-mono break-all hover:underline flex-1"
                        >
                            {marketData.source.url}
                        </a>
                        <svg className="w-3 h-3 text-yellow-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                    </div>
                </div>
            )}
        </div>
    )
}