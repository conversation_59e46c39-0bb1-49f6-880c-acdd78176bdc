'use client'
import React, {useCallback, useEffect, useState} from 'react';
import { GoogleMap, use<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>luster<PERSON> } from '@react-google-maps/api';

const containerStyle = {
	width: '100%',
	height: '100%',
	position: 'absolute',
	top: '0',
	left: '0',
	backgroundColor: 'transparent',
} as React.CSSProperties;

function GoogleMapStreetView({location}: {location: {latitude: number, longitude: number}}) {
    const {isLoaded} = useJsApiLoader({
		id: 'google-map-script',
		googleMapsApiKey: `${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`,
	});

    const [map, setMap] = React.useState<google.maps.Map | null>(null);
    const [panorama, setPanorama] = useState<google.maps.StreetViewPanorama | null>(null);

    const updateCenter = useCallback((newLat: number, newLng: number) => {
		if (map) {
			const newCenter = new google.maps.LatLng(newLat, newLng)
			map.setCenter(newCenter);
            
            // Update panorama position if it exists
            if (panorama) {
                panorama.setPosition(newCenter);
            }
		}
	}, [map, panorama]);

    const onLoad = useCallback(function callback(map: google.maps.Map) {
		setMap(map);
        
        // Initialize Street View by default
        const panoramaOptions = {
            position: { lat: location?.latitude || 0, lng: location?.longitude || 0 },
            pov: { heading: 0, pitch: 0 },
            visible: true,
            enableCloseButton: false,
        };
        
        // Use the map's div to display Street View directly
        const streetViewPanorama = map.getStreetView();
        streetViewPanorama.setOptions(panoramaOptions);
        streetViewPanorama.setVisible(true);
        setPanorama(streetViewPanorama);
	}, [location]);

	const onUnmount = useCallback(function callback(map: google.maps.Map) {
		setMap(null);
        setPanorama(null);
	}, []);

	const center = {
		lat: location?.latitude || 0,
		lng: location?.longitude || 0,
	}

	useEffect(() => {
		if (location && map) {
			updateCenter(location.latitude || 0, location.longitude || 0);
		}
	}, [map, location, updateCenter]);

    return isLoaded ? (
        <>
            <GoogleMap
                mapContainerStyle={containerStyle}
                center={center}
                zoom={15}
                onLoad={onLoad}
                onUnmount={onUnmount}
                options={{
                    disableDefaultUI: false,
                    zoomControl: true,
                    mapTypeControl: false,
                    scaleControl: false,
                    streetViewControl: true,
                    rotateControl: true,
                    fullscreenControl: true
                }}
            />
        </>
    ) : null
}

export default React.memo(GoogleMapStreetView);