'use client'
import React, {useEffect, useState} from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useJsA<PERSON><PERSON>oa<PERSON>, DirectionsService, DirectionsRenderer, Circle } from '@react-google-maps/api';
import {useCallback} from "react";
import { createGlobalStyle } from 'styled-components';



const containerStyle = {
	width: '100%',
	height: '100%',
	position: 'absolute',
	top: '0',
	left: '0',
	backgroundColor: 'transparent',
} as React.CSSProperties; 

interface Props {
    latitude: number;
    longitude: number;
    selectedLocation: { [key: string]: any }[];
    setColorPrimary: React.Dispatch<React.SetStateAction<string[]>>;
}

function GoogleMapComponent({ latitude, longitude, selectedLocation, setColorPrimary }: Props) {
    const {isLoaded} = useJsApiLoader({
        id: 'google-map-script',
        googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string,
    })
    
    const [map, setMap] = React.useState<google.maps.Map | null>(null);
    const [directions, setDirections] = useState<google.maps.DirectionsResult[]>([]);
    const [routeEndPoints, setRouteEndPoints] = useState<{lat: number; lng: number; color: string}[]>([]);
    
    // Array of different colors for routes
    const routeColors = ['#0ac5b2', '#FF5733', '#C70039', '#900C3F', '#581845', '#FFC300', '#DAF7A6', '#33C7FF', '#4287f5', '#8e44ad', '#16a085', '#2ecc71', '#f1c40f', '#e67e22', '#e74c3c', '#7f8c8d', '#9b59b6', '#3498db', '#1abc9c', '#2980b9', '#27ae60', '#f39c12', '#d35400', '#c0392b'];

    const updateCenter = useCallback((newLat: number, newLng: number) => {
		if (map) {
			const newCenter = new google.maps.LatLng(newLat, newLng)
			map.setCenter(newCenter);
		}
	}, [map, selectedLocation]);

	const onLoad = useCallback(function callback(map: google.maps.Map) {
		setMap(map);
	}, []);

	const onUnmount = useCallback(function callback(map: google.maps.Map) {
		setMap(null);
	}, []);

	const center = {
		lat: latitude,
		lng: longitude
	}

	useEffect(() => {
		if (latitude && longitude) {
			updateCenter(latitude, longitude)
		}
	}, [map, latitude, longitude, updateCenter]);


    const updateMapBounds = (map: google.maps.Map | null, locations: any[], zoom = 14) => {
		if (!map || locations.length === 0) return;
	
		const bounds = new google.maps.LatLngBounds();
		locations.forEach(({ lat, lon, latitude, longitude }) => {
			if (lat && lon) {
				bounds.extend(new google.maps.LatLng(lat, lon));
			} else if (latitude && longitude) {
				bounds.extend(new google.maps.LatLng(latitude, longitude));
			}
		});
	
		if (locations.length === 1) {
			map.setZoom(zoom);
			map.setCenter(bounds.getCenter());
		} else {
			map.fitBounds(bounds);
		}
	};
	
	useEffect(() => {
		const locations = selectedLocation.length
			? [
				{
					lat: latitude,
					lon: longitude
				},
				...selectedLocation.map(item => ({ lat: item.latitude, lon: item.longitude }))
			]
			: [];
	
		//updateMapBounds(map, locations);
	}, [map, selectedLocation]);

    useEffect(() => {
        if (!isLoaded) return;
        
        const fetchDirections = async () => {
            const directionsService = new google.maps.DirectionsService();
            const origin = { lat: latitude, lng: longitude };
            
            const results = await Promise.all(
                selectedLocation.map((location) => {
                    return new Promise<google.maps.DirectionsResult | null>((resolve) => {
                        directionsService.route(
                            {
                                origin,
                                destination: { lat: location.latitude, lng: location.longitude },
                                travelMode: google.maps.TravelMode.WALKING,
                            },
                            (result, status) => {
                                if (status === google.maps.DirectionsStatus.OK) {
                                    resolve(result);
                                } else {
                                    console.error(`Error fetching directions: ${status}`);
                                    resolve(null);
                                }
                            }
                        );
                    });
                })
            );
            
            const validResults = results.filter(Boolean) as google.maps.DirectionsResult[];
            setDirections(validResults);
            
            // Extract end points with their corresponding colors
            const endPoints = validResults.map((result, index) => {
                const route = result.routes[0];
                const leg = route.legs[0];
                const endLocation = leg.end_location;
                
                return {
                    lat: endLocation.lat(),
                    lng: endLocation.lng(),
                    color: routeColors[index % routeColors.length]
                };
            });
            
            setRouteEndPoints(endPoints);
            
            // Set colors only once when directions change
            const newColors = validResults.map((_, index) => routeColors[index % routeColors.length]);
            setColorPrimary(newColors);
        };
        
        fetchDirections();
    }, [isLoaded, latitude, longitude, selectedLocation]);

    const StyledMarker = createGlobalStyle`
        .marker-place{
            background: #5E48F8;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.25));
            box-sizing: border-box;
        }
        .marker-poi{
            background: #0ac5b2;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.25));
            box-sizing: border-box;
        }
        ${routeColors.map((color, index) => `
            .route-end-point-${index} {
                background-color: ${color};
                width: 20px;
                height: 20px;
                border-radius: 50%;
                filter: drop-shadow(0px 2px 5px rgba(0, 0, 0, 0.3));
                box-sizing: border-box;
            }
        `)}
    `

    return isLoaded ? (
        <>
            <StyledMarker />
            <GoogleMap
                mapContainerStyle={containerStyle}
                center={center}
                zoom={15}
                onLoad={onLoad}
                onUnmount={onUnmount}
                options={{
                    disableDefaultUI: false,
                    zoomControl: true,
                    mapTypeControl: false,
                    scaleControl: false,
                    streetViewControl: true,
                    rotateControl: true,
                    fullscreenControl: true
                }}
            >
                <Marker
                    position={{
                        lat: latitude,
                        lng: longitude,
                    }}
                    icon={{
                        url: ' ',
                        scaledSize: new google.maps.Size(20, 20),
                        anchor: new google.maps.Point(10, 10),
                    }}
                    label={{
                        text: ' ',
                        className: `marker-place`
                    }}
                />
                {/*selectedLocation.map((location, index) => (
                    <Marker
                        key={index}
                        position={{ lat: location.latitude, lng: location.longitude }}
                        icon={{
                            url: ' ',
                            scaledSize: new google.maps.Size(50, 50),
                        }}
                        label={{
                            text: ' ',
                            className: `marker-poi`
                        }}
                    />
                ))*/}
                
                {directions.map((direction, index) => {
                    const routeColor = routeColors[index % routeColors.length];
                    
                    return (
                        <DirectionsRenderer 
                            key={`direction-${index}`}
                            directions={direction}
                            options={{
                                suppressMarkers: true,
                                polylineOptions: {
                                    strokeColor: routeColor,
                                    strokeWeight: 4
                                }
                            }}
                        />
                    )
                })}
                
                {/* Add circle markers at the end of each route */}
                {routeEndPoints.map((point, index) => (
                    <Marker
                        key={`endpoint-${index}`}
                        position={{ lat: point.lat, lng: point.lng }}
                        icon={{
                            url: ' ',
                            scaledSize: new google.maps.Size(16, 16),
                            anchor: new google.maps.Point(8, 8),
                        }}
                        label={{
                            text: ' ',
                            className: `route-end-point-${index % routeColors.length}`
                        }}
                        options={{
                            zIndex: 1000 + index // Ensure endpoint circles appear above the route lines
                        }}
                    />
                ))}
            </GoogleMap>
        </>
    ) :
    <div className="skeleton"></div>
}

export default React.memo(GoogleMapComponent);
