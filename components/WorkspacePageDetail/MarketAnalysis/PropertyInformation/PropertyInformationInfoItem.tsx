import { faCheck, faPen, faTimes } from "@fortawesome/free-solid-svg-icons";
import { faBed } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import { useState } from "react";
import { useDebounce } from "@/helpers/hooks/useDebounce";
import { updatePropertyDetails } from "@/actions/propertyDetailsActions";
import { useSearchParams } from "next/navigation";


interface Props {
    icon: IconDefinition
    label: string
    keyItem: string
    value: string | number | undefined
    prefix?: string
    prefixPosition?: 'left' | 'right'
    inputType?: 'number' | 'select' | 'date'
    format?: 'price' | 'date'
}

export default function PropertyInformationInfoItem({ icon, label, value, prefix, prefixPosition, inputType = 'number', keyItem, format }: Props) {
    const [isEditing, setIsEditing] = useState(false);
    const [editedValue, setEditedValue] = useState(value);
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')

    const handleUpdateUnit = useDebounce(async (value: string | number | undefined) => {
        await updatePropertyDetails(addressId as string, { [keyItem]: value })
    }, 500)

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEditedValue(e.target.value);
        handleUpdateUnit(e.target.value)
    }

    return(
        <>
            <div className="group relative">
                <div className="flex items-center gap-2 mb-1">
                    <FontAwesomeIcon icon={icon} className="h-4 w-4 text-indigo-500" />
                    <h4 className="text-xs font-medium text-gray-500">{label}</h4>
                </div>
                {
                    !isEditing ?
                        <div className="flex items-center">
                            <p className="text-base font-medium pr-2 text-gray-900">
                                {editedValue 
                                    ? (format === 'price'
                                        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(editedValue))
                                        : format === 'date'
                                            ? (() => {
                                                try {
                                                  return new Date(String(editedValue)).toISOString().split('T')[0];
                                                } catch (e) {
                                                  return String(editedValue);
                                                }
                                              })()
                                            : (prefix 
                                                ? (prefixPosition === 'left' 
                                                    ? `${prefix} ${editedValue}` 
                                                    : `${editedValue} ${prefix}`)
                                                : editedValue))
                                    : 'N/A'}
                            </p>
                            {
                                addressId ? <FontAwesomeIcon icon={faPen} onClick={() => setIsEditing(true)} className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-indigo-600" /> : null
                            }
                        </div>
                        :
                        <div className="flex items-center">
                            {
                                inputType === 'number' ?
                                    <input 
                                        type="number" 
                                        value={editedValue || value} 
                                        onChange={handleChange}
                                        className="text-base w-full border-b border-gray-300 focus:border-indigo-500 focus:ring-0 py-1 px-0 outline-none"
                                    />  
                                :
                                    inputType === 'select' ?
                                        <select
                                            value={editedValue || value}
                                            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleChange(e as any)}
                                            className="text-base w-full border-b border-gray-300 focus:border-indigo-500 focus:ring-0 py-1 px-0 outline-none"
                                        >
                                            <option value="Yes">Yes</option>
                                            <option value="No">No</option>
                                        </select>
                                :
                                    inputType === 'date' ?
                                        <input 
                                            type="date" 
                                            value={editedValue || value} 
                                            onChange={handleChange}
                                            className="text-base w-full border-b border-gray-300 focus:border-indigo-500 focus:ring-0 py-1 px-0 outline-none"
                                        />  : null
                            }
                            <div className="flex ml-2">
                            <button
                                onClick={() => setIsEditing(false)}
                                className="text-green-600 hover:text-green-800 mr-1"
                                title="Save"
                            >
                                <FontAwesomeIcon icon={faCheck} className="h-4 w-4" />
                            </button>
                            <button
                                onClick={() => setIsEditing(false)}
                                className="text-red-600 hover:text-red-800"
                                title="Cancel"
                            >
                                <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
                            </button>
                            </div>
                        </div>
                }
            </div>
        </>
    )
}