import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import type React from "react";
import PropertyImages from "../PropertyImages";
import GoogleMapStreetView from "../GoogleMapStreetView";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBath, faBed, faCalendarAlt, faDirections, faFaucet, faHandHoldingDollar, faHouseChimney, faPen, faRulerCombined, faFire, faParking, faWarehouse, faHouseChimneyWindow, faXmark, faImages, faPlus, faMagicWandSparkles, faSpinner, faCheck, faStar, faChevronLeft, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { useState, useEffect } from "react";
import PropertyInformationInfoItem from "./PropertyInformationInfoItem";
import { createClient } from "@/utils/supabase/client";
import { fetchPropertyDetails } from "@/actions/propertyActions";
import { useSearchParams } from "next/navigation";
import Spinner from "@/components/UI/Spinner";
import ModalBase from "@/components/UI/ModalBase";
// import ImageUploadModal from "../ImageUploadModal"; // Assuming you have a modal component for image upload

export default function PropertyInformation() {
    const { 
        propertyDetails, 
        isLoadingPropertyDetails, 
        searchPlace, 
        updateMarketAnalysisState,
        allImageUrls
    } = useMarketAnalysis();
    const [activeTab, setActiveTab] = useState<'basic' | 'features' | 'construction'>('basic');
    const [isRefreshing, setIsRefreshing] = useState(false);
    const searchParams = useSearchParams();
    const addressId = searchParams.get('addressId');
    const [isChatExpanded, setIsChatExpanded] = useState(false);
    const [imageTab, setImageTab] = useState<'streetView' | 'images'>('streetView');
    const [mainImage, setMainImage] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isLoadingImages, setIsLoadingImages] = useState(false);
    const [propertyImages, setPropertyImages] = useState<string[]>([]);
    const [isSearchingImages, setIsSearchingImages] = useState(false);
    const [showAgenticImageResults, setShowAgenticImageResults] = useState(false);
    const [agenticImageResults, setAgenticImageResults] = useState<any>(null);
    const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
    const [showUploadModal, setShowUploadModal] = useState(false);

    // Check for chat state using body class
    useEffect(() => {
        // Initial check
        checkChatState();
        
        // Set up a mutation observer to watch for class changes on the body
        const observer = new MutationObserver(checkChatState);
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'] 
        });
        
        // Also check on window resize
        window.addEventListener('resize', checkChatState);
        
        return () => {
            observer.disconnect();
            window.removeEventListener('resize', checkChatState);
        };
    }, []);
    
    // Function to check if chat is expanded
    const checkChatState = () => {
        const isChatExpandedNow = document.body.classList.contains('chat-expanded') || window.innerWidth < 1280;
        if (isChatExpandedNow !== isChatExpanded) {
            setIsChatExpanded(isChatExpandedNow);
        }
    };

    const addressSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('street_number'))?.long_name + ' ' + searchPlace?.address_components?.find((component: any) => component.types.includes('route'))?.long_name
    const displayAddressSearch = typeof searchPlace === 'object' && searchPlace?.formatted_address
    const citySearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('locality'))?.long_name
    const stateSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('administrative_area_level_1'))?.short_name
    const zipcodeSearch = typeof searchPlace === 'object' && searchPlace?.address_components?.find((component: any) => component.types.includes('postal_code'))?.short_name
    const latSearch = typeof searchPlace === 'object' && searchPlace?.geometry?.location?.lat()
    const lonSearch = typeof searchPlace === 'object' && searchPlace?.geometry?.location?.lng()

    const displayAddress = propertyDetails?.address?.display_address || displayAddressSearch
    const address = propertyDetails?.address?.street_address || addressSearch
    const city = propertyDetails?.address?.city || citySearch
    const state = propertyDetails?.address?.state || stateSearch
    const zipcode = propertyDetails?.address?.zip || zipcodeSearch
    const lat = propertyDetails?.address?.latitude || latSearch
    const lon = propertyDetails?.address?.longitude || lonSearch

    const getGoogleMapsUrl = () => {
        const encodedAddress = encodeURIComponent(`${address}, ${city}, ${state} ${zipcode}`);
        return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
    };

    const handleRefresh = async () => {
        setIsRefreshing(true)
        updateMarketAnalysisState({ isLoadingPropertyDetails: true })

        const supabase = createClient();
        const {data: propAddressData, error: propAddressError} = await supabase.from('prop_addresses').select('*').eq('id', addressId as string).single();
        const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId as string).single();

        const propertyDetails = await fetchPropertyDetails(propAddressData?.address as string, propAddressData?.city as string, propAddressData?.state as string, propAddressData?.zip as string)
        
        updateMarketAnalysisState({ propertyDetails, isLoadingPropertyDetails: false })

        const {data: newPropertyDetails, error: newPropertyDetailsError} = await supabase
            .from('prop_details')
            .update({
                data: propertyDetails?.data
            }).eq('prop_id', propData.id);
            
        setIsRefreshing(false);
    }

    const handleImageTabChange = (tab: 'streetView' | 'images') => {
        setImageTab(tab);
    };

    const handleOpenModal = (index: number = 0) => {
        setSelectedImageIndex(index);
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedImageIndex(0);
    };

    const handleSetMainImage = async (imageUrl: string) => {
        setMainImage(imageUrl);
        handleCloseModal();
        
        // Update the main image in the database
        if (addressId) {
            try {
                const supabase = createClient();
                
                // Get the property record first
                const { data: propData, error: propError } = await supabase
                    .from('prop')
                    .select('id')
                    .eq('address_id', addressId)
                    .single();
                
                if (propError) {
                    console.error('Error finding property:', propError);
                    return;
                }
                
                // Update the main_img_url field
                const { error: updateError } = await supabase
                    .from('prop')
                    .update({ main_img_url: imageUrl })
                    .eq('id', propData.id);
                
                if (updateError) {
                    console.error('Error updating main image:', updateError);
                } else {
                    console.log('Main image updated successfully');
                }
            } catch (error) {
                console.error('Error updating main image:', error);
            }
        }
    };

    // Load property images when component mounts or when allImageUrls changes
    useEffect(() => {
        if (allImageUrls && allImageUrls.length > 0) {
            setPropertyImages(allImageUrls);
        }
    }, [allImageUrls]);

    // Load existing main image from database
    useEffect(() => {
        const loadMainImage = async () => {
            if (addressId) {
                try {
                    const supabase = createClient();
                    
                    // Get the property record with main image
                    const { data: propData, error: propError } = await supabase
                        .from('prop')
                        .select('main_img_url')
                        .eq('address_id', addressId)
                        .single();
                    
                    if (!propError && propData?.main_img_url) {
                        setMainImage(propData.main_img_url);
                    }
                } catch (error) {
                    console.error('Error loading main image:', error);
                }
            }
        };

        loadMainImage();
    }, [addressId]);

    const loadMoreImages = async () => {
        if (!address) return;
        
        setIsLoadingImages(true);
        try {
            // Call the property images API to get more images
            const response = await fetch(`/api/property-images?address=${encodeURIComponent(address)}&city=${encodeURIComponent(city || '')}&state=${encodeURIComponent(state || '')}&zipCode=${encodeURIComponent(zipcode || '')}&count=10`);
            
            if (response.ok) {
                const data = await response.json();
                if (data.images && data.images.length > 0) {
                    const newImages = data.images.map((img: any) => img.image || img.thumbnail).filter(Boolean);
                    setPropertyImages(prev => [...prev, ...newImages]);
                    updateMarketAnalysisState({ allImageUrls: [...propertyImages, ...newImages] });
                }
            }
        } catch (error) {
            console.error('Error loading more images:', error);
        } finally {
            setIsLoadingImages(false);
        }
    };

    const handleAgenticImageSearch = async () => {
        if (!address) return;
        
        setIsSearchingImages(true);
        
        try {
            const response = await fetch('/api/agentic-image-search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address,
                    unit: '',
                    searchType: 'building_exterior' // Specify this is for building exterior images
                })
            });

            if (!response.ok) {
                throw new Error('Image search failed');
            }

            const results = await response.json();
            setAgenticImageResults(results);
            setShowAgenticImageResults(true);
            
        } catch (error) {
            console.error('Agentic image search failed:', error);
            alert('Unable to search for images at this time. Please try again later.');
        } finally {
            setIsSearchingImages(false);
        }
    };

    const handleAcceptAgenticImages = async () => {
        if (!agenticImageResults || !agenticImageResults.data) return;
        
        try {
            // Add the agentic images to existing images
            const selectedImageUrls = agenticImageResults.data
                .filter((img: any) => img.selected)
                .map((img: any) => img.url);
            
            if (selectedImageUrls.length > 0) {
                setPropertyImages(prev => [...prev, ...selectedImageUrls]);
                updateMarketAnalysisState({ allImageUrls: [...propertyImages, ...selectedImageUrls] });
            }
            
            setShowAgenticImageResults(false);
            setAgenticImageResults(null);
        } catch (error) {
            console.error('Failed to save agentic images:', error);
            alert('Failed to save the images. Please try again.');
        }
    };

    const handleRejectAgenticImages = () => {
        setShowAgenticImageResults(false);
        setAgenticImageResults(null);
    };

    const toggleImageSelection = (index: number) => {
        if (!agenticImageResults) return;
        
        const updatedResults = {
            ...agenticImageResults,
            data: agenticImageResults.data.map((img: any, i: number) => 
                i === index ? { ...img, selected: !img.selected } : img
            )
        };
        
        setAgenticImageResults(updatedResults);
    };
    
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faHouseChimney} className="h-4 w-4 text-indigo-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Building Information</h3>
                    </div>
                    {addressId && !isLoadingPropertyDetails && (
                        <button 
                            onClick={handleRefresh}
                            disabled={isRefreshing}
                            className="cursor-pointer rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                            {isRefreshing ? (
                                <div className="flex items-center gap-x-1">
                                    <Spinner size="sm" />
                                    Refreshing...
                                </div>
                            ) : 'Refresh'}
                        </button>
                    )}
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
                {/* Hidden PropertyImages component to load initial images */}
                {address && !allImageUrls && (
                    <div className="hidden">
                        <PropertyImages 
                            key={`images-${address}`}
                            address={address as string}
                            city={city}
                            state={state}
                            zipCode={zipcode}
                            isLoading={isLoadingPropertyDetails}
                        />
                    </div>
                )}

                <div className="flex flex-col md:flex-row gap-6">
                    <div className="flex-1">
                        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-6">
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Property Address</h4>
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                {
                                    address && (
                                        <div>
                                            <p className="text-base font-medium text-gray-900 mb-1">{address}</p>
                                            <p className="text-sm text-gray-600">{city ? `${city}, ` : ''}{state ? `${state} ` : ''}{zipcode ? `${zipcode}` : ''}</p>
                                        </div>
                                    )
                                }
                                <a 
                                href={getGoogleMapsUrl()} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="mt-2 sm:mt-0 inline-flex items-center gap-2 text-xs px-3 py-1.5 text-indigo-600 bg-white border border-indigo-200 rounded-full hover:bg-indigo-50 transition-colors"
                                >
                                <FontAwesomeIcon icon={faDirections} className="h-3 w-3" />
                                <span>Get Directions</span>
                                </a>
                            </div>
                        </div>

                        <div className="mb-6">
                            <div className="border-b border-gray-200 flex flex-wrap items-center justify-between">
                                <nav className="flex flex-wrap -mb-px">
                                    <button
                                        onClick={() => setActiveTab('basic')}
                                        className={`py-2 px-3 md:px-4 text-sm font-medium border-b-2 cursor-pointer ${
                                        activeTab === 'basic'
                                            ? 'border-indigo-500 text-indigo-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        Basic Info
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('features')}
                                        className={`py-2 px-3 md:px-4 text-sm font-medium border-b-2 cursor-pointer ${
                                        activeTab === 'features'
                                            ? 'border-indigo-500 text-indigo-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        Features
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('construction')}
                                        className={`py-2 px-3 md:px-4 text-sm font-medium border-b-2 cursor-pointer ${
                                        activeTab === 'construction'
                                            ? 'border-indigo-500 text-indigo-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        Construction
                                    </button>
                                </nav>
                            </div>
                        </div>

                        {
                            !isLoadingPropertyDetails ?
                                <div>
                                    {/* Basic Info Tab */}
                                    {activeTab === 'basic' && (
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <PropertyInformationInfoItem icon={faBed} label="Beds" keyItem="bed_count" value={propertyDetails?.data?.bed_count} />
                                            <PropertyInformationInfoItem icon={faFaucet} label="Baths" keyItem="bath_count" value={propertyDetails?.data?.bath_count} />
                                            <PropertyInformationInfoItem icon={faRulerCombined} label="Square Feet" keyItem="gross_sq_ft" prefix="sq ft" prefixPosition="right" value={propertyDetails?.data?.gross_sq_ft} />
                                            <PropertyInformationInfoItem icon={faRulerCombined} label="Lot Size" keyItem="lot_size_acre" prefix="acres" prefixPosition="right" value={propertyDetails?.data?.lot_size_acre} />
                                            <PropertyInformationInfoItem icon={faCalendarAlt} label="Year Built" keyItem="year_built" value={propertyDetails?.data?.year_built} />
                                            <PropertyInformationInfoItem icon={faHouseChimney} label="Property Type" keyItem="property_use_code_mapped" value={propertyDetails?.data?.property_use_code_mapped} />
                                            <PropertyInformationInfoItem icon={faCalendarAlt} label="Last Sale Date" format="date" keyItem="last_sale_date" inputType="date" value={propertyDetails?.data?.last_sale_date ? new Date(String(propertyDetails.data.last_sale_date)).toLocaleDateString() : 'N/A'} />
                                            <PropertyInformationInfoItem icon={faHandHoldingDollar} label="Last Sale Amount" format="price" keyItem="last_sale_amount" value={propertyDetails?.data?.last_sale_amount} />
                                        </div>
                                    )}
                                    
                                    {/* Features Tab */}
                                    {activeTab === 'features' && (
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <PropertyInformationInfoItem icon={faFire} label="Heating" keyItem="hvacc_heating_code" value={propertyDetails?.data?.hvacc_heating_code} />
                                            <PropertyInformationInfoItem icon={faFire} label="Cooling" keyItem="hvacc_cooling_code" value={propertyDetails?.data?.hvacc_cooling_code} />
                                            <PropertyInformationInfoItem icon={faParking} label="Parking Type" keyItem="parking_garage_code" value={propertyDetails?.data?.parking_garage_code} />
                                            <PropertyInformationInfoItem icon={faParking} label="Parking Spaces" keyItem="parking_space_count" value={propertyDetails?.data?.parking_space_count} />
                                            <PropertyInformationInfoItem icon={faWarehouse} label="Basement Area" keyItem="basement_sq_ft" prefix="sq ft" prefixPosition="right" value={propertyDetails?.data?.basement_sq_ft} />
                                            <PropertyInformationInfoItem icon={faWarehouse} label="Finished Basement" keyItem="basement_finished_sq_ft" prefix="sq ft" prefixPosition="right" value={propertyDetails?.data?.basement_finished_sq_ft} />
                                            <PropertyInformationInfoItem icon={faWarehouse} label="Unfinished Basement" keyItem="basement_unfinished_sq_ft" prefix="sq ft" prefixPosition="right" value={propertyDetails?.data?.basement_unfinished_sq_ft} />
                                            <PropertyInformationInfoItem icon={faHouseChimneyWindow} label="Flooring" keyItem="flooring_material_code" value={propertyDetails?.data?.flooring_material_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimneyWindow} label="Laundry Room" keyItem="has_laundry_room" inputType="select" value={propertyDetails?.data?.has_laundry_room ? 'Yes' : 'No'} />
                                            <PropertyInformationInfoItem icon={faFire} label="Fireplaces" keyItem="fireplace_count" value={propertyDetails?.data?.fireplace_count} />
                                        </div>
                                    )}
                                    
                                    {/* Construction Tab */}
                                    {activeTab === 'construction' && (
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <PropertyInformationInfoItem icon={faHouseChimney} label="Structure Style" keyItem="structure_style_code" value={propertyDetails?.data?.structure_style_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimneyWindow} label="Roof Material" keyItem="roof_material_code" value={propertyDetails?.data?.roof_material_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimneyWindow} label="Roof Construction" keyItem="roof_construction_code" value={propertyDetails?.data?.roof_construction_code} />
                                            <PropertyInformationInfoItem icon={faParking} label="Driveway Material" keyItem="driveway_material_code" value={propertyDetails?.data?.driveway_material_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimney} label="Construction Type" keyItem="construction_code" value={propertyDetails?.data?.construction_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimneyWindow} label="Exterior" keyItem="exterior_code" value={propertyDetails?.data?.exterior_code} />
                                            <PropertyInformationInfoItem icon={faHouseChimney} label="Sewer" keyItem="sewer_usage_code" value={propertyDetails?.data?.sewer_usage_code} />
                                            <PropertyInformationInfoItem icon={faFaucet} label="Water Source" keyItem="water_source_code" value={propertyDetails?.data?.water_source_code} />
                                        </div>
                                    )}
                                </div>
                                :
                                <div className="flex flex-col items-center justify-center py-6">
                                    <Spinner size="lg" text="Loading property data..." />
                                </div>
                        }
                    </div>
                    <div className="flex-1">
                        {/* Image/Street View Tabs - Outside the main container */}
                        <div className="mb-2">
                            <div className="flex justify-center bg-white rounded-lg border border-gray-200 shadow-sm p-1">
                                <button
                                    onClick={() => handleImageTabChange('streetView')}
                                    className={`flex-1 py-2 px-4 text-sm font-medium transition-all duration-200 rounded-md ${
                                        imageTab === 'streetView' 
                                            ? 'text-white bg-indigo-600 shadow-sm' 
                                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                                    }`}
                                >
                                    Google StreetView
                                </button>
                                <button
                                    onClick={() => handleImageTabChange('images')}
                                    className={`flex-1 py-2 px-4 text-sm font-medium transition-all duration-200 rounded-md ${
                                        imageTab === 'images' 
                                            ? 'text-white bg-indigo-600 shadow-sm' 
                                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                                    }`}
                                >
                                    Images ({propertyImages.length})
                                </button>
                            </div>
                        </div>
                        
                        {/* Main Content Area */}
                        <div className="relative w-full min-h-[350px] rounded-lg overflow-hidden bg-black">
                            
                            {imageTab === 'streetView' && (
                                <div className="h-full">
                                    {lat && lon ? (
                                        <GoogleMapStreetView 
                                            location={{ latitude: lat, longitude: lon }}
                                        />
                                    ) : (
                                        <div className="h-full flex items-center justify-center bg-gray-900 text-white">
                                            <div className="text-center">
                                                <p className="mb-2">Street View not available</p>
                                                <p className="text-sm text-gray-400">Location coordinates not found</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                            
                            {imageTab === 'images' && (
                                <div className="h-[350px]">
                                    {propertyImages.length > 0 ? (
                                        <div className="h-full relative">
                                            <img 
                                                src={mainImage || propertyImages[0]} 
                                                alt="Property" 
                                                className="h-full w-full object-cover cursor-pointer"
                                                style={{ objectFit: 'cover' }}
                                                onClick={() => {
                                                    const idx = mainImage ? propertyImages.findIndex((img) => img === mainImage) : 0;
                                                    handleOpenModal(idx >= 0 ? idx : 0);
                                                }}
                                                onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    target.style.display = 'none';
                                                }}
                                            />
                                            <div className="absolute bottom-4 right-4 flex gap-2">
                                                <button
                                                    onClick={() => {
                                                        const idx = mainImage ? propertyImages.findIndex((img) => img === mainImage) : 0;
                                                        handleOpenModal(idx >= 0 ? idx : 0);
                                                    }}
                                                    className="px-3 py-2 bg-white/90 backdrop-blur-sm text-gray-800 rounded-lg hover:bg-white transition-colors flex items-center gap-2"
                                                >
                                                    <FontAwesomeIcon icon={faImages} className="h-4 w-4" />
                                                    View All ({propertyImages.length})
                                                </button>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="h-full flex flex-col items-center justify-center bg-gray-900 text-white">
                                            <FontAwesomeIcon icon={faImages} className="h-12 w-12 text-gray-400 mb-4" />
                                            <p className="mb-4">No images available</p>
                                            <div className="flex gap-2">
                                                <button
                                                    onClick={handleAgenticImageSearch}
                                                    disabled={isSearchingImages}
                                                    className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                                >
                                                    {isSearchingImages ? (
                                                        <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                                    )}
                                                    {isSearchingImages ? 'Searching...' : 'Find Images'}
                                                </button>
                                                <button
                                                    onClick={loadMoreImages}
                                                    disabled={isLoadingImages}
                                                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                                                >
                                                    {isLoadingImages ? (
                                                        <Spinner size="sm" />
                                                    ) : (
                                                        <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                    )}
                                                    {isLoadingImages ? 'Loading...' : 'Load Images'}
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            
                    {/* Image Gallery Modal */}
        {isModalOpen && propertyImages.length > 0 && (
            <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
                <div className="relative w-full max-w-6xl h-full max-h-[90vh] bg-white rounded-lg overflow-hidden flex flex-col">
                    {/* Modal Header */}
                    <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between flex-shrink-0">
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Property Images</h3>
                            <p className="text-sm text-gray-500">Select a main image or upload new ones</p>
                        </div>
                        <button
                            onClick={handleCloseModal}
                            className="text-gray-400 hover:text-gray-600 p-2"
                        >
                            <FontAwesomeIcon icon={faXmark} className="h-5 w-5" />
                        </button>
                    </div>

                    {/* Modal Content */}
                    <div className="flex flex-1 min-h-0">
                        {/* Main Image Display */}
                        <div className="flex-1 bg-gray-100 flex items-center justify-center p-6">
                            <div className="relative max-w-full max-h-full">
                                <img
                                    src={propertyImages[selectedImageIndex]}
                                    alt={`Property image ${selectedImageIndex + 1}`}
                                    className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                                />
                                
                                {/* Navigation arrows */}
                                {propertyImages.length > 1 && (
                                    <>
                                        <button
                                            onClick={() => setSelectedImageIndex(prev => prev > 0 ? prev - 1 : propertyImages.length - 1)}
                                            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-colors"
                                        >
                                            <FontAwesomeIcon icon={faChevronLeft} className="h-4 w-4" />
                                        </button>
                                        <button
                                            onClick={() => setSelectedImageIndex(prev => prev < propertyImages.length - 1 ? prev + 1 : 0)}
                                            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-colors"
                                        >
                                            <FontAwesomeIcon icon={faChevronRight} className="h-4 w-4" />
                                        </button>
                                    </>
                                )}
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="w-80 bg-white border-l border-gray-200 flex flex-col flex-shrink-0">
                            {/* Actions */}
                            <div className="p-4 border-b border-gray-200 flex-shrink-0">
                                <div className="flex flex-col gap-2">
                                    <button
                                        onClick={() => handleSetMainImage(propertyImages[selectedImageIndex])}
                                        className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
                                    >
                                        <FontAwesomeIcon icon={faStar} className="h-4 w-4" />
                                        Set as Main Image
                                    </button>
                                    <button
                                        onClick={() => {
                                            // This could trigger a different upload modal or direct file input
                                            console.log('Upload functionality would go here');
                                        }}
                                        className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                                    >
                                        <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                        Upload New Images
                                    </button>
                                </div>
                            </div>

                            {/* Thumbnails */}
                            <div className="flex-1 overflow-y-auto p-4">
                                <h4 className="text-sm font-medium text-gray-700 mb-3">All Images ({propertyImages.length})</h4>
                                <div className="grid grid-cols-2 gap-2">
                                    {propertyImages.map((image, index) => (
                                        <div
                                            key={index}
                                            className={`aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-all relative ${
                                                index === selectedImageIndex ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200 hover:border-indigo-300'
                                            }`}
                                            onClick={() => setSelectedImageIndex(index)}
                                        >
                                            <img
                                                src={image}
                                                alt={`Property thumbnail ${index + 1}`}
                                                className="w-full h-full object-cover"
                                            />
                                            {mainImage === image && (
                                                <div className="absolute inset-0 bg-indigo-500/20 flex items-center justify-center">
                                                    <div className="bg-indigo-500 text-white rounded-full p-1">
                                                        <FontAwesomeIcon icon={faStar} className="h-3 w-3" />
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )}

            {/* Agentic Image Search Results Modal */}
            {showAgenticImageResults && agenticImageResults && (
                <ModalBase
                    isOpen={showAgenticImageResults}
                    onClose={handleRejectAgenticImages}
                    title="AI Found Property Images"
                    subtitle="Select images to add to your property"
                    icon={faMagicWandSparkles}
                    iconBgColor="bg-purple-100"
                    iconColor="text-purple-600"
                    maxWidth="max-w-4xl"
                    actions={
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={handleRejectAgenticImages}
                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleAcceptAgenticImages}
                                disabled={!agenticImageResults.data?.some((img: any) => img.selected)}
                                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faCheck} className="h-4 w-4" />
                                Add Selected Images ({agenticImageResults.data?.filter((img: any) => img.selected).length || 0})
                            </button>
                        </div>
                    }
                >
                    {/* Search Query */}
                    {agenticImageResults.searchQuery && (
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-gray-700 mb-2">Search Query Used:</h4>
                            <p className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
                                {agenticImageResults.searchQuery}
                            </p>
                        </div>
                    )}

                    {/* OpenAI Analysis */}
                    {agenticImageResults.openaiAnalysis && (
                        <div className="bg-blue-50 rounded-lg p-4 mb-6">
                            <h4 className="text-sm font-semibold text-blue-700 mb-2 flex items-center gap-2">
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                                AI Analysis & Pre-selection:
                            </h4>
                            <p className="text-sm text-blue-800 bg-white p-3 rounded border">
                                {agenticImageResults.openaiAnalysis}
                            </p>
                        </div>
                    )}

                    {/* Found Images */}
                    <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4">
                            Found Images ({agenticImageResults.data?.length || 0}):
                        </h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {agenticImageResults.data?.map((image: any, index: number) => (
                                <div key={index} className="relative group">
                                    <div 
                                        className={`aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 cursor-pointer transition-all ${
                                            image.selected ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200 hover:border-indigo-300'
                                        }`}
                                        onClick={() => toggleImageSelection(index)}
                                    >
                                        <img 
                                            src={image.url} 
                                            alt={`Property image ${index + 1}`}
                                            className="w-full h-full object-cover"
                                            onError={(e) => {
                                                (e.target as HTMLImageElement).src = '/default-home-list-img.svg';
                                            }}
                                        />
                                        
                                        {/* Selection overlay */}
                                        {image.selected && (
                                            <div className="absolute inset-0 bg-indigo-500/20 flex items-center justify-center">
                                                <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                                    <FontAwesomeIcon icon={faCheck} className="text-white h-4 w-4" />
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    
                                    {/* Image info */}
                                    {image.description && (
                                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{image.description}</p>
                                    )}
                                    {image.source && (
                                        <p className="text-xs text-gray-400">{image.source}</p>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </ModalBase>
            )}
        </div>
    );
}
