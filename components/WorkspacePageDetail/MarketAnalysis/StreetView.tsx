import GoogleMapStreetView from "./GoogleMapStreetView";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useState } from "react";

export default function StreetView() {
    const { searchPlace } = useMarketAnalysis();
    const [showStreetView, setShowStreetView] = useState(false);
    return (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
            {
                typeof searchPlace !== 'string' && (
                    <div>
                        <div className="flex items-center justify-between cursor-pointer hover:bg-gray-50 rounded-lg p-2 mb-2" onClick={() => setShowStreetView(!showStreetView)}>
                            <div>
                                <h3 className="text-lg font-medium text-gray-800">Street View</h3>
                            </div>
                            <div>
                                {!showStreetView ? (
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                ) : (
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                                    </svg>
                                )}
                            </div>
                        </div>
                        {
                            showStreetView && (
                                <div className="relative w-full h-[600px] rounded-lg overflow-hidden">
                                    <GoogleMapStreetView location={{latitude: searchPlace?.geometry.location.lat(), longitude: searchPlace?.geometry.location.lng()}} />
                                </div>
                            )
                        }
                    </div>
                )
            }
        </div>
    )
}