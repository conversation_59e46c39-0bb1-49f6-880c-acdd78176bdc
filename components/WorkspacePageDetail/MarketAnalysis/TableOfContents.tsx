'use client';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
    faBuilding, 
    faClipboardList, 
    faMapMarkerAlt, 
    faHome, 
    faSun, 
    faUsers, 
    faMap,
    faLightbulb,
    faArrowUp,
    faArrowDown,
    faMinus
} from "@fortawesome/free-solid-svg-icons";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import { useState, useEffect } from "react";

// Define types for section IDs and nav items
type SectionId = 'building' | 'tax-history' | 'street-view' | 'units' | 'solar' | 'locations' | 'map' | 'ai-summary';
type PositionType = 'top' | 'middle' | 'bottom';

interface NavItem {
    id: SectionId;
    label: string;
    icon: IconDefinition;
}

interface TableOfContentsProps {
    activeSection: SectionId;
    onNavigate: (sectionId: SectionId) => void;
}

export default function TableOfContents({ activeSection, onNavigate }: TableOfContentsProps) {
    const [position, setPosition] = useState<PositionType>('middle');

    // Navigation items with icons matching the parent containers
    const navItems: NavItem[] = [
        { id: 'building', label: 'Building Information', icon: faHome },
        { id: 'ai-summary', label: 'AI Summary', icon: faLightbulb},
        { id: 'tax-history', label: 'Tax History', icon: faClipboardList },
        { id: 'units', label: 'Building Units', icon: faBuilding },
        { id: 'solar', label: 'Solar & Demographics', icon: faSun },
        { id: 'locations', label: 'Nearby Locations', icon: faMapMarkerAlt }
    ];

    // Get position classes based on current position
    const getPositionClasses = () => {
        switch (position) {
            case 'top':
                return 'fixed left-4 top-24 z-40';
            case 'bottom':
                return 'fixed left-4 bottom-8 z-40';
            case 'middle':
            default:
                return 'fixed left-4 top-1/2 transform -translate-y-1/2 z-40';
        }
    };

    // Set specific position
    const setSpecificPosition = (newPosition: PositionType) => {
        setPosition(newPosition);
    };

    // Get position icon
    const getPositionIcon = () => {
        switch (position) {
            case 'top': return faArrowUp;
            case 'bottom': return faArrowDown;
            case 'middle': return faMinus;
            default: return faMinus;
        }
    };

    // Get position label
    const getPositionLabel = () => {
        switch (position) {
            case 'top': return 'Top Position';
            case 'bottom': return 'Bottom Position';
            case 'middle': return 'Middle Position';
            default: return 'Middle Position';
        }
    };

    return (
        <div className={getPositionClasses()}>
            <div className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-lg py-4 px-3">
                {/* Position Control */}
                <div className="mb-3 pb-3 border-b border-gray-200">
                    <div className="relative group">
                        {/* Current Position Indicator */}
                        <div className="w-12 h-8 rounded-lg flex items-center justify-center bg-gray-100 text-gray-600 cursor-pointer">
                            <FontAwesomeIcon 
                                icon={getPositionIcon()} 
                                className="h-3 w-3" 
                            />
                        </div>
                        
                        {/* Invisible bridge to prevent hover break */}
                        <div className="absolute left-full w-2 h-full top-0 opacity-0 group-hover:opacity-100"></div>
                        
                        {/* Position Selection Menu */}
                        <div className="absolute left-full ml-2 top-0 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-100 pointer-events-none group-hover:pointer-events-auto z-50">
                            <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 space-y-2 min-w-[100px]">
                                {/* Top Position Button */}
                                <button
                                    onClick={() => setSpecificPosition('top')}
                                    disabled={position === 'top'}
                                    className={`w-full flex items-center justify-start px-3 py-2 rounded-md text-xs font-medium transition-colors duration-200 ${
                                        position === 'top'
                                            ? 'bg-indigo-100 text-indigo-600 cursor-not-allowed opacity-50'
                                            : 'bg-gray-50 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 hover:shadow-sm'
                                    }`}
                                    title="Move to top"
                                >
                                    <FontAwesomeIcon icon={faArrowUp} className="h-3 w-3 mr-2" />
                                    Top
                                </button>
                                
                                {/* Middle Position Button */}
                                <button
                                    onClick={() => setSpecificPosition('middle')}
                                    disabled={position === 'middle'}
                                    className={`w-full flex items-center justify-start px-3 py-2 rounded-md text-xs font-medium transition-colors duration-200 ${
                                        position === 'middle'
                                            ? 'bg-indigo-100 text-indigo-600 cursor-not-allowed opacity-50'
                                            : 'bg-gray-50 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 hover:shadow-sm'
                                    }`}
                                    title="Move to middle"
                                >
                                    <FontAwesomeIcon icon={faMinus} className="h-3 w-3 mr-2" />
                                    Middle
                                </button>
                                
                                {/* Bottom Position Button */}
                                <button
                                    onClick={() => setSpecificPosition('bottom')}
                                    disabled={position === 'bottom'}
                                    className={`w-full flex items-center justify-start px-3 py-2 rounded-md text-xs font-medium transition-colors duration-200 ${
                                        position === 'bottom'
                                            ? 'bg-indigo-100 text-indigo-600 cursor-not-allowed opacity-50'
                                            : 'bg-gray-50 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 hover:shadow-sm'
                                    }`}
                                    title="Move to bottom"
                                >
                                    <FontAwesomeIcon icon={faArrowDown} className="h-3 w-3 mr-2" />
                                    Bottom
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Navigation Items */}
                <div className="space-y-3">
                    {navItems.map((item) => (
                        <div key={item.id} className="relative group">
                            <button
                                onClick={() => onNavigate(item.id)}
                                className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200 ${
                                    activeSection === item.id
                                        ? 'bg-indigo-600 text-white shadow-md scale-110'
                                        : 'bg-gray-50 text-gray-600 hover:bg-indigo-50 hover:text-indigo-600 hover:shadow-sm'
                                }`}
                                title={item.label}
                            >
                                <FontAwesomeIcon 
                                    icon={item.icon} 
                                    className={`h-5 w-5 ${activeSection === item.id ? 'text-white' : ''}`} 
                                />
                            </button>
                            
                            {/* Navigation Tooltip */}
                            <div className="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                <div className="bg-gray-900 text-white text-sm py-2 px-3 rounded-lg whitespace-nowrap shadow-lg">
                                    {item.label}
                                    <div className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-y-4 border-y-transparent border-r-4 border-r-gray-900"></div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
} 