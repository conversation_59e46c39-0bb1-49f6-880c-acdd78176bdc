import { useState, useEffect } from 'react';
import { fetchRealEstateNewsWithSummaries, StateNewsResponse, NewsFilters } from '@/actions/newsActions';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt, faNewspaper, faRobot, faFilter, faChevronDown, faCalendarAlt, faBuilding } from '@fortawesome/free-solid-svg-icons';
import Spinner from '@/components/UI/Spinner';

export default function RealEstateNews() {
    const { selectedStates } = useMarketAnalysis();
    const [newsData, setNewsData] = useState<StateNewsResponse[]>([]);
    const [allArticles, setAllArticles] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [filters, setFilters] = useState<NewsFilters>({});
    const [showFilters, setShowFilters] = useState(false);

    useEffect(() => {
        if (selectedStates.length > 0) {
            resetAndFetchNews();
        }
    }, [selectedStates, filters]);

    const resetAndFetchNews = () => {
        setCurrentPage(1);
        setAllArticles([]);
        setHasMore(true);
        fetchNews(1, true);
    };

    const fetchNews = async (page: number = currentPage, reset: boolean = false) => {
        if (page === 1) {
            setIsLoading(true);
        } else {
            setIsLoadingMore(true);
        }
        setError(null);
        
        try {
            const stateAbbreviations = selectedStates.map(state => state.abbreviation);
            const news = await fetchRealEstateNewsWithSummaries(stateAbbreviations, page, filters);
            
            let newArticles: any[] = [];
            
            if (reset) {
                setNewsData(news);
                newArticles = news.flatMap(stateNews => 
                    stateNews.articles.map(article => ({ ...article, state: stateNews.state }))
                );
                setAllArticles(newArticles);
            } else {
                // Append new articles for pagination
                newArticles = news.flatMap(stateNews => 
                    stateNews.articles.map(article => ({ ...article, state: stateNews.state }))
                );
                setAllArticles(prev => [...prev, ...newArticles]);
            }
            
            // Check if there are more articles to load
            const hasMoreArticles = news.some(stateNews => stateNews.hasMore);
            setHasMore(hasMoreArticles);
            
            if (newArticles.length === 0) {
                setHasMore(false);
            }
        } catch (err) {
            console.error('Error fetching news:', err);
            setError('Failed to fetch real estate news. Please try again later.');
        } finally {
            setIsLoading(false);
            setIsLoadingMore(false);
        }
    };

    const loadMoreNews = () => {
        if (!isLoadingMore && hasMore) {
            const nextPage = currentPage + 1;
            setCurrentPage(nextPage);
            fetchNews(nextPage, false);
        }
    };

    const updateFilters = (newFilters: Partial<NewsFilters>) => {
        setFilters(prev => ({ ...prev, ...newFilters }));
    };

    if (isLoading) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="flex flex-col items-center justify-center py-10">
                    <Spinner size="lg" className="mb-3" />
                    <p className="text-sm text-gray-600">Loading real estate news...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="text-center py-10">
                    <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-4xl mb-4" />
                    <p className="text-red-600 mb-2">{error}</p>
                    <button 
                        onClick={() => resetAndFetchNews()}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    if (selectedStates.length === 0) {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <div className="text-center py-10">
                    <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-4xl mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Real Estate News</h3>
                    <p className="text-gray-600">Select states to view the latest real estate news and market insights.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                        <FontAwesomeIcon icon={faNewspaper} className="text-indigo-600 text-xl" />
                        <h2 className="text-xl font-semibold text-gray-900">
                            Real Estate News
                        </h2>
                        <span className="text-sm text-gray-500">
                            ({selectedStates.length === 1 ? selectedStates[0].name : `${selectedStates.length} states`})
                        </span>
                    </div>
                    
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                        <FontAwesomeIcon icon={faFilter} className="text-xs" />
                        Filters
                        <FontAwesomeIcon 
                            icon={faChevronDown} 
                            className={`text-xs transition-transform ${showFilters ? 'rotate-180' : ''}`} 
                        />
                    </button>
                </div>

                {/* Filters */}
                {showFilters && (
                    <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Date Range Filter */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                                    Date Range
                                </label>
                                <select
                                    value={filters.dateRange || 'month'}
                                    onChange={(e) => updateFilters({ dateRange: e.target.value as any })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                >
                                    <option value="day">Past Day</option>
                                    <option value="week">Past Week</option>
                                    <option value="month">Past Month</option>
                                    <option value="year">Past Year</option>
                                </select>
                            </div>

                            {/* Property Type Filter */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    <FontAwesomeIcon icon={faBuilding} className="mr-2" />
                                    Property Type
                                </label>
                                <select
                                    value={filters.propertyType || ''}
                                    onChange={(e) => updateFilters({ propertyType: e.target.value as any || undefined })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                >
                                    <option value="">All Types</option>
                                    <option value="residential">Residential</option>
                                    <option value="commercial">Commercial</option>
                                    <option value="multifamily">Multifamily</option>
                                    <option value="industrial">Industrial</option>
                                </select>
                            </div>
                        </div>
                    </div>
                )}

                <p className="text-gray-600">Latest real estate market news and insights for your subscribed states.</p>
            </div>

            <div className="p-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {allArticles.map((article, index) => (
                        <div key={`${article.state}-${index}`} className="news-article-card border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                            {/* Article Image */}
                            <div className="relative h-48 bg-gray-100">
                                {article.imageUrl ? (
                                    <Image
                                        src={article.imageUrl}
                                        alt={article.title}
                                        fill
                                        className="object-cover news-article-image"
                                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                        quality={95}
                                        priority={index < 6}
                                        placeholder="blur"
                                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                            // Show fallback icon
                                            const fallback = target.parentElement?.querySelector('.fallback-icon');
                                            if (fallback) {
                                                (fallback as HTMLElement).style.display = 'flex';
                                            }
                                        }}
                                    />
                                ) : null}
                                
                                {/* Fallback icon - hidden by default, shown when image fails */}
                                <div className="fallback-icon absolute inset-0 flex items-center justify-center bg-gray-100" style={{ display: article.imageUrl ? 'none' : 'flex' }}>
                                    <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-3xl" />
                                </div>

                                {/* State and Source badges */}
                                <div className="absolute top-2 left-2 flex gap-1">
                                    <div className="bg-indigo-600 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                                        {selectedStates.find(s => s.abbreviation === article.state)?.abbreviation || article.state}
                                    </div>
                                    {article.source && (
                                        <div className="bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                                            {article.source}
                                        </div>
                                    )}
                                </div>
                                
                                {/* Date badge */}
                                {article.date && (
                                    <div className="absolute top-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                                        {new Date(article.date).toLocaleDateString('en-US', { 
                                            month: 'short', 
                                            day: 'numeric' 
                                        })}
                                    </div>
                                )}
                            </div>

                            {/* Article Content */}
                            <div className="p-4">
                                <h4 className="font-medium text-gray-900 mb-2 line-clamp-2 text-sm leading-tight">
                                    {article.title}
                                </h4>

                                {/* AI Summary */}
                                {article.aiSummary && (
                                    <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                        <div className="flex items-center gap-2 mb-2">
                                            <FontAwesomeIcon icon={faRobot} className="text-blue-600 text-sm" />
                                            <span className="text-xs font-medium text-blue-700">AI Summary</span>
                                        </div>
                                        <p className="text-xs text-blue-800 leading-relaxed">
                                            {article.aiSummary}
                                        </p>
                                    </div>
                                )}

                                {/* Original Snippet */}
                                <p className="text-xs text-gray-600 mb-3 line-clamp-3">
                                    {article.snippet}
                                </p>

                                {/* Read More Link */}
                                <a
                                    href={article.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center gap-1 text-indigo-600 hover:text-indigo-800 text-xs font-medium"
                                >
                                    Read Full Article
                                    <FontAwesomeIcon icon={faExternalLinkAlt} className="text-xs" />
                                </a>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Load More Button */}
                {hasMore && (
                    <div className="mt-6 text-center">
                        <button
                            onClick={loadMoreNews}
                            disabled={isLoadingMore}
                            className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2 mx-auto"
                        >
                            {isLoadingMore ? (
                                <>
                                    <Spinner size="sm" />
                                    Loading More...
                                </>
                            ) : (
                                'Load More News'
                            )}
                        </button>
                    </div>
                )}

                {allArticles.length === 0 && !isLoading && (
                    <div className="text-center py-10">
                        <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-4xl mb-4" />
                        <p className="text-gray-600">No news articles found for the selected filters.</p>
                    </div>
                )}
            </div>
        </div>
    );
} 