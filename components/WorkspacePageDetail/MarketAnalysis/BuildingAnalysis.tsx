import React, { useState, useEffect, forwardRef } from 'react';
import { useMarketAnalysis } from '@/context/MarketAnalysisContext';
import { useSearchParams } from 'next/navigation';
import { getAiSummary } from '@/actions/propertyActions';
import { createClient } from "@/utils/supabase/client";
import Spinner from "@/components/UI/Spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faChartLine, 
  faMapMarkerAlt, 
  faDollarSign, 
  faHome, 
  faCheckCircle, 
  faTimesCircle,
  faStar,
  faExclamationTriangle,
  faLightbulb,
  faPercent,
  faArrowUp
} from "@fortawesome/free-solid-svg-icons";

interface BuildingAnalysisProps {
  aiSummaryRef?: React.RefObject<HTMLDivElement | null>;
}

const BuildingAnalysis = forwardRef<HTMLDivElement, BuildingAnalysisProps>(({ aiSummaryRef }, ref) => {
  const { aiSummary, updateMarketAnalysisState, isLoadingAiSummary } = useMarketAnalysis();
  const searchParams = useSearchParams()
  const addressId = searchParams.get('addressId')
  const marketAddress = searchParams.get('marketAddress')
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState<string>('overview')
  const [isChatExpanded, setIsChatExpanded] = useState(false);

  // Check if chat is expanded
  useEffect(() => {
    // Initial check
    checkChatState();
    
    // Set up a mutation observer to watch for class changes on the body
    const observer = new MutationObserver(checkChatState);
    observer.observe(document.body, { 
        attributes: true, 
        attributeFilter: ['class'] 
    });
    
    // Also check on window resize
    window.addEventListener('resize', checkChatState);
    
    return () => {
        observer.disconnect();
        window.removeEventListener('resize', checkChatState);
    };
  }, []);
  
  // Function to check if chat is expanded
  const checkChatState = () => {
    const isChatExpandedNow = document.body.classList.contains('chat-expanded') || window.innerWidth < 1280;
    if (isChatExpandedNow !== isChatExpanded) {
        setIsChatExpanded(isChatExpandedNow);
    }
  };

  const handleRefresh = () => {
    updateMarketAnalysisState({ isLoadingAiSummary: true })
    setIsRefreshing(true)
    
    getAiSummary(marketAddress as string || '').then(async (data) => {
      updateMarketAnalysisState({ aiSummary: data, isLoadingAiSummary: false })

      const supabase = createClient();

      const { data: propData, error: propError } = await supabase
      .from('prop')
      .update({ ai_summary: data })
      .eq('address_id', addressId as string)
      .select()

      if (propError) {
        console.error('Error updating prop:', propError);
      }
      
    })
  }

  const renderIcon = (type: 'pro' | 'con') => {
    if (type === 'pro') {
      return (
        <FontAwesomeIcon icon={faCheckCircle} className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
      );
    } else {
      return (
        <FontAwesomeIcon icon={faTimesCircle} className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
      );
    }
  };
  
  const renderTabButton = (tabName: string, label: string, icon: any) => (
    <button
      onClick={() => setActiveTab(tabName)}
      className={`py-2 px-3 md:px-4 text-sm font-medium border-b-2 cursor-pointer transition-colors duration-200 flex items-center gap-2 ${
        activeTab === tabName
          ? 'border-indigo-500 text-indigo-600'
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
      }`}
    >
      <FontAwesomeIcon icon={icon} className="h-4 w-4" />
      {label}
    </button>
  );

  const renderOverviewTab = () => (
    <div className="overflow-y-auto h-full px-1">
      {/* AI Summary Card */}
      <div id="ai-summary" ref={aiSummaryRef} className="bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl p-6 mb-6 shadow-sm scroll-mt-20">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
            <FontAwesomeIcon icon={faLightbulb} className="h-5 w-5 text-indigo-600" />
          </div>
          <h4 className="text-lg font-semibold text-gray-800">AI Analysis Summary</h4>
        </div>
        <p className="text-gray-700 leading-relaxed">{aiSummary?.summary}</p>
      </div>
      
      {/* Pros and Cons Grid */}
      <div className={`grid md:grid-cols-2 gap-6 mb-6`}>
        {/* Pros Card */}
        <div className="bg-white rounded-xl shadow-sm border border-green-100 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Key Advantages</h4>
            </div>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              {aiSummary?.pros_cons?.pros?.map((pro: string, index: number) => (
                <li key={index} className="flex items-center">
                  {renderIcon('pro')}
                  <span className="text-gray-700 leading-relaxed">{pro}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Cons Card */}
        <div className="bg-white rounded-xl shadow-sm border border-red-100 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4 text-red-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Considerations</h4>
            </div>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              {aiSummary?.pros_cons?.cons?.map((item: string, index: number) => (
                <li key={index} className="flex items-center">
                  {renderIcon('con')}
                  <span className="text-gray-700 leading-relaxed">{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

                         {/* Area Overview Card */}
             {aiSummary?.poi_summary && (
         <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6 overflow-hidden">
           <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
             <div className="flex items-center gap-3">
               <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                 <FontAwesomeIcon icon={faMapMarkerAlt} className="h-4 w-4 text-blue-600" />
               </div>
               <h4 className="text-lg font-semibold text-gray-800">Area Overview</h4>
             </div>
           </div>
           <div className="p-6">
             <p className="text-gray-700 leading-relaxed">{aiSummary.poi_summary}</p>
           </div>
         </div>
       )}

      {/* Points of Interest Card */}
      {aiSummary?.points_of_interest && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faStar} className="h-4 w-4 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Points of Interest</h4>
            </div>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Distance</th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Details</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {Array.isArray(aiSummary.points_of_interest) ? (
                    aiSummary.points_of_interest.map((poi: any, index: number) => (
                      <tr key={index} className="hover:bg-gray-50 transition-colors">
                        <td className="px-4 py-4 text-sm font-medium text-gray-900">{poi.type || poi.category || 'N/A'}</td>
                        <td className="px-4 py-4 text-sm text-gray-600">{poi.distance || poi.proximity || 'N/A'}</td>
                        <td className="px-4 py-4 text-sm text-gray-700">{poi.details || poi.description || poi.name || 'N/A'}</td>
                      </tr>
                    ))
                  ) : Object.entries(aiSummary.points_of_interest || {}).map(([category, items]: [string, any]) => (
                    Array.isArray(items) ? items.map((item: any, index: number) => (
                      <tr key={`${category}-${index}`} className="hover:bg-gray-50 transition-colors">
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 capitalize">{category}</td>
                        <td className="px-4 py-4 text-sm text-gray-600">{item.distance || item.proximity || 'Near'}</td>
                        <td className="px-4 py-4 text-sm text-gray-700">{item.name || item.details || item}</td>
                      </tr>
                    )) : (
                      <tr key={category} className="hover:bg-gray-50 transition-colors">
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 capitalize">{category}</td>
                        <td className="px-4 py-4 text-sm text-gray-600">-</td>
                        <td className="px-4 py-4 text-sm text-gray-700">{typeof items === 'string' ? items : JSON.stringify(items)}</td>
                      </tr>
                    )
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );



  const renderFinancialTab = () => (
    <div className="overflow-y-auto h-full px-1">
      <div className="grid grid-cols-1 gap-6">
        {/* Cash Flow Potential Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faDollarSign} className="h-4 w-4 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Cash Flow Potential</h4>
            </div>
          </div>
          <div className="p-6">
            <p className="text-gray-700 leading-relaxed mb-4">{aiSummary?.financial_prospects?.cash_flow_potential || 'No cash flow analysis available.'}</p>
            
            {aiSummary?.financial_prospects?.rental_potential && (
              <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-semibold text-gray-700">{aiSummary.financial_prospects.rental_potential.type || 'Rental Potential'}</span>
                  <span className={`text-sm font-bold px-3 py-1 rounded-full ${
                    aiSummary.financial_prospects.rental_potential.rating === 'Strong' || aiSummary.financial_prospects.rental_potential.rating === 'High' ? 'text-green-700 bg-green-100' :
                    aiSummary.financial_prospects.rental_potential.rating === 'Medium' ? 'text-yellow-700 bg-yellow-100' : 'text-red-700 bg-red-100'
                  }`}>
                    {aiSummary.financial_prospects.rental_potential.rating || aiSummary.financial_prospects.rental_potential.assessment || 'Assessment Pending'}
                  </span>
                </div>
                {aiSummary.financial_prospects.rental_potential.score && (
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all duration-500 ${
                        aiSummary.financial_prospects.rental_potential.score >= 70 ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                        aiSummary.financial_prospects.rental_potential.score >= 50 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-gradient-to-r from-red-500 to-rose-500'
                      }`} 
                      style={{ width: `${Math.min(100, Math.max(0, aiSummary.financial_prospects.rental_potential.score))}%` }}
                    ></div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
                         {/* Appreciation Outlook Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faArrowUp} className="h-4 w-4 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Appreciation Outlook</h4>
            </div>
          </div>
          <div className="p-6">
            <p className="text-gray-700 leading-relaxed">{aiSummary?.financial_prospects?.appreciation_outlook || 'No appreciation analysis available.'}</p>
          </div>
        </div>
        
        {/* Opportunities and Risks Grid */}
        <div className={`grid grid-cols-1 md:grid-cols-2 gap-6 mb-6`}>
          {/* Opportunity Areas Card */}
          <div className="bg-white rounded-xl shadow-sm border border-green-100 overflow-hidden">
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={faLightbulb} className="h-4 w-4 text-green-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-800">Opportunities</h4>
              </div>
            </div>
            <div className="p-6">
              <ul className="space-y-3">
                {aiSummary?.financial_prospects?.opportunity_areas?.map((item: string, index: number) => (
                  <li key={index} className="flex items-center">
                    {renderIcon('pro')}
                    <span className="text-gray-700 leading-relaxed">{item}</span>
                  </li>
                )) || (
                  <li className="text-gray-500 italic">No opportunity data available.</li>
                )}
              </ul>
            </div>
          </div>

          {/* Risk Factors Card */}
          <div className="bg-white rounded-xl shadow-sm border border-red-100 overflow-hidden">
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4 text-red-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-800">Risk Factors</h4>
              </div>
            </div>
            <div className="p-6">
              <ul className="space-y-3">
                {aiSummary?.financial_prospects?.risk_factors?.map((item: string, index: number) => (
                  <li key={index} className="flex items-center">
                    {renderIcon('con')}
                    <span className="text-gray-700 leading-relaxed">{item}</span>
                  </li>
                )) || (
                  <li className="text-gray-500 italic">No risk factors data available.</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  
  const renderPropertyTab = () => (
    <div className="overflow-y-auto h-full px-1">
      <div className={`grid grid-cols-1 ${isChatExpanded ? '' : 'md:grid-cols-2'} gap-6 mb-6`}>
        {/* Property Attributes Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faHome} className="h-4 w-4 text-orange-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Property Attributes</h4>
            </div>
          </div>
          <div className="p-6">
            <ul className="space-y-3 list-disc list-inside marker:text-gray-800 marker:text-xl">
              {aiSummary?.property_attributes?.map((attribute: string, index: number) => (
                <li key={index} className="text-gray-700 leading-relaxed">
                  {attribute}
                </li>
              )) || (
                <li className="text-gray-500 italic list-none">No property attributes available.</li>
              )}
            </ul>
          </div>
        </div>
        
        {/* Market Conditions Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faChartLine} className="h-4 w-4 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-800">Market Conditions</h4>
            </div>
          </div>
          <div className="p-6">
            <p className="text-gray-700 leading-relaxed">
              {aiSummary?.market_conditions || 'No market conditions data available.'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

    return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Card Header */}
      <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <FontAwesomeIcon icon={faLightbulb} className="h-4 w-4 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">AI Summary</h3>
          </div>
          {addressId && (
            <div>
              {!isLoadingAiSummary ? (
                <button 
                  onClick={handleRefresh}
                  className="cursor-pointer flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Refresh
                </button>
              ) : isRefreshing ? (
                <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg">
                  <Spinner size="sm" />
                  Refreshing...
                </button>
              ) : null}
            </div>
          )}
        </div>
      </div>

      {/* Card Content */}
      <div className="p-6">
        {isLoadingAiSummary ? (
          <div className="flex flex-col items-center justify-center h-full py-12">
            <Spinner size="lg" className="mb-3" />
            <p className="text-sm text-gray-600">Loading analysis data...</p>
          </div>
        ) : (
          <div className="flex-1 flex flex-col">
            {/* Tabs navigation - Updated to match PropertyInformation style */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex flex-wrap -mb-px">
                {renderTabButton('overview', 'Overview', faChartLine)}
                {renderTabButton('property', 'Property Details', faHome)}
                {renderTabButton('financial', 'Financial Analysis', faDollarSign)}
              </nav>
            </div>
            
            {/* Tab content */}
            <div className="flex-1 overflow-auto">
              {activeTab === 'overview' && renderOverviewTab()}
              {activeTab === 'property' && renderPropertyTab()}
              {activeTab === 'financial' && renderFinancialTab()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

BuildingAnalysis.displayName = 'BuildingAnalysis';

export default BuildingAnalysis;