import { useMarketAnalysis } from "@/context/MarketAnalysisContext"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { TaxHistoryType, TaxRecord } from "@/types/TaxHistoryType";
import { createClient } from "@/utils/supabase/client";
import { fetchPropertyDetails, fetchTaxHistory } from "@/actions/propertyActions";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPencilAlt, faSave, faTimes, faPlus, faTrash, faChartLine, faClipboardList } from "@fortawesome/free-solid-svg-icons";
import Spinner from "@/components/UI/Spinner";

export default function TaxHistory(){
    const { taxHistory, isLoadingTaxHistory, updateMarketAnalysisState } = useMarketAnalysis()
    const [isRefreshing, setIsRefreshing] = useState(false)
    const [editMode, setEditMode] = useState(false)
    const [editingRecords, setEditingRecords] = useState<TaxRecord[]>([])
    const [isSaving, setIsSaving] = useState(false)
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const [isChatExpanded, setIsChatExpanded] = useState(false);

    // Check for chat state using body class
    useEffect(() => {
        // Initial check
        checkChatState();
        
        // Set up a mutation observer to watch for class changes on the body
        const observer = new MutationObserver(checkChatState);
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'] 
        });
        
        // Also check on window resize
        window.addEventListener('resize', checkChatState);
        
        return () => {
            observer.disconnect();
            window.removeEventListener('resize', checkChatState);
        };
    }, []);
    
    // Function to check if chat is expanded
    const checkChatState = () => {
        const isChatExpandedNow = document.body.classList.contains('chat-expanded') || window.innerWidth < 1280;
        if (isChatExpandedNow !== isChatExpanded) {
            setIsChatExpanded(isChatExpandedNow);
        }
    };

    const handleRefresh = async () => {
        setIsRefreshing(true)
        updateMarketAnalysisState({ isLoadingTaxHistory: true })

        const supabase = createClient();
        const {data: propAddressData, error: propAddressError} = await supabase.from('prop_addresses').select('*').eq('id', addressId as string).single();
        const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId as string).single();

        const taxHistoryData = await fetchTaxHistory(propAddressData?.address as string, propAddressData?.city as string, propAddressData?.state as string, propAddressData?.zip as string)
        
        updateMarketAnalysisState({ taxHistory: taxHistoryData, isLoadingTaxHistory: false })

        const {data: newPropertyDetails, error: newPropertyDetailsError} = await supabase
            .from('prop_tax_history')
            .update({
                data: taxHistoryData?.tax_records
            }).eq('prop_id', propData.id);
        
        setIsRefreshing(false)
    }

    // Start editing mode
    const startEditing = () => {
        setEditingRecords(sortedTaxRecords);
        setEditMode(true);
    }

    // Cancel editing
    const cancelEditing = () => {
        setEditMode(false);
    }

    // Save changes
    const saveChanges = async () => {
        if (!addressId) return;
        
        setIsSaving(true);
        
        try {
            const supabase = createClient();
            const {data: propData, error: propError} = await supabase
                .from('prop')
                .select('*')
                .eq('address_id', addressId)
                .single();
                
            if (propError) throw propError;
            
            // Update the tax history records
            const {data: updateResult, error: updateError} = await supabase
                .from('prop_tax_history')
                .update({
                    data: editingRecords
                })
                .eq('prop_id', propData.id);
                
            if (updateError) throw updateError;
            
            // Update the state
            const updatedTaxHistory: TaxHistoryType = {
                tax_records: editingRecords,
                timestamp: taxHistory?.timestamp || new Date().toISOString()
            };
            
            updateMarketAnalysisState({ taxHistory: updatedTaxHistory });
            setEditMode(false);
        } catch (error) {
            console.error("Error saving tax records:", error);
            alert("There was an error saving the tax records. Please try again.");
        } finally {
            setIsSaving(false);
        }
    }

    // Add new tax record
    const addNewRecord = () => {
        const newRecord: TaxRecord = {
            assessed_tax_year: new Date().getFullYear(),
            assessed_value_land: 0,
            assessed_value_total: 0,
            tax_bill_amount: 0
        };
        
        setEditingRecords([...editingRecords, newRecord]);
    }

    // Remove a tax record
    const removeRecord = (index: number) => {
        const newRecords = [...editingRecords];
        newRecords.splice(index, 1);
        setEditingRecords(newRecords);
    }

    // Handle input change
    const handleInputChange = (index: number, field: keyof TaxRecord, value: string) => {
        const newRecords = [...editingRecords];
        
        // Convert to number for numeric fields
        if (field === 'assessed_tax_year') {
            newRecords[index][field] = parseInt(value) || 0;
        } else if (field === 'assessed_value_land' || field === 'assessed_value_total' || field === 'tax_bill_amount') {
            // Remove commas and convert to number
            const numValue = parseFloat(value.replace(/,/g, '')) || 0;
            newRecords[index][field] = numValue;
        }
        
        setEditingRecords(newRecords);
    }

    // Filter out duplicate years and sort by year in ascending order
    const uniqueTaxRecords = taxHistory?.tax_records?.reduce<TaxRecord[]>((acc, record) => {
        if (!acc.some(item => item.assessed_tax_year === record.assessed_tax_year)) {
            acc.push(record);
        }
        return acc;
    }, []) || [];
    
    const sortedTaxRecords = uniqueTaxRecords
        .sort((a, b) => (a.assessed_tax_year || 0) - (b.assessed_tax_year || 0));

    // Transform data for Recharts
    const chartData = (editMode ? editingRecords : sortedTaxRecords).map(record => ({
        year: record.assessed_tax_year?.toString() || '',
        assessed_value_land: record.assessed_value_land || 0,
        assessed_value_total: record.assessed_value_total || 0,
        tax_bill_amount: record.tax_bill_amount || 0
    }));

    // Formatting functions for chart
    const formatValue = (value: number) => {
        return `$${value.toLocaleString()}`;
    };

    const formatYear = (year: string) => {
        return year;
    };

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                    <p className="font-medium text-gray-900 mb-2">{`Year: ${label}`}</p>
                    {payload.map((entry: any, index: number) => (
                        <p key={index} className="text-sm" style={{ color: entry.color }}>
                            {`${entry.name}: ${formatValue(entry.value)}`}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faClipboardList} className="h-4 w-4 text-green-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Tax History</h3>
                    </div>
                    <div className="flex items-center gap-2">
                        {editMode && (
                            <>
                                <button 
                                    onClick={addNewRecord}
                                    className="cursor-pointer flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-all">
                                    <FontAwesomeIcon icon={faPlus} className="text-xs" />
                                    Add Year
                                </button>
                                <button 
                                    onClick={cancelEditing}
                                    className="cursor-pointer flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-all">
                                    <FontAwesomeIcon icon={faTimes} className="text-xs" />
                                    Cancel
                                </button>
                                <button 
                                    onClick={saveChanges}
                                    disabled={isSaving}
                                    className="cursor-pointer flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-white bg-green-600 rounded-lg shadow-sm hover:bg-green-700 transition-all disabled:opacity-50">
                                    {isSaving ? (
                                        <>
                                            <Spinner size="sm" />
                                            Saving...
                                        </>
                                    ) : (
                                        <>
                                            <FontAwesomeIcon icon={faSave} className="text-xs" />
                                            Save
                                        </>
                                    )}
                                </button>
                            </>
                        )}
                        {!editMode && taxHistory && (
                            <button
                                onClick={startEditing}
                                className="cursor-pointer px-3 py-1.5 text-sm text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors flex items-center gap-2"
                            >
                                <FontAwesomeIcon icon={faPencilAlt} className="h-3 w-3" />
                                Edit
                            </button>
                        )}
                        {addressId && !isLoadingTaxHistory && !editMode && (
                            <button 
                                onClick={handleRefresh}
                                disabled={isRefreshing}
                                className="cursor-pointer rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                                {isRefreshing ? (
                                    <div className="flex items-center gap-x-1">
                                        <Spinner size="sm" />
                                        Refreshing...
                                    </div>
                                ) : 'Refresh'}
                            </button>
                        )}
                    </div>
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
                
                {!isLoadingTaxHistory ? (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Chart Section */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                            <div className="bg-gradient-to-r from-gray-50 to-indigo-50 p-4 border-b border-gray-200">
                                <h3 className="text-lg font-medium text-gray-900 mb-1">Tax Trends</h3>
                                <p className="text-sm text-gray-600">Assessment values and tax bills over time</p>
                            </div>
                            <div className="p-6">
                                <div className="relative h-[350px]">
                                    {/* Left Y-axis label */}
                                    <div className="absolute -left-6 top-[45%] transform -translate-y-1/2 -rotate-90">
                                        <span className="text-xs font-bold text-gray-700">Assessed Values ($)</span>
                                    </div>
                                    
                                    {/* Right Y-axis label */}
                                    <div className="absolute -right-6 top-[45%] transform -translate-y-1/2 rotate-90">
                                        <span className="text-xs font-bold text-pink-600">Tax Bill Amount ($)</span>
                                    </div>
                                    
                                    {/* Chart container with padding for labels */}
                                    <div className="h-full px-12">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <LineChart 
                                                data={chartData}
                                                margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                                <XAxis 
                                                    dataKey="year" 
                                                    tick={{ fontSize: 12 }}
                                                    tickFormatter={formatYear}
                                                />
                                                <YAxis 
                                                    yAxisId="left"
                                                    tick={{ fontSize: 12 }}
                                                    tickFormatter={formatValue}
                                                />
                                                <YAxis 
                                                    yAxisId="right"
                                                    orientation="right"
                                                    tick={{ fontSize: 12, fill: '#ec4899' }}
                                                    tickFormatter={formatValue}
                                                />
                                                <Tooltip content={<CustomTooltip />} />
                                                <Legend 
                                                    wrapperStyle={{ 
                                                        paddingTop: '20px',
                                                        fontSize: '12px'
                                                    }}
                                                />
                                                <Line 
                                                    yAxisId="left"
                                                    type="monotone" 
                                                    dataKey="assessed_value_land" 
                                                    stroke="#3b82f6" 
                                                    strokeWidth={2}
                                                    dot={{ r: 4, fill: '#3b82f6' }}
                                                    activeDot={{ r: 6, fill: '#3b82f6' }}
                                                    name="Assessed Land Value"
                                                />
                                                <Line 
                                                    yAxisId="left"
                                                    type="monotone" 
                                                    dataKey="assessed_value_total" 
                                                    stroke="#10b981" 
                                                    strokeWidth={2}
                                                    dot={{ r: 4, fill: '#10b981' }}
                                                    activeDot={{ r: 6, fill: '#10b981' }}
                                                    name="Assessed Total Value"
                                                />
                                                <Line 
                                                    yAxisId="right"
                                                    type="monotone" 
                                                    dataKey="tax_bill_amount" 
                                                    stroke="#ec4899" 
                                                    strokeWidth={3}
                                                    dot={{ r: 4, fill: '#ec4899' }}
                                                    activeDot={{ r: 6, fill: '#ec4899' }}
                                                    name="Tax Bill Amount"
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Table Section */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                            <div className="bg-gray-50 p-4 border-b border-gray-200">
                                <h3 className="text-lg font-medium text-gray-900 mb-1">Tax Records</h3>
                                <p className="text-sm text-gray-600">
                                    {editMode ? 'Edit tax history records' : `${sortedTaxRecords.length} years of data`}
                                </p>
                            </div>
                            
                            <div className="overflow-x-auto max-h-[380px] overflow-y-auto">
                                {(editMode ? editingRecords : sortedTaxRecords).length > 0 ? (
                                    <table className="min-w-full">
                                        <thead className="bg-gray-50 sticky top-0 z-10">
                                            <tr>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                        Year
                                                    </div>
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                        Land Value
                                                    </div>
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                        Total Value
                                                    </div>
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                                                        Tax Bill
                                                    </div>
                                                </th>
                                                {editMode && (
                                                    <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">
                                                        Actions
                                                    </th>
                                                )}
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-100">
                                            {(editMode ? editingRecords : sortedTaxRecords).map((record, index) => (
                                                <tr key={index} className="hover:bg-gray-50 transition-colors">
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {editMode ? (
                                                            <input 
                                                                type="number" 
                                                                className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                                value={record.assessed_tax_year}
                                                                onChange={(e) => handleInputChange(index, 'assessed_tax_year', e.target.value)}
                                                            />
                                                        ) : (
                                                            <span className="font-semibold">{record.assessed_tax_year}</span>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                                                        {editMode ? (
                                                            <div className="relative">
                                                                <span className="absolute left-2 top-1 text-gray-500 text-sm">$</span>
                                                                <input 
                                                                    type="text" 
                                                                    className="w-28 pl-5 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                                    value={record.assessed_value_land?.toLocaleString()}
                                                                    onChange={(e) => handleInputChange(index, 'assessed_value_land', e.target.value)}
                                                                />
                                                            </div>
                                                        ) : (
                                                            <span className="font-medium">
                                                                {record.assessed_value_land != null ? `$${record.assessed_value_land.toLocaleString()}` : 'N/A'}
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                                                        {editMode ? (
                                                            <div className="relative">
                                                                <span className="absolute left-2 top-1 text-gray-500 text-sm">$</span>
                                                                <input 
                                                                    type="text" 
                                                                    className="w-28 pl-5 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                                    value={record.assessed_value_total?.toLocaleString()}
                                                                    onChange={(e) => handleInputChange(index, 'assessed_value_total', e.target.value)}
                                                                />
                                                            </div>
                                                        ) : (
                                                            <span className="font-medium">
                                                                {record.assessed_value_total != null ? `$${record.assessed_value_total.toLocaleString()}` : 'N/A'}
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                                                        {editMode ? (
                                                            <div className="relative">
                                                                <span className="absolute left-2 top-1 text-gray-500 text-sm">$</span>
                                                                <input 
                                                                    type="text" 
                                                                    className="w-28 pl-5 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                                    value={record.tax_bill_amount?.toLocaleString()}
                                                                    onChange={(e) => handleInputChange(index, 'tax_bill_amount', e.target.value)}
                                                                />
                                                            </div>
                                                        ) : (
                                                            <span className="font-medium">
                                                                {record.tax_bill_amount != null ? `$${record.tax_bill_amount.toLocaleString()}` : 'N/A'}
                                                            </span>
                                                        )}
                                                    </td>
                                                    {editMode && (
                                                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                            <button 
                                                                onClick={() => removeRecord(index)}
                                                                className="flex items-center gap-1 px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors"
                                                            >
                                                                <FontAwesomeIcon icon={faTrash} className="text-xs" />
                                                                Remove
                                                            </button>
                                                        </td>
                                                    )}
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                ) : (
                                    <div className="flex flex-col items-center justify-center h-[380px] text-center">
                                        <div className="text-gray-400 mb-3">
                                            <FontAwesomeIcon icon={faChartLine} className="text-3xl" />
                                        </div>
                                        <p className="text-sm text-gray-600">No tax history records available</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 p-8">
                        <div className="flex flex-col items-center justify-center py-10">
                            <Spinner size="lg" className="mb-3" />
                            <p className="text-sm text-gray-600">Loading tax history...</p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}