import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import SearchPlace from "../SearchPlace";
import StateSelect from "../StateSelect";   
import LocalSearchHistory from "../LocalSearchHistory";
import PortfolioItem from "@/components/WorkspacePageDetail/PortfolioManagement/PortfolioItem";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown, faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";

interface MarketAnalysisHeaderProps {
    portfolios: { [key: string]: any }[]
    handleGetPortfolios: () => void
}

export default function MarketAnalysisHeader({ portfolios, handleGetPortfolios }: MarketAnalysisHeaderProps) {
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const [isSearchPageStart, setIsSearchPageStart] = useState(true);
    const [isRecentSearchesExpanded, setIsRecentSearchesExpanded] = useState(false);
    const [activeSection, setActiveSection] = useState<'recent' | 'portfolios'>('portfolios');
    const {id} = useParams()
    const {push} = useRouter()
    

    const { 
        selectedStates, 
        searchPlace, 
        errorState, 
        updateMarketAnalysisState
    } = useMarketAnalysis();

    useEffect(() => {
        if(typeof searchPlace !== 'string'){
            setIsSearchPageStart(false)
        }
    }, [searchPlace])

    useEffect(() => {
        if(typeof searchPlace !== 'string'){
            const searchLink = document.querySelector('.nav-search-link')
            if(searchLink){
                searchLink.addEventListener('click', () => {
                    updateMarketAnalysisState({
                        searchPlace: ''
                    })
                })
            }
        }
    }, [searchPlace])

    const handlePortfolioSelect = (portfolio: any) => {
        push(`/workspace/${id}/?portfolioId=${portfolio.id}`)
    }

    return (
        <div className={`flex flex-col gap-y-6 ${addressId ? 'hidden' : ''} ${typeof searchPlace === 'string' ? 'h-full items-center justify-center' : ''}`}>
                {/* Modern Search Interface */}
                <div className={`${typeof searchPlace === 'string' ? 'w-full max-w-[800px] mx-auto pb-10' : 'w-full max-w-[1200px] mx-auto pt-8'}`}>
                    
                    {/* Logo and Title - only on search start */}
                    {typeof searchPlace === 'string' && (
                        <div className="text-center mb-12">
                            <div className="flex items-center justify-center gap-x-4 mb-3">
                                <div>
                                    <svg
                                        width="30"
                                        height="30"
                                        viewBox="0 0 30 30"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="breathing-animation"
                                    >
                                        <defs>
                                            <clipPath id="clip0_13579_24864">
                                                <rect width="30" height="30" />
                                            </clipPath>
                                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%" gradientUnits="userSpaceOnUse">
                                                <stop offset="0%" style={{ stopColor: "#000000", stopOpacity: 1 }} />
                                                <stop offset="100%" style={{ stopColor: "#000000", stopOpacity: 1 }} />
                                            </linearGradient>
                                        </defs>
                                        <g clipPath="url(#clip0_13579_24864)">
                                            <path d="M15 0C15 10 10 15 0 15C10 15 15 20 15 30C15 20 20 15 30 15C20 15 15 10 15 0Z" fill="url(#gradient1)" />
                                        </g>
                                        
                                    </svg>
                                </div>
                                <div>
                                    <h1 className="text-4xl font-bold text-gray-900 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>Property Search</h1>
                                </div>
                            </div>
                            
                            <p className="text-lg text-gray-600 font-medium antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>
                                Discover comprehensive property intelligence and market insights
                            </p>
                        </div>
                    )}

                    {/* Search Bar Container */}
                    <div className={`${isSearchPageStart ? 'mb-10' : 'mb-8'}`}>
                        <div className="relative">
                            <SearchPlace isSearchPageStart={isSearchPageStart} />
                        </div>
                    </div>

                    {/* Available States Section */}
                    <div>
                        <div className="text-center mb-6">
                            <h3 className="text-lg font-semibold text-gray-800 mb-2 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>Your States</h3>
                            {/*<p className="text-sm text-gray-600 antialiased" style={{ fontSmooth: 'always', WebkitFontSmoothing: 'antialiased', MozOsxFontSmoothing: 'grayscale' }}>Select states to search within your subscription</p>*/}
                        </div>
                        
                        <div className="flex items-center justify-center">
                            <div className="flex items-center gap-2">
                                <StateSelect isSearchPageStart={isSearchPageStart} />
                                <a 
                                    href={`/workspace/${id}/pricing`}
                                    className="w-10 h-10 bg-black hover:bg-gray-800 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 flex items-center justify-center"
                                    title="Add more states"
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    {/* Toggle Section */}
                    {
                        typeof searchPlace === 'string' && (
                            <>
                                <div className="flex justify-center mb-6 mt-6">
                                    <div className="inline-flex bg-gray-100 rounded-lg p-1">
                                        <button 
                                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                                                activeSection === 'portfolios' 
                                                    ? 'bg-white text-gray-900 shadow-sm' 
                                                    : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                            onClick={() => setActiveSection('portfolios')}
                                        >
                                            Portfolios
                                        </button>
                                        <button 
                                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                                                activeSection === 'recent' 
                                                    ? 'bg-white text-gray-900 shadow-sm' 
                                                    : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                            onClick={() => setActiveSection('recent')}
                                        >
                                            Recent Searches
                                        </button>
                                    </div>
                                </div>

                                {/* Recent Searches Section */}
                                {activeSection === 'recent' && (
                                    <div className='h-[362px]'>
                                        <LocalSearchHistory />
                                    </div>
                                )}

                                {/* Portfolios Section */}
                                {activeSection === 'portfolios' && portfolios.length > 0 && (
                                    <div className='h-[362px]'>
                                        <div className="flex items-center justify-center gap-x-4">
                                            {
                                                portfolios.slice(0, 3).map((portfolio) => (
                                                    <div className="w-1/3" key={portfolio.id}>
                                                        <PortfolioItem key={portfolio.id} portfolio={portfolio} onPortfolioSelect={handlePortfolioSelect} onUpdateTrigger={handleGetPortfolios} edit={false} />
                                                    </div>
                                                ))
                                            }
                                        </div>
                                        <div className="flex justify-center mb-8">
                                            <Link href={`/workspace/${id}`} className="text-indigo-600 hover:text-indigo-800 transition-colors mt-5">View All Portfolios</Link>
                                        </div>
                                    </div>
                                )}
                            </>
                        )
                    }

                </div>

                {
                    errorState && (
                        <div className="max-w-[800px] mx-auto px-4">
                            <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
                                <div className="flex items-center gap-x-2">
                                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                                    <p className="text-red-600">
                                        Selected address is in {errorState.abbreviation}, but not in the selected states: {selectedStates.map(state => state.abbreviation).join(', ')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )
                }
            </div>
    )
}