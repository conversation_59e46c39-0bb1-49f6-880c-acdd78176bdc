import Markdown from 'react-markdown'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import 'katex/dist/katex.min.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMagicWandSparkles } from '@fortawesome/free-solid-svg-icons';
import Image from 'next/image';

interface Props {
    item: { [key: string]: any }
}

export default function MessageAssistant({ item }: Props) {
    const hasAttachments = item?.content?.attachments && item.content.attachments.length > 0;
    
    return (
        <div className="flex items-start">
            <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <FontAwesomeIcon icon={faMagicWandSparkles} className="text-indigo-600 h-4 w-4" />
                </div>
            </div>
            <div className="ml-3 chat-assistant max-w-[70%]">
                <Markdown
                    remarkPlugins={[remarkMath]}
                    rehypePlugins={[rehypeKatex]}
                >
                    {item?.content?.text || item?.message}
                </Markdown>
                
                {/* Display attachments if any */}
                {hasAttachments && (
                    <div className="flex flex-wrap gap-2 mt-2">
                        {item.content.attachments.map((attachment: any, index: number) => (
                            <div key={index} className="relative">
                                {attachment.type === 'image' ? (
                                    <a href={attachment.url} target="_blank" rel="noopener noreferrer">
                                        <div className="h-32 w-32 rounded-md overflow-hidden border border-gray-200">
                                            <Image 
                                                src={attachment.url} 
                                                alt="Attached image"
                                                width={128}
                                                height={128}
                                                className="h-full w-full object-cover"
                                            />
                                        </div>
                                    </a>
                                ) : (
                                    <a 
                                        href={attachment.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="flex items-center justify-center bg-gray-100 rounded-md p-2 border border-gray-200"
                                    >
                                        <span className="text-xs text-blue-600 underline">
                                            View attachment
                                        </span>
                                    </a>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}