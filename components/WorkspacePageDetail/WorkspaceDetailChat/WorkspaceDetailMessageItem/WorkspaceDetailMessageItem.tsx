import MessageAssistant from "./MessageAssistant"
import MessageUser from "./MessageUser"

interface Props {
    item: { [key: string]: any }
}

export default function WorkspaceDetailMessageItem({ item }: Props) {
    return (
        <>
            {
                item?.role === 'user' ? (
                    <MessageUser item={item} />
                ) : item?.role === 'assistant' ? (
                    <MessageAssistant item={item} />
                ) : null
            }
        </>
    )
}