export default function MessageGenerate({ messagesGenerated }: { messagesGenerated: { [key: string]: any }[] }) {
    return (
        <div className="flex items-start">
            <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <span className="text-sm font-medium text-indigo-600">AI</span>
                </div>
            </div>
            <div className="ml-3 chat-assistant">
                <div className="text-sm font-medium text-indigo-600">Thinking...</div>
                <div className="relative">
                    {messagesGenerated?.length > 1 && (
                        <div className="absolute left-[3.2px] top-4 bottom-4 w-[1px] bg-indigo-400"></div>
                    )}
                    {messagesGenerated?.map((item, index) => (
                        <div key={index} className="flex items-center gap-x-2 mt-2">
                            <div className="w-2 h-2 bg-indigo-600 rounded-full relative z-10"></div>
                            <p className="text-sm text-indigo-400">{item?.content?.text}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}