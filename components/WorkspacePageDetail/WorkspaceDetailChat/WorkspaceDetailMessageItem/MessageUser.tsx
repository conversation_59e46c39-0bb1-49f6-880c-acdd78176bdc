import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '@/context/AuthContext';
import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import Image from 'next/image';

interface Props {
    item: { [key: string]: any }
}

export default function MessageUser({ item }: Props) {
    const { user, userDataDB } = useAuth();


    return (
        <div className="flex items-start justify-end">
            <div className="mr-3 max-w-[70%]">
                <p className="text-sm text-gray-900 mt-[6px] text-right">
                    {item?.content?.text || item?.message}
                </p>
                
                {/* Display attachments if any */}
                {item?.content?.image_urls && (
                    <div className="flex flex-wrap gap-2 mt-2 justify-end">
                        {item.content.image_urls.map((url: any, index: number) => (
                            <div key={index} className="relative">
                                <a href={url} target="_blank" rel="noopener noreferrer">
                                    <div className="h-32 w-32 rounded-md overflow-hidden border border-gray-200">
                                        <Image 
                                            src={url} 
                                            alt="Attached image"
                                            width={128}
                                            height={128}
                                            className="h-full w-full object-cover"
                                        />
                                    </div>
                                </a>
                            </div>
                        ))}
                    </div>
                )}
            </div>
            <div className="flex-shrink-0">
                {userDataDB?.photo ? (
                    <div className="h-8 w-8 rounded-full overflow-hidden">
                        <Image 
                            src={userDataDB.photo} 
                            alt="User profile" 
                            width={32} 
                            height={32}
                            className="object-cover w-full h-full"
                        />
                    </div>
                ) : (
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <FontAwesomeIcon icon={faUser} className="text-gray-600 h-4 w-4" />
                    </div>
                )}
            </div>
        </div>
    )
}