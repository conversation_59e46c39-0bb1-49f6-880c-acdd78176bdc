import { useState, useRef, useEffect } from "react"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faPaperPlane, faPaperclip, faImage } from "@fortawesome/free-solid-svg-icons"

interface Props {
    isLoadingResponse: boolean
    handleSendMessageChat: (message: string, files?: File[]) => void
}

export default function WorkspaceDetailChatInput({ isLoadingResponse, handleSendMessageChat }: Props) {
    const [sendMessage, setSendMessage] = useState('')
    const [files, setFiles] = useState<File[]>([])
    const fileInputRef = useRef<HTMLInputElement>(null)
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    // Adjust textarea height automatically
    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
        }
    }, [sendMessage])

    const handleSendMessageInput = () => {
        if(isLoadingResponse) return

        handleSendMessageChat(sendMessage, files)
        setSendMessage('')
        setFiles([])
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const newFiles = Array.from(e.target.files);
            setFiles(prev => [...prev, ...newFiles]);
        }
    };
    
    const handlePaste = (e: React.ClipboardEvent) => {
        const items = e.clipboardData?.items;
        if (items) {
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const file = items[i].getAsFile();
                    if (file) {
                        setFiles(prev => [...prev, file]);
                    }
                }
            }
        }
    };
    
    const removeFile = (index: number) => {
        setFiles(files.filter((_, i) => i !== index));
    };

    return(
        <div className="p-4">
            <div className="flex flex-col">
                <div className="flex items-end space-x-3">
                    <div className="flex-1 rounded-lg border-grey-200 ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-indigo-600">
                        <textarea
                            ref={textareaRef}
                            value={sendMessage}
                            onChange={(e) => setSendMessage(e.target.value)}
                            onPaste={handlePaste}
                            onKeyDown={(e) => {
                                if(e.key === 'Enter' && !e.shiftKey && !isLoadingResponse) {
                                    e.preventDefault();
                                    handleSendMessageInput();
                                }
                            }}
                            placeholder="Ask about your portfolio or properties..."
                            className="block w-full resize-none border-0 bg-transparent py-2 px-3 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                            rows={1}
                            style={{ maxHeight: '150px' }}
                        />
                        
                        {/* File previews */}
                        {files.length > 0 && (
                            <div className="flex flex-wrap gap-2 p-2 border-t border-gray-200">
                                {files.map((file, index) => (
                                    <div key={index} className="relative inline-block">
                                        {file.type.startsWith('image/') ? (
                                            <div className="h-16 w-16 rounded overflow-hidden border border-gray-200">
                                                <img
                                                    src={URL.createObjectURL(file)}
                                                    alt={`Attachment ${index + 1}`}
                                                    className="h-full w-full object-cover"
                                                />
                                            </div>
                                        ) : (
                                            <div className="h-16 w-16 rounded flex items-center justify-center bg-gray-100 border border-gray-200">
                                                <FontAwesomeIcon icon={faPaperclip} className="text-gray-500" />
                                                <span className="text-xs text-gray-500 ml-1">{file.name.slice(0, 5)}...</span>
                                            </div>
                                        )}
                                        <button
                                            type="button"
                                            onClick={() => removeFile(index)}
                                            className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full h-4 w-4 flex items-center justify-center text-xs"
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="inline-flex items-center rounded-lg bg-gray-100 p-2 text-gray-600 hover:bg-gray-200"
                    >
                        <FontAwesomeIcon icon={faImage} />
                    </button>
                    <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleFileChange}
                        className="hidden"
                    />

                    <button
                        type="button"
                        disabled={isLoadingResponse || (sendMessage.length === 0 && files.length === 0)}
                        onClick={handleSendMessageInput}
                        className="disabled:opacity-50 inline-flex items-center rounded-lg bg-indigo-600 p-2 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    >
                        <FontAwesomeIcon icon={faPaperPlane} />
                    </button>
                </div>
            </div>
        </div>
    )
}