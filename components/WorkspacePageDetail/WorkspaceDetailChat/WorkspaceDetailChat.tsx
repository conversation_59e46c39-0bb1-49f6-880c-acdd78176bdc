import useWebsocketPortfolio from "@/helpers/hooks/portfolio/useWebsocketPortfolio"
import { useCallback, useEffect, useState, useRef } from "react"
import WorkspaceDetailChatInput from "./WorkspaceDetailChatInput"
import WorkspaceDetailMessageItem from "./WorkspaceDetailMessageItem/WorkspaceDetailMessageItem"
import useHistory from "@/helpers/hooks/portfolio/useHistory"
import { usePortfolio } from "@/context/PortfolioContext"
import MessageGenerate from "./WorkspaceDetailMessageItem/MessageGenerate"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faChevronUp, faMagicWandSparkles, faPaperPlane, faMinimize, faWindowMinimize, faPaperclip, faImage, faInfoCircle, faList, faClockRotateLeft, faArrowLeft, faPlus, faTrash } from "@fortawesome/free-solid-svg-icons"
import { useModal } from "@/context/ModalContext"
import modalType from "@/constants/modalType"
import { ChatActiveType } from "@/types/ChatActiveType"
import { ChatListType } from "@/types/ChatListType"
import modalTriggerType from "@/constants/modalTriggerType";


interface WorkspaceDetailChatProps {
    isChatExpanded: boolean;
    setIsChatExpanded: React.Dispatch<React.SetStateAction<boolean>>;
    chatActive: ChatActiveType | null;
    portfolioId: string
    chatList: ChatListType | null
    handleChatActive: (chat_id: string) => void
    handleCreateChat: () => void
}

export default function WorkspaceDetailChat({ isChatExpanded, setIsChatExpanded, chatActive, portfolioId, chatList, handleChatActive, handleCreateChat }: WorkspaceDetailChatProps){
    const { messages, setMessages, handleSendMessageChat, isLoadingResponse, messagesGenerated } = useWebsocketPortfolio(chatActive?.id || '')
    const { history, setHistory } = useHistory(chatActive?.id || '')
    const messagesContainerRef = useRef<HTMLDivElement>(null)
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const [collapsedInput, setCollapsedInput] = useState("")
    const [isInputExpanded, setIsInputExpanded] = useState(false)
    const [files, setFiles] = useState<File[]>([])
    const fileInputRef = useRef<HTMLInputElement>(null)
    const { showModal, updateModalData, modalTrigger, updateModalTrigger } = useModal()
    const [footerOffset, setFooterOffset] = useState<number>(50)
    const [isOpenChatList, setIsOpenChatList] = useState(false)

    
    useEffect(() => {
        if(portfolioId && chatActive?.id) {
            setHistory(null)
            setMessages([])
        }
    }, [portfolioId, chatActive?.id])

    
    useEffect(() => {
        console.log(messages)
        setIsOpenChatList(false)
    }, [messages])

    useEffect(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight
        }
    }, [messages, messagesGenerated, history?.messages])
    
    // Adjust textarea height on input
    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
            
            if (textareaRef.current.scrollHeight > 40) {
                setIsInputExpanded(true)
            } else {
                setIsInputExpanded(false)
            }
        }
    }, [collapsedInput])
    
    // Calculate footer offset for collapsed chat positioning
    useEffect(() => {
        const updateOffset = () => {
            const footerEl = document.getElementById('portfolio-id-info')
            if (footerEl) {
                // Add 41px margin above the footer (increased by 25px from 16px)
                setFooterOffset(footerEl.getBoundingClientRect().height)
            }
        }

        // Initial calculation & on resize to keep responsive
        updateOffset()
        window.addEventListener('resize', updateOffset)
        return () => window.removeEventListener('resize', updateOffset)
    }, [])


    useEffect(() => {
        if(modalTrigger === modalTriggerType.deleteChatMessages){  
            setHistory(null)
            setMessages([])
            updateModalTrigger(null)
        }
    }, [modalTrigger])
    
    const renderMessageItem = useCallback(
        (item: any, index: number) => (
            <WorkspaceDetailMessageItem
                key={index}
                item={item}
            />
        ),
        [handleSendMessageChat]
    );

    const handleCollapsedSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (collapsedInput.trim() || files.length > 0) {
            handleSendMessageChat(collapsedInput, files);
            setCollapsedInput("");
            setFiles([]);
            setIsChatExpanded(true)
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const newFiles = Array.from(e.target.files);
            setFiles(prev => [...prev, ...newFiles]);
        }
    };
    
    const handlePaste = (e: React.ClipboardEvent) => {
        const items = e.clipboardData?.items;
        if (items) {
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const file = items[i].getAsFile();
                    if (file) {
                        setFiles(prev => [...prev, file]);
                    }
                }
            }
        }
    };
    
    const removeFile = (index: number) => {
        setFiles(files.filter((_, i) => i !== index));
    };

    const openDisclaimerModal = () => {
        showModal(modalType.aiDisclaimer);
    };

    // If this is the expanded version, always render when forceExpanded is true
    if (isChatExpanded) {
        return(
            <div className="w-full bg-white/70 backdrop-blur-[5px] rounded-lg md:shadow-sm flex flex-col h-full focus-within:bg-white focus-within:shadow-purple-300">
                <div className="p-4 border-b border-gray-200">
                    <div className="flex justify-between items-center flex-shrink-0">
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 flex items-center">
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="text-indigo-600 mr-2" />
                                Merlin
                            </h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Your AI assistant for real estate insights
                            </p>
                        </div>
                        <button 
                            onClick={() => {
                                // Set internal state to collapsed and notify parent
                                setIsChatExpanded(false);
                            }}
                            className="text-gray-500 hover:text-gray-700 p-2 md:p-2 rounded-full hover:bg-gray-100 transition-colors"
                            title="Close"
                        >
                            <FontAwesomeIcon icon={faWindowMinimize} className="h-4 w-4 md:h-3 md:w-3" />
                        </button>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                        <div className="cursor-pointer" onClick={() => setIsOpenChatList(!isOpenChatList)} >
                            <FontAwesomeIcon icon={faClockRotateLeft} className="cursor-pointer text-xs mr-1" />
                            <span className="text-xs">Chat History</span>
                        </div>
                        <div className="cursor-pointer text-red-500" onClick={() => {
                            showModal(modalType.deleteChatMessages)
                            updateModalData({
                                chatId: chatActive?.id,
                                chatName: chatActive?.name
                            })
                        }} >
                            <FontAwesomeIcon icon={faTrash} className="cursor-pointer text-xs mr-1" />
                            <span className="text-xs">Clear messages</span>
                        </div>
                    </div>
                </div>
                
                
                {
                    isOpenChatList ?
                        <div className="flex-1 p-4 overflow-y-auto min-h-0">
                            <div className="flex items-center justify-between mb-1">
                                <div className="cursor-pointer" onClick={() => setIsOpenChatList(false)}>
                                    <FontAwesomeIcon icon={faArrowLeft} className="cursor-pointer text-xs mr-1" />
                                    <span className="text-xs">Back</span>
                                </div>
                                <div className="cursor-pointer" onClick={async () => {
                                    await handleCreateChat()
                                    setIsOpenChatList(false)
                                }}>
                                    <FontAwesomeIcon icon={faPlus} className="cursor-pointer text-xs mr-1" />
                                    <span className="text-xs">Add chat</span>
                                </div>
                            </div>
                            {chatList && chatList?.chats?.length > 0 ?
                                <>
                                    {
                                        chatList?.chats?.map(item => (
                                            <div key={item?.id} className="flex items-center justify-between gap-x-4 group hover:bg-gray-100 px-2 rounded-lg mt-2">
                                                <div>
                                                    <p className="text-sm cursor-pointer"  onClick={() => {
                                                        handleChatActive(item?.id)
                                                        setIsOpenChatList(false)
                                                    }}>{item?.name}</p>
                                                </div>
                                            </div>
                                        ))
                                    }
                                </> : null    
                            }
                        </div>
                        :
                        <div className="flex-1 p-4 overflow-y-auto min-h-0" ref={messagesContainerRef}>
                            <div className="space-y-4">
                                {history && history?.messages && history?.messages?.map(renderMessageItem)}
                                {messages?.map(renderMessageItem)}
                                {messagesGenerated?.length > 0 ? <MessageGenerate messagesGenerated={messagesGenerated} /> : null}
                            </div>
                        </div>
                }
                
                <div className="flex-shrink-0">
                    <WorkspaceDetailChatInput isLoadingResponse={isLoadingResponse} handleSendMessageChat={handleSendMessageChat} />
                    
                    {/* Disclaimer link */}
                    <div className="px-4 py-2 border-t border-gray-100 text-center">
                        <button
                            onClick={openDisclaimerModal}
                            className="text-xs text-gray-500 hover:text-indigo-600 flex items-center justify-center mx-auto"
                        >
                            <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                            <span>Disclaimer: AI-generated responses may not be accurate. Always verify information.</span>
                        </button>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="fixed left-0 right-0 flex justify-center z-50" style={{ bottom: `${footerOffset}px` }}>
            <div className={`w-full max-w-2xl mx-4 bg-white/70 backdrop-blur-[5px] rounded-[25px] pl-2 pr-2 border border-gray-200 transition-all duration-200 ${isInputExpanded ? 'p-4' : ''} focus-within:bg-white focus-within:shadow-md focus-within:shadow-purple-300`}>
                <form onSubmit={handleCollapsedSubmit} className="flex flex-col">
                    <div className={`flex items-center ${isInputExpanded ? 'mb-3' : ''}`}>
                        <button 
                            type="button"
                            onClick={() => {
                                // Update both internal state and notify parent
                                setIsChatExpanded(true);
                            }}
                            className="p-2.5 text-indigo-600 hover:text-indigo-800 transition-colors"
                        >
                            <FontAwesomeIcon icon={faChevronUp} className="h-4 w-4" />
                        </button>
                        
                        <div className="flex-1 relative flex items-center pt-1">
                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-indigo-600">
                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                            </div>
                            <textarea
                                ref={textareaRef}
                                value={collapsedInput}
                                onChange={(e) => setCollapsedInput(e.target.value)}
                                onKeyDown={(e) => {
                                    if(e.key === 'Enter' && !e.shiftKey){
                                        e.preventDefault()
                                        handleCollapsedSubmit(e)
                                    }
                                }}
                                onPaste={handlePaste}
                                placeholder="Ask Merlin anything..."
                                className="w-full pl-8 pr-2 py-2.5 outline-none text-sm min-h-[40px] max-h-[200px] resize-none bg-transparent"
                                rows={1}
                            />
                        </div>
                        
                        <div className="flex items-center space-x-1">
                            <button 
                                type="button" 
                                onClick={() => fileInputRef.current?.click()}
                                className="p-2.5 text-gray-500 hover:text-indigo-600 transition-colors"
                            >
                                <FontAwesomeIcon icon={faPaperclip} className="h-4 w-4" />
                            </button>
                            <input
                                ref={fileInputRef}
                                type="file"
                                multiple
                                onChange={handleFileChange}
                                className="hidden"
                            />
                            <button 
                                type="submit" 
                                disabled={!collapsedInput.trim() && files.length === 0}
                                className="p-2.5 text-indigo-600 disabled:text-gray-300 hover:text-indigo-800 transition-colors"
                            >
                                <FontAwesomeIcon icon={faPaperPlane} className="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                    
                    {/* File previews */}
                    {files.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-1 mb-3 px-2">
                            {files.map((file, index) => (
                                <div key={index} className="relative inline-block group">
                                    {file.type.startsWith('image/') ? (
                                        <div className="h-16 w-16 rounded-lg overflow-hidden border border-gray-200 shadow-sm">
                                            <img
                                                src={URL.createObjectURL(file)}
                                                alt={`Attachment ${index + 1}`}
                                                className="h-full w-full object-cover"
                                            />
                                        </div>
                                    ) : (
                                        <div className="h-16 w-16 rounded-lg flex items-center justify-center bg-gray-100 border border-gray-200 shadow-sm">
                                            <FontAwesomeIcon icon={faPaperclip} className="text-gray-500" />
                                        </div>
                                    )}
                                    <button
                                        type="button"
                                        onClick={() => removeFile(index)}
                                        className="absolute -top-1.5 -right-1.5 bg-red-500 text-white rounded-full h-5 w-5 flex items-center justify-center text-xs shadow-sm hover:bg-red-600 transition-colors"
                                    >
                                        ×
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </form>
            </div>
        </div>
    )
}