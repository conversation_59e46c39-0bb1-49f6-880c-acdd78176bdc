import modalType from "@/constants/modalType";
import { useModal } from "@/context/ModalContext";
import { usePortfolio } from "@/context/PortfolioContext";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
	faFilePdf,
	faTrash,
	faBuilding,
	faSearch
} from "@fortawesome/free-solid-svg-icons";
import Image from "next/image";
import Spinner from "@/components/UI/Spinner";
import PropertyImage from "./PortfolioManagement/PropertyImage";
import { useParams, useRouter } from "next/navigation";

export default function WorkspaceDetailProperties(){
	const { showModal, updateModalData } = useModal()
	const { properties, isLoadingProperties, selectedPortfolio, propertyStats } = usePortfolio()
	const router = useRouter()
	const params = useParams()


	// Get completion color based on score
	const getCompletionColor = (score: number) => {
		if (score >= 80) return 'text-green-600 bg-green-100'
		if (score >= 60) return 'text-yellow-600 bg-yellow-100'
		if (score >= 40) return 'text-orange-600 bg-orange-100'
		return 'text-red-600 bg-red-100'
	}

	return(
		<>
			{/* Portfolio Name Header */}
			{selectedPortfolio && (
				<div className="flex items-center justify-between mb-6">
					<div className="flex items-center gap-2">
						<h2 className="text-2xl font-bold text-gray-900">{selectedPortfolio.name}</h2>
						<p className="text-sm text-gray-600 mt-1">
							{properties?.length || 0} {properties?.length === 1 ? 'property' : 'properties'}
						</p>
					</div>
					<button 
						onClick={() => {
						showModal(modalType.documentUpload)
							updateModalData({
								propId: null,
								selectedPortfolioId: selectedPortfolio?.id,
								individual: false
							})
						}}
						className="cursor-pointer flex items-center px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors"
					>
						<FontAwesomeIcon icon={faFilePdf} className="mr-2 h-4 w-4" />
						Document Upload
					</button>
				</div>
			)}

			{isLoadingProperties ? (
				<div className="flex flex-col items-center justify-center py-10">
					<Spinner size="lg" className="mb-3" />
					<p className="text-sm text-gray-600">Loading properties...</p>
				</div>
			) : (
				<div className="space-y-4">
					
					{/* Properties Table */}
					<div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Image</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
									<th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Units</th>
									<th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Docs</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AI Summary</th>
									<th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
										Completion
									</th>
									<th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12"></th>
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{properties?.map((property: { [key: string]: any }) => {
									const stats = propertyStats?.find(stat => stat?.id === property?.prop_id)
									return (
										<tr key={property.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => {
											const params = new URLSearchParams(window.location.search);
											params.set('addressId', property?.id);
											params.set('marketAddress', `${property?.address} ${property?.city} ${property?.state} ${property?.zip}`);
											router.push(`${window.location.pathname}?${params.toString()}`);
										}}>
											{/* Image */}
											<td className="px-6 py-4 whitespace-nowrap">
												<div className="w-24 h-24 rounded-md overflow-hidden bg-gray-100 relative">
													{property?.main_img_url ? (
														<Image src={property.main_img_url} alt={property.address} fill className="object-cover" />
													) : property?.address && property?.city && property?.state ? (
														<PropertyImage address={property.address} city={property.city} state={property.state} zip={property.zip} className="w-full h-full" />
													) : (
														<Image src="/default-home-list-img.svg" alt="default" fill className="object-contain opacity-50" />
													)}
												</div>
											</td>

											{/* Address */}
											<td className="px-6 py-4 whitespace-normal max-w-sm">
												<div className="font-medium text-gray-900 line-clamp-2">{property.address}</div>
												<div className="text-sm text-gray-500">{property.city}, {property.state} {property.zip}</div>
											</td>

											{/* Units */}
											<td className="px-6 py-4 text-center text-sm text-gray-700">
												{stats ? stats.totalUnits : 0}
											</td>

											{/* Documents */}
											<td className="px-6 py-4 text-center text-sm text-gray-700">
												{stats ? stats.documentsCount : 0}
											</td>

											{/* AI Summary */}
											<td className="px-6 py-4 whitespace-normal max-w-md">
												{property.ai_summary ? (
													<p className="text-sm text-gray-700 line-clamp-3">
														{typeof property.ai_summary === 'string' ? property.ai_summary : property.ai_summary.summary || 'AI analysis available'}
													</p>
												) : (
													<span className="text-sm text-gray-400">-</span>
												)}
											</td>

											{/* Completion */}
											<td className="px-6 py-4 text-center">
												{stats ? (
													<div className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getCompletionColor(stats.completionScore)}`}>
														{stats.completionScore}%
													</div>
												) : (
													<span className="text-sm text-gray-400">-</span>
												)}
											</td>

											{/* Delete Action */}
											<td className="px-6 py-4 whitespace-nowrap text-center">
												<button 
													onClick={(e) => {
														e.stopPropagation();
														showModal(modalType.deleteAddress);
														updateModalData({ addressId: property?.id, portfolioId: selectedPortfolio?.id, name: property?.address });
													}} 
													className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors cursor-pointer" 
													title="Delete property"
												>
													<FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
												</button>
											</td>
										</tr>
									)
								})}
							</tbody>
						</table>
					</div>

					{/* Empty State */}
					{(!properties || properties.length === 0) && (
						<div className="text-center py-12">
							<FontAwesomeIcon icon={faBuilding} className="h-12 w-12 text-gray-400 mb-4" />
							<h3 className="text-lg font-medium text-gray-900 mb-2">No Properties Found</h3>
							<p className="text-sm text-gray-500 mb-6">
								Add your first property to get started with portfolio analysis
							</p>
							<Link 
								href={`/workspace/${params?.id}/search`}
								className="inline-flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors shadow-sm hover:shadow-md"
							>
								<FontAwesomeIcon icon={faSearch} className="h-4 w-4" />
								Search Properties
							</Link>
						</div>
					)}
				</div>
			)}
		</>
	)
}