'use client'
import { usePathname } from "next/navigation"
import Footer from "./Footer"
import PortfolioIdInfo from "../WorkspacePageDetail/PortfolioIdInfo"


/**
 * Client component to conditionally render the footer based on route
 */
export default function ClientFooter() {
  const pathname = usePathname()
  const isWorkspacePage = pathname?.includes('/workspace')
  
  if (isWorkspacePage) {
    return <PortfolioIdInfo />
  }
  
  return <Footer />
}
