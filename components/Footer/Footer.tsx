'use client'
import Link from 'next/link';

export default function Footer() {
    return (
        <footer className="fixed bottom-0 left-0 right-0 z-50">
            {/* Blurred background layer */}
            <div className="w-full fixed top-0 left-0 z-50 bg-transparent backdrop-blur-[5px]">
                <nav className="w-full transition-all duration-300">
                    <div className="mx-auto px-4 sm:px-6 lg:px-8">
                        <div 
                            className="absolute inset-0"
                            style={{
                                borderTop: '5px solid transparent',
                                borderImage: 'linear-gradient(to bottom, white, transparent) 1',
                            }}
                        />
                    </div>
                </nav>
            </div>
            
            {/* Text content layer - above the blur */}
            <div className="relative z-10 px-4 py-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.3)', backdropFilter: 'blur(5px)' }}>
                <div className="flex justify-center items-center gap-6 text-sm text-gray-400">
                    <Link
                        href="https://relm.ai/pro"
                        className="hover:text-black transition-colors"
                    >
                        About
                    </Link>
                    <Link
                        href="https://relm.ai/terms-of-use"
                        className="hover:text-black transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        Terms
                    </Link>
                    <Link
                        href="https://relm.ai/privacy-policy"
                        className="hover:text-black transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        Privacy
                    </Link>
                    <span>© {new Date().getFullYear()} Relm, Inc.</span>
                </div>
            </div>
        </footer>
    )
}