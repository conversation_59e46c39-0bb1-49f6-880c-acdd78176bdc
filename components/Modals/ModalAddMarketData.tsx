'use client';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faXmark, faCalendarAlt, faDollarSign, faBed, faBath, faRulerCombined, faFileText } from '@fortawesome/free-solid-svg-icons';
import ModalBase from '@/components/UI/ModalBase';

interface ModalAddMarketDataProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (marketDataEntry: MarketDataEntry) => void;
    isLoading?: boolean;
}

interface MarketDataEntry {
    date: string;
    price: number;
    listingType: string;
    beds: number;
    baths: number;
    sqft: number;
    description: string;
}

interface MarketDataErrors {
    date?: string;
    price?: string;
    listingType?: string;
    beds?: string;
    baths?: string;
    sqft?: string;
    description?: string;
}

export default function ModalAddMarketData({ isOpen, onClose, onSubmit, isLoading = false }: ModalAddMarketDataProps) {
    const [formData, setFormData] = useState<MarketDataEntry>({
        date: new Date().toISOString().split('T')[0],
        price: 0,
        listingType: 'for_rent',
        beds: 0,
        baths: 0,
        sqft: 0,
        description: ''
    });

    const [errors, setErrors] = useState<MarketDataErrors>({});

    const handleInputChange = (field: keyof MarketDataEntry, value: string | number) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    const validateForm = () => {
        const newErrors: MarketDataErrors = {};
        
        if (!formData.date) {
            newErrors.date = 'Date is required';
        }
        
        if (!formData.price || formData.price <= 0) {
            newErrors.price = 'Price must be greater than 0';
        }
        
        if (!formData.description.trim()) {
            newErrors.description = 'Description is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    const handleClose = () => {
        setFormData({
            date: new Date().toISOString().split('T')[0],
            price: 0,
            listingType: 'for_rent',
            beds: 0,
            baths: 0,
            sqft: 0,
            description: ''
        });
        setErrors({});
        onClose();
    };

    return (
        <ModalBase
            isOpen={isOpen}
            onClose={handleClose}
            title="Add Market Data Entry"
            subtitle="Manually add a market data point for this unit"
            icon={faPlus}
            iconBgColor="bg-green-100"
            iconColor="text-green-600"
            maxWidth="max-w-2xl"
            actions={
                <div className="flex justify-end gap-3">
                    <button
                        onClick={handleClose}
                        disabled={isLoading}
                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleSubmit}
                        disabled={isLoading}
                        className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                    >
                        <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                        {isLoading ? 'Adding...' : 'Add Entry'}
                    </button>
                </div>
            }
        >
            <div className="space-y-6">
                {/* Date and Price Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            <FontAwesomeIcon icon={faCalendarAlt} className="h-4 w-4 mr-2 text-gray-500" />
                            Date
                        </label>
                        <input
                            type="date"
                            value={formData.date}
                            onChange={(e) => handleInputChange('date', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors ${
                                errors.date ? 'border-red-300' : 'border-gray-200'
                            }`}
                        />
                        {errors.date && <p className="text-red-500 text-xs mt-1">{errors.date}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            <FontAwesomeIcon icon={faDollarSign} className="h-4 w-4 mr-2 text-gray-500" />
                            Price
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 font-medium">$</span>
                            </div>
                            <input
                                type="number"
                                value={formData.price || ''}
                                onChange={(e) => handleInputChange('price', Number(e.target.value))}
                                placeholder="0"
                                className={`w-full pl-8 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors ${
                                    errors.price ? 'border-red-300' : 'border-gray-200'
                                }`}
                            />
                        </div>
                        {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
                    </div>
                </div>

                {/* Listing Type */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Listing Type
                    </label>
                    <select
                        value={formData.listingType}
                        onChange={(e) => handleInputChange('listingType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors"
                    >
                        <option value="for_rent">For Rent</option>
                        <option value="rented">Rented</option>
                        <option value="for_sale">For Sale</option>
                        <option value="sold">Sold</option>
                        <option value="pending">Pending</option>
                        <option value="market_analysis">Market Analysis</option>
                        <option value="off_market">Off Market</option>
                    </select>
                </div>

                {/* Property Details Row */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            <FontAwesomeIcon icon={faBed} className="h-4 w-4 mr-2 text-gray-500" />
                            Bedrooms
                        </label>
                        <input
                            type="number"
                            value={formData.beds || ''}
                            onChange={(e) => handleInputChange('beds', Number(e.target.value))}
                            placeholder="0"
                            min="0"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            <FontAwesomeIcon icon={faBath} className="h-4 w-4 mr-2 text-gray-500" />
                            Bathrooms
                        </label>
                        <input
                            type="number"
                            step="0.5"
                            value={formData.baths || ''}
                            onChange={(e) => handleInputChange('baths', Number(e.target.value))}
                            placeholder="0"
                            min="0"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            <FontAwesomeIcon icon={faRulerCombined} className="h-4 w-4 mr-2 text-gray-500" />
                            Square Feet
                        </label>
                        <input
                            type="number"
                            value={formData.sqft || ''}
                            onChange={(e) => handleInputChange('sqft', Number(e.target.value))}
                            placeholder="0"
                            min="0"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors"
                        />
                    </div>
                </div>

                {/* Description */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FontAwesomeIcon icon={faFileText} className="h-4 w-4 mr-2 text-gray-500" />
                        Description
                    </label>
                    <textarea
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter a description for this market data entry..."
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-100 focus:border-green-300 transition-colors resize-none ${
                            errors.description ? 'border-red-300' : 'border-gray-200'
                        }`}
                    />
                    {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                </div>

                {/* Info Box */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-blue-800 mb-2">ℹ️ Manual Entry Information</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                        <li>• This entry will be marked as "Manual Entry" with 100% confidence</li>
                        <li>• All fields except beds, baths, and sqft are optional</li>
                        <li>• You can edit this entry later from the market data list</li>
                    </ul>
                </div>
            </div>
        </ModalBase>
    );
} 