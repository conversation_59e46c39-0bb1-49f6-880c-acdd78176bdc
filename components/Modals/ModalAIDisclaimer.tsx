'use client'
import { useModal } from "@/context/ModalContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";

export default function ModalAIDisclaimer() {
    const { closeModal } = useModal();

    return (
        <div className="relative w-full max-w-md mx-auto mt-[10vh] bg-white rounded-xl shadow-lg p-6">
            <div className="flex flex-col">
                <div className="flex items-center space-x-3 mb-4">
                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-amber-500 h-6 w-6" />
                    <h3 className="text-lg font-semibold text-gray-900">AI Assistant Disclaimer</h3>
                </div>
                
                <div className="mb-6 text-sm text-gray-600 space-y-4">
                    <p>
                        <span className="font-semibold">Information Accuracy:</span> <PERSON>, our AI assistant, uses advanced machine learning to provide information about real estate and your portfolio. However, it may occasionally provide inaccurate, incomplete, or outdated information.
                    </p>
                    
                    <p>
                        <span className="font-semibold">Verification Required:</span> Always verify any information, analyses, or recommendations provided by Merlin with official sources, professional advisors, or additional research before making financial or business decisions.
                    </p>
                    
                    <p>
                        <span className="font-semibold">Not Financial Advice:</span> Information provided by Merlin should not be considered financial, legal, or investment advice. Consult with qualified professionals for advice tailored to your specific situation.
                    </p>

                    <p>
                        <span className="font-semibold">AI Limitations:</span> Merlin has limited knowledge about recent events, specific market conditions, or your complete financial situation. Its responses are based on general patterns and may not reflect the most current information available.
                    </p>
                </div>
                
                <div className="flex justify-end">
                    <button
                        onClick={closeModal}
                        className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2"
                    >
                        I Understand
                    </button>
                </div>
            </div>
        </div>
    );
} 