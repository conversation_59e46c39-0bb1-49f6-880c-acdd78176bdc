import { useState, useRef, useCallback, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark, faCloudArrowUp, faUpload, faCrop, faCheck, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { useModal } from "@/context/ModalContext";
import { useAuth } from "@/context/AuthContext";
import { updateWorkspaceName, ensureWorkspaceLogosBucket } from "@/actions/workspaceActions";
import { createClient } from "@/utils/supabase/client";
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

export default function ModalWorkspaceLogo() {
    const { closeModal, modalData } = useModal();
    const { handleGetUserWorkspaces } = useAuth();
    
    // States
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string>('');
    const [isDragging, setIsDragging] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string>('');
    const [uploadProgress, setUploadProgress] = useState<string>('');
    const [step, setStep] = useState<'upload' | 'crop' | 'preview'>('upload');
    const [croppedBlob, setCroppedBlob] = useState<Blob | null>(null);
    
    // Crop states
    const [crop, setCrop] = useState<Crop>({
        unit: '%',
        width: 90,
        height: 90,
        x: 5,
        y: 5
    });
    const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
    const [croppedImageUrl, setCroppedImageUrl] = useState<string>('');
    
    // Refs
    const fileInputRef = useRef<HTMLInputElement>(null);
    const imgRef = useRef<HTMLImageElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    // Ensure bucket exists on mount
    useEffect(() => {
        ensureWorkspaceLogosBucket();
    }, []);

    // File validation
    const isValidImageFile = (file: File): boolean => {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!validTypes.includes(file.type)) {
            setUploadError('Please select a valid image file (JPEG, PNG, GIF, WebP, SVG)');
            return false;
        }
        
        if (file.size > maxSize) {
            setUploadError('File size must be less than 5MB');
            return false;
        }
        
        return true;
    };

    // Handle file selection
    const handleFileSelect = (file: File) => {
        setUploadError('');
        
        if (!isValidImageFile(file)) {
            return;
        }
        
        // Clean up previous URLs
        if (previewUrl) {
            URL.revokeObjectURL(previewUrl);
        }
        if (croppedImageUrl) {
            URL.revokeObjectURL(croppedImageUrl);
        }
        
        setSelectedFile(file);
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        setCroppedBlob(null); // Reset cropped blob for new file
        setCroppedImageUrl(''); // Reset cropped image URL
        setStep('crop');
    };

    // Drag and drop handlers
    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    }, []);

    // File input change handler
    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    // Generate cropped image
    const generateCroppedImage = useCallback(async () => {
        if (!completedCrop || !imgRef.current || !canvasRef.current) {
            console.error('Missing required elements for cropping');
            return null;
        }

        const image = imgRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            console.error('Could not get canvas context');
            return null;
        }

        // Ensure image is loaded
        if (!image.complete || image.naturalWidth === 0) {
            console.error('Image not loaded properly');
            return null;
        }

        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        // Set canvas size to crop size
        const cropWidth = Math.round(completedCrop.width);
        const cropHeight = Math.round(completedCrop.height);
        
        canvas.width = cropWidth;
        canvas.height = cropHeight;

        // Clear canvas
        ctx.clearRect(0, 0, cropWidth, cropHeight);

        // Draw the cropped image
        try {
            ctx.drawImage(
                image,
                Math.round(completedCrop.x * scaleX),
                Math.round(completedCrop.y * scaleY),
                Math.round(completedCrop.width * scaleX),
                Math.round(completedCrop.height * scaleY),
                0,
                0,
                cropWidth,
                cropHeight
            );

            // Convert to WebP blob
            return new Promise<Blob | null>((resolve) => {
                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            console.error('Failed to create blob from canvas');
                            resolve(null);
                        }
                    },
                    'image/webp',
                    0.9 // Quality
                );
            });
        } catch (error) {
            console.error('Error drawing image to canvas:', error);
            return null;
        }
    }, [completedCrop]);

    // Handle crop completion
    const handleCropComplete = async () => {
        setUploadError('');
        
        try {
            const croppedBlobResult = await generateCroppedImage();
            if (croppedBlobResult) {
                const url = URL.createObjectURL(croppedBlobResult);
                setCroppedImageUrl(url);
                setCroppedBlob(croppedBlobResult);
                setStep('preview');
            } else {
                setUploadError('Failed to crop image. Please try adjusting the crop area and try again.');
            }
        } catch (error: any) {
            console.error('Error cropping image:', error);
            setUploadError('Failed to crop image. Please try again.');
        }
    };

    // Upload to Supabase
    const uploadToSupabase = async (): Promise<string> => {
        if (!selectedFile || !modalData?.id) {
            throw new Error('Missing file or workspace ID');
        }

        if (!croppedBlob) {
            throw new Error('No cropped image available. Please crop the image first.');
        }

        const supabase = createClient();

        // Create file path
        const timestamp = new Date().getTime();
        const fileName = `workspace-${modalData.id}-${timestamp}.webp`;
        const filePath = `workspacelogos/${fileName}`;

        // Delete old logo if it exists
        if (modalData.logo_url) {
            try {
                // Extract file path from URL if it's a Supabase URL
                const urlParts = modalData.logo_url.split('/workspacelogos/');
                if (urlParts.length > 1) {
                    const oldFilePath = `workspacelogos/${urlParts[1]}`;
                    await supabase.storage.from('workspacelogos').remove([oldFilePath]);
                }
            } catch (error) {
                console.log('Could not delete old logo:', error);
                // Continue with upload even if deletion fails
            }
        }

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
            .from('workspacelogos')
            .upload(filePath, croppedBlob, {
                contentType: 'image/webp',
                upsert: true
            });

        if (uploadError) {
            throw new Error(`Upload failed: ${uploadError.message}`);
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
            .from('workspacelogos')
            .getPublicUrl(filePath);

        return publicUrl;
    };

    // Handle final save
    const handleSave = async () => {
        console.log('handleSave called, croppedBlob:', croppedBlob);
        console.log('step:', step);
        console.log('modalData:', modalData);
        
        if (step !== 'preview') {
            setUploadError('Please complete the cropping step first.');
            return;
        }
        
        if (!croppedBlob) {
            setUploadError('No cropped image available. Please crop the image first.');
            return;
        }
        
        setIsUploading(true);
        setUploadError('');
        setUploadProgress('Preparing image...');

        try {
            // Upload to Supabase and get URL
            setUploadProgress('Uploading to storage...');
            const logoUrl = await uploadToSupabase();
            
            // Update workspace with new logo URL
            setUploadProgress('Updating workspace...');
            await updateWorkspaceName(modalData?.id, modalData?.name, logoUrl);
            
            // Refresh workspace list
            setUploadProgress('Refreshing workspace list...');
            handleGetUserWorkspaces();
            
            setUploadProgress('Complete!');
            
            // Close modal after a brief delay to show completion
            setTimeout(() => {
                closeModal();
            }, 500);
        } catch (error: any) {
            console.error('Error saving workspace logo:', error);
            setUploadError(error.message || 'Failed to save workspace logo');
            setUploadProgress('');
        } finally {
            setIsUploading(false);
        }
    };

    // Handle remove logo
    const handleRemoveLogo = async () => {
        setIsUploading(true);
        setUploadError('');

        try {
            // Update workspace to remove logo URL
            await updateWorkspaceName(modalData?.id, modalData?.name, '');
            
            // Refresh workspace list
            handleGetUserWorkspaces();
            
            // Close modal
            closeModal();
        } catch (error: any) {
            console.error('Error removing workspace logo:', error);
            setUploadError(error.message || 'Failed to remove workspace logo');
        } finally {
            setIsUploading(false);
        }
    };

    // Cleanup URLs on unmount
    useEffect(() => {
        return () => {
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
            }
            if (croppedImageUrl) {
                URL.revokeObjectURL(croppedImageUrl);
            }
        };
    }, [previewUrl, croppedImageUrl]);

    return (
        <div className="flex w-full h-full items-center justify-center">
            <div className="bg-white w-[600px] max-h-[90vh] overflow-y-auto rounded-2xl p-6 z-10 relative">
                <FontAwesomeIcon 
                    icon={faXmark} 
                    className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors" 
                    onClick={() => closeModal()}
                />
                
                <h3 className="text-xl font-semibold text-gray-900 text-center mb-6">
                    Workspace Logo
                </h3>

                {/* Upload Step */}
                {step === 'upload' && (
                    <div className="space-y-4">
                        <div
                            className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                                isDragging 
                                    ? 'border-indigo-500 bg-indigo-50' 
                                    : 'border-gray-300 hover:border-gray-400'
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                        >
                            <FontAwesomeIcon 
                                icon={faCloudArrowUp} 
                                className="h-12 w-12 text-gray-400 mb-4" 
                            />
                            <p className="text-lg font-medium text-gray-900 mb-2">
                                Drop your logo here, or click to browse
                            </p>
                            <p className="text-sm text-gray-500 mb-4">
                                Supports JPEG, PNG, GIF, WebP, SVG up to 5MB
                            </p>
                            <button
                                onClick={() => fileInputRef.current?.click()}
                                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                            >
                                <FontAwesomeIcon icon={faUpload} className="mr-2 h-4 w-4" />
                                Choose File
                            </button>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                onChange={handleFileInputChange}
                                className="hidden"
                            />
                        </div>

                        {/* Current Logo */}
                        {modalData?.logo_url && (
                            <div className="border rounded-xl p-4">
                                <h4 className="font-medium text-gray-900 mb-3">Current Logo</h4>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <img 
                                            src={modalData.logo_url} 
                                            alt="Current logo" 
                                            className="w-12 h-12 rounded-lg object-cover border"
                                        />
                                        <span className="text-sm text-gray-600">Current workspace logo</span>
                                    </div>
                                    <button
                                        onClick={handleRemoveLogo}
                                        disabled={isUploading}
                                        className="text-red-600 hover:text-red-700 text-sm font-medium disabled:opacity-50"
                                    >
                                        Remove Logo
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Crop Step */}
                {step === 'crop' && previewUrl && (
                    <div className="space-y-4">
                        <div className="text-center">
                            <p className="text-sm text-gray-600 mb-4">
                                Adjust the crop area to select your logo
                            </p>
                        </div>
                        
                        <div className="flex justify-center">
                            <ReactCrop
                                crop={crop}
                                onChange={(_, percentCrop) => setCrop(percentCrop)}
                                onComplete={(c) => setCompletedCrop(c)}
                                aspect={1}
                                minWidth={50}
                                minHeight={50}
                            >
                                <img
                                    ref={imgRef}
                                    src={previewUrl}
                                    alt="Crop preview"
                                    className="max-w-full max-h-96 object-contain"
                                    onLoad={() => {
                                        // Ensure initial crop is set when image loads
                                        if (imgRef.current) {
                                            const { width, height } = imgRef.current;
                                            const size = Math.min(width, height) * 0.8;
                                            const x = (width - size) / 2;
                                            const y = (height - size) / 2;
                                            
                                            setCrop({
                                                unit: 'px',
                                                width: size,
                                                height: size,
                                                x: x,
                                                y: y
                                            });
                                        }
                                    }}
                                />
                            </ReactCrop>
                        </div>

                        <div className="flex justify-between">
                            <button
                                onClick={() => setStep('upload')}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                Back
                            </button>
                            <button
                                onClick={handleCropComplete}
                                disabled={!completedCrop}
                                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <FontAwesomeIcon icon={faCrop} className="mr-2 h-4 w-4" />
                                Crop Image
                            </button>
                        </div>
                    </div>
                )}

                {/* Preview Step */}
                {step === 'preview' && croppedImageUrl && (
                    <div className="space-y-4">
                        <div className="text-center">
                            <p className="text-sm text-gray-600 mb-4">
                                Preview your workspace logo
                            </p>
                        </div>
                        
                        <div className="flex justify-center">
                            <div className="border rounded-xl p-4 bg-gray-50">
                                <img
                                    src={croppedImageUrl}
                                    alt="Logo preview"
                                    className="w-24 h-24 rounded-lg object-cover border bg-white"
                                />
                            </div>
                        </div>

                        <div className="text-center text-sm text-gray-600">
                            <p>This logo will be saved as WebP format for optimal performance</p>
                        </div>

                        <div className="flex justify-between">
                            <button
                                onClick={() => setStep('crop')}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                Back to Crop
                            </button>
                            <button
                                onClick={handleSave}
                                disabled={isUploading}
                                className="inline-flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isUploading ? (
                                    <>
                                        <FontAwesomeIcon icon={faSpinner} className="mr-2 h-4 w-4 animate-spin" />
                                        {uploadProgress || 'Saving...'}
                                    </>
                                ) : (
                                    <>
                                        <FontAwesomeIcon icon={faCheck} className="mr-2 h-4 w-4" />
                                        Save Logo
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                )}

                {/* Hidden canvas for image processing */}
                <canvas ref={canvasRef} className="hidden" />

                {/* Error message */}
                {uploadError && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-600 text-sm">{uploadError}</p>
                    </div>
                )}
            </div>
        </div>
    );
} 