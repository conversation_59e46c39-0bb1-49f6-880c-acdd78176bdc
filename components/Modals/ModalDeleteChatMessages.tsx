import {faTrashCan, faXmark} from "@fortawesome/free-solid-svg-icons";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {useModal} from "@/context/ModalContext";
import modalTriggerType from "@/constants/modalTriggerType";
import {deleteChatMessages} from "@/actions/chatActions"

export default function ModalDeleteChatMessages(){
	const { closeModal, modalData, updateModalTrigger } = useModal()

	const handleDeleteChatMessages = async () => {
		updateModalTrigger(modalTriggerType.deleteChatMessages)
        deleteChatMessages(modalData?.chatId)
		closeModal()
	}

	return (
		<div className='flex w-full h-full items-center justify-center'>
			<div className={'bg-white w-[448px] rounded-2xl p-6 z-10 relative'}>
				<FontAwesomeIcon
					icon={faXmark}
					className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors"
					onClick={() => closeModal()}
				/>
				<h3 className="text-xl font-semibold text-gray-900 text-center">Delete Chat Messages</h3>
				<p className="text-center text-gray-600 mb-6">Are you sure you want to delete messages from <span className="font-semibold">{modalData?.chatName}</span>?</p>
				<div className={'flex items-center justify-center gap-x-5'}>
					<button
						onClick={() => closeModal()}
						className="cursor-pointer px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600">
						Cancel
					</button>
					<button
						onClick={() => handleDeleteChatMessages()}
						className="cursor-pointer px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
						Delete
					</button>
				</div>
			</div>
		</div>
	)
}