import {createPortfolio} from "@/actions/workspaceActions";
import { useModal } from "@/context/ModalContext"
import { faXmark } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import modalTriggerType from "@/constants/modalTriggerType";
import { createChat } from "@/actions/chatActions";


export default function ModalCreatePortfolio() {
	const { closeModal, modalData, updateModalTrigger } = useModal()
	const [name, setName] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string>('');

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		try {
			createPortfolio(name, modalData?.workspaceId as string).then((data) => {
				closeModal()
				createChat(data?.id as string)
				updateModalTrigger(modalTriggerType.createPortfolio)
			})

		} catch (error: any) {
			console.log('Error creating portfolio:', error);
			setError(error.message || 'Failed to create portfolio');
		} finally {
			setIsLoading(false);
		}
	}

	return (
		<>
			<div className="flex w-full h-full items-center justify-center">
				<div className="bg-white w-[448px] rounded-2xl p-6 z-10 relative">
					<FontAwesomeIcon
						icon={faXmark}
						className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors"
						onClick={() => closeModal()}
					/>
					<h3 className="text-xl font-semibold text-gray-900 text-center">Create Portfolio</h3>
					<form onSubmit={handleSubmit}>
						<div className="mt-6">
							<input required className="block w-full rounded-xl border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-500 text-base transition-shadow" placeholder="Enter portfolio name" type="text" value={name} onChange={(e) => setName(e.target.value)} name="portfolio-name"></input>
						</div>
						<div className="mt-6">
							<button
								disabled={isLoading}
								className="cursor-pointer w-full px-4 py-3 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
								{isLoading ? 'Creating...' : modalData?.saveToPortfolio ? 'Create & Save to Portfolio' : 'Create Portfolio'}
							</button>
						</div>
						{error && <p className="text-red-500 text-xs text-center mt-3">{error}</p>}
					</form>
				</div>
			</div>
		</>
	)
}