import { createWorkspace } from "@/actions/workspaceActions";
import pathName from "@/constants/pathName";
import { useModal } from "@/context/ModalContext"
import { faXmark } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {useAuth} from "@/context/AuthContext";


export default function ModalCreateWorkspace() {
	const {handleGetUserWorkspaces, userDataDB} = useAuth()
    const { closeModal } = useModal()
    const router = useRouter();
    const [name, setName] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string>('');

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        
        try {
            const workspace = await createWorkspace(name);
            console.log('Workspace created:', workspace);
            
            closeModal()
	        handleGetUserWorkspaces()
            router.push(`${pathName.workspace}/${workspace.id}${!userDataDB?.is_superuser ? pathName.pricing : ''}`);
          } catch (error: any) {
            console.log('Error creating workspace:', error);
            setError(error.message || 'Failed to create workspace');
          } finally {
            setIsLoading(false);
          }
    }

    return (
        <>
            <div className="flex w-full h-full items-center justify-center">
                <div className="bg-white w-[448px] rounded-2xl p-6 z-10 relative">
                        <FontAwesomeIcon 
                            icon={faXmark} 
                            className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors" 
                            onClick={() => closeModal()}
                        />
                    <h3 className="text-xl font-semibold text-gray-900 text-center">Create Workspace</h3>
                    <form onSubmit={handleSubmit}>
                        <div className="mt-6">
                            <input required className="block w-full rounded-xl border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-500 text-base transition-shadow" placeholder="Enter workspace name" type="text" value={name} onChange={(e) => setName(e.target.value)} name="portfolio-name"></input>
                        </div>
                        <div className="mt-6">
                            <button 
                                disabled={isLoading}
                                className="cursor-pointer w-full px-4 py-3 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
                                   {isLoading ? 'Creating...' : 'Create Workspace'}
                                </button>
                        </div>
                        {error && <p className="text-red-500 text-xs text-center mt-3">{error}</p>}
                    </form>
                </div>
            </div>
        </>
    )
}