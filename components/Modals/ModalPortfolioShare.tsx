import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark, faUser, faEnvelope, faShare, faUserGroup, faToggleOn, faToggleOff, faCheck } from "@fortawesome/free-solid-svg-icons";
import { useModal } from "@/context/ModalContext";
import { useEffect, useState } from "react";
import { checkEmailExists, dataroomEvents, getSharedList, updateDataRoomPermissions } from "@/actions/dataRoomActions";

export default function ModalPortfolioShare() {
    const { closeModal, modalData } = useModal();
    const [sharedData, setSharedData] = useState<{ [key: string]: any } | null>(null);
    const [canShare, setCanShare] = useState(false);
    const [canDownload, setCanDownload] = useState(false);
    const [firstName, setFirstName] = useState('');
    const [email, setEmail] = useState('');

    const [errorFirstName, setErrorFirstName] = useState('');
    const [errorEmail, setErrorEmail] = useState('');
    const [isSendingSuccess, setIsSendingSuccess] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const validateEmail = (email: string) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    };

    const handleShare = async () => {
        setIsLoading(true);
        setErrorFirstName('');
        setErrorEmail('');
      
        if (!firstName) {
          setErrorFirstName('First name is required');
        }
      
        if (!email) {
          setErrorEmail('Email is required');
        } else if (!validateEmail(email)) {
          setErrorEmail('Invalid email format');
        }
      
        if (!firstName || !email || !validateEmail(email)) {
          setIsLoading(false);
          return;
        }

        try {
            const emailExists = await checkEmailExists(email);
            
            const res = await fetch('/api/jwt-token-generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    owner_id: modalData?.ownerId,
                    portfolio_id: modalData?.portfolioId,
                    sender_id: modalData?.senderId,
                    can_share: canShare,
                    can_download: canDownload,
                    first_name: firstName,
                    email: email,
                    isRegistered: emailExists ? true : false,
                    tokenType: 'invite_portfolio',
                }),
            });
            
            const { token } = await res.json();
            
            await fetch('/api/invite-send-email', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: email,
                  first_name: firstName,
                  portfolio_name: modalData?.portfolioName,
                  invite_link: `${process.env.NEXT_PUBLIC_APP_URL}/portfolio/${modalData?.portfolioId}?token=${token}`,
                }),
            });

            setIsSendingSuccess(true);
            dataroomEvents(modalData?.senderId, 'share', modalData?.portfolioId);

            // Refresh shared list
            const updatedList = await getSharedList(modalData?.senderId, modalData?.portfolioId);
            setSharedData(updatedList);

            setTimeout(() => {
                setIsSendingSuccess(false);
                setFirstName('');
                setEmail('');
                setCanShare(false);
                setCanDownload(false);
            }, 3000);
        } catch (error) {
            console.error('Error sharing portfolio:', error);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if(modalData?.senderId) {
            getSharedList(modalData?.senderId, modalData?.portfolioId).then((res) => {
                setSharedData(res);
            });
        }
    }, [modalData]);

    const ToggleSwitch = ({ enabled, onChange, label }: { enabled: boolean; onChange: () => void; label: string }) => (
        <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-gray-700 min-w-[80px]">{label}</span>
            <button
                onClick={onChange}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                    enabled ? 'bg-indigo-600' : 'bg-gray-300'
                }`}
            >
                <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200 ease-in-out ${
                        enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                />
            </button>
        </div>
    );

    return (
        <div className="flex w-full h-full items-center justify-center p-4">
            <div className="bg-white w-full max-w-2xl rounded-3xl shadow-2xl overflow-hidden">
                {/* Header */}
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6 relative">
                    <button
                        onClick={closeModal}
                        className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
                    >
                        <FontAwesomeIcon icon={faXmark} className="h-6 w-6" />
                    </button>
                    <div className="flex items-center gap-3">
                        <div className="bg-white/20 rounded-full p-3">
                            <FontAwesomeIcon icon={faShare} className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h2 className="text-2xl font-bold text-white">Share Portfolio</h2>
                            <p className="text-indigo-100 text-sm mt-1">
                                Invite collaborators to view "{modalData?.portfolioName}"
                            </p>
                        </div>
                    </div>
                </div>

                <div className="p-8">
                    {/* Invite New User Card */}
                    <div className="bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl p-6 border border-gray-200 mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                            <FontAwesomeIcon icon={faUser} className="h-5 w-5 text-indigo-600" />
                            Invite New User
                        </h3>
                        
                        {/* Input Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <FontAwesomeIcon icon={faUser} className="h-4 w-4 text-gray-400" />
                                </div>
                                <input
                                    type="text"
                                    placeholder="First name"
                                    value={firstName}
                                    onChange={(e) => setFirstName(e.target.value)}
                                    className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                                        errorFirstName ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                                    }`}
                                />
                                {errorFirstName && (
                                    <p className="text-red-500 text-xs mt-1 ml-1">{errorFirstName}</p>
                                )}
                            </div>
                            
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <FontAwesomeIcon icon={faEnvelope} className="h-4 w-4 text-gray-400" />
                                </div>
                                <input
                                    type="email"
                                    placeholder="Email address"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                                        errorEmail ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                                    }`}
                                />
                                {errorEmail && (
                                    <p className="text-red-500 text-xs mt-1 ml-1">{errorEmail}</p>
                                )}
                            </div>
                        </div>

                        {/* Permissions */}
                        <div className="flex flex-col sm:flex-row gap-6 mb-6">
                            <ToggleSwitch
                                enabled={canShare}
                                onChange={() => setCanShare(!canShare)}
                                label="Can Share"
                            />
                            <ToggleSwitch
                                enabled={canDownload}
                                onChange={() => setCanDownload(!canDownload)}
                                label="Can Download"
                            />
                        </div>

                        {/* Send Invite Button */}
                        <div className="flex justify-end">
                            <button
                                onClick={handleShare}
                                disabled={isLoading}
                                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                {isLoading ? (
                                    <div className="flex items-center gap-2">
                                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                                        Sending...
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2">
                                        <FontAwesomeIcon icon={faShare} className="h-4 w-4" />
                                        Send Invite
                                    </div>
                                )}
                            </button>
                        </div>

                        {/* Success Message */}
                        {isSendingSuccess && (
                            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-xl">
                                <div className="flex items-center gap-2 text-green-800">
                                    <FontAwesomeIcon icon={faCheck} className="h-4 w-4" />
                                    <span className="font-medium">Invitation sent successfully!</span>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Shared Users List */}
                    {sharedData && sharedData.length > 0 && (
                        <div className="bg-white rounded-2xl border border-gray-200">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                    <FontAwesomeIcon icon={faUserGroup} className="h-5 w-5 text-indigo-600" />
                                    Shared with ({sharedData.length})
                                </h3>
                            </div>
                            
                            <div className="max-h-80 overflow-y-auto">
                                {sharedData.map((item: any, index: number) => (
                                    <div
                                        key={item.id}
                                        className={`px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors ${
                                            index !== sharedData.length - 1 ? 'border-b border-gray-100' : ''
                                        }`}
                                    >
                                        <div className="flex items-center gap-4">
                                            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                                <span className="text-white font-semibold text-sm">
                                                    {item.userData.email.charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">{item.userData.email}</p>
                                                <p className="text-sm text-gray-500">
                                                    {item.userData.first_name || 'Invited User'}
                                                </p>
                                            </div>
                                        </div>
                                        
                                        <div className="flex items-center gap-6">
                                            <div className="flex items-center gap-3">
                                                <span className="text-sm font-medium text-gray-700 min-w-[80px]">Can Share</span>
                                                <button
                                                    onClick={() => {
                                                        if (!sharedData) return;
                                                        const updatedCanShare = !item.can_share;
                                                      
                                                        setSharedData(
                                                          sharedData.map((dataItem: any) =>
                                                            dataItem.id === item.id
                                                              ? { ...dataItem, can_share: updatedCanShare }
                                                              : dataItem
                                                          )
                                                        );
                                                      
                                                        updateDataRoomPermissions(
                                                          updatedCanShare,
                                                          item.can_download,
                                                          item.userData.id,
                                                          modalData?.portfolioId
                                                        );
                                                    }}
                                                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                                                        item.can_share ? 'bg-indigo-600' : 'bg-gray-300'
                                                    }`}
                                                >
                                                    <span
                                                        className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200 ease-in-out ${
                                                            item.can_share ? 'translate-x-6' : 'translate-x-1'
                                                        }`}
                                                    />
                                                </button>
                                            </div>
                                            
                                            <div className="flex items-center gap-3">
                                                <span className="text-sm font-medium text-gray-700 min-w-[90px]">Can Download</span>
                                                <button
                                                    onClick={() => {
                                                        if (!sharedData) return;
                                                        const updatedCanDownload = !item.can_download;
                                                      
                                                        setSharedData(
                                                          sharedData.map((dataItem: any) =>
                                                            dataItem.id === item.id
                                                              ? { ...dataItem, can_download: updatedCanDownload }
                                                              : dataItem
                                                          )
                                                        );
                                                      
                                                        updateDataRoomPermissions(
                                                          item.can_share,
                                                          updatedCanDownload,
                                                          item.userData.id,
                                                          modalData?.portfolioId
                                                        );
                                                    }}
                                                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                                                        item.can_download ? 'bg-indigo-600' : 'bg-gray-300'
                                                    }`}
                                                >
                                                    <span
                                                        className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200 ease-in-out ${
                                                            item.can_download ? 'translate-x-6' : 'translate-x-1'
                                                        }`}
                                                    />
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}