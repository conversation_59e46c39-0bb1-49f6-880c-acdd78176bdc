import {useModal} from "@/context/ModalContext";
import {faXmark, faPlus} from "@fortawesome/free-solid-svg-icons";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {useEffect, useState} from "react";
import {getMemberWorkspace, addMemberWorkspace} from "@/actions/workspaceActions";

export default function ModalMembersWorkspace(){
	const { closeModal, modalData } = useModal()
	const [members, setMembers] = useState<any[] | null>(null)
	const [email, setEmail] = useState<string>('')

	useEffect(() => {
		getMemberWorkspace(modalData?.id).then((data) => {
			console.log(data)
			setMembers(data)
		})
	}, []);

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault()

		try {
			addMemberWorkspace(modalData?.id, email, 'member')
		} catch (error: any) {
			console.log(error)
		}
	}

	return(
		<div className='flex w-full h-full items-center justify-center'>
			<div className={'bg-white w-[540px] rounded-2xl p-6 z-10 relative'}>
				<FontAwesomeIcon
					icon={faXmark}
					className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors"
					onClick={() => closeModal()}
				/>
				<h3 className="text-xl font-semibold text-gray-900">Manage Members</h3>
				<form onSubmit={handleSubmit}>
					<div className="flex gap-3 my-6">
						<div className='flex-1'>
							<input placeholder="Enter member's email" className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
								required type="email" value={email} onChange={(e) => setEmail(e.target.value)}/>
						</div>
						<div>
							<button type="submit"
									className="cursor-pointer flex items-center px-4 leading-[42px] bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700 disabled:opacity-50">
								<FontAwesomeIcon icon={faPlus} className="mr-2 w-3 h-3" />
								Add Member
							</button>
						</div>
					</div>
				</form>
				{
					members && members?.length > 0 ?
					<>
						<h4 className="font-medium text-gray-900 my-6">Current Members</h4>
						<div className="flex flex-col gap-2">
							{members.map((member) => (
								<div key={member?.user_id} className="border border-gray-200 rounded-lg">
									<div className="flex items-center justify-between p-4">
										<div>
											<p className="font-medium text-gray-900">{member?.user_id}</p>
											<p className="text-sm text-gray-500 capitalize">{member?.roles?.name}</p>
										</div>
									</div>
								</div>
							))}
						</div>
					</> : null
				}
			</div>
		</div>
	)
}