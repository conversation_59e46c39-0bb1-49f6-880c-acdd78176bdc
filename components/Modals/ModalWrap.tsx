'use client'
import { useModal } from "@/context/ModalContext"
//import {usePathname, useRouter} from "next/navigation";
import modalType from "@/constants/modalType";
import ModalCreateWorkspace from "./ModalCreateWorkspace";
import ModalEditWorkspace from "./ModalEditWorkspace";
import ModalWorkspaceLogo from "./ModalWorkspaceLogo";
import ModalCreatePortfolio from "@/components/Modals/ModalCreatePortfolio";
import ModalDeleteWorkspace from "@/components/Modals/ModalDeleteWorkspace";
import ModalMembersWorkspace from "@/components/Modals/ModalMembersWorkspace";
import ModalDeleteAddress from "@/components/Modals/ModalDeleteAddress";
import ModalDocumentUpload from "@/components/Modals/ModalDocumentUpload/ModalDocumentUpload";
import ModalDeletePortfolio from "./ModalDeletePortfolio";
import ModalAIDisclaimer from "./ModalAIDisclaimer";
import ModalDeletePortfolioDocumnet from "./ModalDeletePortfolioDocumnet";
import ModalPortfolioShare from "./ModalPortfolioShare";
import ModalDeleteChat from "./ModalDeleteChat";
import ModalDeleteChatMessages from "./ModalDeleteChatMessages";

export default function ModalWrap() {
	const { modal, closeModal } = useModal()

	const handleModalClose = () => {
		closeModal()
	}
    
	return (
		<>
            {
                modal ?
                    <div className="fixed w-full h-full left-0 top-0 z-[99999] p-4 bg-black/30 backdrop-blur-sm transition-opacity">
                        <div className={'absolute w-full h-full left-0 top-0'} onClick={() => handleModalClose()}></div>

                        {modal === modalType.createWorkspace ? <ModalCreateWorkspace /> : null}
                        {modal === modalType.editWorkspace ? <ModalEditWorkspace /> : null}
                        {modal === modalType.workspaceLogo ? <ModalWorkspaceLogo /> : null}
	                    {modal === modalType.deleteWorkspace ? <ModalDeleteWorkspace /> : null}
	                    {modal === modalType.membersWorkspace ? <ModalMembersWorkspace /> : null}
	                    {modal === modalType.createPortfolio ? <ModalCreatePortfolio /> : null}
	                    {modal === modalType.deletePortfolio ? <ModalDeletePortfolio /> : null}
	                    {modal === modalType.deleteAddress ? <ModalDeleteAddress /> : null}
	                    {modal === modalType.documentUpload ? <ModalDocumentUpload /> : null}
                        {modal === modalType.aiDisclaimer ? <ModalAIDisclaimer /> : null}
                        {modal === modalType.deletePortfolioDocument ? <ModalDeletePortfolioDocumnet /> : null}
                        {modal === modalType.portfolioShare ? <ModalPortfolioShare /> : null}
						{modal === modalType.deleteChat ? <ModalDeleteChat /> : null}
						{modal === modalType.deleteChatMessages ? <ModalDeleteChatMessages /> : null}
                    </div>
                : null
            }
        </>

	)
}