import { faChevronDown, faChevronRight, faCloudArrowUp, faXmark, faSpinner, faCheck, faFileAlt, faBuilding, faTag, faExpand, faHome, faUsers, faRobot, faMagicWandSparkles, faCheckCircle } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useModal } from "@/context/ModalContext";
import { useState, useCallback, useEffect, useRef } from "react";
import { getPropUnits, documentsMetadata, processDocuments, getPropUnitsModal, getPropsUnitsModal } from "@/actions/documentUploadActions";
import { Dropdown } from "primereact/dropdown";
import { createClient } from "@/utils/supabase/client";
import useDragAndDrop from "@/helpers/hooks/documentUpload/useDragAndDrop";
import { useSearchParams } from "next/navigation";
import { useAuth } from "@/context/AuthContext";
import MultiLevelDropdown from "./MultiLevelDropdown";
import MultiLevelAddressDropdown from "./MultiLevelAddressDropdown";
import { documentGenerateSlides } from "@/actions/documentGenerateSlides";
import modalTriggerType from "@/constants/modalTriggerType";

// Types for address and unit data
interface Unit {
    id: string;
    unit: string;
    beds: number;
    baths: number;
}

interface Address {
    id: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    units: Unit[];
}

// AI Prediction interface
interface AIPrediction {
    property: string;
    unit: string | null;
    documentType: {
        main: string;
        sub: string | null;
    };
    confidence: number;
}

const MAX_FILES_LIMIT = 50;

export default function ModalDocumentUpload()  {
    const { closeModal, modalData, updateModalTrigger } = useModal();
    const { user } = useAuth();
	const { isDragging, files, setFiles, errorMessage, handleDragOver, handleDragLeave, handleDrop, handleFileChange, removeFile } = useDragAndDrop();
    const portfolioId = modalData?.selectedPortfolioId;
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')

    // State to track document type for each file
    const [fileDocumentTypes, setFileDocumentTypes] = useState<{[key: number]: {main: string, sub: string | null}}>({});
    
    // State for addresses data
    const [addresses, setAddresses] = useState<Address[]>([]);
    
    // State to track address/unit selection for each file
    const [fileAddressSelections, setFileAddressSelections] = useState<{[key: number]: {address: string, unit: string | null}}>({});
    
    // State to track upload status for each file
    const [fileUploadStatus, setFileUploadStatus] = useState<{[key: number]: 'idle' | 'uploading' | 'success' | 'error'}>({});
    
    // State to track upload errors for each file
    const [fileUploadErrors, setFileUploadErrors] = useState<{[key: number]: string}>({});

    // State to track which dropdowns are expanded
    const [expandedAddressDropdowns, setExpandedAddressDropdowns] = useState<{[key: number]: boolean}>({});
    const [expandedDocTypeDropdowns, setExpandedDocTypeDropdowns] = useState<{[key: number]: boolean}>({});

    // State for file preview
    const [hoveredFile, setHoveredFile] = useState<number | null>(null);
    // const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    // AI Coding states
    const [aiCodingStatus, setAiCodingStatus] = useState<{[key: number]: 'idle' | 'loading' | 'success' | 'error'}>({});
    const [aiPredictions, setAiPredictions] = useState<{[key: number]: AIPrediction}>({});

    // Helper function to auto-select unit for new files
    const autoSelectUnitForNewFiles = (startIndex: number, count: number) => {
        if (modalData?.unitId && modalData?.unitNumber && addresses.length > 0) {
            const address = addresses[0];
            const targetUnit = address.units.find(unit => unit.id === modalData.unitId);
            
            if (targetUnit) {
                const preSelection = {
                    address: address.address,
                    unit: targetUnit.unit
                };
                
                const newSelections: {[key: number]: {address: string, unit: string | null}} = {};
                for (let i = 0; i < count; i++) {
                    newSelections[startIndex + i] = preSelection;
                }
                
                setFileAddressSelections(prev => ({
                    ...prev,
                    ...newSelections
                }));
            }
        }
    };

    // Custom file change handler with limit
    const handleFileChangeWithLimit = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = Array.from(event.target.files || []);
        const totalFiles = files.length + selectedFiles.length;
        
        if (totalFiles > MAX_FILES_LIMIT) {
            alert(`Maximum ${MAX_FILES_LIMIT} files allowed per upload session. You're trying to add ${selectedFiles.length} files but already have ${files.length} files selected.`);
            return;
        }
        
        const currentFileCount = files.length;
        handleFileChange(event);
        
        // Auto-select unit for new files after they're added
        setTimeout(() => {
            autoSelectUnitForNewFiles(currentFileCount, selectedFiles.length);
        }, 0);
    };

    // Custom drop handler with limit
    const handleDropWithLimit = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const droppedFiles = Array.from(event.dataTransfer.files);
        const totalFiles = files.length + droppedFiles.length;
        
        if (totalFiles > MAX_FILES_LIMIT) {
            alert(`Maximum ${MAX_FILES_LIMIT} files allowed per upload session. You're trying to add ${droppedFiles.length} files but already have ${files.length} files selected.`);
            return;
        }
        
        const currentFileCount = files.length;
        handleDrop(event);
        
        // Auto-select unit for new files after they're added
        setTimeout(() => {
            autoSelectUnitForNewFiles(currentFileCount, droppedFiles.length);
        }, 0);
    };

    // AI Coding function
    const handleAICoding = async (fileIndex: number, file: File) => {
        setAiCodingStatus(prev => ({
            ...prev,
            [fileIndex]: 'loading'
        }));

        try {
            // Call AI API with just file metadata (no file content conversion)
            const response = await fetch('/api/ai-document-coding', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                    addresses: addresses,
                    portfolioId: portfolioId
                })
            });

            if (!response.ok) {
                throw new Error('AI coding failed');
            }

            const prediction: AIPrediction = await response.json();
            
            // Apply the prediction
            setAiPredictions(prev => ({
                ...prev,
                [fileIndex]: prediction
            }));

            // Auto-apply the predictions
            if (prediction.property) {
                updateFileAddressSelection(fileIndex, {
                    address: prediction.property,
                    unit: prediction.unit
                });
            }

            if (prediction.documentType) {
                updateFileDocumentType(fileIndex, prediction.documentType);
            }

            setAiCodingStatus(prev => ({
                ...prev,
                [fileIndex]: 'success'
            }));

            // Reset success state after 3 seconds
            setTimeout(() => {
                setAiCodingStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'idle'
                }));
            }, 3000);

        } catch (error) {
            console.error('AI coding error:', error);
            setAiCodingStatus(prev => ({
                ...prev,
                [fileIndex]: 'error'
            }));

            // Reset error state after 3 seconds
            setTimeout(() => {
                setAiCodingStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'idle'
                }));
            }, 3000);
        }
    };

    // Function to update document type for specific file
    const updateFileDocumentType = (fileIndex: number, documentType: {main: string, sub: string | null}) => {
        setFileDocumentTypes(prev => ({
            ...prev,
            [fileIndex]: documentType
        }));
        // Close the dropdown after selection
        setExpandedDocTypeDropdowns(prev => ({
            ...prev,
            [fileIndex]: false
        }));
    };

    // Function to update address/unit selection for specific file
    const updateFileAddressSelection = (fileIndex: number, selection: {address: string, unit: string | null}) => {
        setFileAddressSelections(prev => ({
            ...prev,
            [fileIndex]: selection
        }));
        // Close the dropdown after selection
        setExpandedAddressDropdowns(prev => ({
            ...prev,
            [fileIndex]: false
        }));
    };

    // Function to toggle address dropdown
    const toggleAddressDropdown = (fileIndex: number) => {
        setExpandedAddressDropdowns(prev => ({
            ...prev,
            [fileIndex]: !prev[fileIndex]
        }));
        // Close doc type dropdown if open
        setExpandedDocTypeDropdowns(prev => ({
            ...prev,
            [fileIndex]: false
        }));
    };

    // Function to toggle document type dropdown
    const toggleDocTypeDropdown = (fileIndex: number) => {
        setExpandedDocTypeDropdowns(prev => ({
            ...prev,
            [fileIndex]: !prev[fileIndex]
        }));
        // Close address dropdown if open
        setExpandedAddressDropdowns(prev => ({
            ...prev,
            [fileIndex]: false
        }));
    };

    // Function to handle file preview
    // const handleFileHover = (fileIndex: number, file: File) => {
    //     setHoveredFile(fileIndex);
    //     if (file.type === 'application/pdf') {
    //         const url = URL.createObjectURL(file);
    //         setPreviewUrl(url);
    //     }
    // };

    // const handleFileLeave = () => {
    //     setHoveredFile(null);
    //     if (previewUrl) {
    //         URL.revokeObjectURL(previewUrl);
    //         setPreviewUrl(null);
    //     }
    // };

    // Function to select entire property (no unit)
    const selectEntireProperty = (fileIndex: number, address: Address) => {
        updateFileAddressSelection(fileIndex, {
            address: address.address,
            unit: null
        });
    };

    // Function to select specific unit
    const selectUnit = (fileIndex: number, address: Address, unit: Unit) => {
        updateFileAddressSelection(fileIndex, {
            address: address.address,
            unit: unit.unit
        });
    };

    useEffect(() => {
        if (modalData?.selectedPortfolioId) {
            getPropsUnitsModal(modalData?.individual ? modalData?.propId : modalData?.selectedPortfolioId, modalData?.individual).then(data => {
                // Transform the data to match our Address interface
                if (data && Array.isArray(data)) {
                    const transformedAddresses: Address[] = data.map((item: any) => ({
                        id: item.address_id || item.id,
                        address: item.address?.address || item.address,
                        city: item.address?.city || item.city,
                        state: item.address?.state || item.state,
                        zip: item.address?.zip || item.zip,
                        units: item.units?.map((unit: any) => ({
                            id: unit.id,
                            unit: unit.unit,
                            beds: unit.beds || 0,
                            baths: unit.baths || 0
                        })) || []
                    }));
                    setAddresses(transformedAddresses);

                    // Auto-select unit if unitId and unitNumber are provided
                    if (modalData?.unitId && modalData?.unitNumber && transformedAddresses.length > 0) {
                        const address = transformedAddresses[0]; // For individual property, there should be only one address
                        const targetUnit = address.units.find(unit => unit.id === modalData.unitId);
                        
                        if (targetUnit) {
                            // Pre-populate all files with this unit selection
                            const preSelection = {
                                address: address.address,
                                unit: targetUnit.unit
                            };
                            
                            // Set initial selections for all current files
                            const initialSelections: {[key: number]: {address: string, unit: string | null}} = {};
                            files.forEach((_, index) => {
                                initialSelections[index] = preSelection;
                            });
                            setFileAddressSelections(initialSelections);
                        }
                    }
                }
            })   
        }
    }, [modalData?.selectedPortfolioId, modalData?.unitId, modalData?.unitNumber, files.length])

    // Function to remove file document type when file is removed
    const handleRemoveFile = (index: number) => {
        removeFile(index);
        
        // Helper function to reindex state objects
        const reindexState = (prevState: {[key: number]: any}) => {
            const newState = { ...prevState };
            delete newState[index];
            const reindexedState: {[key: number]: any} = {};
            let newIndex = 0;
            for (let i = 0; i < files.length - 1; i++) {
                if (i < index && prevState[i]) {
                    reindexedState[newIndex] = prevState[i];
                } else if (i > index && prevState[i]) {
                    reindexedState[newIndex] = prevState[i];
                }
                if (i !== index) newIndex++;
            }
            return reindexedState;
        };

        setFileDocumentTypes(prev => reindexState(prev));
        setFileAddressSelections(prev => reindexState(prev));
        setFileUploadStatus(prev => reindexState(prev));
        setFileUploadErrors(prev => reindexState(prev));
        setExpandedAddressDropdowns(prev => reindexState(prev));
        setExpandedDocTypeDropdowns(prev => reindexState(prev));
        setAiCodingStatus(prev => reindexState(prev));
        setAiPredictions(prev => reindexState(prev));
    };

    // Check if upload button should be disabled
    const isUploading = Object.values(fileUploadStatus).some(status => status === 'uploading');
    const isUploadDisabled = files.length === 0 || isUploading || files.some((_, index) => {
        const hasDocumentType = fileDocumentTypes[index]?.main;
        const hasAddressSelection = fileAddressSelections[index]?.address;
        
        // Debug logging
        console.log(`File ${index}:`, {
            hasDocumentType,
            hasAddressSelection,
            documentType: fileDocumentTypes[index],
            addressSelection: fileAddressSelections[index]
        });
        
        return !hasDocumentType || !hasAddressSelection;
    });

    // Debug logging for overall state
    console.log('Upload validation:', {
        filesLength: files.length,
        isUploading,
        isUploadDisabled,
        fileDocumentTypes,
        fileAddressSelections
    });

    // Get display text for selections with truncation
    const getAddressDisplayText = (fileIndex: number, short: boolean = false) => {
        const selection = fileAddressSelections[fileIndex];
        if (!selection) return short ? "Property" : "Select property";
        
        const address = selection.address.length > 20 && short ? 
            selection.address.substring(0, 20) + "..." : 
            selection.address;
        
        if (selection.unit) {
            return short ? `${address} - U${selection.unit}` : `${selection.address} - Unit ${selection.unit}`;
        }
        return short ? `${address} - All` : `${selection.address} - Entire Property`;
    };

    const getDocTypeDisplayText = (fileIndex: number, short: boolean = false) => {
        const docType = fileDocumentTypes[fileIndex];
        if (!docType) return short ? "Doc Type" : "Select document type";
        
        if (docType.sub && !short) {
            return `${docType.main} - ${docType.sub}`;
        }
        return short ? docType.main.substring(0, 12) + (docType.main.length > 12 ? "..." : "") : docType.main;
    };

    const uploadFiles = async () => {
        // Check if portfolioId is available
        if (!portfolioId) {
            console.error("Portfolio ID is missing");
            // Set error for all files
            const errorStatus: {[key: number]: 'error'} = {};
            const errors: {[key: number]: string} = {};
            files.forEach((_, index) => {
                errorStatus[index] = 'error';
                errors[index] = 'Portfolio ID is missing. Please try again.';
            });
            setFileUploadStatus(errorStatus);
            setFileUploadErrors(errors);
            return;
        }

        const supabase = createClient();
        const {data: propData, error: propError} = modalData?.individual ? 
            await supabase.from('prop').select('*').eq('address_id', modalData?.propId).single() : 
            await supabase.from('prop').select('id, address_id').eq('is_deleted', false).eq('portfolio_id', modalData?.selectedPortfolioId);
        const propId = modalData?.individual ? propData?.id : propData;

        const urlsFiles = []
        
        // Check if propId/propData is available
        if (!propId && !propData) {
            console.error("Property data is missing");
            // Set error for all files
            const errorStatus: {[key: number]: 'error'} = {};
            const errors: {[key: number]: string} = {};
            files.forEach((_, index) => {
                errorStatus[index] = 'error';
                errors[index] = 'Property data is missing. Please try again.';
            });
            setFileUploadStatus(errorStatus);
            setFileUploadErrors(errors);
            return;
        }
        
        // Initialize all files as uploading
        const initialStatus: {[key: number]: 'uploading'} = {};
        files.forEach((_, index) => {
            initialStatus[index] = 'uploading';
        });
        setFileUploadStatus(initialStatus);
        setFileUploadErrors({});
        
        // Process each file individually
        for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
            const file = files[fileIndex];
            const documentType = fileDocumentTypes[fileIndex];
            const addressSelection = fileAddressSelections[fileIndex];
            
            try {
                // Find the correct property ID based on the selected address for this file
                let currentPropId;
                if (modalData?.individual) {
                    currentPropId = propId;
                } else {
                    // Find the property that matches the selected address for this file
                    
                    const selectedAddressString = addressSelection?.address;
                    
                    // First, find the address object by matching the address string
                    const selectedAddressObj = addresses.find(addr => addr.address === selectedAddressString);
                    const selectedAddressId = selectedAddressObj?.id;
                    
                    // Then find the property using the address ID
                    const matchingProp = propData?.find((prop: any) => prop.address_id === selectedAddressId);
                    currentPropId = matchingProp?.id;
                    
                    if (!currentPropId) {
                        throw new Error(`No property found for selected address: ${selectedAddressString} (ID: ${selectedAddressId})`);
                    }
                }
                
                // Construct the path based on selection
                const path = `${portfolioId}/${currentPropId}`;
                console.log(path)
                
                // Create filename from original name (lowercase, spaces to underscores)
                const timestamp = new Date().getTime();
                const originalName = file.name.split('.').slice(0, -1).join('.'); 
                const fileName = `${originalName.toLowerCase().replace(/ /g, '_')}_${timestamp}`;
                
                // Get file extension from the original filename
                const fileExtension = file.name.split('.').pop();
                const fullPath = `${path}/${fileName}.${fileExtension}`;
                
                const { data: fileData, error: fileError } = await supabase.storage
                    .from('documents')
                    .upload(fullPath, file);
                
                if (fileError) {
                    console.error("Error uploading file:", fileError);
                    setFileUploadStatus(prev => ({
                        ...prev,
                        [fileIndex]: 'error'
                    }));
                    setFileUploadErrors(prev => ({
                        ...prev,
                        [fileIndex]: `Upload failed: ${fileError.message}`
                    }));
                    continue;
                }

                

                const { data: { publicUrl } } = await supabase.storage
                    .from('documents')
                    .getPublicUrl(fullPath);

                urlsFiles.push(publicUrl)

                // Mark file as successful
                setFileUploadStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'success'
                }));
                    
                // Get document type and subtype info
                const mainType = documentType?.main || '';
                const subType = documentType?.sub || '';
                const unitInfo = addressSelection?.unit || '';
                
                // Save metadata first (required)
                documentsMetadata(
                    portfolioId, 
                    currentPropId, 
                    `${fileName}.${fileExtension}`, 
                    publicUrl, 
                    modalData?.propIndidual, 
                    unitInfo, 
                    mainType, 
                    subType,
                    `${originalName}.${fileExtension}`
                );                
                
            } catch (error) {
                console.error("Error in upload process for file:", file.name, error);
                setFileUploadStatus(prev => ({
                    ...prev,
                    [fileIndex]: 'error'
                }));
                setFileUploadErrors(prev => ({
                    ...prev,
                    [fileIndex]: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
                }));
            }
        }
        
        processDocuments(urlsFiles, portfolioId).then((data) => {
            localStorage.setItem('task_token', data.task_token)
            setTimeout(() => {
                updateModalTrigger(modalTriggerType.documentProcess)
            }, 500)
        })

        documentGenerateSlides(portfolioId, user?.user?.id as string)

        setTimeout(() => {
            closeModal()
        }, 1000)
    };

    return (
        <div className='flex w-full h-full items-center justify-center'>
            <div className={'bg-white w-[800px] max-h-[90vh] rounded-2xl shadow-2xl z-10 relative flex flex-col'}>
                <FontAwesomeIcon
                    icon={faXmark}
                    className="h-5 w-5 text-gray-400 cursor-pointer absolute top-6 right-6 hover:text-gray-600 transition-colors z-20"
                    onClick={() => closeModal()}
                />

                <div className="p-6 border-b border-gray-100">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Upload Documents
                        {modalData?.unitNumber && (
                            <span className="text-lg font-medium text-gray-600 ml-2">
                                - Unit {modalData.unitNumber}
                            </span>
                        )}
                    </h2>
                    <p className="text-gray-600">
                        {modalData?.unitNumber 
                            ? `Upload PDF files for Unit ${modalData.unitNumber}`
                            : "Select or drag PDF files to upload to your portfolio"
                        }
                    </p>
                    <div className="mt-2 text-sm text-gray-500">
                        Maximum {MAX_FILES_LIMIT} files per upload session
                        {modalData?.unitNumber && (
                            <span className="text-indigo-600 ml-2">
                                • Files will be automatically tagged to Unit {modalData.unitNumber}
                            </span>
                        )}
                    </div>
                </div>

                <div className="flex-1 p-6 overflow-hidden">
                    {files.length === 0 && (
                        <div
                            className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 flex flex-col items-center justify-center h-[400px] ${
                                isDragging 
                                    ? "border-blue-500 bg-blue-50 shadow-lg" 
                                    : "border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDropWithLimit}
                        >
                            <div className="bg-gray-100 rounded-full p-6 mb-6">
                                <FontAwesomeIcon
                                    icon={faCloudArrowUp}
                                    className="h-12 w-12 text-gray-400"
                                />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                Drop your PDF files here
                            </h3>
                            <p className="text-gray-500 mb-6">
                                or click to browse from your computer
                            </p>
                            <label className="bg-blue-600 text-white px-6 py-3 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors font-medium shadow-sm">
                                Browse Files
                                <input
                                    type="file"
                                    className="hidden"
                                    onChange={handleFileChangeWithLimit}
                                    accept=".pdf"
                                    multiple={true}
                                />
                            </label>
                            <p className="text-xs text-gray-400 mt-4">
                                Supports PDF files up to 50MB each • Max {MAX_FILES_LIMIT} files
                            </p>
                        </div>
                    )}

                    {files.length > 0 && (
                        <div className="h-[400px] flex flex-col">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="font-semibold text-gray-900">
                                    {files.length} / {MAX_FILES_LIMIT} file{files.length > 1 ? 's' : ''} selected
                                </h3>
                                {files.length < MAX_FILES_LIMIT && (
                                    <label className="text-blue-600 hover:text-blue-700 cursor-pointer text-sm font-medium">
                                        + Add more files
                                        <input
                                            type="file"
                                            className="hidden"
                                            onChange={handleFileChangeWithLimit}
                                            accept=".pdf"
                                            multiple={true}
                                        />
                                    </label>
                                )}
                            </div>
                            
                            <div className="flex-1 overflow-y-auto space-y-3">
                                {files.map((file, index) => (
                                    <div key={index} className="relative">
                                        {/* Main file row */}
                                        <div 
                                            className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 relative"
                                            // onMouseEnter={() => handleFileHover(index, file)}
                                            // onMouseLeave={handleFileLeave}
                                        >
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center flex-1 min-w-0">
                                                    {/* File icon */}
                                                    <div className="flex-shrink-0 mr-3">
                                                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                            <FontAwesomeIcon 
                                                                icon={faFileAlt} 
                                                                className="h-5 w-5 text-blue-600" 
                                                            />
                                                        </div>
                                                    </div>
                                                    
                                                    {/* File name */}
                                                    <div className="flex-1 min-w-0 mr-4">
                                                        <p className="text-sm font-medium text-gray-900 truncate">
                                                            {file.name}
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            {(file.size / 1024 / 1024).toFixed(2)} MB
                                                        </p>
                                                    </div>
                                                    
                                                    {/* Action buttons */}
                                                    <div className="flex items-center space-x-2">
                                                        {/* AI Coding Button */}
                                                        <button
                                                            onClick={() => handleAICoding(index, file)}
                                                            disabled={aiCodingStatus[index] === 'loading'}
                                                            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border ${
                                                                aiCodingStatus[index] === 'success'
                                                                    ? 'bg-emerald-50 text-emerald-700 border-emerald-200'
                                                                    : aiCodingStatus[index] === 'error'
                                                                    ? 'bg-red-50 text-red-700 border-red-200'
                                                                    : aiCodingStatus[index] === 'loading'
                                                                    ? 'bg-purple-50 text-purple-700 border-purple-200'
                                                                    : 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100'
                                                            }`}
                                                        >
                                                            {aiCodingStatus[index] === 'loading' ? (
                                                                <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                                                            ) : aiCodingStatus[index] === 'success' ? (
                                                                <FontAwesomeIcon icon={faCheckCircle} className="h-3 w-3" />
                                                            ) : (
                                                                <FontAwesomeIcon icon={faMagicWandSparkles} className="h-3 w-3" />
                                                            )}
                                                            <span className="hidden sm:inline">
                                                                {aiCodingStatus[index] === 'loading' ? 'Identifying...' :
                                                                 aiCodingStatus[index] === 'success' ? 'AI Done!' :
                                                                 aiCodingStatus[index] === 'error' ? 'AI Error' : 'Tag with AI'}
                                                            </span>
                                                        </button>

                                                        {/* Manual Selection Title
                                                        <div className="hidden sm:flex items-center text-xs text-gray-500 font-medium">
                                                            Tag manually:
                                                        </div> */}

                                                        {/* Address/Unit selector */}
                                                        <button
                                                            onClick={() => toggleAddressDropdown(index)}
                                                            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border ${
                                                                fileAddressSelections[index] 
                                                                    ? 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100' 
                                                                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                                                            }`}
                                                        >
                                                            <FontAwesomeIcon icon={faBuilding} className="h-3 w-3" />
                                                            <span className="hidden sm:inline max-w-24 truncate">
                                                                {getAddressDisplayText(index, true)}
                                                            </span>
                                                            <FontAwesomeIcon 
                                                                icon={expandedAddressDropdowns[index] ? faChevronDown : faChevronRight} 
                                                                className="h-3 w-3" 
                                                            />
                                                        </button>
                                                        
                                                        {/* Document type selector */}
                                                        <button
                                                            onClick={() => toggleDocTypeDropdown(index)}
                                                            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border ${
                                                                fileDocumentTypes[index] 
                                                                    ? 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100' 
                                                                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                                                            }`}
                                                        >
                                                            <FontAwesomeIcon icon={faTag} className="h-3 w-3" />
                                                            <span className="hidden sm:inline max-w-24 truncate">
                                                                {getDocTypeDisplayText(index, true)}
                                                            </span>
                                                            <FontAwesomeIcon 
                                                                icon={expandedDocTypeDropdowns[index] ? faChevronDown : faChevronRight} 
                                                                className="h-3 w-3" 
                                                            />
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                {/* Status/Remove button */}
                                                <div className="flex-shrink-0 ml-4">
                                                    {fileUploadStatus[index] === 'uploading' ? (
                                                        <FontAwesomeIcon 
                                                            icon={faSpinner} 
                                                            className="h-5 w-5 text-blue-500 animate-spin" 
                                                        />
                                                    ) : fileUploadStatus[index] === 'success' ? (
                                                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                            <FontAwesomeIcon 
                                                                icon={faCheck} 
                                                                className="h-4 w-4 text-green-600" 
                                                            />
                                                        </div>
                                                    ) : fileUploadStatus[index] === 'error' ? (
                                                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                                            <FontAwesomeIcon 
                                                                icon={faXmark} 
                                                                className="h-4 w-4 text-red-600" 
                                                            />
                                                        </div>
                                                    ) : (
                                                        <button
                                                            onClick={() => handleRemoveFile(index)}
                                                            className="w-8 h-8 bg-gray-100 hover:bg-red-100 rounded-full flex items-center justify-center transition-colors group"
                                                        >
                                                            <FontAwesomeIcon 
                                                                icon={faXmark} 
                                                                className="h-4 w-4 text-gray-400 group-hover:text-red-500" 
                                                            />
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                            
                                            {/* Error message */}
                                            {fileUploadErrors[index] && (
                                                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                                                    <p className="text-sm text-red-600">
                                                        {fileUploadErrors[index]}
                                                    </p>
                                                </div>
                                            )}

                                            {/* AI Success Message */}
                                            {aiCodingStatus[index] === 'success' && aiPredictions[index] && (
                                                <div className="mt-3 p-3 bg-emerald-50 border border-emerald-200 rounded-md">
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <FontAwesomeIcon icon={faRobot} className="h-4 w-4 text-emerald-600" />
                                                        <p className="text-sm font-medium text-emerald-800">AI Coding Complete!</p>
                                                    </div>
                                                    <p className="text-xs text-emerald-700">
                                                        Predicted: {aiPredictions[index].property}
                                                        {aiPredictions[index].unit && ` - Unit ${aiPredictions[index].unit}`}
                                                        {' • '}{aiPredictions[index].documentType.main}
                                                        {' • '}{Math.round(aiPredictions[index].confidence)}% confidence
                                                    </p>
                                                </div>
                                            )}

                                            {/* File preview on hover */}
                                            {/* {hoveredFile === index && previewUrl && (
                                                <div className="absolute left-full top-0 ml-4 z-20 bg-white border border-gray-200 rounded-lg shadow-xl p-2 w-64 h-80">
                                                    <div className="text-xs text-gray-500 mb-2 font-medium">Preview</div>
                                                    <iframe
                                                        src={previewUrl}
                                                        className="w-full h-full rounded border-0"
                                                        title={`Preview of ${file.name}`}
                                                    />
                                                </div> */}
                                            {/* )} */}
                                        </div>
                                        
                                        {/* Expandable dropdowns */}
                                        {expandedAddressDropdowns[index] && (
                                            <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                                                <h4 className="text-sm font-medium text-gray-900 mb-2">
                                                    Select Property/Unit
                                                </h4>
                                                <p className="text-xs text-gray-500 mb-3">
                                                    Current: {getAddressDisplayText(index)}
                                                </p>
                                                
                                                {/* Custom property/unit selector */}
                                                <div className="space-y-2 max-h-48 overflow-y-auto">
                                                    {addresses.map((address) => (
                                                        <div key={address.id} className="border border-gray-200 rounded-lg">
                                                            {/* Entire Property Option */}
                                                            <button
                                                                onClick={() => selectEntireProperty(index, address)}
                                                                className={`w-full text-left p-3 rounded-t-lg transition-colors flex items-center gap-3 ${
                                                                    fileAddressSelections[index]?.address === address.address && !fileAddressSelections[index]?.unit
                                                                        ? 'bg-blue-50 text-blue-700 border-blue-200'
                                                                        : 'hover:bg-gray-50'
                                                                }`}
                                                            >
                                                                <FontAwesomeIcon icon={faHome} className="h-4 w-4 text-gray-500" />
                                                                <div>
                                                                    <div className="font-medium text-sm">{address.address}</div>
                                                                    <div className="text-xs text-gray-500">Entire Property</div>
                                                                </div>
                                                            </button>
                                                            
                                                            {/* Units */}
                                                            {address.units.length > 0 && (
                                                                <div className="border-t border-gray-200">
                                                                    {address.units.map((unit) => (
                                                                        <button
                                                                            key={unit.id}
                                                                            onClick={() => selectUnit(index, address, unit)}
                                                                            className={`w-full text-left p-3 transition-colors flex items-center gap-3 last:rounded-b-lg ${
                                                                                fileAddressSelections[index]?.address === address.address && fileAddressSelections[index]?.unit === unit.unit
                                                                                    ? 'bg-green-50 text-green-700 border-green-200'
                                                                                    : 'hover:bg-gray-50'
                                                                            }`}
                                                                        >
                                                                            <FontAwesomeIcon icon={faUsers} className="h-4 w-4 text-gray-500" />
                                                                            <div>
                                                                                <div className="font-medium text-sm">Unit {unit.unit}</div>
                                                                                <div className="text-xs text-gray-500">
                                                                                    {unit.beds} bed{unit.beds !== 1 ? 's' : ''}, {unit.baths} bath{unit.baths !== 1 ? 's' : ''}
                                                                                </div>
                                                                            </div>
                                                                        </button>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                        
                                        {expandedDocTypeDropdowns[index] && (
                                            <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                                                <h4 className="text-sm font-medium text-gray-900 mb-2">
                                                    Select Document Type
                                                </h4>
                                                <p className="text-xs text-gray-500 mb-3">
                                                    Current: {getDocTypeDisplayText(index)}
                                                </p>
                                                <MultiLevelDropdown
                                                    value={fileDocumentTypes[index] || null}
                                                    onChange={(documentType) => updateFileDocumentType(index, documentType)}
                                                    placeholder="Select document type"
                                                    className="w-full"
                                                />
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Footer with upload button */}
                {files.length > 0 && (
                    <div className="p-6 bg-gray-50 rounded-b-2xl border-t border-gray-100">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-600">
                                {files.length} file{files.length > 1 ? 's' : ''} ready to upload
                                {isUploadDisabled && !isUploading && (
                                    <span className="text-amber-600 ml-2">
                                        • Please complete all selections
                                    </span>
                                )}
                            </div>
                            <button 
                                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 ${
                                    isUploadDisabled 
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                                        : 'bg-blue-600 text-white cursor-pointer hover:bg-blue-700 shadow-sm hover:shadow-md'
                                }`}
                                disabled={isUploadDisabled}
                                onClick={uploadFiles}
                            >
                                {isUploading && (
                                    <FontAwesomeIcon 
                                        icon={faSpinner} 
                                        className="h-4 w-4 animate-spin" 
                                    />
                                )}
                                {isUploading ? 'Uploading Documents...' : 'Upload Documents'}
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}