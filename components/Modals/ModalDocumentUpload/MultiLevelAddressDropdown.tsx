import { faChevronDown, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useRef, useState } from "react";
import { useEffect } from "react";

// Types for address and unit data
interface Unit {
    id: string;
    unit: string;
    beds: number;
    baths: number;
    // Add other unit properties as needed
}

interface Address {
    id: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    units: Unit[];
}

interface AddressDropdownProps {
    addresses: Address[];
    value: {address: string, unit: string | null} | null;
    onChange: (value: {address: string, unit: string | null}) => void;
    placeholder: string;
    className?: string;
    showOnlyUnits?: boolean;
}

// Multi-level address dropdown component
export default function MultiLevelAddressDropdown({ 
    addresses, 
    value, 
    onChange, 
    placeholder, 
    className,
    showOnlyUnits = false
}: AddressDropdownProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const toggleExpanded = (addressId: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(addressId)) {
            newExpanded.delete(addressId);
        } else {
            newExpanded.add(addressId);
        }
        setExpandedNodes(newExpanded);
    };

    const handleSelect = (address: string, unit: string | null = null) => {
        const selection = {
            address,
            unit
        };
        onChange(selection);
        setIsOpen(false);
    };

    // Function to render only units in a flat list
    const renderUnitsOnly = () => {
        const allUnits: Array<{unit: Unit, address: Address}> = [];
        
        addresses.forEach(addressData => {
            if (addressData.units && addressData.units.length > 0) {
                addressData.units.forEach(unit => {
                    allUnits.push({ unit, address: addressData });
                });
            }
        });

        return allUnits.map(({ unit, address }) => {
            const fullAddress = `${address.address}, ${address.city}, ${address.state} ${address.zip}`;
            
            return (
                <div
                    key={`${address.id}-${unit.id}`}
                    className="px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between"
                    onClick={() => handleSelect(address.address, unit.unit)}
                >
                    <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-900">
                            {unit.unit}
                            {unit.beds > 0 && unit.baths > 0 && (
                                <span className="text-xs text-gray-500 ml-2">
                                    ({unit.beds}bed/{unit.baths}bath)
                                </span>
                            )}
                        </span>
                        <span className="text-xs text-gray-500">{fullAddress}</span>
                    </div>
                </div>
            );
        });
    };

    const renderAddresses = () => {
        return addresses.map((addressData) => {
            const hasUnits = addressData.units && addressData.units.length > 0;
            const isExpanded = expandedNodes.has(addressData.id);
            const fullAddress = `${addressData.address}, ${addressData.city}, ${addressData.state} ${addressData.zip}`;

            return (
                <div key={addressData.id}>
                    <div
                        className="px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between"
                        onClick={() => {
                            if (hasUnits) {
                                toggleExpanded(addressData.id);
                            } else {
                                handleSelect(addressData.address);
                            }
                        }}
                    >
                        <span className="text-sm font-medium text-gray-900">{fullAddress}</span>
                        {hasUnits && (
                            <FontAwesomeIcon
                                icon={isExpanded ? faChevronDown : faChevronRight}
                                className="h-3 w-3 text-gray-400"
                            />
                        )}
                    </div>
                    {hasUnits && isExpanded && (
                        <div className="bg-gray-50">
                            {addressData.units.map((unit) => (
                                <div
                                    key={unit.id}
                                    className="pl-6 pr-3 py-2 cursor-pointer hover:bg-gray-200 flex items-center justify-between"
                                    onClick={() => handleSelect(addressData.address, unit.unit)}
                                >
                                    <span className="text-sm text-gray-700">
                                        {unit.unit}
                                        {unit.beds > 0 && unit.baths > 0 && (
                                            <span className="text-xs text-gray-500 ml-2">
                                                ({unit.beds}bed/{unit.baths}bath)
                                            </span>
                                        )}
                                    </span>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            );
        });
    };

    // Format display value
    const getDisplayValue = () => {
        if (!value) return placeholder;
        if (showOnlyUnits && value.unit) {
            return value.unit;
        }
        if (value.unit) {
            return `${value.address} > ${value.unit}`;
        }
        return value.address;
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <div
                className={`w-full px-3 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors text-sm cursor-pointer flex items-center justify-between ${className}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className={value ? 'text-gray-900' : 'text-gray-400'}>
                    {getDisplayValue()}
                </span>
                <FontAwesomeIcon
                    icon={isOpen ? faChevronDown : faChevronRight}
                    className="h-3 w-3 text-gray-400"
                />
            </div>
            {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
                    {addresses && addresses.length > 0 ? (
                        showOnlyUnits ? renderUnitsOnly() : renderAddresses()
                    ) : (
                        <div className="px-3 py-2 text-sm text-gray-500">
                            {showOnlyUnits ? "No units available" : "No addresses available"}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
} 