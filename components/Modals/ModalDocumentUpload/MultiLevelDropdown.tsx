import { faChevronDown, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useRef, useState } from "react";
import { useEffect } from "react";

// Multi-level document type structure
const documentStructure = {
    "Operating & Financials": {
        "Financials": {},
        "Leases": {
            "Unit Leases": {},
            "Rent Roll": {},
            "Other": {}
        },
        "Expenses": {
            "Contracts": {},
            "Service Bills": {},
            "Utility Bills": {},
            "Other": {}
        },
        "Taxes": {
            "General": {},
            "Notices": {},
            "Tax Bills": {},
            "Other": {}
        },
        "Insurance": {
            "Insurance Bills": {},
            "Insurance Claims": {},
            "Insurance Policies": {},
            "Other": {}
        },
        "Other": {}
    },
    "Purchase Agreements": {},
    "Third Party Reports": {
        "Inspections": {},
        "Maintenance": {},
        "Environmental": {},
        "Other": {}
    },
    "Title": {
        "Deed": {},
        "Survey": {},
        "Other": {}
    },
    "Insurance": {
        "Contracts": {},
        "Claims": {},
        "Other": {}
    }
};

// Multi-level dropdown component
export default function MultiLevelDropdown({ value, onChange, placeholder, className }: {
    value: {main: string, sub: string | null} | null;
    onChange: (value: {main: string, sub: string | null}) => void;
    placeholder: string;
    className?: string;
}) {
    const [isOpen, setIsOpen] = useState(false);
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const toggleExpanded = (path: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(path)) {
            newExpanded.delete(path);
        } else {
            newExpanded.add(path);
        }
        setExpandedNodes(newExpanded);
    };

    const handleSelect = (fullPath: string) => {
        const pathParts = fullPath.split(' > ');
        const documentType = {
            main: pathParts[0],
            sub: pathParts.length > 1 ? pathParts[pathParts.length - 1] : null
        };
        onChange(documentType);
        setIsOpen(false);
    };

    const renderOptions = (data: any, currentPath: string = '', level: number = 0) => {
        return Object.keys(data).map((key) => {
            const fullPath = currentPath ? `${currentPath} > ${key}` : key;
            const hasChildren = data[key] && Object.keys(data[key]).length > 0;
            const isExpanded = expandedNodes.has(fullPath);

            return (
                <div key={fullPath}>
                    <div
                        className={`px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between ${
                            level > 0 ? `pl-${6 + level * 4}` : ''
                        }`}
                        style={{ paddingLeft: level > 0 ? `${24 + level * 16}px` : '12px' }}
                        onClick={() => {
                            if (hasChildren) {
                                toggleExpanded(fullPath);
                            } else {
                                handleSelect(fullPath);
                            }
                        }}
                    >
                        <span className="text-sm">{key}</span>
                        {hasChildren && (
                            <FontAwesomeIcon
                                icon={isExpanded ? faChevronDown : faChevronRight}
                                className="h-3 w-3 text-gray-400"
                            />
                        )}
                    </div>
                    {hasChildren && isExpanded && (
                        <div>
                            {renderOptions(data[key], fullPath, level + 1)}
                        </div>
                    )}
                </div>
            );
        });
    };

    // Format display value
    const getDisplayValue = () => {
        if (!value) return placeholder;
        if (value.sub) {
            return `${value.main} > ${value.sub}`;
        }
        return value.main;
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <div
                className={`w-full px-3 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors text-sm cursor-pointer flex items-center justify-between ${className}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className={value ? 'text-gray-900' : 'text-gray-400'}>
                    {getDisplayValue()}
                </span>
                <FontAwesomeIcon
                    icon={isOpen ? faChevronDown : faChevronRight}
                    className="h-3 w-3 text-gray-400"
                />
            </div>
            {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
                    {renderOptions(documentStructure)}
                </div>
            )}
        </div>
    );
};