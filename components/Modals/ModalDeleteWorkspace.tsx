import {faTrashCan, faXmark} from "@fortawesome/free-solid-svg-icons";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {useAuth} from "@/context/AuthContext";
import {useModal} from "@/context/ModalContext";
import {deleteWorkspace} from "@/actions/workspaceActions";
import { useState } from "react";

export default function ModalDeleteWorkspace(){
	const {handleGetUserWorkspaces} = useAuth()
	const { closeModal, modalData } = useModal()
	const [confirmationText, setConfirmationText] = useState('');
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string>('');

	const workspaceName = modalData?.name || '';
	const isConfirmationValid = confirmationText === workspaceName;

	const handleDeleteWorkspace = async () => {
		if (!isConfirmationValid) {
			setError('Please type the exact workspace name to confirm deletion');
			return;
		}

		setIsDeleting(true);
		setError('');

		try {
			await deleteWorkspace(modalData?.id);
			handleGetUserWorkspaces();
			closeModal();
		} catch (error: any) {
			console.error('Error deleting workspace:', error);
			setError(error.message || 'Failed to delete workspace');
		} finally {
			setIsDeleting(false);
		}
	}

	return (
		<div className='flex w-full h-full items-center justify-center'>
			<div className={'bg-white w-[500px] rounded-2xl p-6 z-10 relative'}>
				<FontAwesomeIcon
					icon={faXmark}
					className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors"
					onClick={() => closeModal()}
				/>
				<div className="flex items-center justify-center mb-4">
					<div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
						<FontAwesomeIcon icon={faTrashCan} className="h-6 w-6 text-red-600" />
					</div>
				</div>
				<h3 className="text-xl font-semibold text-gray-900 text-center mb-2">Delete Workspace</h3>
				<p className="text-center text-gray-600 mb-4">
					This action cannot be undone. This will permanently delete the workspace 
					<span className="font-semibold text-gray-900"> "{workspaceName}"</span> and all of its data.
				</p>
				
				<div className="mb-4">
					<label className="block text-sm font-medium text-gray-700 mb-2">
						Please type <span className="font-semibold text-gray-900">{workspaceName}</span> to confirm:
					</label>
					<input
						type="text"
						value={confirmationText}
						onChange={(e) => setConfirmationText(e.target.value)}
						placeholder={workspaceName}
						className="block w-full rounded-xl border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 text-base transition-shadow"
					/>
					{confirmationText && !isConfirmationValid && (
						<p className="text-red-500 text-sm mt-1">
							Workspace name doesn't match. Please type "{workspaceName}" exactly.
						</p>
					)}
				</div>

				{error && (
					<div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
						<p className="text-red-600 text-sm">{error}</p>
					</div>
				)}

				<div className={'flex items-center justify-center gap-x-3'}>
					<button
						onClick={() => closeModal()}
						disabled={isDeleting}
						className="cursor-pointer px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
						Cancel
					</button>
					<button
						onClick={handleDeleteWorkspace}
						disabled={!isConfirmationValid || isDeleting}
						className="cursor-pointer px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
						{isDeleting ? 'Deleting...' : 'Delete Workspace'}
					</button>
				</div>
			</div>
		</div>
	)
}