import { updateWorkspaceName } from "@/actions/workspaceActions";
import { useModal } from "@/context/ModalContext"
import { faXmark, faImage } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import modalType from "@/constants/modalType";

export default function ModalEditWorkspace() {
    const { handleGetUserWorkspaces } = useAuth()
    const { closeModal, modalData, showModal, updateModalData } = useModal()
    const [name, setName] = useState('');
    const [logoUrl, setLogoUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string>('');

    // Set initial values from modal data
    useEffect(() => {
        if (modalData?.name) {
            setName(modalData.name);
        }
        if (modalData?.logo_url) {
            setLogoUrl(modalData.logo_url);
        }
    }, [modalData]);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        
        try {
            await updateWorkspaceName(modalData?.id, name, logoUrl);
            console.log('Workspace updated successfully');
            
            closeModal()
            handleGetUserWorkspaces() // Refresh the workspace list
        } catch (error: any) {
            console.log('Error updating workspace:', error);
            setError(error.message || 'Failed to update workspace');
        } finally {
            setIsLoading(false);
        }
    }

    const handleLogoUpload = () => {
        // Pass current workspace data to logo modal
        updateModalData({...modalData});
        showModal(modalType.workspaceLogo);
    }

    return (
        <>
            <div className="flex w-full h-full items-center justify-center">
                <div className="bg-white w-[448px] rounded-2xl p-6 z-10 relative">
                    <FontAwesomeIcon 
                        icon={faXmark} 
                        className="h-5 w-5 text-black cursor-pointer absolute top-3 right-3 hover:text-gray-500 transition-colors" 
                        onClick={() => closeModal()}
                    />
                    <h3 className="text-xl font-semibold text-gray-900 text-center">Edit Workspace</h3>
                    <form onSubmit={handleSubmit}>
                        <div className="mt-6">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Workspace Name
                            </label>
                            <input 
                                required 
                                className="block w-full rounded-xl border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-500 text-base transition-shadow" 
                                placeholder="Enter workspace name" 
                                type="text" 
                                value={name} 
                                onChange={(e) => setName(e.target.value)} 
                                name="workspace-name"
                            />
                        </div>
                        
                        {/* Logo Section */}
                        <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Workspace Logo
                            </label>
                            <div className="flex items-center space-x-3">
                                {modalData?.logo_url ? (
                                    <img 
                                        src={modalData.logo_url} 
                                        alt="Current logo" 
                                        className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                                    />
                                ) : (
                                    <div className="w-12 h-12 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                        <FontAwesomeIcon icon={faImage} className="h-5 w-5 text-gray-400" />
                                    </div>
                                )}
                                <button
                                    type="button"
                                    onClick={handleLogoUpload}
                                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors"
                                >
                                    <FontAwesomeIcon icon={faImage} className="mr-2 h-4 w-4" />
                                    {modalData?.logo_url ? 'Change Logo' : 'Upload Logo'}
                                </button>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                                Upload a custom logo for your workspace
                            </p>
                        </div>

                        <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Logo URL (Optional)
                            </label>
                            <input 
                                className="block w-full rounded-xl border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-500 text-base transition-shadow" 
                                placeholder="Enter logo URL (https://...)" 
                                type="url" 
                                value={logoUrl} 
                                onChange={(e) => setLogoUrl(e.target.value)} 
                                name="workspace-logo"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                                Or enter a URL to an image that will be used as the workspace logo
                            </p>
                        </div>
                        <div className="mt-6">
                            <button 
                                disabled={isLoading || !name.trim()}
                                className="cursor-pointer w-full px-4 py-3 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                {isLoading ? 'Updating...' : 'Update Workspace'}
                            </button>
                        </div>
                        {error && <p className="text-red-500 text-xs text-center mt-3">{error}</p>}
                    </form>
                </div>
            </div>
        </>
    )
} 