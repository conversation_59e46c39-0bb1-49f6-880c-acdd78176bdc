import pathName from "@/constants/pathName";
import { useAuth } from "@/context/AuthContext";
import { useParams, useRouter } from "next/navigation";
import { useState, useRef, useEffect } from "react";
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';

export default function HeaderSelectWorkspace() {
    const { workspaces } = useAuth();
    const [selectedWorkspace, setSelectedWorkspace] = useState<string | null>(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const params = useParams();
    const router = useRouter();

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <>
            <div className="relative ml-4 inline-block" ref={dropdownRef}>
                <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="h-8 flex items-center px-3 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors text-sm"
                >
                    <span className="mr-2">{workspaces?.find(ws => ws.id === params.id)?.name || 'Select workspace'}</span>
                    <FontAwesomeIcon icon={faChevronDown} className="h-4 w-4 text-gray-500" />
                </button>
                {dropdownOpen && (
                    <div className="absolute right-0 mt-2 w-full bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                        {workspaces?.map(ws => (
                            <Link
                                href={`${pathName.workspace}/${ws.id}`}
                                key={ws.id}
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={() => setDropdownOpen(false)}
                            >
                                {ws.name}
                            </Link>
                        ))}
                    </div>
                )}
            </div>
        </>
    )
}