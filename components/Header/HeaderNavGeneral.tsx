import pathName from "@/constants/pathName";
import { useAuth } from "@/context/AuthContext";
import Link from "next/link";
import { usePathname } from "next/navigation";


export default function HeaderNavGeneral() {
    const path = usePathname();
    
    return (
        <>
            {
                path === '/' || path === pathName.pricing || path === pathName.workspace || path === pathName.shared || path.includes(pathName.portfolio) ?
                    <div className="hidden lg:flex items-center gap-x-4 absolute left-0 right-0 justify-center w-full">
                        <div>
                            <Link href={'/'} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path === '/' 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Home
                            </Link>
                        </div>
                        <div>
                            <Link href={`${pathName.shared}`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path.includes(pathName.shared) 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                                Shared
                            </Link>
                        </div>
                        {/*<div>
                            <Link href={pathName.pricing} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path === pathName.pricing 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Pricing
                            </Link>
                        </div>*/}
                    </div>
                : null
            }
        </>
    )
}
