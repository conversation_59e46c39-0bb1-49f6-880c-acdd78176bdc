import pathName from "@/constants/pathName";
import { useAuth } from "@/context/AuthContext";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";

export default function HeaderNavWorkspace()  {
    const path = usePathname();
    const isWorkspace = path.includes(pathName.workspace) && path.split('/').length >= 3;
    const pathLength = path.split('/').length;
    const params = useParams();
    const workspaceId = params.id;

    return (
        <>
            {
                isWorkspace ? 
                    <div className="hidden lg:flex items-center gap-x-4 absolute left-0 right-0 justify-center w-full">
                        <div>
                            <Link href={`/workspace/${workspaceId}${pathName.search}`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out nav-search-link
                                    ${path.includes(pathName.search) 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Property Search
                            </Link>
                        </div>
                        <div>
                            <Link href={`/workspace/${workspaceId}`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${pathLength === 3 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Portfolios
                            </Link>
                        </div>
                        
                        {/* <div> */}
                        {/*<Link href={`/workspace/${workspaceId}${pathName.workspaceDataRoom}`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path.includes(pathName.workspaceDataRoom) 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Data Room
                            </Link>*/}
                        {/* </div> */}
                        <div>
                            <Link href={`/workspace/${workspaceId}/market-brief`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path.includes('/market-brief') 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Market Brief
                            </Link>
                        </div>
                        <div>
                            <Link href={`/workspace/${workspaceId}${pathName.pricing}`} className={`
                                    px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out
                                    ${path.includes(pathName.pricing) 
                                        ? 'bg-black text-white shadow-lg drop-shadow-[0_0_5px_white]' 
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-sm'
                                    }
                                `}>
                            Data Access
                            </Link>
                        </div>
                    </div>
                : null
            }
        </>
    )
}