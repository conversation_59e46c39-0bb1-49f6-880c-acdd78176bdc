'use client'
import Image from 'next/image';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import HeaderNavWorkspace from './HeaderNavWorkspace';
import HeaderNavGeneral from './HeaderNavGeneral';
import { usePathname } from 'next/navigation';
import HeaderBurger from './HeaderBurger';


export default function Header() {
    
    return (
        <div className="w-full fixed top-0 left-0 z-50 bg-white/30 backdrop-blur-[10px]">
            <nav className="w-full transition-all duration-300">
                <div className="mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex py-2 items-center justify-between">
                        <div className="relative z-10">
                            <Link href="/" className="text-xl font-bold text-indigo-600 relative group">
                                <Image 
                                src="/relm-logo.webp" 
                                alt="Relm Logo" 
                                width={90} 
                                height={40}
                                priority
                                className='mt-3'
                                
                                />
                            </Link>
                        </div>


                        <HeaderNavGeneral />
                        <HeaderNavWorkspace />
                        
                        <HeaderBurger />
                    </div>
                </div>
            </nav>
        </div>
    )
}