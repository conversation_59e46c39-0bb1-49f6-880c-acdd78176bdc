import { faGoogle, faMicrosoft } from "@fortawesome/free-brands-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { createClient } from "@/utils/supabase/client";

export default function SocialButtons() {

    const handleOAuthLogin = async (provider: 'google' | 'azure') => {
        const { error } = await createClient().auth.signInWithOAuth({
            provider,
            options: {
                redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}`,
            },
        });

        if (error) {
            console.log('Error OAuth login:', error.message);
        } 
    }
    
    return (
        <div className="grid grid-cols-2 gap-4">
            <button 
                onClick={() => handleOAuthLogin('google')}
                className="w-full flex justify-center items-center gap-3 py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
            >
                <FontAwesomeIcon 
                    icon={faGoogle} 
                    className="h-5 w-5 text-[#4285F4]" 
                />
                Google
            </button>

            <button 
                onClick={() => handleOAuthLogin('azure')}
                className="w-full flex justify-center items-center gap-3 py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
            >
                <FontAwesomeIcon 
                    icon={faMicrosoft} 
                    className="h-5 w-5 text-[#00A4EF]" 
                />
                Microsoft
            </button>
        </div>
    )
}