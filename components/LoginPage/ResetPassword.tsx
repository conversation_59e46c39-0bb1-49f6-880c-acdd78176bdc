import { createClient } from "@/utils/supabase/client";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faArrowLeft } from "@fortawesome/free-solid-svg-icons";

interface Props {
    setForm: (form: 'signin' | 'signup' | 'resetpassword') => void;
}

export default function ResetPassword({ setForm }: Props) {
    const [email, setEmail] = useState('')
    const [error, setError] = useState('')
    const [success, setSuccess] = useState('')
    const [isLoading, setIsLoading] = useState(false)

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setError('')
        setSuccess('')
        setIsLoading(true)
        
        const { error } = await createClient().auth.resetPasswordForEmail(email)

        if(error) {
            console.log('Error reset password:', error.message);
            setError(error.message)
        } else {
            setSuccess('Check your email for a reset link.')
        }
        
        setIsLoading(false)
    }
    
    return (
        <>
            <div className="mb-8">
                <button
                    type="button"
                    onClick={() => setForm('signin')}
                    className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors mb-6"
                >
                    <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
                    Back to sign in
                </button>
                
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Reset your password</h2>
                <p className="text-gray-600">
                    Enter your email address and we'll send you a link to reset your password.
                </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Email Field */}
                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email address
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FontAwesomeIcon icon={faEnvelope} className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            autoComplete="email"
                            required
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                            placeholder="Enter your email"
                        />
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex">
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">
                                    Reset failed
                                </h3>
                                <div className="mt-1 text-sm text-red-700">
                                    {error}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Success Message */}
                {success && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex">
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-green-800">
                                    Reset link sent!
                                </h3>
                                <div className="mt-1 text-sm text-green-700">
                                    {success}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Submit Button */}
                <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    {isLoading ? (
                        <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Sending reset link...
                        </div>
                    ) : (
                        'Send reset instructions'
                    )}
                </button>

                {/* Back to Sign In */}
                <div className="text-center">
                    <button
                        type="button"
                        onClick={() => setForm('signin')}
                        className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                    >
                        Remember your password? Sign in
                    </button>
                </div>
            </form>
        </>
    )
}