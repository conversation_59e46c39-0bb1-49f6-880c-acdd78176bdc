import { insertDataRoomGuest } from "@/actions/userActions";
import cookieName from "@/constants/cookieName";
import pathName from "@/constants/pathName";
import { createClient } from "@/utils/supabase/client";
import { deleteCookie, hasCookie } from "cookies-next";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash, faEnvelope, faLock } from "@fortawesome/free-solid-svg-icons";

interface Props {
    setForm: (form: 'signin' | 'signup' | 'resetpassword') => void;
    tokenEmail: string | null;
    tokenData: any | null;
    setTokenData: React.Dispatch<React.SetStateAction<any>>;
}

export default function SignIn({ setForm, tokenEmail, tokenData, setTokenData }: Props) {
    const [email, setEmail] = useState(tokenEmail || '')
    const [password, setPassword] = useState('')
    const [error, setError] = useState('')
    const [showPassword, setShowPassword] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const {push} = useRouter()
    
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setError('')
        setIsLoading(true)
        
        const { data, error } = await createClient().auth.signInWithPassword({
            email,
            password,
        });

        if(hasCookie(cookieName.invitePortfolioToken) && tokenData && data?.user?.id) {
            insertDataRoomGuest(tokenData, data?.user?.id as string).then(() => {
                deleteCookie(cookieName.invitePortfolioToken)
                setTokenData(null)
            })
        }

        if (error) {
            console.log('Error sign in:', error.message);
            setError(error.message);
        } else {
            push('/')
        }
        
        setIsLoading(false)
    }
    
    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Welcome back</h2>
                <p className="text-gray-600">
                    Don't have an account?{' '}
                    <button 
                        type="button"
                        onClick={() => setForm('signup')}
                        className="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors"
                    >
                        Sign up for free
                    </button>
                </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Email Field */}
                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email address
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FontAwesomeIcon icon={faEnvelope} className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            autoComplete="email"
                            required
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                            placeholder="Enter your email"
                        />
                    </div>
                </div>

                {/* Password Field */}
                <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FontAwesomeIcon icon={faLock} className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            id="password"
                            name="password"
                            type={showPassword ? "text" : "password"}
                            autoComplete="current-password"
                            required
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                            placeholder="Enter your password"
                        />
                        <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowPassword(!showPassword)}
                        >
                            <FontAwesomeIcon 
                                icon={showPassword ? faEyeSlash : faEye} 
                                className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" 
                            />
                        </button>
                    </div>
                </div>

                {/* Forgot Password */}
                <div className="flex items-center justify-between">
                    <div className="text-sm">
                        <button
                            type="button"
                            onClick={() => setForm('resetpassword')}
                            className="font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
                        >
                            Forgot your password?
                        </button>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex">
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">
                                    Sign in failed
                                </h3>
                                <div className="mt-1 text-sm text-red-700">
                                    {error}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Submit Button */}
                <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    {isLoading ? (
                        <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Signing in...
                        </div>
                    ) : (
                        'Sign in'
                    )}
                </button>
            </form>
        </>
    )
}