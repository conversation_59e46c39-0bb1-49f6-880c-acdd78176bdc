'use client';

import { ReactNode, Suspense } from 'react';
import Spinner from './UI/Spinner';

interface SuspenseSearchParamsWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * A specialized wrapper for components that use useSearchParams
 * 
 * This component provides a Suspense boundary around components that use
 * useSearchParams to be used within a Suspense boundary, as required by Next.js 15.2.4+
 */
export default function SuspenseSearchParamsWrapper({ 
  children,
  fallback = <div className="flex items-center justify-center h-screen">
    <Spinner size="lg" text="Loading..." fullPage={false} />
  </div>
}: SuspenseSearchParamsWrapperProps) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
} 