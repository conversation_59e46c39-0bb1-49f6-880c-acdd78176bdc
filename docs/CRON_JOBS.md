# Cron Jobs Documentation

## Daily News Preload Cron Job

The application includes an automated cron job that preloads news data for all US states and national news daily at 4:00 AM EST (9:00 AM UTC).

### Configuration

The cron job is configured in `vercel.json`:

```json
{
  "crons": [
    {
      "path": "/api/cron/daily-news-preload",
      "schedule": "0 9 * * *"
    }
  ],
  "functions": {
    "app/api/cron/daily-news-preload/route.ts": {
      "maxDuration": 300
    }
  }
}
```

### What it does

1. **Preloads National News**: Fetches and caches national real estate news
2. **Preloads State News**: Fetches and caches news for all 50 US states in batches
3. **Preloads FRED Data**: Fetches and caches economic indicators from FRED API
4. **AI Analysis**: Generates sentiment scores and summaries for all articles
5. **Image Enhancement**: Fetches Open Graph images from article URLs

### Security

The cron job is protected by a secret token. Set the `CRON_SECRET` environment variable:

```bash
CRON_SECRET=your-secure-secret-key
```

### Manual Testing

You can manually trigger the cron job for testing:

#### Local Development
```bash
npm run trigger-news-preload
```

#### Production
```bash
npm run trigger-news-preload:prod
```

Or use the script directly:
```bash
node scripts/trigger-news-preload.js local
node scripts/trigger-news-preload.js production
```

### API Endpoint

**URL**: `/api/cron/daily-news-preload`
**Method**: `GET` or `POST`
**Headers**: 
- `Authorization: Bearer {CRON_SECRET}`

### Response Format

```json
{
  "success": true,
  "timestamp": "2024-01-15T09:00:00.000Z",
  "duration": "45 seconds",
  "totalStates": 51,
  "successCount": 51,
  "errorCount": 0,
  "message": "Daily news preload completed. 51 successful, 0 errors."
}
```

### Error Handling

- Individual state failures don't stop the entire process
- Graceful fallbacks for API failures
- Detailed error logging and reporting
- Redis cache failures are handled gracefully

### Performance Considerations

- **Batch Processing**: States are processed in batches of 8 to avoid API rate limits
- **Delays**: 2-second delays between batches to be respectful to external APIs
- **Timeout**: 5-minute maximum execution time
- **Caching**: All data is cached for 1.1 days to reduce API calls

### Monitoring

The cron job provides detailed logging:

- Start/end timestamps
- Success/error counts per batch
- Individual error messages
- Total execution duration
- Cache hit/miss statistics

### Environment Variables Required

```bash
# News API
SERPER_API_KEY=your-serper-api-key

# AI Analysis
OPENAI_API_KEY=your-openai-api-key

# Economic Data
FRED_API_KEY=your-fred-api-key

# Image Enhancement
UNSPLASH_ACCESS_KEY=your-unsplash-access-key

# Caching (optional)
REDIS_URL=your-redis-url

# Cron Security
CRON_SECRET=your-secure-secret-key
```

### Troubleshooting

1. **Unauthorized Error**: Check that `CRON_SECRET` is set correctly
2. **Timeout Issues**: Reduce batch size in the cron job code
3. **API Rate Limits**: Increase delays between batches
4. **Memory Issues**: Process fewer states per batch

### Deployment

The cron job is automatically deployed with your Vercel application. No additional setup required beyond environment variables.

### Local Development

For local development, you can run the cron job manually or set up a local scheduler using node-cron if needed. 