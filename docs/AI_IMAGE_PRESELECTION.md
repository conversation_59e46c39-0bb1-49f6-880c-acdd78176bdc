# AI Image Pre-selection Feature

## Overview
The AI Image Pre-selection feature uses OpenAI to analyze image titles from property search results and automatically pre-select images that are most likely to match the specific property address and unit.

## How It Works

### 1. Image Search Process
1. User clicks "Find Images" for a property unit
2. System searches for images using Serper.dev API
3. Image titles are collected from search results
4. OpenAI analyzes the titles to determine which images likely match the property
5. Matching images are automatically pre-selected for the user

### 2. OpenAI Analysis
The system sends the following information to OpenAI:
- Property address
- Unit number (if specified)
- List of image titles with indices

OpenAI considers:
- Exact address matches
- Street name matches
- Unit number matches
- Building name matches
- Neighborhood matches
- Property type relevance

### 3. Response Format
OpenAI returns:
```json
{
  "matchingIndices": [0, 3, 7],
  "analysis": "Selected images based on exact address matches and unit number correlations..."
}
```

## Technical Implementation

### API Endpoint
- **Route**: `/api/agentic-image-search`
- **Method**: POST
- **New Feature**: `analyzeImageTitlesWithOpenAI()` function

### Components Updated
- `PropertyUnitsImages.tsx`: Added UI for displaying OpenAI analysis and pre-selected indicators

### Environment Requirements
- `OPENAI_API_KEY`: Required for title analysis
- `SERPER_API_KEY`: Required for image search

## UI Changes

### Modal Enhancements
1. **AI Analysis Section**: Shows OpenAI's reasoning for image selection
2. **Pre-selection Count**: Displays how many images were automatically selected
3. **AI Match Indicators**: Visual badges on pre-selected images
4. **Enhanced Metadata**: Shows analysis results and selection statistics

### Visual Elements
- Blue highlight box showing OpenAI analysis
- "AI Match" badges on pre-selected images
- Count of pre-selected images in the modal

## Error Handling
- Graceful fallback if OpenAI API is unavailable
- Continues with normal functionality if analysis fails
- Logs errors for debugging without breaking the flow

## Benefits
1. **Time Saving**: Users don't need to manually review all images
2. **Accuracy**: AI helps identify the most relevant images
3. **User Experience**: Smart pre-selection reduces cognitive load
4. **Flexibility**: Users can still manually adjust selections

## Example Usage
```
Address: "50 West St, New York, NY 10006, USA"
Unit: "2"
OpenAI Analysis: "Selected 3 images that specifically mention '50 West St' and 'unit 2' in their titles, indicating they are likely photos of the exact property unit requested."
``` 