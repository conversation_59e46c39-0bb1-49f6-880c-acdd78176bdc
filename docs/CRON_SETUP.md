# Daily News Analysis Cron Job Setup

## Overview
The application automatically runs a daily news analysis cron job that fetches and analyzes real estate news for all 50 US states at 2:00 AM EST every morning.

## Features
- ✅ Fetches news for all 50 US states
- ✅ Generates AI summaries and sentiment scores
- ✅ Updates FRED economic data
- ✅ Caches all data in Redis for 24 hours
- ✅ Processes states in batches to respect API limits
- ✅ Comprehensive logging and error handling

## Environment Variables Required

Add these to your `.env` file:

```bash
# Cron Job Security
CRON_SECRET=your_secure_cron_secret_key_here

# Enable Cron Jobs (set to 'true' to enable in development)
ENABLE_CRON=false

# Application Base URL
NEXT_PUBLIC_BASE_URL=http://localhost:3001
```

## How It Works

### 1. Automatic Startup
- Cron jobs are automatically initialized when the application starts
- Only runs in production or when `ENABLE_CRON=true`
- Uses EST timezone for consistent scheduling

### 2. Daily Schedule
- **Time**: 2:00 AM EST (7:00 AM UTC)
- **Frequency**: Every day
- **Duration**: ~5-10 minutes depending on API response times

### 3. Processing Flow
1. Processes all 50 US states in batches of 10
2. Fetches real estate news for each state
3. Generates AI summaries and sentiment scores
4. Updates FRED economic data
5. Caches all data in Redis for 24 hours
6. Logs comprehensive statistics

## API Endpoints

### Cron Job Endpoint
```
GET /api/cron/daily-news-analysis
Authorization: Bearer {CRON_SECRET}
```

### Manual Trigger (for testing)
```
GET /api/cron/trigger-manual
```

### Initialize Cron Jobs
```
GET /api/init-cron
```

## Testing

### Manual Trigger
You can manually trigger the cron job for testing:

```bash
curl http://localhost:3001/api/cron/trigger-manual
```

### Check Logs
The cron job provides detailed logging:
- ✅ Success indicators
- ⚠️ Warning messages  
- ❌ Error details
- 📊 Processing statistics

## Production Deployment

### 1. Set Environment Variables
```bash
CRON_SECRET=your_secure_random_string
ENABLE_CRON=true
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

### 2. Verify Startup
Check logs for:
```
🚀 Starting cron job scheduler...
✅ Cron job scheduled: Daily news analysis will run at 2:00 AM EST every day
```

### 3. Monitor Execution
Daily logs will show:
```
⏰ Triggering daily news analysis cron job at: [timestamp]
📊 Processing 50 states in 5 batches of 10
✅ Daily news analysis cron job completed successfully
```

## Benefits

### For Users
- **Faster Loading**: Cached data loads instantly
- **Fresh Content**: Daily updates ensure current market data
- **Comprehensive Coverage**: All 50 states analyzed automatically

### For System
- **Cost Efficiency**: Reduces API calls by caching results
- **Performance**: Pre-processed data improves response times
- **Reliability**: Automated updates ensure data freshness

## Troubleshooting

### Cron Job Not Running
1. Check `ENABLE_CRON=true` in environment
2. Verify `CRON_SECRET` is set
3. Check application logs for initialization messages

### API Failures
- Cron job continues processing other states if one fails
- Errors are logged with specific state and reason
- Failed states can be retried on next run

### Cache Issues
- Redis connection failures are handled gracefully
- System falls back to direct API calls if cache unavailable
- Cache is automatically refreshed daily

## Security

- Cron endpoint requires `Authorization: Bearer {CRON_SECRET}`
- Secret should be a strong, random string
- Only accessible via authenticated requests 