/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable fast refresh and hot reload for development
  reactStrictMode: true,
  
  experimental: {
    // Disable turbopack for now to avoid SWC issues
    // turbo: {
    //   resolveAlias: {
    //     canvas: './empty-module.ts',
    //   },
    // },
    forceSwcTransforms: true,
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
      // Specific patterns for common domains
      {
        protocol: 'https',
        hostname: '*.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: '*.cloudfront.net',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'source.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'flagcdn.com',
      }
    ],
    // Allow all domains as fallback
    domains: [],
    // Disable optimization for external images to prevent errors
    unoptimized: false,
    // Add error handling
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  //pdf
  experimental: {
    // Enable turbo mode for faster development
    turbo: {
      resolveAlias: {
        canvas: './empty-module.ts',
      },
    },
  },
  
  webpack: (config, { dev, isServer }) => {
    config.resolve.alias.canvas = false;
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
    };
    
    // Enable hot reload in development
    if (dev && !isServer) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }
    
    return config;
  },
}

module.exports = nextConfig 