'use client'
import SignIn from '@/components/LoginPage/SignIn';
import SignUp from '@/components/LoginPage/SignUp';
import ResetPassword from '@/components/LoginPage/ResetPassword';
import SocialButtons from '@/components/LoginPage/SocialButtons';
import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { getCookie, getCookies, setCookie, deleteCookie, hasCookie } from 'cookies-next';
import cookieName from '@/constants/cookieName';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine, faBuilding, faUsers, faShieldAlt, faArrowRight, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

function LoginContent() {
    const [form, setForm] = useState<'signin' | 'signup' | 'resetpassword'>('signin');
    const searchParams = useSearchParams();
    const [tokenEmail, setTokenEmail] = useState<string | null>(null);
    const [tokenFirstName, setTokenFirstName] = useState<string | null>(null);
    const [tokenData, setTokenData] = useState<any | null>(null);

    const handleDecodeToken = async () => {
        const token = getCookie(cookieName.invitePortfolioToken)
        const res = await fetch('/api/jwt-decode-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token: token }),
        })

        const data = await res.json()
        return data
    }
    
    useEffect(() => {
        // Check for signup or resetpassword parameters
        if (searchParams.has('signup')) {
          setForm('signup');
        } else if (searchParams.has('resetpassword')) {
          setForm('resetpassword');
        }
      }, [searchParams]);

    useEffect(() => {
        if(hasCookie(cookieName.invitePortfolioToken)) {
            handleDecodeToken().then((data) => {
                if(data?.payload?.tokenType === 'invite_portfolio') {
                    setForm('signup')
                    setTokenEmail(data?.payload?.email)
                    setTokenFirstName(data?.payload?.first_name)
                    setTokenData(data?.payload)
                }
            })
        }
    }, [hasCookie(cookieName.invitePortfolioToken)])
    
    return (
        <div className="h-full flex">
            {/* Left Side - Marketing Content (Hidden on Mobile) */}
            <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-indigo-900 via-indigo-800 to-purple-900 relative overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                    }} />
                </div>
                
                <div className="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
                    {/* Logo */}
                    <div className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg">
                                <Image 
                                    src="/relm-logo-small.svg" 
                                    alt="Relm Professional" 
                                    width={32} 
                                    height={32}
                                    className="w-8 h-8"
                                />
                            </div>
                            <div>
                                <h1 className="text-2xl font-bold">Relm Professional</h1>
                                <p className="text-indigo-200 text-sm">Real Estate Intelligence Platform</p>
                            </div>
                        </div>
                    </div>

                    {/* Main Heading */}
                    <div className="mb-8">
                        <h2 className="text-4xl font-bold leading-tight mb-4">
                            Transform Your Deal Management with Next-Gen Property Research
                        </h2>
                    </div>

                    {/* Features */}
                    <div className="space-y-4 mb-12">
                        <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                                <FontAwesomeIcon icon={faChartLine} className="h-5 w-5" />
                            </div>
                            <div>
                                <h3 className="font-semibold">Advanced Analytics</h3>
                                <p className="text-indigo-200 text-sm">Real-time market insights and performance metrics</p>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                                <FontAwesomeIcon icon={faBuilding} className="h-5 w-5" />
                            </div>
                            <div>
                                <h3 className="font-semibold">Portfolio Management</h3>
                                <p className="text-indigo-200 text-sm">Centralized property data and document management</p>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                                <FontAwesomeIcon icon={faUsers} className="h-5 w-5" />
                            </div>
                            <div>
                                <h3 className="font-semibold">Team Collaboration</h3>
                                <p className="text-indigo-200 text-sm">Secure sharing and real-time collaboration tools</p>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                                <FontAwesomeIcon icon={faShieldAlt} className="h-5 w-5" />
                            </div>
                            <div>
                                <h3 className="font-semibold">Enterprise Security</h3>
                                <p className="text-indigo-200 text-sm">Bank-level security with role-based access control</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="w-full lg:w-1/2 flex flex-col justify-center px-6 py-12 lg:px-12 bg-gray-50">
                {/* Mobile Logo */}
                <div className="lg:hidden mb-8 text-center">
                    <div className="flex items-center justify-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <Image 
                                src="/relm-logo-small.svg" 
                                alt="Relm Professional" 
                                width={24} 
                                height={24}
                                className="w-6 h-6 filter brightness-0 invert"
                            />
                        </div>
                        <div>
                            <h1 className="text-xl font-bold text-gray-900">Relm Professional</h1>
                        </div>
                    </div>
                    <p className="text-gray-600 text-sm">Real Estate Intelligence Platform</p>
                </div>

                <div className="w-full max-w-md mx-auto">
                    {
                        form === 'signin' ? <SignIn setForm={setForm} tokenEmail={tokenEmail} tokenData={tokenData} setTokenData={setTokenData} /> :
                        form === 'signup' ? <SignUp setForm={setForm} tokenEmail={tokenEmail} tokenFirstName={tokenFirstName} tokenData={tokenData} setTokenData={setTokenData} /> :
                        form === 'resetpassword' ? <ResetPassword setForm={setForm} /> : null
                    }
                    
                    {
                        form !== 'resetpassword' && (
                            <>
                                <div className="relative my-6">
                                    <div className="absolute inset-0 flex items-center">
                                        <div className="w-full border-t border-gray-300"></div>
                                    </div>
                                    <div className="relative flex justify-center text-sm">
                                        <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                                    </div>
                                </div>
                                <SocialButtons />
                            </>
                        )
                    }
                </div>
            </div>
        </div>
    );
}

export default function LoginPage() {
    return (
        <Suspense fallback={
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
        }>
            <LoginContent />
        </Suspense>
    );
}