'use client'

import { Inter } from 'next/font/google'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',  
  weight: ['400', '500', '600', '700'],
})

function GlobalErrorContent({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html lang="en" className={inter.className}>
      <body className="antialiased bg-gray-50">
        <div className="flex flex-col items-center justify-center min-h-screen px-4 text-center">
          <h2 className="text-3xl font-bold text-red-600 mb-4">Something went wrong!</h2>
          <p className="text-gray-700 mb-6">{error.message || 'An unexpected error occurred'}</p>
          <button
            onClick={() => reset()}
            className="px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
          >
            Try again
          </button>
        </div>
      </body>
    </html>
  )
}

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <GlobalErrorContent error={error} reset={reset} />
  )
} 