import { NextRequest, NextResponse } from 'next/server'
import sendgrid from '@sendgrid/mail'

sendgrid.setA<PERSON><PERSON><PERSON>(process.env.SENDGRID_API_KEY!)

export async function POST(req: NextRequest) {
  const body = await req.json()
  const { email, first_name, portfolio_name, invite_link } = body

  if (!email || !first_name || !portfolio_name || !invite_link) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
  }

  try {
    await sendgrid.send({
      to: email,
      from: '<EMAIL>', 
      templateId: 'd-240a305d07554bd4b80354471900a735',
      dynamicTemplateData: {
        first_name,
        portfolio_name,
        invite_link,
      },
    })

    return NextResponse.json({ message: 'Invite sent successfully' }, { status: 200 })
  } catch (error: any) {
    console.error('SendGrid Error:', error?.response?.body || error.message)
    return NextResponse.json({ error: 'Failed to send invite' }, { status: 500 })
  }
}