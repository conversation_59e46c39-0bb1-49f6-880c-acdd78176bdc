'use server'

import { NextRequest, NextResponse } from 'next/server';
import { fetchRealEstateNewsWithSummaries } from '@/actions/newsActions';
import { fetchFredCharts } from '@/actions/fredActions';

// All US states for comprehensive analysis
const ALL_US_STATES = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

export async function GET(request: NextRequest) {
    try {
        // Verify this is a legitimate cron request
        const authHeader = request.headers.get('authorization');
        const cronSecret = 'cwn87rh3934rjn38nfg90c438n938gn34';
        
        if (authHeader !== `Bearer ${cronSecret}`) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        console.log('🚀 Starting daily news analysis cron job at:', new Date().toISOString());
        
        const startTime = Date.now();
        let successCount = 0;
        let errorCount = 0;
        const errors: string[] = [];

        // Process states in batches to avoid overwhelming APIs
        const batchSize = 10;
        const batches = [];
        for (let i = 0; i < ALL_US_STATES.length; i += batchSize) {
            batches.push(ALL_US_STATES.slice(i, i + batchSize));
        }

        console.log(`📊 Processing ${ALL_US_STATES.length} states in ${batches.length} batches of ${batchSize}`);

        // Process each batch sequentially to be respectful to APIs
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            console.log(`🔄 Processing batch ${batchIndex + 1}/${batches.length}: ${batch.join(', ')}`);

            try {
                // Fetch news for this batch of states
                const newsResults = await fetchRealEstateNewsWithSummaries(batch, 1, {});
                
                // Count successful states
                newsResults.forEach(stateResult => {
                    if (stateResult.articles.length > 0) {
                        successCount++;
                        console.log(`✅ ${stateResult.state}: ${stateResult.articles.length} articles processed`);
                    } else {
                        errorCount++;
                        console.log(`⚠️ ${stateResult.state}: No articles found`);
                    }
                });

                // Add a small delay between batches to be respectful
                if (batchIndex < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
                }

            } catch (error) {
                console.error(`❌ Error processing batch ${batchIndex + 1}:`, error);
                batch.forEach(state => {
                    errorCount++;
                    errors.push(`${state}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                });
            }
        }

        // Also refresh FRED economic data
        console.log('📈 Refreshing FRED economic data...');
        try {
            const fredCharts = await fetchFredCharts(1, 12);
            console.log(`✅ FRED data refreshed: ${fredCharts.length} charts updated`);
        } catch (fredError) {
            console.error('❌ Error refreshing FRED data:', fredError);
            errors.push(`FRED: ${fredError instanceof Error ? fredError.message : 'Unknown error'}`);
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        const summary = {
            timestamp: new Date().toISOString(),
            duration: `${duration} seconds`,
            totalStates: ALL_US_STATES.length,
            successCount,
            errorCount,
            successRate: `${Math.round((successCount / ALL_US_STATES.length) * 100)}%`,
            errors: errors.length > 0 ? errors : undefined
        };

        console.log('🎉 Daily news analysis cron job completed:', summary);

        return NextResponse.json({
            success: true,
            message: 'Daily news analysis completed successfully',
            summary
        });

    } catch (error) {
        console.error('💥 Fatal error in daily news analysis cron job:', error);
        
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// Also support POST for manual triggers
export async function POST(request: NextRequest) {
    return GET(request);
} 