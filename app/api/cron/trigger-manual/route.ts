import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    try {
        console.log('🔧 Manual trigger for daily news analysis cron job');
        
        const cronSecret = process.env.CRON_SECRET || 'your-secret-key';
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';
        
        // Trigger the cron job
        const response = await fetch(`${baseUrl}/api/cron/daily-news-analysis`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${cronSecret}`,
                'Content-Type': 'application/json',
            },
        });

        if (response.ok) {
            const result = await response.json();
            return NextResponse.json({
                success: true,
                message: 'Manual cron job trigger completed successfully',
                result
            });
        } else {
            const error = await response.text();
            return NextResponse.json({
                success: false,
                error: `Cron job failed: ${error}`
            }, { status: 500 });
        }
    } catch (error) {
        console.error('Error triggering manual cron job:', error);
        
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    return GET(request);
} 