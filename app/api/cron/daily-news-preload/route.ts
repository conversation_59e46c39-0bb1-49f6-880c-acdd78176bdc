import { NextRequest, NextResponse } from 'next/server';
import { fetchRealEstateNewsWithSummaries } from '@/actions/newsActions';
import { fetchFredCharts } from '@/actions/fredActions';

// All US states for comprehensive preloading
const ALL_US_STATES = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

export async function GET(request: NextRequest) {
    try {
        // Verify this is a legitimate cron request
        const authHeader = request.headers.get('authorization');
        const cronSecret = process.env.CRON_SECRET || 'relm-intel-cron-2024';
        
        if (authHeader !== `Bearer ${cronSecret}`) {
            console.log('Unauthorized cron request attempt');
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        console.log('🚀 Starting daily news preload cron job at:', new Date().toISOString());
        
        const startTime = Date.now();
        let successCount = 0;
        let errorCount = 0;
        const errors: string[] = [];

        // First, preload national news
        try {
            console.log('📰 Preloading national news...');
            await fetchRealEstateNewsWithSummaries(['NATIONAL'], 1, {});
            successCount++;
            console.log('✅ National news preloaded successfully');
        } catch (error) {
            console.error('❌ Error preloading national news:', error);
            errors.push(`National: ${error instanceof Error ? error.message : 'Unknown error'}`);
            errorCount++;
        }

        // Process states in batches to avoid overwhelming APIs
        const batchSize = 8; // Smaller batches for better reliability
        const batches = [];
        for (let i = 0; i < ALL_US_STATES.length; i += batchSize) {
            batches.push(ALL_US_STATES.slice(i, i + batchSize));
        }

        console.log(`📊 Processing ${ALL_US_STATES.length} states in ${batches.length} batches of ${batchSize}`);

        // Process each batch with delays
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            console.log(`🔄 Processing batch ${batchIndex + 1}/${batches.length}: ${batch.join(', ')}`);
            
            try {
                // Fetch news for this batch of states
                await fetchRealEstateNewsWithSummaries(batch, 1, {});
                successCount += batch.length;
                console.log(`✅ Batch ${batchIndex + 1} completed successfully`);
            } catch (error) {
                console.error(`❌ Error in batch ${batchIndex + 1}:`, error);
                errors.push(`Batch ${batchIndex + 1} (${batch.join(', ')}): ${error instanceof Error ? error.message : 'Unknown error'}`);
                errorCount += batch.length;
            }
            
            // Add delay between batches to be respectful to APIs (except for last batch)
            if (batchIndex < batches.length - 1) {
                console.log('⏳ Waiting 2 seconds before next batch...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        // Preload FRED charts data
        try {
            console.log('📈 Preloading FRED economic data...');
            await fetchFredCharts(1, 12); // Get all 12 charts
            console.log('✅ FRED data preloaded successfully');
        } catch (error) {
            console.error('❌ Error preloading FRED data:', error);
            errors.push(`FRED: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        const summary = {
            success: true,
            timestamp: new Date().toISOString(),
            duration: `${duration} seconds`,
            totalStates: ALL_US_STATES.length + 1, // +1 for national
            successCount,
            errorCount,
            errors: errors.length > 0 ? errors : undefined,
            message: `Daily news preload completed. ${successCount} successful, ${errorCount} errors.`
        };

        console.log('🎉 Daily news preload cron job completed:', summary);

        return NextResponse.json(summary);

    } catch (error) {
        console.error('💥 Fatal error in daily news preload cron job:', error);
        
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// Also support POST method for manual triggers
export async function POST(request: NextRequest) {
    return GET(request);
} 