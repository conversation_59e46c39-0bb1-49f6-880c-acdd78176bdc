import { NextRequest, NextResponse } from 'next/server';

const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

function parseSolarData(data: any) {
  const countPanels = data?.panelsCount ?? 24;
  const solar = data?.solarPotential;
  const config = data?.solarPotential?.solarPanelConfigs?.find((c: any) => c.panelsCount === countPanels);
  const carbonPerMWh = solar?.carbonOffsetFactorKgPerMwh ?? 0;
 

  if (!solar || !config) return null;

  const systemSizeKw = (countPanels * solar.panelCapacityWatts) / 1000;
  const roofAreaSqft = solar.buildingStats?.areaMeters2 ? (solar.buildingStats.areaMeters2 * 10.7639).toFixed(0) : '—';
  const annualKwh = config.yearlyEnergyDcKwh;

  const annualCO2Tons = ((annualKwh / 1000) * carbonPerMWh / 1000).toFixed(2);
  const totalCO2Tons = (parseFloat(annualCO2Tons) * 25).toFixed(2);

  const trees = Math.round(parseFloat(annualCO2Tons) * 113); // 1 тонна CO2 ≈ 113 деревьев/год

  return {
    systemSize: `${systemSizeKw.toFixed(1)} kW`,
    panels: countPanels,
    roofArea: `${roofAreaSqft} sq ft`,
    annualProduction: `${Math.round(annualKwh).toLocaleString()} kWh`,
    co2OffsetAnnual: `${annualCO2Tons} tons`,
    co2Offset25Year: `${totalCO2Tons} tons`,
    trees: `${trees} trees planted annually`
  };
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const lat = searchParams.get('lat')
  const lng = searchParams.get('lng')
  
  const res = await fetch(
    `https://solar.googleapis.com/v1/buildingInsights:findClosest?location.latitude=${lat}&location.longitude=${lng}&requiredQuality=HIGH&key=${apiKey}`,
    {
      headers: {
        'Referer': request.headers.get('referer') || process.env.NEXT_PUBLIC_SITE_URL || ''
      }
    }
  );

  if (!res.ok) {
    console.error("Failed to fetch solar data:", res.status);
    return NextResponse.json({ message: 'Failed to fetch solar data' }, { status: 500 })
  }

  const data = await res.json();
  const parsedData = parseSolarData(data);

  if (!parsedData) {
    return NextResponse.json({ message: 'No usable solar config found' }, { status: 404 });
  }

  return NextResponse.json({ parsedData })
} 