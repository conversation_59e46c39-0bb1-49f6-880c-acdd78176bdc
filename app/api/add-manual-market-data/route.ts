import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { canonicalizeListingType } from '@/utils/formatters';

export async function POST(request: NextRequest) {
    try {
        const { addressId, unitId, unitNumber, marketDataEntry } = await request.json();

        if (!addressId || !unitId || !marketDataEntry) {
            return NextResponse.json(
                { error: 'Missing required fields: addressId, unitId, and marketDataEntry are required' },
                { status: 400 }
            );
        }

        const supabase = await createClient();

        // Get property data
        const { data: propData, error: propError } = await supabase
            .from('prop')
            .select('*')
            .eq('address_id', addressId)
            .single();

        if (propError) {
            return NextResponse.json(
                { error: `Failed to get property data: ${propError.message}` },
                { status: 500 }
            );
        }

        // Create manual market data entry with source tracking
        const manualEntry = {
            list_date: marketDataEntry.date || new Date().toISOString().split('T')[0],
            list_price: Number(marketDataEntry.price) || 0,
            listing_type: canonicalizeListingType(marketDataEntry.listingType || 'manual_entry'),
            description: {
                text: marketDataEntry.description || 'Manual entry',
                beds: Number(marketDataEntry.beds) || 0,
                baths_full: Number(marketDataEntry.baths) || 0,
                sqft: Number(marketDataEntry.sqft) || 0,
                type: marketDataEntry.listingType || 'manual_entry'
            },
            source: {
                id: 'manual_entry',
                type: 'manual',
                added_by: 'user',
                added_at: new Date().toISOString(),
                sources: ['Manual Entry'],
                disclaimer: "Manually entered by user. Please verify accuracy."
            },
            confidence: 100, // Manual entries have 100% confidence
            bedrooms: Number(marketDataEntry.beds) || 0,
            bathrooms: Number(marketDataEntry.baths) || 0,
            squareFeet: Number(marketDataEntry.sqft) || 0,
            unitNumber: unitNumber,
            lastEvent: canonicalizeListingType(marketDataEntry.listingType || 'manual_entry'),
            sourceUrl: 'manual_entry',
            images: [],
            history: [{
                date: marketDataEntry.date || new Date().toISOString().split('T')[0],
                type: canonicalizeListingType(marketDataEntry.listingType || 'manual_entry'),
                price: Number(marketDataEntry.price) || 0,
                isLatest: true
            }],
            last_sold_date: marketDataEntry.listingType === 'sold' ? marketDataEntry.date : null,
            last_sold_price: marketDataEntry.listingType === 'sold' ? Number(marketDataEntry.price) : 0,
            price_per_sqft: (marketDataEntry.sqft && marketDataEntry.sqft > 0) ? 
                Math.round(Number(marketDataEntry.price) / Number(marketDataEntry.sqft)) : 0,
            property_id: `manual_${addressId}_${unitId}`,
            flags: {
                is_pending: (marketDataEntry.listingType || '').toLowerCase() === 'pending',
                is_for_rent: (marketDataEntry.listingType || '').toLowerCase().includes('rent'),
                is_contingent: null
            }
        };

        // Check if market data record exists for this unit
        const { data: existingMarketData, error: fetchError } = await supabase
            .from('prop_market_data')
            .select('*')
            .eq('prop_id', propData.id)
            .eq('unit_id', unitId)
            .single();

        let result;
        if (fetchError && fetchError.code === 'PGRST116') {
            // No existing record, create new one
            const { data: insertedData, error: insertError } = await supabase
                .from('prop_market_data')
                .insert({
                    prop_id: propData.id,
                    unit_id: unitId,
                    unit: unitNumber,
                    data: [manualEntry]
                })
                .select();

            if (insertError) {
                return NextResponse.json(
                    { error: `Failed to create market data: ${insertError.message}` },
                    { status: 500 }
                );
            }
            result = insertedData;
        } else if (existingMarketData) {
            // Existing record found, append to data array
            const updatedData = [...(existingMarketData.data || []), manualEntry];
            
            const { data: updatedMarketData, error: updateError } = await supabase
                .from('prop_market_data')
                .update({
                    data: updatedData
                })
                .eq('id', existingMarketData.id)
                .select();

            if (updateError) {
                return NextResponse.json(
                    { error: `Failed to update market data: ${updateError.message}` },
                    { status: 500 }
                );
            }
            result = updatedMarketData;
        } else {
            return NextResponse.json(
                { error: `Database error: ${fetchError?.message || 'Unknown database error'}` },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            message: 'Manual market data entry added successfully',
            data: manualEntry,
            saved: true
        });

    } catch (error) {
        console.error('Add manual market data error:', error);
        return NextResponse.json(
            { error: 'Failed to add manual market data entry' },
            { status: 500 }
        );
    }
} 