import { NextRequest, NextResponse } from 'next/server';
import { preloadAllMarketData, getCacheStatus, clearAllCache } from '@/actions/preloadActions';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    switch (action) {
      case 'preload':
        await preloadAllMarketData();
        return NextResponse.json({ 
          success: true, 
          message: 'Market data preload completed successfully' 
        });
        
      case 'status':
        const status = await getCacheStatus();
        return NextResponse.json({ 
          success: true, 
          data: status 
        });
        
      case 'clear':
        await clearAllCache();
        return NextResponse.json({ 
          success: true, 
          message: 'All cache cleared successfully' 
        });
        
      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: preload, status, or clear' 
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Preload API error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to execute preload action' 
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const status = await getCacheStatus();
    return NextResponse.json({ 
      success: true, 
      data: status,
      message: 'Cache status retrieved successfully'
    });
  } catch (error) {
    console.error('Cache status API error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to get cache status' 
    }, { status: 500 });
  }
} 