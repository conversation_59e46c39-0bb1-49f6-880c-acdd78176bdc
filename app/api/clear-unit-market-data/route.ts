import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
    try {
        const { unitId, addressId } = await request.json();

        if (!unitId || !addressId) {
            return NextResponse.json(
                { error: 'Unit ID and Address ID are required' },
                { status: 400 }
            );
        }

        const supabase = await createClient();

        // Get the property data first
        const { data: propData, error: propError } = await supabase
            .from('prop')
            .select('*')
            .eq('address_id', addressId)
            .single();

        if (propError || !propData) {
            console.error('Error fetching property data:', propError);
            return NextResponse.json(
                { error: 'Property not found' },
                { status: 404 }
            );
        }

        // Delete all market data for this unit
        const { error: deleteError } = await supabase
            .from('prop_market_data')
            .delete()
            .eq('unit_id', unitId)
            .eq('prop_id', propData.id);

        if (deleteError) {
            console.error('Error clearing market data:', deleteError);
            return NextResponse.json(
                { error: 'Failed to clear market data' },
                { status: 500 }
            );
        }

        console.log(`Successfully cleared market data for unit ${unitId}`);

        return NextResponse.json({
            success: true,
            message: 'Market data cleared successfully',
            unitId,
            addressId
        });

    } catch (error) {
        console.error('Clear market data error:', error);
        return NextResponse.json(
            { error: 'Failed to clear market data' },
            { status: 500 }
        );
    }
} 