import { NextRequest, NextResponse } from 'next/server';
import { createClient } from 'redis';

export async function GET(request: NextRequest) {
    try {
        console.log('Testing Redis connection...');
        
        if (!process.env.REDIS_URL) {
            return NextResponse.json({
                success: false,
                error: 'REDIS_URL environment variable not set',
                timestamp: new Date().toISOString()
            }, { status: 500 });
        }

        const redisClient = createClient({
            url: process.env.REDIS_URL
        });
        
        redisClient.on('error', (err: any) => {
            console.error('Redis Client Error', err);
        });
        
        await redisClient.connect();
        console.log('Redis connected successfully');
        
        // Test write
        const testKey = 'test_connection';
        const testValue = JSON.stringify({ 
            message: 'Redis is working!', 
            timestamp: new Date().toISOString() 
        });
        
        await redisClient.setEx(testKey, 60, testValue); // 1 minute expiry
        console.log('Test data written to Redis');
        
        // Test read
        const retrievedValue = await redisClient.get(testKey);
        console.log('Test data read from Redis:', retrievedValue);
        
        await redisClient.disconnect();
        
        return NextResponse.json({
            success: true,
            message: 'Redis connection test successful',
            testData: JSON.parse(retrievedValue || '{}'),
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('Redis connection test failed:', error);
        
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
} 