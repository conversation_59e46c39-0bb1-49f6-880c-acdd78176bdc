import { NextRequest, NextResponse } from 'next/server';
import { canonicalizeListingType } from '@/utils/formatters';
import OpenAI from 'openai';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

const SERPER_API_KEY = process.env.SERPER_API_KEY;



export async function POST(request: NextRequest) {
    try {
        const { address, unit, beds, baths, sqft } = await request.json();

        if (!address) {
            return NextResponse.json(
                { error: 'Address is required' },
                { status: 400 }
            );
        }

        if (!SERPER_API_KEY) {
            return NextResponse.json(
                { error: 'Serper API key not configured' },
                { status: 500 }
            );
        }

        // Perform real market data search
        const searchResults = await performRealMarketSearch(address, unit, beds, baths, sqft);

        return NextResponse.json({
            success: true,
            data: searchResults.data,
            sources: searchResults.sources,
            searchQuery: searchResults.primaryQuery,
            totalResults: searchResults.data.length,
            searchTime: new Date().toISOString(),
            disclaimer: "AI-generated data from real search results. Please verify independently."
        });

    } catch (error) {
        console.error('Market search error:', error);
        return NextResponse.json(
            { error: 'Failed to perform market search' },
            { status: 500 }
        );
    }
}

async function performRealMarketSearch(address: string, unit: string, beds: number, baths: number, sqft: number) {
    const results: any[] = [];
    const sources: string[] = [];
    
    try {
        // Create search queries for rent and sales
        const baseAddress = address.replace(/,.*$/, ''); // Remove city/state for cleaner search
        const unitSuffix = unit ? ` unit ${unit}` : '';
        
        const rentQuery = `"${baseAddress}"${unitSuffix} rent`;
        const salesQuery = `"${baseAddress}"${unitSuffix} sale`;

        console.log('Searching for rental data:', rentQuery);
        console.log('Searching for sales data:', salesQuery);

        // Search for rental data
        const rentData = await searchWithSerper(rentQuery);
        
        // Search for sales data  
        const salesData = await searchWithSerper(salesQuery);

        // Combine all search results for AI analysis
        const allSearchResults = [
            ...rentData.organic || [],
            ...salesData.organic || []
        ];

        if (allSearchResults.length === 0) {
            return {
                data: [],
                sources: [],
                primaryQuery: rentQuery
            };
        }

        // Extract market data using OpenAI
        const extractedData = await extractMarketDataWithAI(allSearchResults, address, unit, beds, baths, sqft);
        
        results.push(...extractedData.marketData);
        sources.push(...extractedData.sources);

        // Sort results by confidence and recency
        results.sort((a, b) => {
            const confidenceSort = (b.confidence || 0) - (a.confidence || 0);
            if (confidenceSort !== 0) return confidenceSort;
            
            const dateA = new Date(a.date || '2020-01-01');
            const dateB = new Date(b.date || '2020-01-01');
            return dateB.getTime() - dateA.getTime();
        });

        return {
            data: results.slice(0, 10), // Return top 10 results
            sources: [...new Set(sources)], // Remove duplicates
            primaryQuery: rentQuery
        };

    } catch (error) {
        console.error('Real market search error:', error);
        return {
            data: [],
            sources: [],
            primaryQuery: `"${address}" ${unit ? `unit ${unit}` : ''} rent`
        };
    }
}

async function searchWithSerper(query: string) {
    try {
        const response = await fetch('https://google.serper.dev/search', {
            method: 'POST',
            headers: {
                'X-API-KEY': SERPER_API_KEY!,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                q: query,
                gl: 'us',
                hl: 'en',
                num: 10,
            }),
        });

        if (!response.ok) {
            throw new Error(`Serper API error: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Serper search error:', error);
        return { organic: [] };
    }
}

async function extractMarketDataWithAI(searchResults: any[], address: string, unit: string, beds: number, baths: number, sqft: number) {
    try {
        // Pass serper results directly to OpenAI without scraping content
        const searchContext = searchResults.map(result => ({
            title: result.title,
            link: result.link,
            snippet: result.snippet,
            date: result.date,
            position: result.position
        }));

        const prompt = `You are a real estate data analyst. Analyze the following search results for property "${address}" ${unit ? `unit ${unit}` : ''} and extract market data for rent prices, sale prices, and other relevant market information.

PROPERTY DETAILS:
- Address: ${address}
- Unit: ${unit || 'N/A'}
- Bedrooms: ${beds}
- Bathrooms: ${baths}
- Square Feet: ${sqft}

SEARCH RESULTS:
${JSON.stringify(searchContext, null, 2)}

Extract specific rental and sales data from these search results using titles and snippets only. Look for:
1. Rental prices (monthly rent)
2. Sale prices (purchase prices)
3. Market analysis data
4. Historical pricing information
5. Listing information

For each data point found, provide:
- date: When this price/info was recorded (YYYY-MM-DD format, estimate if needed)
- price: The actual dollar amount (numbers only, no commas)
- listingType: One of "Rental Listing", "Sale Listing", "Lease Agreement", "Market Analysis", "Price History"
- confidence: How confident you are in this data (0-100)
- description: Brief description of what this data represents
- source: The domain name from the link
- sourceUrl: The complete URL from the search results

ONLY extract data that you can clearly identify from the search results. Do not make up or estimate prices that aren't mentioned in the content.

Provide your response in valid JSON format:
{
  "marketData": [
    {
      "date": "YYYY-MM-DD",
      "price": number,
      "listingType": "string",
      "confidence": number,
      "description": "string",
      "source": "string",
      "sourceUrl": "string"
    }
  ],
  "sources": ["list of unique domains"],
  "summary": "Brief summary of market data found"
}`;

        const response = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert real estate data analyst. Extract only factual market data from search result snippets and titles. Do not invent or estimate data not present in the sources. Always include source URLs.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 2000,
            temperature: 0.1
        });

        const responseText = response.choices[0]?.message?.content;
        if (!responseText) {
            throw new Error('No response from OpenAI');
        }

        let analysis;
        try {
            // Try to parse as direct JSON first
            analysis = JSON.parse(responseText);
        } catch (parseError) {
            try {
                // If direct parsing fails, try to extract JSON from markdown code blocks
                const jsonMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
                if (jsonMatch && jsonMatch[1]) {
                    analysis = JSON.parse(jsonMatch[1]);
                } else {
                    // Try to find JSON-like content without markdown
                    const jsonStart = responseText.indexOf('{');
                    const jsonEnd = responseText.lastIndexOf('}');
                    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                        const jsonContent = responseText.slice(jsonStart, jsonEnd + 1);
                        analysis = JSON.parse(jsonContent);
                    } else {
                        throw new Error('No valid JSON found in response');
                    }
                }
            } catch (secondParseError) {
                console.error('Failed to parse OpenAI response:', responseText);
                console.error('Parse errors:', parseError, secondParseError);
                throw new Error('Invalid JSON response from OpenAI');
            }
        }
        
        // Validate and clean the extracted data
        const validatedMarketData = (analysis.marketData || []).map((item: any) => ({
            date: item.date || new Date().toISOString().split('T')[0],
            price: typeof item.price === 'number' ? item.price : 0,
            listingType: canonicalizeListingType(item.listingType || 'Market Analysis'),
            confidence: Math.min(100, Math.max(0, item.confidence || 50)),
            description: item.description || 'Market data extracted from search results',
            source: item.source || 'search results',
            sourceUrl: item.sourceUrl || ''
        })).filter((item: any) => item.price > 0); // Only include items with valid prices

        return {
            marketData: validatedMarketData,
            sources: analysis.sources || [],
            summary: analysis.summary || 'Market data extracted from search results'
        };

    } catch (error) {
        console.error('AI extraction error:', error);
        return {
            marketData: [],
            sources: [],
            summary: 'Failed to extract market data from search results'
        };
    }
} 