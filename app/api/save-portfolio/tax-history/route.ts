import { fetchTaxHistory } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { taxHistory, newPropId, address, city, state, zipcode } = await request.json();

    const supabase = await createClient();

    if(taxHistory){
        const { data: newTaxHistory, error: newTaxHistoryError } = await supabase.from('prop_tax_history')
        .insert(taxHistory)
    }else{
        console.log('no tax history data, getting')
        const data = await fetchTaxHistory(address, city, state, zipcode)

        const taxHistoryInsert = data?.tax_records?.map((tax: any) => ({
            prop_id: newPropId,
            assessed_tax_year: tax?.assessed_tax_year,
            assessed_value_land: tax?.assessed_value_land,
            assessed_value_total: tax?.assessed_value_total,
            tax_bill_amount: tax?.tax_bill_amount,
        }))

        const { data: newTaxHistory, error: newTaxHistoryError } = await supabase.from('prop_tax_history')
        .insert(taxHistoryInsert)
    }


    return NextResponse.json({ message: 'Demographics saved' });
}