import { fetchSchoolData } from '@/actions/marketAnalysisActions/cherreActions';
import { fetchNearbyPlacesWithGoogleMaps } from '@/actions/marketAnalysisActions/googleMapsActions';
import { getTransitData } from '@/actions/marketAnalysisActions/unitDataActions';
import { fetchDemographics } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { nearbyLocations, schoolData, myLocations, transitData, newPropId, address, zipcode, displayAddress, lat, lon } = await request.json();

    const supabase = await createClient();

    const nearbyLocactionInsert = [
        {
            prop_id: newPropId,
            data: nearbyLocations || [],
            type: 'nearby_locations'
        },
        {
            prop_id: newPropId,
            data: schoolData || await fetchSchoolData(`${address}, ${zipcode}`) || [],
            type: 'schools'
        },
        {
            prop_id: newPropId,
            data: myLocations || [],
            type: 'my_locations'
        },
        {
            prop_id: newPropId,
            data: transitData || await getTransitData(lat, lon) || [],
            type: 'transit'
        }
    ]

    const {data: newNearbyLocations, error: newNearbyLocationsError} = await supabase
        .from('pois')
        .insert(nearbyLocactionInsert)

    if(newNearbyLocationsError){
        console.log(newNearbyLocationsError)
    }


    return NextResponse.json({ message: 'Nearby locations saved' });
}