import { NextRequest, NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export async function POST(request: NextRequest) {
    const { address_id, portfolio_id, main_img_url, img_urls } = await request.json();

    const supabase = await createClient();

    const { data: newProp, error: newPropError } = await supabase.from('prop')
    .insert({
        address_id: address_id,
        portfolio_id: portfolio_id,
        main_img_url: main_img_url,
        img_urls: img_urls || [],
        //ai_summary: aiSummary
    })
    
    console.log(address_id || 'no address_id', portfolio_id || 'no portfolio_id', main_img_url || 'no main_img_url', img_urls || 'no img_urls')
    return NextResponse.json({ message: 'Prop info saved' });
}