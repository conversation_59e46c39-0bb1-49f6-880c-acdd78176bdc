import { fetchPropertyDetails } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { propertyDetails, newPropId, address, city, state, zipcode } = await request.json();

    const supabase = await createClient();

    if(propertyDetails){
        const {data: newPropertyDetails, error: newPropertyDetailsError} = await supabase
            .from('prop_details')
            .insert({
                prop_id: newPropId,
                ...propertyDetails?.data
            })
    }else{
        console.log('no property details data, getting')
        
        const data = await fetchPropertyDetails(address, city, state, zipcode)

        const {data: newPropertyDetails, error: newPropertyDetailsError} = await supabase
            .from('prop_details')
            .insert({
                prop_id: newPropId,
                ...data?.data
            })
    }


    return NextResponse.json({ message: 'Demographics saved' });
}