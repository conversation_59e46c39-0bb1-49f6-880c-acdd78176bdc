import { fetchDemographics } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { demographicsData, newPropId, address, city, state, zipcode } = await request.json();

    const supabase = await createClient();

    if(demographicsData){
        const { data: newDemographicsData, error: newDemographicsDataError } = await supabase.from('prop_demographics')
        .insert(demographicsData)
    }else{
        console.log('no demographics data, getting')
        const data = await fetchDemographics(address, city, state, zipcode)

        const demographicsDataInsert = {
            prop_id: newPropId,
            vintage: data?.demographics?.vintage,
            population_2020_count: data?.demographics?.population_2020_count,
            population_median_age: data?.demographics?.population_median_age,
            median_household_income: demographicsData?.demographics?.median_household_income,
            average_household_income: demographicsData?.demographics?.average_household_income,
            crime_total_risk: demographicsData?.demographics?.crime_total_risk,
        }

        const { data: newDemographicsData, error: newDemographicsDataError } = await supabase.from('prop_demographics')
        .insert(demographicsDataInsert)
    }


    return NextResponse.json({ message: 'Demographics saved' });
}