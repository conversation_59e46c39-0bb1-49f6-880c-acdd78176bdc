import { fetchUnitData } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { unitData, marketData,address, city, state, zipcode, newPropId } = await request.json();

    const supabase = await createClient();

    if(unitData){
        const { data: newUnits, error: newUnitsError } = await supabase
            .from('prop_units')
            .insert(unitData)
            
        const { data: newMarketData, error: newMarketDataError } = await supabase
            .from('prop_market_data')
            .insert(marketData)
    }else{
        console.log('no unit data, getting')
        const unitsData = await fetchUnitData(address, city, state, zipcode)

        if(unitsData && unitsData?.data?.units?.length > 0){
            const unitsDataInsert = unitsData?.data?.units?.map((unit: any) => ({
                id: unit?.id,
                prop_id: newPropId,
                unit: unit?.unit || 'N/A',
                beds: unit?.bed_count || 0,
                baths: unit?.bath_count || 0,
                hoa_fee: unit?.hoa_fee || 0,
                document_recorded_date: unit?.document_recorded_date || null,
            }))
    
            const { data: newUnits, error: newUnitsError } = await supabase
                .from('prop_units')
                .insert(unitsDataInsert)

            const marketDataInsert = unitData?.data?.market_data?.map((market: any) => ({
                prop_id: newPropId,
                unit: market.unit,
                data: market.data,
                unit_id: market.unit_id
            }))

            const { data: newMarketData, error: newMarketDataError } = await supabase
                .from('prop_market_data')
                .insert(marketDataInsert)
        }
    }


    return NextResponse.json({ message: 'Units saved' });
}