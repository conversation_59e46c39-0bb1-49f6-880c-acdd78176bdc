import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { solarData, newPropId, lat, lon } = await request.json();

    const supabase = await createClient();

    if(solarData){
        const {data: newSolarData, error: newSolarDataError} = await supabase
        .from('prop_solar_potential')
        .insert({
            prop_id: newPropId,
            data: solarData || [],
        })
    }else{
        console.log('no solar data, getting') 
        
        const response = await fetch(`/api/solar-potential?lat=${lat}&lng=${lon}`)
        const data = await response.json()
        

        const {data: newSolarData, error: newSolarDataError} = await supabase
        .from('prop_solar_potential')
        .insert({
            prop_id: newPropId,
            data: data?.parsedData || [],
        })
    }

    return NextResponse.json({ message: 'Solar saved' });
}