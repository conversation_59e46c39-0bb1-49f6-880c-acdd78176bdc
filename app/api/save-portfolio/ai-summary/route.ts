import { getAiSummary } from '@/actions/propertyActions';
import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    const { aiSummary, displayAddress, newPropId } = await request.json();

    const supabase = await createClient();

    if(aiSummary){
        const { data: newAiSummary, error: newAiSummaryError } = await supabase.from('prop')
        .update({
            ai_summary: aiSummary
        })
        .eq('id', newPropId)
    }else{
        console.log('no ai summary, getting')
        const data = await getAiSummary(displayAddress)
        const { data: newAiSummary, error: newAiSummaryError } = await supabase.from('prop')
        .update({
            ai_summary: data
        })
        .eq('id', newPropId)
    }


    return NextResponse.json({ message: 'AI summary saved' });
}