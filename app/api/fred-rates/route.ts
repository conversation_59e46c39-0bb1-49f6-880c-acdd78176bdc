import { NextRequest, NextResponse } from 'next/server';

const FRED_API_KEY = process.env.FRED_API_KEY;
const FRED_BASE_URL = "https://api.stlouisfed.org/fred";

export async function GET(req: NextRequest) {
  try {
    // 30-Year Fixed Rate Mortgage Average in the United States (MORTGAGE30US)
    const seriesId = 'MORTGAGE30US';
    
    const response = await fetch(
      `${FRED_BASE_URL}/series/observations?series_id=${seriesId}&api_key=${FRED_API_KEY}&file_type=json&limit=1&sort_order=desc`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
      }
    );
    
    if (!response.ok) {
      throw new Error(`FRED API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.observations || data.observations.length === 0) {
      throw new Error('No mortgage rate data available');
    }
    
    const latestRate = data.observations[0];
    
    return NextResponse.json({
      rate: parseFloat(latestRate.value),
      date: latestRate.date,
      series_id: seriesId,
      source: 'Federal Reserve Economic Data (FRED)'
    });

  } catch (error: any) {
    console.error('Error fetching FRED mortgage rates:', error.message);
    return NextResponse.json(
      { 
        error: 'Failed to fetch mortgage rates',
        fallbackRate: 7.5 // Default fallback rate if FRED fails
      }, 
      { status: 500 }
    );
  }
} 