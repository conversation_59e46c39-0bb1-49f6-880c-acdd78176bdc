import { NextRequest, NextResponse } from 'next/server';


export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const lat = searchParams.get('lat');
  const lon = searchParams.get('lon');

  if (!lat || !lon) {
    return NextResponse.json(
      { error: 'Missing required parameters: lat and lon' }, 
      { status: 400 }
    );
  }

  try {

    const response = await fetch(`https://external.transitapp.com/v3/public/nearby_routes?lat=${lat}&lon=${lon}&max_distance=500&should_update_realtime=true`, {
        method: 'GET',
        headers: {
            'apiKey': process.env.TRANSIT_API_KEY || '',
            'Accept': 'application/json'
        },
    })
    const data = await response.json()

    return NextResponse.json(data);

  } catch (error: any) {
    console.error('Error fetching transit data:', error?.response?.data || error.message);
    return NextResponse.json(
      { error: 'Failed to fetch transit data' }, 
      { status: 500 }
    );
  }
} 