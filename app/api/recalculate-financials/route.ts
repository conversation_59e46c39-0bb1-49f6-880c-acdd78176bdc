import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { FinancialCalculationService } from '@/services/financialCalculationEnhancedService';

export async function POST(request: NextRequest) {
    try {
        const { addressId, portfolioId, triggerField } = await request.json();
        
        if (!portfolioId) {
            return NextResponse.json(
                { error: 'Portfolio ID is required' },
                { status: 400 }
            );
        }

        const supabase = await createClient();
        
        // Check authentication
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }
        
        // Get current financial data - check if it's property or portfolio level
        let currentFinancials;
        
        if (addressId) {
            // Property level - get prop_id first
            const { data: propData } = await supabase
                .from('prop')
                .select('id')
                .eq('address_id', addressId)
                .single();
                
            if (propData) {
                const { data } = await supabase
                    .from('prop_financials')
                    .select('*')
                    .eq('prop_id', propData.id);
                currentFinancials = data;
            }
        } else {
            // Portfolio level
            const { data } = await supabase
                .from('portfolio_financials')
                .select('*')
                .eq('portfolio_id', portfolioId);
            currentFinancials = data;
        }

        // Convert to the format expected by calculation service
        const financialData: { [key: string]: { [year: number]: number } } = {};
        
        if (currentFinancials) {
            // The financial tables have individual columns for each field
            const financialFields = [
                'rental_income', 'long_term_rental', 'short_term_rental', 'other_income',
                'vacancy_loss', 'credit_loss', 'effective_gross_income', 'property_tax',
                'insurance', 'repairs', 'maintenance', 'professional_fees', 'management_fees',
                'leasing_fees', 'legal_fees', 'accounting_fees', 'engineering_fees',
                'marketing_fees', 'consulting_fees', 'utilities', 'services', 'reserves',
                'total_operating_expenses', 'net_operating_income', 'annual_debt_service',
                'dscr', 'cash_flow_before_taxes', 'cash_flow_after_taxes', 'cumulative_cash_flow',
                'cap_rate', 'gross_rent_multiplier', 'equity_multiple', 'cash_on_cash_return',
                'total_acquisition_cost', 'aggregated_noi', 'blended_cap_rate', 'portfolio_irr'
            ];
            
            currentFinancials.forEach(yearData => {
                const year = yearData.year;
                financialFields.forEach(field => {
                    if (yearData[field] !== null && yearData[field] !== undefined) {
                        if (!financialData[field]) {
                            financialData[field] = {};
                        }
                        financialData[field][year] = yearData[field] || 0;
                    }
                });
            });
        }

        // Progress tracking function (optional)
        const progressCallback = (progress: any) => {
            // Can log calculation progress if needed for debugging
        };

        // Initialize calculation service
        const calculationService = new FinancialCalculationService(progressCallback);
        
        // Perform calculations
        const recalculatedData = await calculationService.recalculateAllFinancials(
            addressId,
            portfolioId,
            financialData
        );

        // Save updated data back to database
        // Group by year for batch updates
        const yearUpdates: { [year: number]: any } = {};
        
        Object.entries(recalculatedData).forEach(([key, yearData]) => {
            Object.entries(yearData).forEach(([year, value]) => {
                const yearInt = parseInt(year);
                if (!yearUpdates[yearInt]) {
                    yearUpdates[yearInt] = {};
                }
                yearUpdates[yearInt][key] = value;
            });
        });

        // Save each year's data sequentially to avoid promise type issues
        for (const [year, updateData] of Object.entries(yearUpdates)) {
            const yearInt = parseInt(year);
            
            if (addressId) {
                // Property level update
                const { data: propData } = await supabase
                    .from('prop')
                    .select('id')
                    .eq('address_id', addressId)
                    .single();
                    
                if (propData) {
                    await supabase
                        .from('prop_financials')
                        .upsert({
                            prop_id: propData.id,
                            year: yearInt,
                            ...updateData,
                            updated_at: new Date().toISOString()
                        }, {
                            onConflict: 'prop_id,year'
                        });
                }
            } else {
                // Portfolio level update
                await supabase
                    .from('portfolio_financials')
                    .upsert({
                        portfolio_id: portfolioId,
                        year: yearInt,
                        ...updateData,
                        updated_at: new Date().toISOString()
                    }, {
                        onConflict: 'portfolio_id,year'
                    });
            }
        }

        // Calculate portfolio completion percentage
        const completionPercentage = await calculationService.calculatePortfolioCompletion(portfolioId);

        // Format the data properly for frontend consumption
        const formattedData = Object.fromEntries(
            Object.entries(recalculatedData).map(([field, yearData]) => [
                field,
                Object.fromEntries(
                    Object.entries(yearData).map(([year, value]) => [
                        year,
                        typeof value === 'number' ? Math.round(value * 100) / 100 : value // Round to 2 decimal places
                    ])
                )
            ])
        );

        return NextResponse.json({
            success: true,
            message: 'Financial calculations completed successfully',
            data: formattedData,
            portfolioCompletion: completionPercentage,
            triggerField
        });

    } catch (error) {
        console.error('Error in financial recalculation:', error);
        return NextResponse.json(
            { error: 'Failed to recalculate financials', details: error },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const portfolioId = searchParams.get('portfolioId');
        
        if (!portfolioId) {
            return NextResponse.json(
                { error: 'Portfolio ID is required' },
                { status: 400 }
            );
        }

        const supabase = await createClient();
        
        // Check authentication
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const calculationService = new FinancialCalculationService();
        const completionPercentage = await calculationService.calculatePortfolioCompletion(portfolioId);

        return NextResponse.json({
            success: true,
            portfolioCompletion: completionPercentage
        });

    } catch (error) {
        console.error('Error getting portfolio completion:', error);
        return NextResponse.json(
            { error: 'Failed to get portfolio completion' },
            { status: 500 }
        );
    }
} 