import { NextRequest, NextResponse } from 'next/server';
import { startCronJobs } from '@/lib/cron-scheduler';

export async function GET(request: NextRequest) {
    try {
        // Start the cron jobs
        startCronJobs();
        
        return NextResponse.json({
            success: true,
            message: 'Cron jobs initialized successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error initializing cron jobs:', error);
        
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    return GET(request);
} 