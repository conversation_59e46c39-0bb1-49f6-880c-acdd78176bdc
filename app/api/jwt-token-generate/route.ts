import { NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const SUPABASE_JWT_SECRET = process.env.SUPABASE_JWT_SECRET!

export async function POST(req: Request) {
  try {
    const data = await req.json()


    const payload = {
      ...data,
    }

    const token = jwt.sign(payload, SUPABASE_JWT_SECRET, {
      algorithm: 'HS256',
      expiresIn: '180d',
    })

    return NextResponse.json({ token })
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}