import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
    try {
        const { addressId, unitId } = await request.json();

        if (!addressId || !unitId) {
            return NextResponse.json(
                { error: 'Missing required fields: addressId and unitId are required' },
                { status: 400 }
            );
        }

        const supabase = await createClient();

        // Get property data
        const { data: propData, error: propError } = await supabase
            .from('prop')
            .select('*')
            .eq('address_id', addressId)
            .single();

        if (propError) {
            return NextResponse.json(
                { error: `Failed to get property data: ${propError.message}` },
                { status: 500 }
            );
        }

        // Delete all market data records for this unit
        const { error: deleteError } = await supabase
            .from('prop_market_data')
            .delete()
            .eq('prop_id', propData.id)
            .eq('unit_id', unitId);

        if (deleteError) {
            return NextResponse.json(
                { error: `Failed to clear market data: ${deleteError.message}` },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            message: 'All market data cleared successfully',
            cleared: true
        });

    } catch (error) {
        console.error('Clear all market data error:', error);
        return NextResponse.json(
            { error: 'Failed to clear all market data' },
            { status: 500 }
        );
    }
} 