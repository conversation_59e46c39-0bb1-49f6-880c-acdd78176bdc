import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';
import { canonicalizeListingType } from '@/utils/formatters';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

// Remove hardcoded fallback API key - use environment variable only
const SERPER_API_KEY = process.env.SERPER_API_KEY;

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        
        // Handle both old format (saving existing data) and new format (search + save)
        if (body.addressId && body.unitId && body.marketData) {
            // Old format - has marketData field, just save the provided market data
            return handleLegacyMarketDataSave(body);
        } else {
            // New format - search for market data and save to database
            const { address, unit, addressId, unitId } = body;
            
            console.log('🔍 Market data search request:', { address, unit, addressId, unitId });

            if (!address) {
                return NextResponse.json(
                    { error: 'Address is required' },
                    { status: 400 }
                );
            }

            if (!SERPER_API_KEY) {
                console.error('❌ Serper API key not configured');
                return NextResponse.json(
                    { error: 'Serper API key not configured. Please set SERPER_API_KEY environment variable.' },
                    { status: 500 }
                );
            }
            
            console.log('✅ Starting comprehensive market search...');

            // Perform comprehensive market data search
            const marketData = await searchComprehensiveMarketData(address, unit);
            
            console.log(`📊 Search completed. Found ${marketData.data.length} data points`);
            console.log('Search summary:', marketData.summary);

            let saved = false;
            if (marketData.data.length > 0 && addressId && unitId) {
                // Save the found data to the database
                try {
                    console.log('💾 Attempting to save market data to database...');
                    await saveMarketDataToDatabase(addressId, unitId, unit || '', marketData.data);
                    saved = true;
                    console.log('✅ Market data saved successfully');
                } catch (saveError) {
                    console.error('❌ Failed to save market data to database:', saveError);
                }
            } else if (marketData.data.length === 0) {
                console.log('ℹ️ No market data found to save');
            } else {
                console.log('⚠️ Market data found but missing addressId/unitId for saving');
            }

            return NextResponse.json({
                success: true,
                data: marketData.data,
                sources: marketData.sources,
                totalResults: marketData.data.length,
                searchTime: new Date().toISOString(),
                summary: marketData.summary,
                disclaimer: "AI-extracted data from real search results. Please verify independently.",
                saved: saved
            });
        }

    } catch (error) {
        console.error('Add market data error:', error);
        return NextResponse.json(
            { error: 'Failed to process market data request' },
            { status: 500 }
        );
    }
}

async function handleLegacyMarketDataSave(body: any) {
    // This handles the old frontend calls that save already-found market data
    const { addressId, unitId, marketData, source, searchQuery } = body;
    
    console.log('💾 Saving accepted market data to database...', { 
        addressId, 
        unitId, 
        marketDataCount: marketData?.length,
        hasUnitId: !!unitId,
        unitIdType: typeof unitId
    });
    
    if (!addressId || !unitId || !marketData || marketData.length === 0) {
        console.error('❌ Missing required data:', { 
            hasAddressId: !!addressId, 
            hasUnitId: !!unitId, 
            hasMarketData: !!marketData,
            marketDataLength: marketData?.length 
        });
        return NextResponse.json({
            success: false,
            message: 'Missing required data for saving market data',
            data: [],
            requiresVerification: true
        });
    }

    try {
        // Get unit information to get the unit number
        const supabase = await createClient();
        
        console.log('🔍 Querying unit data for unitId:', unitId);
        
        // First, let's check if the unit exists at all
        const { data: unitCheckData, error: unitCheckError } = await supabase
            .from('prop_units')
            .select('id, unit')
            .eq('id', unitId);

        if (unitCheckError) {
            console.error('❌ Unit check query failed:', unitCheckError);
            throw new Error(`Failed to check unit existence: ${unitCheckError.message}`);
        }

        console.log('📊 Unit check results:', { 
            foundUnits: unitCheckData?.length || 0,
            units: unitCheckData 
        });

        if (!unitCheckData || unitCheckData.length === 0) {
            throw new Error(`No unit found with ID: ${unitId}`);
        }

        if (unitCheckData.length > 1) {
            console.warn('⚠️ Multiple units found with same ID:', unitCheckData);
            // Use the first one but log the issue
        }

        const unitData = unitCheckData[0];
        const unitNumber = unitData?.unit || '';
        console.log('✅ Found unit number:', unitNumber);

        // Save the market data to database
        await saveMarketDataToDatabase(addressId, unitId, unitNumber, marketData);
        
        console.log('✅ Market data saved successfully');
        
        return NextResponse.json({
            success: true,
            message: 'Market data saved successfully',
            data: marketData,
            saved: true,
            requiresVerification: false
        });
    } catch (error) {
        console.error('❌ Failed to save market data:', error);
        return NextResponse.json({
            success: false,
            message: `Failed to save market data: ${error instanceof Error ? error.message : 'Unknown error'}`,
            data: [],
            requiresVerification: true
        }, { status: 500 });
    }
}

async function searchComprehensiveMarketData(address: string, unit?: string) {
    try {
        // Extract street address and unit for cleaner search
        const streetAddress = address.split(',')[0].trim();
        const unitSuffix = unit ? ` unit ${unit}` : '';
        
        // Create comprehensive search queries
        const rentQuery = `"${streetAddress}"${unitSuffix} rent`;
        const saleQuery = `"${streetAddress}"${unitSuffix} sale`;
        
        console.log('Comprehensive market search for:', streetAddress);
        console.log('Rent query:', rentQuery);
        console.log('Sale query:', saleQuery);

        // Perform parallel searches
        const [rentResults, saleResults] = await Promise.all([
            searchWithSerper(rentQuery),
            searchWithSerper(saleQuery)
        ]);

        // Combine all organic search results
        const allResults = [
            ...(rentResults.organic || []),
            ...(saleResults.organic || [])
        ];

        if (allResults.length === 0) {
            return {
                data: [],
                sources: [],
                summary: 'No market data found for this address'
            };
        }

        // Extract detailed market data using AI
        const extractedData = await extractDetailedMarketData(allResults, streetAddress, unit);
        
        return {
            data: extractedData.marketData,
            sources: extractedData.sources,
            summary: extractedData.summary
        };

    } catch (error) {
        console.error('Comprehensive market search error:', error);
        return {
            data: [],
            sources: [],
            summary: 'Failed to retrieve market data'
        };
    }
}

async function searchWithSerper(query: string) {
    try {
        console.log(`🔍 Searching Serper for: "${query}"`);
        
        // Validate API key before making request
        if (!SERPER_API_KEY) {
            throw new Error('Serper API key not configured');
        }
        
        const response = await fetch('https://google.serper.dev/search', {
            method: 'POST',
            headers: {
                'X-API-KEY': SERPER_API_KEY,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                q: query,
                gl: 'us',
                hl: 'en',
                num: 10,
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Serper API error: ${response.status} ${response.statusText}`);
            console.error(`❌ Error response body:`, errorText);
            
            // Handle specific error codes
            if (response.status === 400) {
                throw new Error(`Serper API rejected request: ${errorText}`);
            } else if (response.status === 401) {
                throw new Error('Serper API key is invalid or expired');
            } else if (response.status === 429) {
                throw new Error('Serper API rate limit exceeded');
            } else {
                throw new Error(`Serper API error: ${response.status} - ${errorText}`);
            }
        }

        const data = await response.json();
        console.log(`✅ Serper returned ${data.organic?.length || 0} organic results`);
        
        if (data.organic && data.organic.length > 0) {
            console.log('First result title:', data.organic[0].title);
        }

        return data;
    } catch (error) {
        console.error('❌ Serper search error:', error);
        return { organic: [] };
    }
}

async function extractDetailedMarketData(searchResults: any[], address: string, unit?: string) {
    try {
        // Pass serper results directly to OpenAI without scraping content
        const searchContext = searchResults.map(result => ({
            title: result.title,
            link: result.link,
            snippet: result.snippet,
            date: result.date,
            position: result.position
        }));

        const prompt = `You are a professional real estate data analyst. Analyze the following search results for "${address}" ${unit ? `unit ${unit}` : ''} and extract ALL available property data including comprehensive details, images, and source tracking.

SEARCH RESULTS:
${JSON.stringify(searchContext, null, 2)}

Extract COMPREHENSIVE property data from these search results. Look for:

1. **BASIC PROPERTY DATA**: 
   - date: Date of listing/transaction (YYYY-MM-DD format)
   - price: Dollar amount (integer, no commas or symbols)
   - bedrooms: Number of bedrooms (integer)
   - bathrooms: Number of bathrooms (decimal allowed, e.g., 2.5)
   - squareFeet: Total square footage (integer)
   - unitNumber: Specific unit designation (e.g., "Apt 2B", "Unit 809", etc.)

2. **LISTING DETAILS** (CRITICAL - ANALYZE CAREFULLY):
   - lastEvent: Type of event based on snippet content:
     * Use "sold" if snippet mentions "sold", "closed", "sale completed"
     * Use "for_sale" if snippet mentions "for sale", "listing", "buy", "purchase"
     * Use "for_rent" if snippet mentions "for rent", "rental", "lease available", "rent"
     * Use "rented" if snippet mentions "rented", "leased", "occupied", "tenant"
     * Use "pending" if snippet mentions "pending", "under contract", "contingent"
     * ANALYZE SNIPPET TEXT TO DETERMINE ACTUAL STATUS
   - images: Array of image URLs found in the listing
   - sourceUrl: The exact URL where this data was found (use the 'link' field)

3. **TRANSACTION HISTORY**:
   - history: Array of historical transactions with date, type, price, and isLatest flag

4. **SOURCE TRACKING** (CRITICAL - REQUIRED):
   - sources: Array of all source URLs/domains where this specific data point was found
   - sourceDomains: Array of domain names (e.g., ["zillow.com", "realtor.com"])
   - sourceTypes: Array describing the type of each source (e.g., ["listing_site", "rental_site", "market_analysis"])

**CRITICAL REQUIREMENTS**:
- Only extract data that is explicitly mentioned in the search results
- Parse information from titles and snippets only (no web scraping)
- Extract specific unit numbers when mentioned
- **MUST include detailed source tracking for each data point**
- **For each market data item, specify ALL sources where that information was found**
- **ALWAYS include the sourceUrl field using the 'link' from search results**
- Be comprehensive - extract as much detail as possible from available text

Return ONLY valid JSON in this exact format:
{
  "marketData": [
    {
      "date": "YYYY-MM-DD",
      "price": number,
      "images": ["url1", "url2", "..."],
      "history": [
        {
          "date": "YYYY-MM-DD",
          "type": "sold|for_rent|for_sale|rented|pending",
          "price": number,
          "isLatest": boolean
        }
      ],
      "bedrooms": number,
      "bathrooms": number,
      "lastEvent": "sold|for_rent|for_sale|rented|pending",
      "squareFeet": number,
      "unitNumber": "string",
      "sourceUrl": "complete URL where data was found",
      "sources": ["url1", "url2", "url3"],
      "sourceDomains": ["domain1.com", "domain2.com"],
      "sourceTypes": ["listing_site", "rental_site", "market_analysis"],
      "confidence": number,
      "description": "string"
    }
  ],
  "sources": ["unique domain names"],
  "summary": "Summary of comprehensive data found"
}`;

        const response = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert real estate data extraction specialist. Extract comprehensive property details including images, unit details, and transaction history from search result snippets and titles only. Return only valid JSON with complete property information and source URLs.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 3000,
            temperature: 0.1
        });

        const responseText = response.choices[0]?.message?.content;
        if (!responseText) {
            throw new Error('No response from OpenAI');
        }

        let analysis;
        try {
            // Try to parse as direct JSON first
            analysis = JSON.parse(responseText);
        } catch (parseError) {
            try {
                // If direct parsing fails, try to extract JSON from markdown code blocks
                const jsonMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
                if (jsonMatch && jsonMatch[1]) {
                    analysis = JSON.parse(jsonMatch[1]);
                } else {
                    // Try to find JSON-like content without markdown
                    const jsonStart = responseText.indexOf('{');
                    const jsonEnd = responseText.lastIndexOf('}');
                    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                        const jsonContent = responseText.slice(jsonStart, jsonEnd + 1);
                        analysis = JSON.parse(jsonContent);
                    } else {
                        throw new Error('No valid JSON found in response');
                    }
                }
            } catch (secondParseError) {
                console.error('Failed to parse OpenAI response:', responseText);
                console.error('Parse errors:', parseError, secondParseError);
                throw new Error('Invalid JSON response from OpenAI');
            }
        }
        
        // Validate and format the extracted data with comprehensive details and source tracking
        const validatedMarketData = (analysis.marketData || [])
            .filter((item: any) => item.price && item.price > 0)
            .map((item: any) => ({
                date: item.date || new Date().toISOString().split('T')[0],
                price: Math.round(Number(item.price)),
                images: Array.isArray(item.images) ? item.images.filter((url: string) => 
                    typeof url === 'string' && url.startsWith('http')
                ) : [],
                history: Array.isArray(item.history) ? item.history.map((h: any) => ({
                    date: h.date || item.date,
                    type: h.type || item.lastEvent || 'unknown',
                    price: Math.round(Number(h.price || item.price)),
                    isLatest: h.isLatest === true
                })) : [{
                    date: item.date || new Date().toISOString().split('T')[0],
                    type: item.lastEvent || 'unknown',
                    price: Math.round(Number(item.price)),
                    isLatest: true
                }],
                bedrooms: Number(item.bedrooms) || 0,
                bathrooms: Number(item.bathrooms) || 0,
                lastEvent: item.lastEvent || item.listingType || item.type || 'market_analysis',
                squareFeet: Number(item.squareFeet) || 0,
                unitNumber: item.unitNumber || unit || '',
                sourceUrl: item.sourceUrl || '',
                // Enhanced source tracking
                sources: Array.isArray(item.sources) ? item.sources : [item.sourceUrl || 'unknown'],
                sourceDomains: Array.isArray(item.sourceDomains) ? item.sourceDomains : 
                    [item.sourceUrl ? new URL(item.sourceUrl).hostname : 'unknown'],
                sourceTypes: Array.isArray(item.sourceTypes) ? item.sourceTypes : ['search_result'],
                confidence: Math.min(100, Math.max(0, item.confidence || 50)),
                description: item.description || 'Property data extracted from search results'
            }))
            .sort((a: any, b: any) => {
                // Sort by confidence, then by date
                if (b.confidence !== a.confidence) {
                    return b.confidence - a.confidence;
                }
                return new Date(b.date).getTime() - new Date(a.date).getTime();
            });

        return {
            marketData: validatedMarketData,
            sources: analysis.sources || [],
            summary: analysis.summary || `Found ${validatedMarketData.length} comprehensive property data points`,
            dataTypes: { 
                rentals: validatedMarketData.filter((item: any) => item.lastEvent && item.lastEvent.includes('rent')).length,
                sales: validatedMarketData.filter((item: any) => (item.lastEvent || '') === 'sold').length,
                analysis: validatedMarketData.length 
            }
        };

    } catch (error) {
        console.error('AI market data extraction error:', error);
        return {
            marketData: [],
            sources: [],
            summary: 'Failed to extract comprehensive market data from search results',
            dataTypes: { rentals: 0, sales: 0, analysis: 0 }
        };
    }
}

async function saveMarketDataToDatabase(addressId: string, unitId: string, unitNumber: string, marketData: any[]) {
    try {
        const supabase = await createClient();
        
        // Get property data
        const { data: propData, error: propError } = await supabase
            .from('prop')
            .select('*')
            .eq('address_id', addressId)
            .single();

        if (propError) {
            throw new Error(`Failed to get property data: ${propError.message}`);
        }

        // Transform the AI-extracted comprehensive market data into the expected format
        const transformedData = marketData.map(item => ({
            list_date: item.date,
            list_price: item.price,
            listing_type: canonicalizeListingType(item.lastEvent || item.listingType || item.type || 'market_analysis'),
            description: {
                text: item.description,
                beds: item.bedrooms || 0,
                baths_full: item.bathrooms || 0,
                sqft: item.squareFeet || 0,
                type: item.lastEvent
            },
            source: {
                id: item.sourceUrl || 'ai_search',
                type: 'ai_extracted',
                listing_id: `ai_extracted_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                url: item.sourceUrl,
                sources: item.sources || [item.sourceUrl || 'unknown'],
                sourceDomains: item.sourceDomains || ['unknown'],
                sourceTypes: item.sourceTypes || ['search_result'],
                disclaimer: {
                    text: "AI-extracted comprehensive data from real search results. Please verify independently."
                }
            },
            // Comprehensive property details
            images: item.images || [],
            history: item.history || [],
            bedrooms: item.bedrooms || 0,
            bathrooms: item.bathrooms || 0,
            squareFeet: item.squareFeet || 0,
            unitNumber: item.unitNumber || unitNumber,
            lastEvent: item.lastEvent,
            sourceUrl: item.sourceUrl,
            confidence: item.confidence,
            last_sold_date: (item.lastEvent || '').toLowerCase() === 'sold' ? item.date : null,
            last_sold_price: (item.lastEvent || '').toLowerCase() === 'sold' ? item.price : 0,
            price_per_sqft: (item.squareFeet && item.squareFeet > 0) ? Math.round(item.price / item.squareFeet) : 0,
            property_id: `ai_${addressId}_${unitId}`,
            flags: {
                is_pending: (item.lastEvent || '').toLowerCase() === 'pending',
                is_for_rent: (item.lastEvent || '').toLowerCase().includes('rent'),
                is_contingent: null
            },
            location: {
                address: {
                    line: addressId.toString(),
                    unit: item.unitNumber || unitNumber
                }
            }
        }));

        // Insert the market data
        const { data: insertedData, error: insertError } = await supabase
            .from('prop_market_data')
            .insert({
                prop_id: propData.id,
                unit_id: unitId,
                unit: unitNumber,
                data: transformedData
            })
            .select();

        if (insertError) {
            throw new Error(`Failed to save market data: ${insertError.message}`);
        }

        console.log(`Successfully saved ${transformedData.length} market data points for unit ${unitNumber}`);
        return insertedData;

    } catch (error) {
        console.error('Database save error:', error);
        throw error;
    }
}
