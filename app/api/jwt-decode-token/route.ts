import { NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const SUPABASE_JWT_SECRET = process.env.SUPABASE_JWT_SECRET!

export async function POST(req: Request) {
  try {
    const { token, tokenType } = await req.json()

    if (!token) {
      return NextResponse.json({ error: 'JWT token is required' }, { status: 400 })
    }

    const verifiedPayload = jwt.verify(token, SUPABASE_JWT_SECRET)

    return NextResponse.json({ payload: verifiedPayload, tokenType: tokenType })
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 401 })
  }
}