import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

const SERPER_API_KEY = process.env.SERPER_API_KEY;

export async function POST(request: NextRequest) {
    try {
        const { address, unit, currentInfo, unitId, addressId } = await request.json();

        if (!address) {
            return NextResponse.json(
                { error: 'Address is required' },
                { status: 400 }
            );
        }

        if (!SERPER_API_KEY) {
            console.error('❌ Serper API key not configured');
            return NextResponse.json(
                { error: 'Serper API key not configured' },
                { status: 500 }
            );
        }

        console.log('🔍 Unit info search request:', { address, unit });

        // Perform real agentic search for unit information
        const searchResults = await performRealUnitInfoSearch(address, unit, currentInfo);

        // Check if beds or baths data is missing and try image analysis if we have unit images
        const finalData = { ...searchResults.data };
        let estimatedFromImages = false;
        let imageAnalysisNotes = '';

        if (unitId && addressId && searchResults.data && 
            (searchResults.data.beds === 0 || searchResults.data.baths === 0 || 
             searchResults.data.confidence_beds < 50 || searchResults.data.confidence_baths < 50)) {
            
            console.log('🖼️ Attempting image analysis for missing room data...');
            
            try {
                const imageEstimates = await estimateRoomsFromImages(unitId, addressId);
                if (imageEstimates.success && 'beds' in imageEstimates) {
                    // Only use image estimates if confidence is reasonable and search data is weak
                    if (imageEstimates.beds > 0 && (searchResults.data.beds === 0 || searchResults.data.confidence_beds < 50)) {
                        finalData.beds = imageEstimates.beds;
                        finalData.confidence_beds = imageEstimates.confidence_beds;
                        estimatedFromImages = true;
                    }
                    
                    if (imageEstimates.baths > 0 && (searchResults.data.baths === 0 || searchResults.data.confidence_baths < 50)) {
                        finalData.baths = imageEstimates.baths;
                        finalData.confidence_baths = imageEstimates.confidence_baths;
                        estimatedFromImages = true;
                    }
                    
                    if (estimatedFromImages) {
                        imageAnalysisNotes = imageEstimates.analysis_notes || 'Room count estimated from property images';
                        console.log('✅ Enhanced data with image analysis');
                    }
                }
            } catch (error) {
                console.error('Image analysis failed:', error);
                // Continue without image analysis if it fails
            }
        }

        return NextResponse.json({
            success: true,
            data: finalData,
            sources: searchResults.sources,
            searchQuery: searchResults.primaryQuery,
            confidence: searchResults.confidence,
            searchTime: new Date().toISOString(),
            estimatedFromImages: estimatedFromImages,
            imageAnalysisNotes: imageAnalysisNotes,
            disclaimer: estimatedFromImages 
                ? "Unit data found via internet search. Room counts estimated from property images where search data was insufficient. Please verify independently."
                : "AI-extracted data from real search results. Please verify independently."
        });

    } catch (error) {
        console.error('Agentic unit info search error:', error);
        return NextResponse.json(
            { error: 'Failed to perform unit info search' },
            { status: 500 }
        );
    }
}

async function performRealUnitInfoSearch(address: string, unit: string, currentInfo: any) {
    try {
        // Extract street address for cleaner search
        const streetAddress = address.split(',')[0].trim();
        const unitSuffix = unit ? ` unit ${unit}` : '';
        
        // Create comprehensive search queries for unit specifications
        const searchQueries = [
            `"${streetAddress}"${unitSuffix} bedrooms bathrooms square feet specs`,
            `"${streetAddress}"${unitSuffix} floor plan apartment details`,
            `"${streetAddress}"${unitSuffix} rent price bedroom bath sqft`,
            `site:zillow.com "${streetAddress}"${unitSuffix}`,
            `site:apartments.com "${streetAddress}"${unitSuffix}`,
            `site:rent.com "${streetAddress}"${unitSuffix}`
        ];

        console.log('🔍 Searching for unit info with queries:', searchQueries);

        // Perform parallel searches for the most important queries
        const [generalResults, floorPlanResults, rentalResults] = await Promise.all([
            searchWithSerper(searchQueries[0]),
            searchWithSerper(searchQueries[1]), 
            searchWithSerper(searchQueries[2])
        ]);

        // Combine all organic search results
        const allResults = [
            ...(generalResults.organic || []),
            ...(floorPlanResults.organic || []),
            ...(rentalResults.organic || [])
        ];

        console.log(`📊 Found ${allResults.length} total search results`);

        if (allResults.length === 0) {
            return {
                data: null,
                sources: [],
                primaryQuery: searchQueries[0],
                confidence: 0
            };
        }

        // Extract unit specifications using AI
        const extractedData = await extractUnitSpecifications(allResults, streetAddress, unit, currentInfo);
        
        return {
            data: extractedData.unitData,
            sources: extractedData.sources,
            primaryQuery: searchQueries[0],
            confidence: extractedData.confidence
        };

    } catch (error) {
        console.error('Unit info search execution error:', error);
        return {
            data: null,
            sources: [],
            primaryQuery: '',
            confidence: 0
        };
    }
}

async function searchWithSerper(query: string) {
    try {
        console.log(`🔍 Searching Serper for: "${query}"`);
        
        // Validate API key before making request
        if (!SERPER_API_KEY) {
            throw new Error('Serper API key not configured');
        }
        
        const response = await fetch('https://google.serper.dev/search', {
            method: 'POST',
            headers: {
                'X-API-KEY': SERPER_API_KEY,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                q: query,
                gl: 'us',
                hl: 'en',
                num: 10,
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Serper API error: ${response.status} ${response.statusText}`);
            console.error(`❌ Error response body:`, errorText);
            
            // Handle specific error codes
            if (response.status === 400) {
                throw new Error(`Serper API rejected request: ${errorText}`);
            } else if (response.status === 401) {
                throw new Error('Serper API key is invalid or expired');
            } else if (response.status === 429) {
                throw new Error('Serper API rate limit exceeded');
            } else {
                throw new Error(`Serper API error: ${response.status} - ${errorText}`);
            }
        }

        const data = await response.json();
        console.log(`✅ Serper returned ${data.organic?.length || 0} organic results`);
        
        return data;
    } catch (error) {
        console.error('❌ Serper search error:', error);
        return { organic: [] };
    }
}

async function extractUnitSpecifications(searchResults: any[], address: string, unit?: string, currentInfo?: any) {
    try {
        const searchContext = searchResults.map(result => ({
            title: result.title,
            link: result.link,
            snippet: result.snippet,
            date: result.date,
            position: result.position
        }));

        const prompt = `You are a professional real estate data analyst. Analyze the following search results for "${address}" ${unit ? `unit ${unit}` : ''} and extract all available unit specifications including bedroom count, bathroom count, square footage, rent, sale price, and HOA fees.

CURRENT INFO (if available):
${currentInfo ? JSON.stringify(currentInfo, null, 2) : 'No current information provided'}

SEARCH RESULTS:
${JSON.stringify(searchContext, null, 2)}

Extract unit specifications from these search results. Look for:

1. **UNIT SPECS**: Number of bedrooms, bathrooms, square footage
2. **PRICING**: Monthly rent, sale price, HOA fees, utilities
3. **AMENITIES**: Features, appliances, parking, storage
4. **BUILDING INFO**: Year built, floors, building amenities

For each specification found, determine:
- **beds**: Number of bedrooms (integer, 0 for studio)
- **baths**: Number of bathrooms (number, can be decimal like 1.5)
- **sqft**: Square footage (integer)
- **rent**: Monthly rent price (integer, 0 if not found)
- **price**: Sale/purchase price (integer, 0 if not found)
- **hoa_fee**: Monthly HOA fee (integer, 0 if not found)
- **confidence**: Your confidence in each data point (0-100)

**IMPORTANT**: Only extract specifications that are explicitly mentioned in the search results. If data is not available, estimate it based on the information provided and based on your internal knowledge. Provide a confidence score for each data point.

Return valid JSON:
{
  "unitData": {
    "beds": number,
    "baths": number,
    "sqft": number,
    "rent": number,
    "price": number,
    "hoa_fee": number,
    "confidence_beds": number,
    "confidence_baths": number,
    "confidence_sqft": number,
    "confidence_rent": number,
    "confidence_price": number,
    "confidence_hoa": number,
    "found_sources": number,
    "analysis_notes": "string"
  },
  "sources": ["unique domain names"],
  "confidence": number
}`;

        console.log('🤖 Sending to OpenAI for unit specification analysis...');

        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert real estate analyst. Extract only factual unit specifications from search results. Be conservative - only include data that is clearly stated in the source material.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 2000,
            temperature: 0.1
        });

        const responseText = response.choices[0]?.message?.content;
        if (!responseText) {
            throw new Error('No response from OpenAI');
        }

        console.log('✅ Received OpenAI analysis');

        // Clean the JSON response (remove markdown formatting if present)
        const cleanedResponse = responseText.replace(/```json\n?|\n?```/g, '').trim();
        const analysis = JSON.parse(cleanedResponse);
        
        // Validate and format the extracted data
        const unitData = analysis.unitData || {};
        const validatedUnitData = {
            beds: Math.max(0, parseInt(unitData.beds) || 0),
            baths: Math.max(0, parseFloat(unitData.baths) || 0),
            sqft: Math.max(0, parseInt(unitData.sqft) || 0),
            rent: Math.max(0, parseInt(unitData.rent) || 0),
            price: Math.max(0, parseInt(unitData.price) || 0),
            hoa_fee: Math.max(0, parseInt(unitData.hoa_fee) || 0),
            confidence_beds: Math.min(100, Math.max(0, unitData.confidence_beds || 0)),
            confidence_baths: Math.min(100, Math.max(0, unitData.confidence_baths || 0)),
            confidence_sqft: Math.min(100, Math.max(0, unitData.confidence_sqft || 0)),
            confidence_rent: Math.min(100, Math.max(0, unitData.confidence_rent || 0)),
            confidence_price: Math.min(100, Math.max(0, unitData.confidence_price || 0)),
            confidence_hoa: Math.min(100, Math.max(0, unitData.confidence_hoa || 0)),
            found_sources: parseInt(unitData.found_sources) || 0,
            analysis_notes: unitData.analysis_notes || 'Unit specifications extracted from search results'
        };

        // Calculate overall confidence
        const confidenceScores = [
            validatedUnitData.confidence_beds,
            validatedUnitData.confidence_baths,
            validatedUnitData.confidence_sqft,
            validatedUnitData.confidence_rent,
            validatedUnitData.confidence_price,
            validatedUnitData.confidence_hoa
        ].filter(score => score > 0); // Only count scores where we found data

        const overallConfidence = confidenceScores.length > 0 
            ? Math.round(confidenceScores.reduce((a, b) => a + b, 0) / confidenceScores.length)
            : 0;

        console.log(`📊 Unit analysis complete. Confidence: ${overallConfidence}%`);

        return {
            unitData: validatedUnitData,
            sources: analysis.sources || [],
            confidence: overallConfidence
        };

    } catch (error) {
        console.error('❌ AI unit specification extraction error:', error);
        return {
            unitData: null,
            sources: [],
            confidence: 0
        };
    }
}

// Function to estimate room counts from property images
async function estimateRoomsFromImages(unitId: string, addressId: string) {
    try {
        console.log('🖼️ Starting image analysis for unit:', unitId);
        
        // Get images from the database
        const supabase = await createClient();
        const { data: unitData, error } = await supabase
            .from('building_units')
            .select('img_urls')
            .eq('id', unitId)
            .eq('address_id', addressId)
            .single();

        if (error || !unitData || !unitData.img_urls || unitData.img_urls.length === 0) {
            console.log('❌ No images found for unit');
            return { success: false, reason: 'No images available' };
        }

        const imageUrls = unitData.img_urls.slice(0, 5); // Analyze up to 5 images
        console.log(`🖼️ Analyzing ${imageUrls.length} images for room estimation`);

        // Use OpenAI Vision API to analyze images for room counts
        const analysisPrompt = `Analyze these property images and estimate the number of bedrooms and bathrooms. 

Look for:
- BEDROOMS: Rooms with beds, bedroom furniture, closets, or clearly designated sleeping areas
- BATHROOMS: Toilets, sinks, showers, bathtubs, or clear bathroom fixtures
- STUDIOS: Single room living spaces with sleeping area

Be conservative in your estimates. Only count rooms you can clearly identify. If an image shows a hallway or unclear space, don't count it.

Return a JSON response with:
{
  "beds": number (0 for studio, count actual bedrooms),
  "baths": number (can be decimal like 1.5),
  "confidence_beds": number (0-100),
  "confidence_baths": number (0-100),
  "analysis_notes": "Brief explanation of what you observed",
  "room_details": "Description of identified rooms"
}`;

        // Prepare image analysis request
        const imageMessages = imageUrls.map((url: string) => ({
            type: "image_url",
            image_url: { url: url }
        }));

        const response = await openai.chat.completions.create({
            model: "gpt-4-vision-preview",
            messages: [
                {
                    role: "user",
                    content: [
                        { type: "text", text: analysisPrompt },
                        ...imageMessages
                    ]
                }
            ],
            max_tokens: 1000,
            temperature: 0.1
        });

        const analysisText = response.choices[0]?.message?.content;
        if (!analysisText) {
            return { success: false, reason: 'No analysis response' };
        }

        // Parse the JSON response
        const cleanedResponse = analysisText.replace(/```json\n?|\n?```/g, '').trim();
        const analysis = JSON.parse(cleanedResponse);

        // Validate and format the results
        const result = {
            success: true,
            beds: Math.max(0, parseInt(analysis.beds) || 0),
            baths: Math.max(0, parseFloat(analysis.baths) || 0),
            confidence_beds: Math.min(100, Math.max(0, analysis.confidence_beds || 0)),
            confidence_baths: Math.min(100, Math.max(0, analysis.confidence_baths || 0)),
            analysis_notes: analysis.analysis_notes || 'Room count estimated from property images',
            room_details: analysis.room_details || ''
        };

        console.log('✅ Image analysis complete:', result);
        return result;

    } catch (error) {
        console.error('❌ Image analysis error:', error);
        return { success: false, reason: error instanceof Error ? error.message : 'Unknown error' };
    }
} 