import { NextRequest, NextResponse } from 'next/server';

const FRED_API_KEY = process.env.FRED_API_KEY;
const FRED_BASE_URL = "https://api.stlouisfed.org/fred";

export async function GET(req: NextRequest) {
  try {
    if (!FRED_API_KEY) {
      throw new Error('FRED API key not configured');
    }

    // Consumer Price Index for All Urban Consumers (CPIAUCSL)
    const seriesId = 'CPIAUCSL';
    
    // Get the last 13 months of data to calculate YoY inflation
    const response = await fetch(
      `${FRED_BASE_URL}/series/observations?series_id=${seriesId}&api_key=${FRED_API_KEY}&file_type=json&limit=13&sort_order=desc`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
      }
    );
    
    if (!response.ok) {
      throw new Error(`FRED API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.observations || data.observations.length < 13) {
      throw new Error('Insufficient inflation data available');
    }
    
    // Calculate year-over-year inflation rate
    const latestCPI = parseFloat(data.observations[0].value);
    const yearAgoCPI = parseFloat(data.observations[12].value);
    const inflationRate = ((latestCPI - yearAgoCPI) / yearAgoCPI) * 100;
    
    return NextResponse.json({
      rate: parseFloat(inflationRate.toFixed(2)),
      date: data.observations[0].date,
      series_id: seriesId,
      source: 'Federal Reserve Economic Data (FRED)',
      latest_cpi: latestCPI,
      year_ago_cpi: yearAgoCPI
    });

  } catch (error: any) {
    console.error('Error fetching FRED inflation rates:', error.message);
    return NextResponse.json(
      { 
        error: 'Failed to fetch inflation rates',
        fallbackRate: 3.0 // Default fallback inflation rate if FRED fails
      }, 
      { status: 500 }
    );
  }
} 