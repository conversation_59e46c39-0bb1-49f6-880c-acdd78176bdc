import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { getToken, sleep, constants } from './utils';

// Image search result interface
interface ImageSearchResult {
  width?: number;
  height?: number;
  thumbnail?: string;
  url: string;
  title: string;
  image: string;
}

// Serper API image result interface
interface SerperImageResult {
  title: string;
  imageUrl: string;
  imageWidth: number;
  imageHeight: number;
  thumbnailUrl: string;
  thumbnailWidth: number;
  thumbnailHeight: number;
  source: string;
  domain: string;
  link: string;
  googleUrl: string;
  position: number;
}

// Serper API response interface
interface SerperResponse {
  searchParameters: {
    q: string;
    type: string;
    autocorrect: boolean;
    location: string;
    engine: string;
    num: number;
    gl: string;
  };
  images: SerperImageResult[];
  credits: number;
}

// Search options interface
interface ImageSearchOptions {
  query: string;
  moderate?: boolean;
  retries?: number;
  iterations?: number;
}

// Simple in-memory cache for search results
const searchResultsCache: Record<string, { timestamp: number, results: ImageSearchResult[], allImageUrls: string[] }> = {};
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Custom implementation of DuckDuckGo image search
 */
async function imageSearch({ 
  query, 
  moderate = false, 
  retries = 2, 
  iterations = 1 
}: ImageSearchOptions): Promise<{results: ImageSearchResult[], allImageUrls: string[]}> {
  // Check if we have cached results for this query
  const cacheKey = `${query}-${moderate}`;
  if (searchResultsCache[cacheKey]) {
    const cachedData = searchResultsCache[cacheKey];
    const now = Date.now();
    
    // If cache is still valid, return it
    if (now - cachedData.timestamp < CACHE_EXPIRATION) {
      console.log('Using cached results for query:', query);
      return { results: cachedData.results, allImageUrls: cachedData.allImageUrls };
    } else {
      // Remove expired cache
      delete searchResultsCache[cacheKey];
    }
  }
  
  let reqUrl = constants.url + 'i.js';
  const keywords = query;
  const p = moderate ? 1 : -1;  // by default moderate false
  let attempt = 0;
  
  let results: ImageSearchResult[] = [];
  const allImageUrls: string[] = [];

  try {
    // Apply search delay to prevent rate limiting
    const delay = constants.getSearchDelay();
    if (delay > 0) {
      console.log(`Applying delay of ${delay}ms before search`);
      await sleep(delay);
    }
    
    // Get the vqd token required for search
    const token = await getToken(keywords);
    
    // Set up params for the image search request
    const params = {
      "l": "wt-wt",
      "o": "json",
      "q": keywords,
      "vqd": token,
      "f": ",,,",
      "p": "" + (p)
    };
    
    let data = null;
    let itr = 0;
    
    // Iterate based on configured iterations
    while (itr < iterations) {
      // Retry logic
      while (true) {
        try {
          console.log(`Searching DuckDuckGo for images with query: ${keywords}`);
          
          // Generate fresh headers for each request
          const headers = constants.getHeaders();
          
          const response = await axios.get(reqUrl, {
            params,
            headers,
            timeout: 8000 // 8 second timeout
          });
          
          data = response.data;
          if (!data.results) throw new Error("No results");
          break;
        } catch (error: any) {
          console.error('DuckDuckGo search error:', error.message);
          
          // For 403 errors, wait longer
          if (error.response && error.response.status === 403) {
            console.log('Received 403 error. Waiting longer before retry...');
            await sleep(10000 + Math.random() * 5000); // 10-15 second wait
          } else {
            await sleep(5000);
          }
          
          attempt += 1;
          
          if (attempt > retries) {
            return { results, allImageUrls };
          }
          
          continue;
        }
      }
      
      // Add results from this iteration
      results = [...results, ...data.results];
      
      // If there are no more results or we've reached our iteration limit, stop
      if (!data.next) {
        break;
      }
      
      // Set up for next page of results
      reqUrl = constants.url + data["next"];
      itr += 1;
      attempt = 0;
      
      // Small delay between fetching next pages
      await sleep(1000 + Math.random() * 2000);
    }
    
    // Cache the results
    if (results.length > 0) {
      searchResultsCache[cacheKey] = {
        timestamp: Date.now(),
        results,
        allImageUrls: []
      };
    }
  } catch (error) {
    console.error('Error in imageSearch:', error);
  }
  
  return { results, allImageUrls };
}

/**
 * Search for images using Serper.dev API
 */
async function serperImageSearch(query: string, location: string = 'United States', maxResults: number = 10): Promise<{results: ImageSearchResult[], allImageUrls: string[]}> {
  // Check if we have cached results for this query
  const cacheKey = `serper-${query}-${location}`;
  if (searchResultsCache[cacheKey]) {
    const cachedData = searchResultsCache[cacheKey];
    const now = Date.now();
    
    // If cache is still valid, return it
    if (now - cachedData.timestamp < CACHE_EXPIRATION) {
      console.log('Using cached Serper results for query:', query);
      return { results: cachedData.results, allImageUrls: cachedData.allImageUrls };
    } else {
      // Remove expired cache
      delete searchResultsCache[cacheKey];
    }
  }
  
  try {
    console.log(`Searching Serper.dev for images with query: ${query}`);
    
    const apiKey = process.env.SERPER_API_KEY;
    if (!apiKey) {
      throw new Error('SERPER_API_KEY environment variable not set');
    }
    
    const response = await axios.post('https://google.serper.dev/images', 
      {
        q: query,
        location: location,
        autocorrect: false,
        num: maxResults
      },
      {
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 second timeout
      }
    );
    
    const data: SerperResponse = response.data;
    
    if (!data.images || data.images.length === 0) {
      console.log('No images found from Serper API');
      return { results: [], allImageUrls: [] };
    }
    
    // Transform Serper results to our ImageSearchResult format
    const transformedResults: ImageSearchResult[] = data.images.map(img => ({
      title: img.title,
      url: img.link,
      image: img.imageUrl,
      thumbnail: img.thumbnailUrl,
      width: img.imageWidth,
      height: img.imageHeight
    }));
    
    // Extract all image URLs for the new img_urls column
    const allImageUrls = data.images.map(img => img.imageUrl);
    
    // Cache the results
    searchResultsCache[cacheKey] = {
      timestamp: Date.now(),
      results: transformedResults,
      allImageUrls
    };
    
    return { results: transformedResults, allImageUrls };
  } catch (error) {
    console.error('Error in serperImageSearch:', error);
    return { results: [], allImageUrls: [] };
  }
}

// Fallback static images for different property types
const fallbackImages: Record<string, string[]> = {
  'house': [
    'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg',
    'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg',
    'https://images.pexels.com/photos/164558/pexels-photo-164558.jpeg'
  ],
  'apartment': [
    'https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg',
    'https://images.pexels.com/photos/1918291/pexels-photo-1918291.jpeg',
    'https://images.pexels.com/photos/1643384/pexels-photo-1643384.jpeg'
  ],
  'condo': [
    'https://images.pexels.com/photos/2102587/pexels-photo-2102587.jpeg',
    'https://images.pexels.com/photos/2096578/pexels-photo-2096578.jpeg',
    'https://images.pexels.com/photos/2079234/pexels-photo-2079234.jpeg'
  ]
};

/**
 * Get a fallback image for a property type
 */
function getFallbackImage(propertyType: string = 'house'): ImageSearchResult {
  let category = 'house'; // Default
  
  // Map the property type to one of our categories
  if (propertyType.toLowerCase().includes('apartment') || 
      propertyType.toLowerCase().includes('flat')) {
    category = 'apartment';
  } else if (propertyType.toLowerCase().includes('condo')) {
    category = 'condo';
  }
  
  // Get random image from the category
  const images = fallbackImages[category];
  const randomIndex = Math.floor(Math.random() * images.length);
  const imageUrl = images[randomIndex];
  
  return {
    image: imageUrl,
    thumbnail: imageUrl,
    url: imageUrl,
    title: `${category.charAt(0).toUpperCase() + category.slice(1)} Property`,
    width: 800,
    height: 600
  };
}

/**
 * API Route handler for searching property images using Serper API (previously DuckDuckGo)
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const address = searchParams.get('address');
    const city = searchParams.get('city');
    const state = searchParams.get('state');
    const zipCode = searchParams.get('zipCode');
    const propertyType = searchParams.get('propertyType') || 'house';
    const count = searchParams.get('count') ? parseInt(searchParams.get('count') || '1') : 1;

    // Validate required parameters
    if (!address && !city) {
      return NextResponse.json(
        { error: 'Address or city is required' },
        { status: 400 }
      );
    }

    // Build search query
    let searchQuery = `${address || ''}`;
    if (city) searchQuery += ` ${city}`;
    if (state) searchQuery += ` ${state}`;
    if (zipCode) searchQuery += ` ${zipCode}`;
    
    // Add contextual terms
    searchQuery += ' property building';

    console.log('Searching for property images with query:', searchQuery);

    try {
      // Execute search with Serper API instead of DuckDuckGo
      const { results, allImageUrls } = await serperImageSearch(
        searchQuery,
        'United States',
        10 // Request up to 10 images
      );

      // Check if we have valid results
      if (!results || results.length === 0) {
        console.log('No results found, using fallback image');
        // Return fallback image
        return NextResponse.json({ 
          images: [getFallbackImage(propertyType)],
          allImageUrls: []
        }, { status: 200 });
      }

      // Return results
      return NextResponse.json({
        images: results.slice(0, count), // Return only requested number of images
        allImageUrls: allImageUrls      // Return all image URLs for saving
      }, { status: 200 });
    } catch (searchError) {
      console.error('Search failed, using fallback image:', searchError);
      // Return fallback image on error
      return NextResponse.json({ 
        images: [getFallbackImage(propertyType)],
        allImageUrls: []
      }, { status: 200 });
    }
  } catch (error) {
    console.error('Error in property-images API:', error);
    return NextResponse.json(
      { error: 'Failed to search for property images' },
      { status: 500 }
    );
  }
} 