import axios from 'axios';

/**
 * Sleep function for retries
 * @param ms Milliseconds to sleep
 */
export const sleep = (ms: number) => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

// Array of different user agents to rotate through
const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/118.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0'
];

/**
 * Get a random user agent from our list
 */
export const getRandomUserAgent = () => {
    const randomIndex = Math.floor(Math.random() * userAgents.length);
    return userAgents[randomIndex];
};

// Keep track of last token request time to avoid rate limiting
let lastTokenRequest = 0;

/**
 * Get vqd token for DuckDuckGo image search
 * @param keywords Search keywords
 */
export const getToken = async (keywords: string): Promise<string> => {
    try {
        // Implement rate limiting for token requests
        const now = Date.now();
        const timeSinceLastRequest = now - lastTokenRequest;
        
        // If less than 2 seconds since last request, add delay
        if (lastTokenRequest > 0 && timeSinceLastRequest < 2000) {
            await sleep(2000 - timeSinceLastRequest + Math.random() * 1000);
        }
        
        lastTokenRequest = Date.now();
        
        const url = 'https://duckduckgo.com/';
        
        const params = {
            q: keywords
        };
        
        const headers = {
            'User-Agent': getRandomUserAgent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://duckduckgo.com/'
        };
        
        const response = await axios.get(url, {
            params,
            headers
        });
        
        const data = response.data;
        const vqdMatch = data.match(/vqd='([^']+)'/);
        
        if (vqdMatch && vqdMatch[1]) {
            return vqdMatch[1];
        }
        
        // Try alternate pattern
        const altMatch = data.match(/vqd=["']([^"']+)['"]/);
        if (altMatch && altMatch[1]) {
            return altMatch[1];
        }
        
        throw new Error('Could not extract vqd token from DuckDuckGo');
    } catch (error) {
        console.error('Error getting DuckDuckGo token:', error);
        throw error;
    }
};

// Track last search time to prevent rate limiting
let lastSearchTime = 0;

/**
 * Constants for DuckDuckGo image search API
 */
export const constants = {
    url: 'https://duckduckgo.com/',
    getHeaders: () => {
        return {
            'User-Agent': getRandomUserAgent(),
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': 'https://duckduckgo.com/',
            'Origin': 'https://duckduckgo.com',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };
    },
    max_iter: 2,
    max_retries: 2,
    getSearchDelay: () => {
        const now = Date.now();
        const timeSinceLastSearch = now - lastSearchTime;
        
        // Updated delay strategy
        if (lastSearchTime === 0) {
            lastSearchTime = now;
            return 0;
        }
        
        // If the last search was recent, wait longer
        let delay = 0;
        if (timeSinceLastSearch < 5000) {
            delay = 5000 - timeSinceLastSearch + Math.random() * 2000;
        }
        
        lastSearchTime = now + delay;
        return delay;
    }
}; 