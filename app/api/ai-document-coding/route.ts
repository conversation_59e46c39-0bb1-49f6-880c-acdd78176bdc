import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface Address {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  units: Array<{
    id: string;
    unit: string;
    beds: number;
    baths: number;
  }>;
}

interface AIPrediction {
  property: string;
  unit: string | null;
  documentType: {
    main: string;
    sub: string | null;
  };
  confidence: number;
}

export async function POST(request: NextRequest) {
  try {
    const { fileName, fileSize, fileType, addresses, portfolioId } = await request.json();

    if (!fileName || !addresses) {
      return NextResponse.json(
        { error: 'Missing required fields: fileName or addresses' },
        { status: 400 }
      );
    }

    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Prepare addresses context for AI
    const addressesContext = addresses.map((addr: Address) => ({
      address: addr.address,
      city: addr.city,
      state: addr.state,
      zip: addr.zip,
      units: addr.units.map(unit => ({
        unit: unit.unit,
        beds: unit.beds,
        baths: unit.baths
      }))
    }));

    // Document types available in the system (matching MultiLevelDropdown structure)
    const documentTypes = {
      "Operating & Financials": {
        "Financials": [],
        "Leases": ["Unit Leases", "Rent Roll", "Other"],
        "Expenses": ["Contracts", "Service Bills", "Utility Bills", "Other"],
        "Taxes": ["General", "Notices", "Tax Bills", "Other"],
        "Insurance": ["Insurance Bills", "Insurance Claims", "Insurance Policies", "Other"],
        "Other": []
      },
      "Purchase Agreements": [],
      "Third Party Reports": {
        "Inspections": [],
        "Maintenance": [],
        "Environmental": [],
        "Other": []
      },
      "Title": {
        "Deed": [],
        "Survey": [],
        "Other": []
      },
      "Insurance": {
        "Contracts": [],
        "Claims": [],
        "Other": []
      }
    };

    const prompt = `You are an AI assistant that analyzes real estate documents and predicts their classification based on file metadata.

CONTEXT:
- File name: ${fileName}
- File size: ${fileSize ? `${Math.round(fileSize / 1024)} KB` : 'Unknown'}
- File type: ${fileType || 'Unknown'}
- Available properties: ${JSON.stringify(addressesContext, null, 2)}
- Available document types: ${JSON.stringify(documentTypes, null, 2)}

TASK:
Analyze this document metadata and predict:
1. Which property it belongs to (match the address from filename patterns)
2. Which unit it applies to (if specific to a unit, otherwise null for entire property)
3. What type of document it is (main category and subcategory)
4. Your confidence level (0-100)

INSTRUCTIONS:
- Base your prediction primarily on the file name and any patterns you can identify
- Look for address components, property names, unit numbers, or document type keywords in the filename
- For property matching, try to match address components or property identifiers
- For document type, look for keywords like "lease", "rent_roll", "invoice", "deed", "survey", etc.
- If you can't determine a specific unit from the filename, set unit to null (entire property)
- Confidence should reflect how certain you are about your predictions based on filename patterns
- Use the exact category names from the document types structure

COMMON FILENAME PATTERNS:
- "123_main_st_unit_2a_lease.pdf" → Property: "123 Main St", Unit: "2A", Type: Operating & Financials > Unit Leases
- "property_deed_456_oak_ave.pdf" → Property: "456 Oak Ave", Unit: null, Type: Title > Deed
- "rent_roll_2024_downtown_complex.pdf" → Property: match "downtown complex", Unit: null, Type: Operating & Financials > Rent Roll
- "utility_bill_march_2024.pdf" → Type: Operating & Financials > Utility Bills

RESPONSE FORMAT:
You must respond with valid JSON in this exact format:
{
  "property": "123 Main Street",
  "unit": "2A" or null,
  "documentType": {
    "main": "Operating & Financials",
    "sub": "Unit Leases"
  },
  "confidence": 85
}`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: 'You are a real estate document analysis expert. You must respond with valid JSON only, no additional text or explanations.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.3,
      response_format: { type: "json_object" }
    });

    const responseContent = completion.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error('No response from OpenAI');
    }

    let prediction: AIPrediction;
    try {
      prediction = JSON.parse(responseContent);
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      // Fallback prediction
      prediction = {
        property: addresses[0]?.address || 'Unknown Property',
        unit: null,
        documentType: {
          main: 'Operating & Financials',
          sub: 'Other'
        },
        confidence: 30
      };
    }

    // Validate and sanitize the prediction
    const validatedPrediction: AIPrediction = {
      property: prediction.property || addresses[0]?.address || 'Unknown Property',
      unit: prediction.unit || null,
      documentType: {
        main: prediction.documentType?.main || 'Operating & Financials',
        sub: prediction.documentType?.sub || null
      },
      confidence: Math.max(0, Math.min(100, prediction.confidence || 50))
    };

    return NextResponse.json(validatedPrediction);

  } catch (error) {
    console.error('Error in AI document coding:', error);
    return NextResponse.json(
      { error: 'Failed to process document with AI' },
      { status: 500 }
    );
  }
} 