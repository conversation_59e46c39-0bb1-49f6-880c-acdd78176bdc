import { NextRequest, NextResponse } from 'next/server';
import { updatePropertyFinancials } from '@/actions/propertyFinancials';
import { updatePortfolioFinancials } from '@/actions/portfolioActions';

interface SaveRequest {
    addressId: string | null;
    portfolioId?: string;
    year: number;
    key: string;
    value: number | string;
    source: 'human' | 'ai' | 'calculator';
}

export async function POST(request: NextRequest) {
    try {
        const { addressId, portfolioId, year, key, value, source }: SaveRequest = await request.json();

        // Validate required fields
        if (!year || !key || value === undefined || value === null) {
            return NextResponse.json({
                success: false,
                error: 'Missing required fields'
            }, { status: 400 });
        }

        // Convert value to number if it's a string
        const numericValue = typeof value === 'string' ? parseFloat(value) : value;
        
        if (isNaN(numericValue) || !isFinite(numericValue)) {
            return NextResponse.json({
                success: false,
                error: 'Invalid numeric value'
            }, { status: 400 });
        }

        // Prepare update data (convert to string as expected by updatePropertyFinancials)
        const updateData = {
            [key]: numericValue.toString()
        };

        let result;

        // Map source to the expected type for updatePropertyFinancials
        const mappedSource: 'human' | 'ai' = source === 'ai' ? 'ai' : 'human';

        // Save to database
        if (addressId) {
            // Property-level save
            result = await updatePropertyFinancials(addressId, updateData, year, mappedSource);
        } else if (portfolioId) {
            // Portfolio-level save
            result = await updatePortfolioFinancials(portfolioId, updateData, year, mappedSource);
        } else {
            return NextResponse.json({
                success: false,
                error: 'Either addressId or portfolioId must be provided'
            }, { status: 400 });
        }

        // Redis caching removed as requested

        return NextResponse.json({
            success: true,
            data: {
                year: year,
                key: key,
                value: numericValue,
                source: source,
                saved: true
            }
        });

    } catch (error) {
        console.error('Error saving financial data:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to save financial data',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
} 