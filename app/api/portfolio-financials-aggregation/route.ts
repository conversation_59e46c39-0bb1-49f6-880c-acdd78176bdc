import { NextRequest, NextResponse } from 'next/server';
import { PortfolioFinancialAggregationService } from '@/services/portfolioFinancialAggregationService';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { portfolio_id, year, action } = body;
    
    if (!portfolio_id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      );
    }
    
    switch (action) {
      case 'aggregate':
        if (!year) {
          return NextResponse.json(
            { error: 'Year is required for aggregation' },
            { status: 400 }
          );
        }
        const aggregation = await PortfolioFinancialAggregationService.aggregatePortfolioFinancials(portfolio_id, year);
        return NextResponse.json({ success: true, data: aggregation });
        
      case 'aggregate_all':
        const allAggregations = await PortfolioFinancialAggregationService.aggregateAllYears(portfolio_id);
        return NextResponse.json({ success: true, data: allAggregations });
        
      case 'update':
        await PortfolioFinancialAggregationService.updatePortfolioFinancials(portfolio_id, year);
        return NextResponse.json({ success: true, message: 'Portfolio financials updated successfully' });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "aggregate", "aggregate_all", or "update"' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Error in portfolio financial aggregation API:', error);
    return NextResponse.json(
      { error: `Failed to process request: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const portfolio_id = searchParams.get('portfolio_id');
    const year = searchParams.get('year');
    
    if (!portfolio_id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      );
    }
    
    if (year) {
      const aggregation = await PortfolioFinancialAggregationService.aggregatePortfolioFinancials(portfolio_id, parseInt(year));
      return NextResponse.json({ success: true, data: aggregation });
    } else {
      const allAggregations = await PortfolioFinancialAggregationService.aggregateAllYears(portfolio_id);
      return NextResponse.json({ success: true, data: allAggregations });
    }
    
  } catch (error) {
    console.error('Error in portfolio financial aggregation API:', error);
    return NextResponse.json(
      { error: `Failed to fetch aggregation: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 