import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import apiEndpointsServer from '@/constants/apiEndpointsServer';

export async function POST(req: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Get the session for authorization
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'No session found' }, { status: 401 });
    }

    // Proxy request to external API
    const response = await fetch(apiEndpointsServer.finance.longTermRental, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('External API error:', errorText);
      return NextResponse.json(
        { error: 'External API request failed', details: errorText },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in long_term_rental calculation:', error);
    return NextResponse.json(
      { error: 'Failed to calculate long term rental' },
      { status: 500 }
    );
  }
} 