import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import apiEndpointsServer from '@/constants/apiEndpointsServer';

export async function GET(
  req: NextRequest,
  { params }: { params: { task_token: string } }
) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { task_token } = await params;
    
    if (!task_token) {
      return NextResponse.json({ error: 'Task token is required' }, { status: 400 });
    }

    // Get the session for authorization
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'No session found' }, { status: 401 });
    }

    // Proxy request to external API
    const response = await fetch(apiEndpointsServer.finance.expenseProjectionsStatus(task_token), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('External API error:', errorText);
      return NextResponse.json(
        { error: 'External API request failed', details: errorText },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in calculate-status:', error);
    return NextResponse.json(
      { error: 'Failed to get calculation status' },
      { status: 500 }
    );
  }
}
