'use client'
import { useModal } from "@/context/ModalContext";
import modalType from "@/constants/modalType";
import {useAuth} from "@/context/AuthContext";
import WorkSpaceItem from "@/components/WorkSpacePage/WorkSpaceItem";
import Spinner from "@/components/UI/Spinner";
import useCheckAuth from "@/helpers/hooks/useCheckAuth";

export default function WorkspacePage() {
    const { showModal } = useModal()
    const {workspaces, isLoadingUserData, workspaceSubscriptionDetails} = useAuth()
    const { user } = useCheckAuth()
    
    const handleCreateWorkspace = async () => {
        showModal(modalType.createWorkspace);
    };

    if(!user) {
        return <></>
    }

    if(isLoadingUserData || !workspaceSubscriptionDetails) {
        return(
            <div className="flex flex-col items-center justify-center py-10">
                <Spinner size="lg" className="mb-3" />
                <p className="text-sm text-gray-600">Loading workspaces...</p>
            </div>
        )
    }

    return (
        <div className="max-w-7xl mx-auto px-4 mt-12">
            <div className="text-center mb-12">
                <h1 className="text-3xl font-bold text-gray-900 mb-4 mt-12">Welcome to Relm Intelligence</h1>
                <p className="text-lg text-gray-600">Select a workspace to continue or create a new one</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div
                    onClick={handleCreateWorkspace}
                    className="cursor-pointer bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border-2 border-dashed border-indigo-200 hover:border-indigo-400 hover:scale-[1.02] h-[180px] flex flex-col justify-between group relative overflow-hidden"
                >
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full -translate-y-10 translate-x-10 opacity-50 group-hover:opacity-70 transition-opacity"></div>
                    <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-pink-100 to-indigo-100 rounded-full translate-y-8 -translate-x-8 opacity-40 group-hover:opacity-60 transition-opacity"></div>
                    
                    <div className="flex mx-auto relative z-10">
                        <div className="text-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-3 mx-auto  group-hover:shadow-xl transition-shadow">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-700 transition-colors">Create New Workspace</h3>
                            <p className="text-sm text-gray-600 mt-1">Create a new workspace for your team</p>
                        </div>
                    </div>
                    <div className="text-indigo-400 flex justify-center relative z-10">
                        <div className="flex items-center gap-1 text-xs font-medium text-indigo-600 group-hover:text-indigo-700 transition-colors">
                            <span>Get Started</span>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                {
                    workspaces && workspaces?.length > 0 && workspaces?.map((workspace) => (
                        <WorkSpaceItem key={workspace.id} workspace={workspace} />
                    ))
                }
            </div>
        </div>
    )
}

