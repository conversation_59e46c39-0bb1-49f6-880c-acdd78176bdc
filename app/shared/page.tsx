'use client'
import { useAuth } from "@/context/AuthContext";
import Spinner from "@/components/UI/Spinner";
import SharedPortfolioItem from "@/components/SharedPage/SharedPortfolioItem";

export default function SharePage() {
    const {sharedList} = useAuth();

    if(!Array.isArray(sharedList)) {
        return (
            <div className="text-center py-8">
                <Spinner size="lg" text="Loading shared portfolios..." fullPage={false} />
            </div>
        )
    }
    
    return (
        <div className="max-w-7xl mx-auto px-4 mt-12">  
            {
                sharedList && sharedList.length > 0 ? 
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {
                            sharedList.map((item) => (
                                <SharedPortfolioItem key={item.portfolioData.id} sharedItem={item} />
                            ))
                        }
                    </div>
                :
                <div className="flex justify-center items-center h-full">
                    <h3 className="text-gray-500 text-center">You don't have any shared Portfolios yet.</h3>
                </div>
            }
        </div>
    )
}