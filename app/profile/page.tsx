'use client'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faBuilding} from '@fortawesome/free-solid-svg-icons';
import { useState} from "react";
import ProfileWorkspace from "@/components/ProfilePage/ProfileWorkspace";
import ProfileInfo from "@/components/ProfilePage/ProfileInfo";

// Set dynamic mode to force-dynamic to handle client-side data fetching
export const dynamic = 'force-dynamic';

export default function Profile(){
	const [selectedTab, setSelectedTab] = useState<'profile' | 'workspace'>('profile')
	
	return(
		<div className='max-w-5xl mx-auto px-4 pb-6 mt-10'>
			<div className="mb-6 flex justify-center">
				<div className="flex rounded-md shadow-sm bg-white p-1.5 space-x-1">
					<button
						onClick={() => setSelectedTab('profile')}
						className={`cursor-pointer flex items-center px-6 py-2.5 text-sm font-medium rounded-md transition-all ${selectedTab === 'profile' ? 'bg-indigo-600 text-white shadow-sm' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>
						<FontAwesomeIcon icon={faUser} className="mr-2 w-3 h-3" />
						Profile
					</button>
					<button
						onClick={() => setSelectedTab('workspace')}
						className={`cursor-pointer flex items-center px-6 py-2.5 text-sm font-medium rounded-md transition-all ${selectedTab === 'workspace' ? 'bg-indigo-600 text-white shadow-sm' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>
						<FontAwesomeIcon icon={faBuilding} className="mr-2 w-3 h-3" />
						Workspace
					</button>
				</div>
			</div>
			<div className='bg-white rounded-lg shadow p-8'>
				{
					selectedTab === 'workspace' ? <ProfileWorkspace /> : 
						selectedTab === 'profile' ? <ProfileInfo /> : null
				}
			</div>
		</div>
	)
}