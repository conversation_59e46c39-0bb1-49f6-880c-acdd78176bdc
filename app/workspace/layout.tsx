'use client'
import PortfolioIdInfo from "@/components/WorkspacePageDetail/PortfolioIdInfo";
import { Suspense } from "react";
import { useAuth } from "@/context/AuthContext";

export default function WorkspaceLayout({ children,}: { children: React.ReactNode; }) {
    const { user } = useAuth()
    
    if(!user) {
        return <></>
    }
    
    return (
      <div className="flex flex-col bg-white inset-0 h-full">
        {/* Main workspace content area */}
        <div className="flex-1 overflow-auto w-full">
          <div className="mx-auto h-full">
            {children}
          </div>
        </div>
        {/* Footer stays visible at all times */}
        <div className="flex-shrink-0">
          <Suspense fallback={<div>Loading...</div>}>
            <PortfolioIdInfo />
          </Suspense>
        </div>
      </div>
    );
  } 