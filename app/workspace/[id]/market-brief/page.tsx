'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useParams } from 'next/navigation';
import { fetchRealEstateNewsWithSummaries, StateNewsResponse, NewsArticle } from '@/actions/newsActions';
import { fetchF<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/actions/fredActions';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faNewspaper, faChartLine, faRobot, faExternalLinkAlt, faCalendarAlt, faFlag, faChartBar, faList, faArrowUp } from '@fortawesome/free-solid-svg-icons';
import MarketScore from '@/components/WorkspacePageDetail/MarketAnalysis/MarketScore';
import { MarketAnalysisSkeleton } from '@/components/UI/SkeletonLoader';
import { getStateName } from '@/utils/stateUtils';
import Image from 'next/image';
import Spinner from '@/components/UI/Spinner';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// Extended NewsArticle interface to include state information
interface NewsArticleWithState extends NewsArticle {
    state: string;
    stateFlag: string;
}

// Cache for article replacement images
interface ImageCache {
    [articleKey: string]: string;
}

// Linear regression calculation function
const calculateLinearRegression = (data: Array<{date: string, value: number}>) => {
    if (!data || data.length < 2) return null;
    
    // Convert dates to numeric values (days since first date)
    const firstDate = new Date(data[0].date).getTime();
    const points = data.map((point, index) => ({
        x: (new Date(point.date).getTime() - firstDate) / (1000 * 60 * 60 * 24), // days
        y: point.value
    }));
    
    const n = points.length;
    const sumX = points.reduce((sum, point) => sum + point.x, 0);
    const sumY = points.reduce((sum, point) => sum + point.y, 0);
    const sumXY = points.reduce((sum, point) => sum + (point.x * point.y), 0);
    const sumXX = points.reduce((sum, point) => sum + (point.x * point.x), 0);
    
    // Calculate slope (m) and y-intercept (b)
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Generate trend line data
    const trendData = data.map((point, index) => ({
        date: point.date,
        value: point.value,
        trend: slope * points[index].x + intercept
    }));
    
    return {
        slope,
        intercept,
        trendData,
        formula: `y = ${slope.toFixed(4)}x + ${intercept.toFixed(2)}`
    };
};

export default function MarketBriefPage() {
    const { workspaceSubscriptionDetails } = useAuth();
    const params = useParams();
    
    // Separate caches for national vs state data
    const [nationalNewsData, setNationalNewsData] = useState<StateNewsResponse[]>([]);
    const [stateNewsData, setStateNewsData] = useState<{[key: string]: StateNewsResponse[]}>({});
    const [allFredCharts, setAllFredCharts] = useState<FredChart[]>([]);
    const [selectedChart, setSelectedChart] = useState<number>(0);
    
    // Image fallback cache
    const [imageCache, setImageCache] = useState<ImageCache>({});
    const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());
    const [failedImages, setFailedImages] = useState<Set<string>>(new Set());
    const [errorCooldowns, setErrorCooldowns] = useState<{[key: string]: number}>({});
    
    const [isLoadingData, setIsLoadingData] = useState(false);
    const [isLoadingStateChange, setIsLoadingStateChange] = useState(false);
    const [isLoadingMarketScore, setIsLoadingMarketScore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedState, setSelectedState] = useState<string>('NATIONAL');
    const [activeTab, setActiveTab] = useState<'analytics' | 'indicators' | 'stories'>('analytics');

    // Get subscribed states for current workspace
    const currentWorkspace = workspaceSubscriptionDetails?.find(w => w.id === params?.id);
    const subscribedStates = currentWorkspace?.active_states || ['NY'];

    // Generate unique key for article to use in image cache
    const getArticleKey = (article: NewsArticleWithState) => {
        return `${article.state}-${article.title.slice(0, 50)}-${article.source}`.replace(/[^a-zA-Z0-9-]/g, '');
    };

    // Handle image fallback with proper error handling and cooldowns
    const handleImageError = async (article: NewsArticleWithState) => {
        const articleKey = getArticleKey(article);
        const now = Date.now();
        
        // Check if this image has failed permanently or is in cooldown
        if (failedImages.has(articleKey)) {
            return;
        }
        
        // Check cooldown period (5 minutes)
        const lastAttempt = errorCooldowns[articleKey];
        if (lastAttempt && now - lastAttempt < 300000) {
            return;
        }
        
        // Don't process if already loading or cached
        if (loadingImages.has(articleKey) || imageCache[articleKey]) {
            return;
        }

        // Mark as loading and set cooldown
        setLoadingImages(prev => new Set(prev).add(articleKey));
        setErrorCooldowns(prev => ({ ...prev, [articleKey]: now }));

        try {
            // Create search query from article title
            const searchQuery = article.title.substring(0, 100); // Limit query length
            
            // Use the existing property-images API endpoint
            const params = new URLSearchParams({
                address: searchQuery,
                city: 'real estate news',
                count: '1'
            });
            
            const response = await fetch(`/api/property-images?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: AbortSignal.timeout(10000) // 10 second timeout
            });
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.images && data.images.length > 0 && data.images[0].image) {
                    const fallbackImageUrl = data.images[0].image;
                    
                    // Cache the replacement image
                    setImageCache(prev => ({
                        ...prev,
                        [articleKey]: fallbackImageUrl
                    }));
                    
                    console.log(`Successfully found fallback image for: ${article.title.substring(0, 50)}...`);
                } else {
                    // Mark as permanently failed if no images found
                    setFailedImages(prev => new Set(prev).add(articleKey));
                }
            } else {
                console.error(`Property images API failed with status: ${response.status}`);
                // Don't mark as permanently failed for server errors
            }
        } catch (error: any) {
            console.error('Failed to fetch fallback image for article:', error.message);
            
            // Mark as permanently failed if it's a network/timeout error
            if (error.name === 'AbortError' || error.name === 'TypeError') {
                setFailedImages(prev => new Set(prev).add(articleKey));
            }
        } finally {
            // Remove from loading set
            setLoadingImages(prev => {
                const newSet = new Set(prev);
                newSet.delete(articleKey);
                return newSet;
            });
        }
    };

    // Get image URL for article (either original or cached fallback)
    const getImageUrl = (article: NewsArticleWithState) => {
        const articleKey = getArticleKey(article);
        return imageCache[articleKey] || article.imageUrl;
    };

    // Check if image should show placeholder
    const shouldShowPlaceholder = (article: NewsArticleWithState) => {
        const articleKey = getArticleKey(article);
        const hasOriginalImage = !!article.imageUrl;
        const hasCachedImage = !!imageCache[articleKey];
        const hasFailed = failedImages.has(articleKey);
        const isLoading = loadingImages.has(articleKey);
        
        return !hasOriginalImage && !hasCachedImage && !isLoading && !hasFailed;
    };

    useEffect(() => {
        if (subscribedStates.length > 0) {
            fetchInitialData();
        }
    }, [subscribedStates]);

    const fetchInitialData = async () => {
        setIsLoadingData(true);
        setError(null);

        try {
            // Fetch national news with up to 50 articles and all 12 FRED charts
            const [newsResults, chartsResults] = await Promise.all([
                Promise.all([
                    fetchRealEstateNewsWithSummaries(['NATIONAL'], 1, {}),
                    fetchRealEstateNewsWithSummaries(['NATIONAL'], 2, {}),
                    fetchRealEstateNewsWithSummaries(['NATIONAL'], 3, {})
                ]).then(results => results.flat()),
                Promise.all([
                    fetchFredCharts(1, 12), // Fetch all 12 charts at once
                ])
            ]);

            setNationalNewsData(newsResults);
            setAllFredCharts(chartsResults[0]);
        } catch (err) {
            console.error('Error fetching market brief data:', err);
            setError('Failed to load market data. Please try again later.');
        } finally {
            setIsLoadingData(false);
        }
    };

    // Handle state selection change
    const handleStateChange = async (newState: string) => {
        if (newState === selectedState) return;
        
        setSelectedState(newState);
        
        if (newState === 'NATIONAL') {
            return;
        }
        
        const existingStateData = stateNewsData[newState];
        if (existingStateData && existingStateData.length > 0) {
            return;
        }
        
        setIsLoadingStateChange(true);
        
        try {
            // Fetch multiple pages for up to 50 articles
            const stateNewsResults = await Promise.all([
                fetchRealEstateNewsWithSummaries([newState], 1, {}),
                fetchRealEstateNewsWithSummaries([newState], 2, {}),
                fetchRealEstateNewsWithSummaries([newState], 3, {})
            ]).then(results => results.flat());
            
            setStateNewsData(prevData => ({
                ...prevData,
                [newState]: stateNewsResults
            }));
        } catch (err) {
            console.error(`Error fetching ${newState} news:`, err);
            setError(`Failed to load news for ${newState}. Please try again.`);
        } finally {
            setIsLoadingStateChange(false);
        }
    };

    // Memoized filtered data to prevent unnecessary re-renders
    const { filteredArticles, stateName } = useMemo(() => {
        if (selectedState === 'NATIONAL') {
            const allArticles = nationalNewsData
                .flatMap(stateNews => 
                    stateNews.articles.map((article: NewsArticle) => ({ 
                        ...article, 
                        state: stateNews.state,
                        stateFlag: stateNews.stateFlag 
                    }))
                )
                .filter((article: NewsArticleWithState) => article.sentimentScore && article.aiSummary)
                .sort((a: NewsArticleWithState, b: NewsArticleWithState) => {
                    const sentimentDiff = (b.sentimentScore || 50) - (a.sentimentScore || 50);
                    if (sentimentDiff !== 0) return sentimentDiff;
                    const dateA = (article: NewsArticleWithState) => {
                        if (!article.date) return 0;
                        try {
                            return new Date(article.date).getTime();
                        } catch {
                            return 0;
                        }
                    };
                    const dateB = (article: NewsArticleWithState) => {
                        if (!article.date) return 0;
                        try {
                            return new Date(article.date).getTime();
                        } catch {
                            return 0;
                        }
                    };
                    return dateB(b) - dateA(a);
                });
            return { filteredArticles: allArticles, stateName: 'National' };
        } else {
            const stateData = stateNewsData[selectedState];
            if (stateData && stateData.length > 0) {
                const articles = stateData
                    .flatMap(stateNews => 
                        stateNews.articles.map((article: NewsArticle) => ({ 
                            ...article, 
                            state: stateNews.state,
                            stateFlag: stateNews.stateFlag 
                        }))
                    )
                    .filter((article: NewsArticleWithState) => article.sentimentScore && article.aiSummary)
                    .sort((a: NewsArticleWithState, b: NewsArticleWithState) => {
                        const sentimentDiff = (b.sentimentScore || 50) - (a.sentimentScore || 50);
                        if (sentimentDiff !== 0) return sentimentDiff;
                        const dateA = (article: NewsArticleWithState) => {
                            if (!article.date) return 0;
                            try {
                                return new Date(article.date).getTime();
                            } catch {
                                return 0;
                            }
                        };
                        const dateB = (article: NewsArticleWithState) => {
                            if (!article.date) return 0;
                            try {
                                return new Date(article.date).getTime();
                            } catch {
                                return 0;
                            }
                        };
                        return dateB(b) - dateA(a);
                    });
                return { filteredArticles: articles, stateName: getStateName(selectedState) };
            }
            return { filteredArticles: [], stateName: getStateName(selectedState) };
        }
    }, [selectedState, nationalNewsData, stateNewsData]);

    const topArticles = filteredArticles.slice(0, 2);
    const remainingArticles = filteredArticles.slice(2, 50);
    
    // Separate articles with and without images
    const articlesWithImages = remainingArticles.filter(article => getImageUrl(article));
    const articlesWithoutImages = remainingArticles.filter(article => !getImageUrl(article));

    // Memoized MarketScore component to prevent unnecessary re-renders - only depends on news articles and Fred charts, not selected chart
    const memoizedMarketScore = useMemo(() => (
        <div className="text-sm">
            <MarketScore 
                newsArticles={filteredArticles}
                fredCharts={allFredCharts}
            />
        </div>
    ), [filteredArticles, allFredCharts]);

    const formatDate = (dateStr: string | undefined) => {
        if (!dateStr) return 'Unknown date';
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return 'Unknown date';
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: 'numeric'
            });
        } catch {
            return 'Unknown date';
        }
    };

    const formatValue = (value: number, units: string) => {
        if (units.toLowerCase().includes('percent')) {
            return `${value.toFixed(2)}%`;
        }
        if (units.toLowerCase().includes('dollar')) {
            return `$${value.toLocaleString()}`;
        }
        if (units.toLowerCase().includes('thousand')) {
            return `${value.toLocaleString()}K`;
        }
        return value.toLocaleString();
    };

    const formatChartDate = (dateStr: string) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short' 
        });
    };

    /*if (subscribedStates.length === 0) {
        return (
            <div className="min-h-screen bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="bg-gray-50 rounded-xl shadow-lg p-8 border border-gray-200">
                        <div className="text-center">
                            <div className="text-6xl mb-4">
                                <FontAwesomeIcon icon={faChartBar} className="text-black" />
                            </div>
                            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Market Brief</h2>
                            <p className="text-gray-600 mb-4">
                                Subscribe to states to access comprehensive market analysis and insights.
                            </p>
                            <a
                                href={`/workspace/${params?.id}/pricing`}
                                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors shadow-md"
                            >
                                Manage Subscriptions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        );
    }*/

    return (
        <div className="min-h-screen bg-white relative">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Compact Header with State Selector */}
                <div className="mb-8">
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h1 className="text-3xl font-bold text-black mb-2">Market Brief</h1>
                            <p className="text-sm text-gray-600">
                                AI Analysis of Real Estate News • {selectedState === 'NATIONAL' ? 'National Coverage' : stateName}
                            </p>
                        </div>
                        
                        {/* Compact State Selector */}
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600 font-medium">Markets:</span>
                            <div className="flex gap-1">
                                <button
                                    onClick={() => handleStateChange('NATIONAL')}
                                    disabled={isLoadingStateChange}
                                    className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-colors ${
                                        selectedState === 'NATIONAL'
                                            ? 'bg-gray-900 text-white shadow-sm'
                                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                    } ${isLoadingStateChange ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                    <span className="text-sm">🇺🇸</span>
                                    <span>US</span>
                                    {isLoadingStateChange && selectedState === 'NATIONAL' && (
                                        <Spinner size="sm" />
                                    )}
                                </button>
                                
                                {subscribedStates.map((state) => (
                                    <button
                                        key={state}
                                        onClick={() => handleStateChange(state)}
                                        disabled={isLoadingStateChange}
                                        className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-colors ${
                                            selectedState === state
                                                ? 'bg-purple-600 text-white shadow-sm'
                                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                        } ${isLoadingStateChange ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    >
                                        <div className="w-3 h-3 rounded-full overflow-hidden border border-white/30">
                                            <img 
                                                src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${state.toLowerCase()}.webp`}
                                                alt={`${state}-flag`}
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                        <span>{state}</span>
                                        {isLoadingStateChange && selectedState === state && (
                                            <Spinner size="sm" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Tab Navigation */}
                    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-4">
                        <div className="flex flex-wrap gap-2">
                            <button
                                onClick={() => setActiveTab('analytics')}
                                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors shadow-sm ${
                                    activeTab === 'analytics'
                                        ? 'bg-purple-600 text-white border-purple-600 shadow-md'
                                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:shadow-md'
                                }`}
                            >
                                <FontAwesomeIcon icon={faChartLine} className="text-sm" />
                                <span className="text-sm font-medium">AI News Analysis</span>
                            </button>
                            <button
                                onClick={() => setActiveTab('indicators')}
                                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors shadow-sm ${
                                    activeTab === 'indicators'
                                        ? 'bg-purple-600 text-white border-purple-600 shadow-md'
                                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:shadow-md'
                                }`}
                            >
                                <FontAwesomeIcon icon={faChartBar} className="text-sm" />
                                <span className="text-sm font-medium">Economic Indicators</span>
                            </button>
                        </div>
                    </div>
                </div>

                {error && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl shadow-lg">
                        <p className="text-red-600">{error}</p>
                        <button 
                            onClick={fetchInitialData}
                            className="mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-md"
                        >
                            Retry
                        </button>
                    </div>
                )}

                {isLoadingData && (
                    <div className="mb-6 bg-gray-100 rounded-xl shadow-lg p-6 border border-gray-200">
                        <div className="flex flex-col items-center justify-center py-10">
                            <Spinner size="lg" className="mb-3" />
                            <p className="text-sm text-gray-600">Loading market data...</p>
                        </div>
                    </div>
                )}

                {isLoadingStateChange && (
                    <div className="mb-6 bg-gray-100 rounded-xl shadow-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-center gap-3">
                            <Spinner size="sm" />
                            <p className="text-sm text-gray-600">
                                Loading {selectedState === 'NATIONAL' ? 'national' : getStateName(selectedState)} data...
                            </p>
                        </div>
                    </div>
                )}

                {!isLoadingData && (
                    <div className="space-y-8 pb-16">
                        {/* Tab Content */}
                        {activeTab === 'analytics' && (
                            <div className="space-y-8">
                                {/* Row 1: AI Market Analysis + Top 2 Stories - Fixed Heights */}
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    {/* AI Market Analysis - Fixed Container Height */}
                                    <div className="h-[calc(100vh-280px)] max-h-[700px] min-h-[500px] flex flex-col">
                                        <div className="flex items-center gap-3 mb-4">
                                            <FontAwesomeIcon icon={faChartLine} className="text-black text-xl" />
                                            <h2 className="text-xl font-semibold text-gray-900">
                                                AI Market Analysis
                                            </h2>
                                        </div>
                                        
                                        <div className="flex-1 overflow-hidden">
                                            {(isLoadingMarketScore || filteredArticles.length === 0 || allFredCharts.length === 0) ? (
                                                <MarketAnalysisSkeleton />
                                            ) : memoizedMarketScore}
                                        </div>
                                    </div>

                                    {/* Top 2 Stories - Fixed Container Height */}
                                    <div className="flex flex-col">
                                        <div className="flex items-center gap-3 mb-4">
                                            <FontAwesomeIcon icon={faNewspaper} className="text-black text-xl" />
                                            <h2 className="text-xl font-semibold text-gray-900">
                                                Top Stories {selectedState === 'NATIONAL' ? 'Nationwide' : getStateName(selectedState)}
                                            </h2>
                                        </div>
                                        
                                        <div className="flex-1 overflow-y-auto pr-2 space-y-3">
                                            {topArticles.length > 0 ? topArticles.map((article, index) => {
                                                const currentImageUrl = getImageUrl(article);
                                                const articleKey = getArticleKey(article);
                                                const isLoadingFallback = loadingImages.has(articleKey);
                                                const hasFailed = failedImages.has(articleKey);
                                                
                                                return (
                                                    <div key={`top-${article.state}-${index}`} className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 overflow-hidden h-[222px] flex">
                                                        {/* Image Section - 35% width */}
                                                        <div className="relative w-[35%] bg-gray-200 flex-shrink-0">
                                                            {currentImageUrl && !hasFailed ? (
                                                                <Image
                                                                    src={currentImageUrl.replace(/&w=\d+&q=\d+/g, '&w=300&q=100')}
                                                                    alt={article.title}
                                                                    fill
                                                                    className="object-cover"
                                                                    onError={() => {
                                                                        // Only call once by checking if already in failed state
                                                                        if (!failedImages.has(articleKey) && !loadingImages.has(articleKey)) {
                                                                            handleImageError(article);
                                                                        }
                                                                    }}
                                                                />
                                                            ) : (
                                                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                                    {isLoadingFallback ? (
                                                                        <div className="flex flex-col items-center gap-1">
                                                                            <Spinner size="sm" />
                                                                            <span className="text-xs text-gray-500">Loading...</span>
                                                                        </div>
                                                                    ) : (
                                                                        <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-2xl" />
                                                                    )}
                                                                </div>
                                                            )}
                                                            {/* State badge - repositioned for horizontal layout */}
                                                            <div className="absolute top-2 left-2">
                                                                <span className="inline-flex items-center gap-1 px-2 py-1 bg-black/70 backdrop-blur-sm text-white text-xs rounded-full">
                                                                    {(article as any).state === 'NATIONAL' ? (
                                                                        <span className="text-sm">🇺🇸</span>
                                                                    ) : (
                                                                        <div className="w-3 h-3 rounded-full overflow-hidden border border-white/30">
                                                                            <img 
                                                                                src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${(article as any).state?.toLowerCase()}.webp`}
                                                                                alt={`${(article as any).state}-flag`}
                                                                                className="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    )}
                                                                    <span className="font-medium text-xs">
                                                                        {(article as any).state === 'NATIONAL' ? 'National' : getStateName((article as any).state || '')}
                                                                    </span>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        
                                                        {/* Content Section - 65% width */}
                                                        <div className="flex-1 p-4 flex flex-col justify-between">
                                                            {/* Header with source and sentiment */}
                                                            <div className="flex items-center justify-between mb-2">
                                                                <span className="text-xs font-medium text-gray-600 truncate">{article.source}</span>
                                                                {article.sentimentScore && (
                                                                    <div className={`px-2 py-1 rounded-full text-xs font-medium shadow-sm ${
                                                                        article.sentimentScore >= 70 ? 'bg-green-100 text-green-800' :
                                                                        article.sentimentScore >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                                                        'bg-red-100 text-red-800'
                                                                    }`}>
                                                                        {article.sentimentScore >= 70 ? 'Positive' :
                                                                            article.sentimentScore >= 40 ? 'Neutral' : 'Negative'}
                                                                    </div>
                                                                )}
                                                            </div>
                                                            
                                                            {/* Title - compressed */}
                                                            <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 leading-tight">
                                                                {article.title}
                                                            </h3>
                                                            
                                                            {/* AI Summary - compressed */}
                                                            {article.aiSummary && (
                                                                <div className="mb-3 flex-1">
                                                                    <p className="text-xs text-gray-700 leading-relaxed line-clamp-3">
                                                                        {article.aiSummary}
                                                                    </p>
                                                                </div>
                                                            )}

                                                            {/* Footer with date and link */}
                                                            <div className="flex items-center justify-between">
                                                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                                                    <FontAwesomeIcon icon={faCalendarAlt} className="text-black text-xs" />
                                                                    <span>{article.date}</span>
                                                                </div>
                                                                <a
                                                                    href={article.link}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="inline-flex items-center gap-1 text-black hover:text-gray-700 text-xs font-medium"
                                                                >
                                                                    Read Full Article
                                                                    <FontAwesomeIcon icon={faExternalLinkAlt} className="text-xs" />
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }) : (
                                                <div className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 p-8 text-center">
                                                    <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-4xl mb-4" />
                                                    <p className="text-gray-600">No news articles available for the selected market.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Row 2: More Market News - Separated by Images */}
                                {(articlesWithImages.length > 0 || articlesWithoutImages.length > 0) && (
                                    <div>
                                        <div className="flex items-center gap-3 mb-4">
                                            <FontAwesomeIcon icon={faNewspaper} className="text-black text-xl" />
                                            <h2 className="text-2xl font-semibold text-gray-900">More Market News</h2>
                                        </div>
                                        
                                        {/* Articles with Images */}
                                        {articlesWithImages.length > 0 && (
                                            <div className="mb-8">
                                                <h3 className="text-lg font-medium text-gray-800 mb-4">Featured Articles</h3>
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                    {articlesWithImages.slice(0, 24).map((article, index) => {
                                                        const currentImageUrl = getImageUrl(article);
                                                        const articleKey = getArticleKey(article);
                                                        const isLoadingFallback = loadingImages.has(articleKey);
                                                        const hasFailed = failedImages.has(articleKey);
                                                        
                                                        return (
                                                            <div key={`with-image-${article.state}-${index}`} className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow">
                                                                <div className="relative h-32 bg-gray-200">
                                                                    {currentImageUrl && !hasFailed ? (
                                                                        <Image
                                                                            src={currentImageUrl.replace(/&w=\d+&q=\d+/g, '&w=400&q=75')}
                                                                            alt={article.title}
                                                                            fill
                                                                            className="object-cover"
                                                                            onError={() => {
                                                                                // Only call once by checking if already in failed state
                                                                                if (!failedImages.has(articleKey) && !loadingImages.has(articleKey)) {
                                                                                    handleImageError(article);
                                                                                }
                                                                            }}
                                                                        />
                                                                    ) : (
                                                                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                                            {isLoadingFallback ? (
                                                                                <div className="flex flex-col items-center gap-1">
                                                                                    <Spinner size="sm" />
                                                                                    <span className="text-xs text-gray-500">Loading...</span>
                                                                                </div>
                                                                            ) : (
                                                                                <FontAwesomeIcon icon={faNewspaper} className="text-gray-400 text-2xl" />
                                                                            )}
                                                                        </div>
                                                                    )}
                                                                    <div className="absolute top-2 left-2">
                                                                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-black/70 backdrop-blur-sm text-white text-xs rounded-full">
                                                                            {(article as any).state === 'NATIONAL' ? (
                                                                                <span className="text-sm">🇺🇸</span>
                                                                            ) : (
                                                                                <div className="w-4 h-4 rounded-full overflow-hidden border border-white/30">
                                                                                    <img 
                                                                                        src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${(article as any).state?.toLowerCase()}.webp`}
                                                                                        alt={`${(article as any).state}-flag`}
                                                                                        className="w-full h-full object-cover"
                                                                                    />
                                                                                </div>
                                                                            )}
                                                                            <span className="font-medium">
                                                                                {(article as any).state === 'NATIONAL' ? 'National' : getStateName((article as any).state || '')}
                                                                            </span>
                                                                        </span>
                                                                    </div>
                                                                    {article.sentimentScore && (
                                                                        <div className="absolute top-2 right-2">
                                                                            <div className={`px-2 py-1 rounded-full text-xs font-medium shadow-sm ${
                                                                                article.sentimentScore >= 70 ? 'bg-green-100 text-green-800' :
                                                                                article.sentimentScore >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                                                                'bg-red-100 text-red-800'
                                                                            }`}>
                                                                                {article.sentimentScore >= 70 ? 'Positive' :
                                                                                 article.sentimentScore >= 40 ? 'Neutral' : 'Negative'}
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                
                                                                <div className="p-4">
                                                                    <div className='flex items-center gap-1 text-xs text-gray-500 mb-2'>
                                                                        <span className="font-medium">{article.source}</span>
                                                                    </div>
                                                                    <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                                                                        <FontAwesomeIcon icon={faCalendarAlt} className="text-black text-xs" />
                                                                        <span>{article.date}</span>
                                                                    </div>
                                                                    
                                                                    <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2">
                                                                        {article.title}
                                                                    </h3>
                                                                    
                                                                    {article.aiSummary && (
                                                                        <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                                                                            {article.aiSummary}
                                                                        </p>
                                                                    )}
                                                                    
                                                                    <a
                                                                        href={article.link}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        className="inline-flex items-center gap-1 text-black hover:text-gray-700 text-xs font-medium"
                                                                    >
                                                                        Read More
                                                                        <FontAwesomeIcon icon={faExternalLinkAlt} className="text-xs" />
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        )}

                                        {/* Articles without Images */}
                                        {articlesWithoutImages.length > 0 && (
                                            <div>
                                                <h3 className="text-lg font-medium text-gray-800 mb-4">Additional Coverage</h3>
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                    {articlesWithoutImages.slice(0, 24).map((article, index) => (
                                                        <div key={`without-image-${article.state}-${index}`} className="bg-gray-100 rounded-lg shadow border border-gray-200 p-4 hover:shadow-md transition-shadow">
                                                            <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                                                                {(article as any).state === 'NATIONAL' ? (
                                                                    <span className="text-sm">🇺🇸</span>
                                                                ) : (
                                                                    <div className="w-3 h-3 rounded-full overflow-hidden border border-gray-300">
                                                                        <img 
                                                                            src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${(article as any).state?.toLowerCase()}.webp`}
                                                                            alt={`${(article as any).state}-flag`}
                                                                            className="w-full h-full object-cover"
                                                                        />
                                                                    </div>
                                                                )}
                                                                <span className="font-medium text-xs">
                                                                    {(article as any).state === 'NATIONAL' ? 'National' : getStateName((article as any).state || '')}
                                                                </span>
                                                                <span>•</span>
                                                                <FontAwesomeIcon icon={faCalendarAlt} className="text-black text-xs" />
                                                                <span>{formatDate(article.date)}</span>
                                                            </div>
                                                            
                                                            <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-3">
                                                                {article.title}
                                                            </h3>
                                                            
                                                            {article.sentimentScore && (
                                                                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mb-2 ${
                                                                    article.sentimentScore >= 70 ? 'bg-green-100 text-green-800' :
                                                                    article.sentimentScore >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                                                    'bg-red-100 text-red-800'
                                                                }`}>
                                                                    {article.sentimentScore >= 70 ? 'Positive' :
                                                                     article.sentimentScore >= 40 ? 'Neutral' : 'Negative'}
                                                                </div>
                                                            )}
                                                            
                                                            <a
                                                                href={article.link}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="inline-flex items-center gap-1 text-black hover:text-gray-700 text-xs font-medium"
                                                            >
                                                                Read More
                                                                <FontAwesomeIcon icon={faExternalLinkAlt} className="text-xs" />
                                                            </a>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}

                        {activeTab === 'indicators' && (
                            <div className="space-y-8">
                                {/* Row 2: Economic Indicators - Fixed Heights */}
                                <div>
                                    <div className="flex items-center gap-3 mb-4">
                                        <FontAwesomeIcon icon={faChartBar} className="text-black text-xl" />
                                        <h2 className="text-2xl font-semibold text-gray-900">Economic Indicators</h2>
                                    </div>
                                    
                                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                        {/* Chart Display - Fixed Height */}
                                        <div className="lg:col-span-2 h-full">
                                            {allFredCharts.length > 0 && (
                                                <div className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 p-6 h-full flex flex-col">
                                                    <div className="mb-4 flex-shrink-0">
                                                        <h4 className="text-md font-medium text-gray-900 mb-2">
                                                            {allFredCharts[selectedChart]?.title}
                                                        </h4>
                                                        <p className="text-sm text-gray-600 mb-2">
                                                            {allFredCharts[selectedChart]?.description}
                                                        </p>
                                                        <div className="flex items-center justify-between">
                                                            <div className="text-xs text-gray-500">
                                                                {allFredCharts[selectedChart]?.units || 'Index'} • {allFredCharts[selectedChart]?.data?.length} points
                                                            </div>
                                                            {(() => {
                                                                const regression = calculateLinearRegression(allFredCharts[selectedChart]?.data || []);
                                                                if (regression) {
                                                                    const slopeThreshold = 0.01; // Threshold for "flat" slope
                                                                    let trendDirection, trendColor;
                                                                    
                                                                    if (Math.abs(regression.slope) < slopeThreshold) {
                                                                        trendDirection = '→';
                                                                        trendColor = 'text-gray-600';
                                                                    } else if (regression.slope > 0) {
                                                                        trendDirection = '↗';
                                                                        trendColor = 'text-green-600';
                                                                    } else {
                                                                        trendDirection = '↘';
                                                                        trendColor = 'text-red-600';
                                                                    }
                                                                    
                                                                    return (
                                                                        <div className="text-xs text-gray-700 font-mono bg-white px-2 py-1 rounded border">
                                                                            <span className={`${trendColor} font-bold`}>{trendDirection}</span> {regression.formula}
                                                                        </div>
                                                                    );
                                                                }
                                                                return null;
                                                            })()}
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="flex-1">
                                                        <ResponsiveContainer width="100%" height="100%">
                                                            <LineChart 
                                                                data={(() => {
                                                                    const regression = calculateLinearRegression(allFredCharts[selectedChart]?.data || []);
                                                                    return regression ? regression.trendData : allFredCharts[selectedChart]?.data;
                                                                })()}
                                                            >
                                                                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                                                <XAxis 
                                                                    dataKey="date" 
                                                                    tick={{ fontSize: 12 }}
                                                                    tickFormatter={formatChartDate}
                                                                />
                                                                <YAxis 
                                                                    tick={{ fontSize: 12 }}
                                                                    tickFormatter={(value) => formatValue(value, allFredCharts[selectedChart]?.units)}
                                                                />
                                                                <Tooltip 
                                                                    labelFormatter={(label) => formatChartDate(label)}
                                                                    formatter={(value: number, name: string) => [
                                                                        formatValue(value, allFredCharts[selectedChart]?.units), 
                                                                        name === 'value' ? 'Actual' : 'Trend'
                                                                    ]}
                                                                    contentStyle={{
                                                                        backgroundColor: '#f9fafb',
                                                                        border: '1px solid #e5e7eb',
                                                                        borderRadius: '8px',
                                                                        fontSize: '12px'
                                                                    }}
                                                                />
                                                                <Line 
                                                                    type="monotone" 
                                                                    dataKey="value" 
                                                                    stroke="#7c3aed" 
                                                                    strokeWidth={3}
                                                                    dot={false}
                                                                    activeDot={{ r: 4, fill: '#7c3aed' }}
                                                                    animationDuration={200}
                                                                    name="Actual"
                                                                />
                                                                <Line 
                                                                    type="monotone" 
                                                                    dataKey="trend" 
                                                                    stroke={(() => {
                                                                        const regression = calculateLinearRegression(allFredCharts[selectedChart]?.data || []);
                                                                        if (regression) {
                                                                            const slopeThreshold = 0.01; // Threshold for "flat" slope
                                                                            if (Math.abs(regression.slope) < slopeThreshold) {
                                                                                return 'rgba(107, 114, 128, 0.6)'; // Grey for flat trend
                                                                            }
                                                                            return regression.slope > 0 ? 'rgba(34, 197, 94, 0.6)' : 'rgba(239, 68, 68, 0.6)'; // Green or Red
                                                                        }
                                                                        return 'rgba(107, 114, 128, 0.6)'; // Gray fallback
                                                                    })()} 
                                                                    strokeWidth={2}
                                                                    strokeDasharray="5 5"
                                                                    dot={false}
                                                                    animationDuration={200}
                                                                    name="Trend"
                                                                />
                                                            </LineChart>
                                                        </ResponsiveContainer>
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Chart Selection List - Fixed Height */}
                                        <div className="lg:col-span-1 h-full">
                                            <div className="bg-gray-100 rounded-xl shadow-lg border border-gray-200 p-4 h-full flex flex-col">
                                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex-shrink-0">Indicator</h4>
                                                <div className="flex-1 overflow-y-auto space-y-2">
                                                    {allFredCharts.slice(0, 12).map((chart, index) => (
                                                        <button
                                                            key={chart.id}
                                                            onClick={() => setSelectedChart(index)}
                                                            className={`w-full text-left p-3 rounded-lg border transition-colors text-sm ${
                                                                selectedChart === index
                                                                    ? 'bg-purple-600 text-white border-purple-600 shadow-md'
                                                                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:shadow-md'
                                                            }`}
                                                        >
                                                            <div className="font-medium mb-1 line-clamp-2">
                                                                {chart.title}
                                                            </div>
                                                            <div className="text-xs opacity-75 line-clamp-2">
                                                                {chart.description}
                                                            </div>
                                                        </button>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Scroll to Top Button */}
            <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="fixed bottom-8 right-8 bg-black text-white rounded-full p-3 shadow-lg hover:bg-gray-800 transition-colors z-30"
            >
                <FontAwesomeIcon icon={faArrowUp} className="text-sm" />
            </button>
        </div>
    );
} 