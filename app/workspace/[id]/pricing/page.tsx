'use client'
import { useEffect, useState } from "react";
import { states, State } from "@/helpers/states";
import { useParams } from "next/navigation";
import { subscriptionCheckout, subscriptionPreview, subscriptionConfirm } from "@/actions/subscriptionActions";
import { useAuth } from "@/context/AuthContext";
import { SubscriptionPreviewType } from "@/types/SubscriptionPreviewType";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck, faChevronDown, faTimes } from "@fortawesome/free-solid-svg-icons";

const MAX_STATES = 10;

export default function Pricing() {
    const [selectedStates, setSelectedStates] = useState<State[]>([]);
    const [paramsStates, setParamsStates] = useState<{added_states: string[], removed_states: string[]} | null>(null);
    const [preview, setPreview] = useState<SubscriptionPreviewType | null>(null);
    const [isPreview, setIsPreview] = useState(false);
    const [isDefaultPricing, setIsDefaultPricing] = useState(false);
    const [isConfirm, setIsConfirm] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [isFeaturesExpanded, setIsFeaturesExpanded] = useState(false);
    const totalPrice = selectedStates.length * 249;
    const isMaxStatesSelected = selectedStates.length >= MAX_STATES;
    const params = useParams();
    const { workspaceSubscriptionDetails } = useAuth();
    
    // Sort states alphabetically
    const sortedStates = [...states].sort((a, b) => a.name.localeCompare(b.name));
    
    // Filter states based on search term
    const filteredStates = sortedStates.filter(state => 
        state.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        state.abbreviation.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    useEffect(() => {
        if(workspaceSubscriptionDetails) {
            const activeStates = workspaceSubscriptionDetails?.find(detail => detail.id === params.id)

            if(activeStates?.active_states && activeStates?.active_states?.length > 0) {
                const mappedStates = activeStates.active_states
                    .map(abbr => states.find(state => state.abbreviation === abbr))
                    .filter((state): state is State => state !== undefined);
                setSelectedStates(mappedStates);
                setParamsStates({added_states: [], removed_states: []});
            }else{
                setIsDefaultPricing(true);
            }
        }
    }, [workspaceSubscriptionDetails])

    const handleStateToggle = async (state: State) => {
        if (selectedStates.includes(state)) {
            // Remove state
            setSelectedStates(prev => prev.filter(s => s !== state));
        } else {
            // Add state (if not at max)
            if (selectedStates.length >= MAX_STATES) {
                return;
            }
            setSelectedStates(prev => [...prev, state]);
        }

        if(paramsStates) {
            setIsPreview(true);
            const activeStates = workspaceSubscriptionDetails?.find(detail => detail.id === params.id)
            const mappedStates = activeStates?.active_states
                    .map(abbr => states.find(state => state.abbreviation === abbr))
                    .filter((state): state is State => state !== undefined);

            if(mappedStates?.includes(state)) {
                setParamsStates(prev => ({
                    ...prev,
                    added_states: prev?.added_states || [],
                    removed_states: prev?.removed_states?.includes(state.abbreviation)
                        ? prev.removed_states.filter(abbr => abbr !== state.abbreviation)
                        : [...(prev?.removed_states || []), state.abbreviation]
                }));
            } else {
                setParamsStates(prev => ({
                    ...prev,
                    added_states: prev?.added_states?.includes(state.abbreviation)
                        ? prev.added_states.filter(abbr => abbr !== state.abbreviation)
                        : [...(prev?.added_states || []), state.abbreviation],
                    removed_states: prev?.removed_states || []
                }));
            }
        }
    };

    const removeSelectedState = (state: State) => {
        handleStateToggle(state);
    };

    const handleCheckout = async () => {
        const states = selectedStates.map(state => state.abbreviation);
        const checkout = await subscriptionCheckout(params?.id as string, states);
        window.location.href = checkout.checkout_url;
    }

    const handleConfirmCheckout = async () => {
        setPreview(null)
        setIsPreview(false)
        setIsConfirm(true)
        setParamsStates({added_states: [], removed_states: []});
        
        setTimeout(() => {
            setIsConfirm(false)
        }, 3000)
        
        const confirm = await subscriptionConfirm(params?.id as string, paramsStates?.added_states || [], paramsStates?.removed_states || []);

        if(confirm.data?.payment_action_required) {
            window.location.href = confirm.data?.update_payment_url
        }
    }

    useEffect(() => {
        if(paramsStates && isPreview) {
            subscriptionPreview(params?.id as string, paramsStates?.added_states || [], paramsStates?.removed_states || []).then(preview => {
                setPreview(preview)
            })
        }
    }, [paramsStates, isPreview])

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (!target.closest('.state-dropdown')) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <div className="min-h-screen bg-white relative overflow-hidden">
            <div className="relative z-10 mx-auto max-w-7xl px-4 py-12">
                <div className="mx-auto max-w-4xl text-center mb-8">
                    <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4">
                        Choose your data access plan
                    </h1>
                    <p className="text-lg leading-8 text-gray-600">
                        Get access to powerful real estate data and AI-driven insights with flexible pricing options tailored to your needs.
                    </p>
                </div>

                <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto h-fit">
                    {/* Per-State Access Card */}
                    <div className="relative group">
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-3xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
                        <div className="relative bg-white border border-gray-200 rounded-3xl shadow-lg p-8 h-full flex flex-col">
                            <div className="flex-none">
                                <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                                    Per-State Access
                                </h3>
                                <p className="text-base text-gray-600 mb-6">
                                    Access comprehensive real estate data and analytics for your selected states.
                                </p>

                                {/* Pricing Section */}
                                <div className="mb-6">
                                    {isDefaultPricing && (
                                        <div className="space-y-2">
                                            <div className="flex items-baseline">
                                                <span className="text-5xl font-bold tracking-tight text-gray-900">
                                                    ${totalPrice}
                                                </span>
                                                <span className="ml-2 text-base text-gray-500">
                                                    /month
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-500">
                                                $249 per state/month
                                            </p>
                                        </div>
                                    )}
                                    
                                    {preview?.data?.success && selectedStates.length > 0 && (
                                        <div className="space-y-3">
                                            <h4 className="text-xl font-semibold text-gray-900">
                                                Updated Subscription
                                            </h4>
                                            <div className="flex items-baseline">
                                                <span className="text-5xl font-bold tracking-tight text-gray-900">
                                                    ${preview.data.pricing.new_monthly_cost}
                                                </span>
                                                <span className="ml-2 text-base text-gray-500">
                                                    /month
                                                </span>
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                                    <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                                                    <span>Previous ${preview.data.pricing.current_monthly_cost}/month</span>
                                                </div>
                                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                                    <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                                                    <span>Immediate Charge {preview.data.pricing.immediate_charge > 0 ? `$${preview.data.pricing.immediate_charge}` : `-$${Math.abs(preview.data.pricing.immediate_charge)}`}</span>
                                                </div>
                                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                                    <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                                                    <span>Next Period Charge ${preview.data.pricing.next_period_charge}/month</span>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* State Selection */}
                                <div className="mb-6">
                                    <div className="flex justify-between items-center mb-4">
                                        <h4 className="text-base font-medium text-gray-900">
                                            Select States
                                        </h4>
                                        <span className="text-sm text-gray-500">
                                            {selectedStates.length}/{MAX_STATES} states selected
                                        </span>
                                    </div>

                                    {/* Selected States Pills */}
                                    {selectedStates.length > 0 && (
                                        <div className="flex flex-wrap gap-2 mb-4 p-3 bg-indigo-50 rounded-xl border border-indigo-100">
                                            {selectedStates.map((state) => (
                                                <div
                                                    key={state.abbreviation}
                                                    className="flex items-center gap-2 bg-white px-3 py-1.5 rounded-full border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-200"
                                                >
                                                    <img 
                                                        src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${state.abbreviation.toLowerCase()}.webp`}
                                                        alt={`${state.abbreviation} flag`}
                                                        className="w-5 h-5 rounded-sm object-cover"
                                                    />
                                                    <span className="text-sm font-medium text-gray-700">{state.name}</span>
                                                    <button
                                                        onClick={() => removeSelectedState(state)}
                                                        className="ml-1 p-0.5 hover:bg-red-100 rounded-full transition-colors"
                                                    >
                                                        <FontAwesomeIcon icon={faTimes} className="w-3 h-3 text-gray-400 hover:text-red-500" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {/* State Dropdown */}
                                    <div className="state-dropdown relative">
                                        <button
                                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                                            disabled={isMaxStatesSelected}
                                            className={`w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 ${
                                                isMaxStatesSelected ? 'opacity-50 cursor-not-allowed' : 'hover:border-indigo-300'
                                            }`}
                                        >
                                            <span className="text-gray-700">
                                                {isMaxStatesSelected ? 'Maximum states selected' : 'Add states...'}
                                            </span>
                                            <FontAwesomeIcon 
                                                icon={faChevronDown} 
                                                className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                                    isDropdownOpen ? 'rotate-180' : ''
                                                }`} 
                                            />
                                        </button>

                                        {isDropdownOpen && !isMaxStatesSelected && (
                                            <div className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-64 overflow-hidden">
                                                <div className="p-3 border-b border-gray-100">
                                                    <input
                                                        type="text"
                                                        placeholder="Search states..."
                                                        value={searchTerm}
                                                        onChange={(e) => setSearchTerm(e.target.value)}
                                                        className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                                    />
                                                </div>
                                                <div className="max-h-48 overflow-y-auto">
                                                    {filteredStates.map((state) => {
                                                        const isSelected = selectedStates.some(s => s.abbreviation === state.abbreviation);
                                                        const canSelect = !isSelected && selectedStates.length < MAX_STATES;
                                                        
                                                        return (
                                                            <button
                                                                key={state.abbreviation}
                                                                onClick={() => {
                                                                    if (isSelected || canSelect) {
                                                                        handleStateToggle(state);
                                                                    }
                                                                }}
                                                                disabled={!isSelected && !canSelect}
                                                                className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                                                                    isSelected ? 'bg-indigo-50 text-indigo-900' : 
                                                                    !canSelect ? 'opacity-50 cursor-not-allowed' : ''
                                                                }`}
                                                            >
                                                                <img 
                                                                    src={`https://gamblespot-images.s3.us-east-1.amazonaws.com/states/coat-of-arms/${state.abbreviation.toLowerCase()}.webp`}
                                                                    alt={`${state.abbreviation} flag`}
                                                                    className="w-6 h-6 rounded-sm object-cover"
                                                                />
                                                                <span className="flex-1 text-sm font-medium">{state.name}</span>
                                                                <span className="text-xs text-gray-500">{state.abbreviation}</span>
                                                                {isSelected && (
                                                                    <FontAwesomeIcon icon={faCheck} className="w-4 h-4 text-indigo-600" />
                                                                )}
                                                            </button>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <p className="mt-3 text-sm text-gray-500">
                                        {selectedStates.length === 0 
                                            ? 'Select one or more states to get started' 
                                            : isMaxStatesSelected
                                            ? 'Maximum states selected. Contact Enterprise for more.'
                                            : `Selected: ${selectedStates.length} ${selectedStates.length === 1 ? 'state' : 'states'}`}
                                    </p>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex-none space-y-4">
                                {isMaxStatesSelected ? (
                                    <a
                                        href="https://calendar.app.google/nEKnLLp1vxkTkVzS6"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="block w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white px-6 py-4 rounded-xl text-center font-medium hover:from-gray-700 hover:to-gray-800 transition-colors duration-200 shadow-lg"
                                    >
                                        Contact Enterprise Sales
                                    </a>
                                ) : (
                                    <>
                                        {isDefaultPricing && (
                                            <button
                                                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-4 rounded-xl font-medium hover:from-indigo-500 hover:to-purple-500 transition-colors duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                                disabled={selectedStates.length === 0}
                                                onClick={handleCheckout}
                                            >
                                                {selectedStates.length === 0 
                                                    ? 'Select states to continue'
                                                    : `Subscribe Now - $${totalPrice}/month`
                                                }
                                            </button>
                                        )}

                                        {paramsStates && (
                                            <button
                                                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-4 rounded-xl font-medium hover:from-indigo-500 hover:to-purple-500 transition-colors duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                                disabled={(paramsStates?.added_states?.length === 0 && paramsStates?.removed_states?.length === 0) || selectedStates.length === 0}
                                                onClick={handleConfirmCheckout}
                                            >
                                                {selectedStates.length === 0 ?
                                                    'Select states to continue'
                                                        : paramsStates?.added_states?.length === 0 && paramsStates?.removed_states?.length === 0
                                                            ? 'Add or remove states to continue'
                                                            : `Confirm - $${preview?.data?.pricing?.new_monthly_cost || ''}/month`
                                                }
                                            </button>
                                        )}
                                        
                                        {isConfirm && (
                                            <p className="text-sm text-green-600 text-center font-medium">
                                                Subscription successfully updated
                                            </p>
                                        )}
                                    </>
                                )}

                                {/* Features List */}
                                <div className="pt-4 border-t border-gray-100">
                                    <button
                                        onClick={() => setIsFeaturesExpanded(!isFeaturesExpanded)}
                                        className="w-full flex items-center justify-between text-left hover:bg-gray-50/50 rounded-lg p-2 -m-2 transition-colors"
                                    >
                                        <h4 className="text-sm font-medium text-gray-900">
                                            What&apos;s included per state
                                        </h4>
                                        <FontAwesomeIcon 
                                            icon={faChevronDown} 
                                            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                                isFeaturesExpanded ? 'rotate-180' : ''
                                            }`} 
                                        />
                                    </button>
                                    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                        isFeaturesExpanded ? 'max-h-96 opacity-100 mt-4' : 'max-h-0 opacity-0'
                                    }`}>
                                        <ul className="space-y-3">
                                            {[
                                                'Real-time market data for selected state',
                                                'Property value predictions',
                                                'Market trend analysis',
                                                'Investment opportunity alerts',
                                                'Detailed neighborhood insights',
                                                'Monthly market reports',
                                            ].map((feature) => (
                                                <li key={feature} className="flex items-start gap-3">
                                                    <FontAwesomeIcon icon={faCheck} className="w-4 h-4 text-indigo-600 mt-0.5 flex-shrink-0" />
                                                    <span className="text-sm text-gray-600">{feature}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Enterprise Card */}
                    <div className="relative group">
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
                        <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-3xl shadow-lg p-8 h-full flex flex-col text-white">
                            <div className="flex-none">
                                <h3 className="text-2xl font-semibold mb-3">
                                    Enterprise
                                </h3>
                                <p className="text-base text-gray-300 mb-6">
                                    Custom solutions for large organizations and teams.
                                </p>
                                <div className="mb-6">
                                    <div className="flex items-baseline">
                                        <span className="text-5xl font-bold tracking-tight">
                                            Custom pricing
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div className="flex-1 flex flex-col justify-between">
                                <div className="space-y-6">
                                    <a
                                        href="https://calendar.app.google/nEKnLLp1vxkTkVzS6"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="block w-full bg-white text-gray-900 px-6 py-4 rounded-xl text-center font-medium hover:bg-gray-100 transition-colors duration-200 shadow-lg"
                                    >
                                        Contact Sales
                                    </a>

                                    <div className="pt-4 border-t border-gray-700">
                                        <h4 className="text-sm font-medium mb-4">Enterprise features</h4>
                                        <ul className="space-y-3">
                                            {[
                                                'Access to all states',
                                                'Custom API integration',
                                                'Dedicated account manager',
                                                'Priority support',
                                                'Custom reporting',
                                                'Team collaboration tools',
                                                'Advanced analytics',
                                                'Bulk data exports',
                                            ].map((feature) => (
                                                <li key={feature} className="flex items-start gap-3">
                                                    <FontAwesomeIcon icon={faCheck} className="w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0" />
                                                    <span className="text-sm text-gray-300">{feature}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}