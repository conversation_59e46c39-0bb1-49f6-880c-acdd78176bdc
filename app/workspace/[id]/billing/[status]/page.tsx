import pathName from "@/constants/pathName";
import { CheckCircle, XCircle } from "lucide-react";
import Link from "next/link";

interface Props {
    params: Promise<{ status: string, id: string }>;
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}



export default async function BillingStatus({params, searchParams}: Props) {
    const {status, id} = await params;
    console.log(status);
    //const {id} = await searchParams;
    const isSuccess = status === "success";
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-6">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-lg">
        <div className="flex flex-col items-center text-center space-y-4">
          {isSuccess ? (
            <>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="w-12 h-12 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-800">Payment Successful!</h1>
              <p className="text-gray-600">
                Thank you for your payment. Your transaction has been completed successfully.
              </p>
              <div className="pt-4">
                <Link 
                  href={`${pathName.workspace}/${id}`} 
                  className="px-6 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
                >
                  Return to Workspace
                </Link>
              </div>
            </>
          ) : (
            <>
              <div className="p-3 bg-red-100 rounded-full">
                <XCircle className="w-12 h-12 text-red-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-800">Payment Cancelled</h1>
              <p className="text-gray-600">
                Your payment process was cancelled. If this was a mistake, please try again.
              </p>
              <div className="pt-4 space-y-3">
                {/*<a 
                  href={''} 
                  className="block px-6 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </a>*/}
                <Link 
                  href={`${pathName.workspace}/${id}`} 
                  className="block px-6 py-2 bg-gray-200 text-gray-800 font-medium rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Return to Workspace
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}