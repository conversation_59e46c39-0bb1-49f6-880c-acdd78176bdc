@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.market-data-calendar{
  width: 200px!important;
  position: absolute !important;
  z-index: 50 !important;
  transform: translateY(-100%) !important;
  margin-bottom: 5px !important;
  bottom: 0 !important;
  left: auto !important;
  right: auto !important;
}

/* Calendar panel styles */
.p-datepicker {
  z-index: 9999 !important;
  position: absolute !important;
  top: auto !important;
  bottom: 100% !important;
}

/* Tag tooltip positioning */
.tag-tooltip {
  z-index: 40 !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  margin-bottom: 8px !important;
}

/* Tag editor popup */
.tag-popup {
  z-index: 50 !important;
  position: absolute !important;
  top: auto !important;
  bottom: 100% !important;
  right: 0 !important;
  margin-bottom: 8px !important;
}

/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

@layer components{
  .chat-assistant{
    @apply text-sm text-gray-900 mt-[6px]
  }

  .chat-assistant ul{
    @apply my-2 list-disc list-inside
  }

  .chat-assistant ol{
    @apply my-2 list-decimal list-inside
  }
  
  
}

/* Hide scrollbar but maintain functionality */
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* For Chrome, Safari and Opera */
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* For Internet Explorer and Edge */
  scrollbar-width: none;  /* For Firefox */
}

/* Fix for the Show Data button */
.rounded-full {
  border-radius: 0.5rem !important;
}

/* Custom scrollbar with purple circle for all pages */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #6366f1; /* Tailwind indigo-500 */
  border-radius: 50px;
  border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: #4f46e5; /* Tailwind indigo-600 */
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #6366f1 #f1f1f1;
}

@layer base{
  html{
    font-size:14px; /* Reduce default font size by 2px while keeping minimum sizes above 10px for text-xs (≈10.5px) */
  }
}

/* High quality image rendering */
.news-article-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimize-quality;
}

/* Smooth image transitions */
.news-article-card {
  transition: all 0.3s ease;
}

.news-article-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.p-dropdown-panel{
  z-index: 9999999 !important;
}

.react-datepicker-wrapper{
  width: 100% !important;
}

/* Custom Bootstrap-like column utilities */
@layer utilities {
  .w-1\/6 {
    width: 16.666667%;
  }
  
  .w-5\/6 {
    width: 83.333333%;
  }
  
  .w-4\/10 {
    width: 40%;
  }
  
  .w-3\/10 {
    width: 30%;
  }
  
  .w-4\/7 {
    width: 57.142857%;
  }
  
  .w-3\/7 {
    width: 42.857143%;
  }
  
  @media (min-width: 1024px) {
    .lg\:w-1\/6 {
      width: 16.666667%;
    }
    
    .lg\:w-5\/6 {
      width: 83.333333%;
    }
    
    .lg\:w-4\/10 {
      width: 40%;
    }
    
    .lg\:w-3\/10 {
      width: 30%;
    }
    
    .lg\:w-4\/7 {
      width: 57.142857%;
    }
    
    .lg\:w-3\/7 {
      width: 42.857143%;
    }
  }
}

/* Scrollbar hiding utility */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Height utilities for responsive layouts */
.h-72 {
  height: 18rem;
}

.min-h-unit-card {
  min-height: 300px;
}

@media (max-width: 1023px) {
  .h-mobile-unit {
    height: 300px;
  }
}

/* Property units layout specific styles */
.property-units-layout {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 300px);
}

@media (min-width: 1024px) {
  .property-units-layout {
    flex-direction: row;
  }
}

/* Simple Unit Selector Container */
.units-rolodex-container {
  position: relative;
  height: 470px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 16px;
  overflow: hidden;
}

.units-rolodex-scroll {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
}

/* Simple scrollbar */
.units-rolodex-scroll::-webkit-scrollbar {
  width: 6px;
}

.units-rolodex-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.units-rolodex-scroll::-webkit-scrollbar-thumb {
  background: #6366f1;
  border-radius: 3px;
}

.units-rolodex-scroll::-webkit-scrollbar-thumb:hover {
  background: #4f46e5;
}

/* Simple Unit Card Styles */
.unit-card-simple {
  position: relative;
  height: 69px;
  max-height: 75px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.unit-card-simple-inner {
  position: relative;
  height: 100%;
  transition: background-color 0.2s ease;
}


/* Simple Add Unit Button */
.add-unit-btn {
  background: #6366f1;
  color: white;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-unit-btn:hover {
  background: #4f46e5;
}

.add-unit-btn:active {
  background: #3730a3;
}

/* Simple units header */
.units-header {
  padding: 16px 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .units-rolodex-container {
    height: 400px;
  }
  
  .unit-card-simple {
    height: 65px;
  }
}

/* Smooth table glare effect after recalculation */
.table-glare-effect {
  position: relative;
  overflow: hidden;
}

.table-glare-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4) 50%,
    transparent
  );
  animation: table-glare 2s ease-in-out;
  z-index: 1;
  pointer-events: none;
}

@keyframes table-glare {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}


@keyframes ticker-scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(calc(-100% + 200px)); }
}

.data-room-wrap .ticker-text {
  animation: ticker-scroll 8s linear infinite;
  animation-play-state: paused;
  white-space: nowrap;
  display: inline-block;
  min-width: 100%;
}

.data-room-wrap .ticker-text.scrolling {
  animation-play-state: running;
}

/* Custom scrollbar styles with business purple */
.data-room-wrap .custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.data-room-wrap .custom-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #e2e8f0, #cbd5e1, #e2e8f0);
  border-radius: 4px;
  margin: 4px 0;
}

.data-room-wrap .custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #5E48F8, #7C69FF, #5E48F8);
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.data-room-wrap .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #4A3BC7, #5E48F8, #4A3BC7);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.data-room-wrap .custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(45deg, #3B2F96, #4A3BC7, #3B2F96);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.data-room-wrap .custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar */
.data-room-wrap .custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #5E48F8 #e2e8f0;
}

/* PDF viewer styles */
.data-room-wrap .pdf-preview {
  width: 100%;
  height: 160px;
  border: none;
  border-radius: 6px;
  background: #f8fafc;
}

.breathing-animation {
  animation: breathing 4.5s ease-in-out infinite;
}

@keyframes breathing {
  0% {
      transform: scale(1);
      fill: url(#gradient1);
  }
  50% {
      transform: scale(var(--random-scale, 1.2));
      fill: url(#gradient2);
  }
  100% {
      transform: scale(1);
      fill: url(#gradient1);
  }
}