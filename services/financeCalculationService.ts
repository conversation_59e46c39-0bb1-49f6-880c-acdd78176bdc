import { FinanceCalculationParams, FinanceCalculationResponse } from '@/types/finance';
import { getBaseUrl } from '@/lib/utils';
import { createClient } from '@/utils/supabase/client';

export class FinanceCalculationService {
    private static async getAuthHeaders() {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
            throw new Error('User not authenticated');
        }

        return {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
        };
    }

    private static getApiUrl(path: string): string {
        // Use local Next.js API routes that proxy to external API
        const baseUrl = getBaseUrl();
        return `${baseUrl}/api/${path}`;
    }

    static async calculateOtherIncome(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/other_income'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to calculate other income');
        }

        return await response.json();
    }

    static async calculateVacancyLoss(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/vacancy_loss'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to calculate vacancy loss');
        }

        return await response.json();
    }

    static async calculateCreditLoss(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/credit_loss'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to calculate credit loss');
        }

        return await response.json();
    }

    static async calculateLongTermRental(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/long_term_rental'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to calculate long term rental');
        }

        return await response.json();
    }

    static async recalculate(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/recalculate'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to recalculate');
        }

        return await response.json();
    }

    static async calculateExpenseProjections(params: FinanceCalculationParams): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const response = await fetch(this.getApiUrl('finance/calculate/overall_expense'), {
            method: 'POST',
            headers,
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error('Failed to calculate expense projections');
        }

        return await response.json();
    }

    // Poll calculation status
    static async getCalculationStatus(taskToken: string): Promise<FinanceCalculationResponse> {
        const headers = await this.getAuthHeaders();
        const url = this.getApiUrl(`finance/calculate-status/${taskToken}`);
        console.log('Fetching calculation status from:', url);

        const response = await fetch(url, {
            method: 'GET',
            headers
        });

        console.log('Status response:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Status API error:', response.status, errorText);
            throw new Error(`Failed to get calculation status: ${response.status} ${errorText}`);
        }

        const result = await response.json();
        console.log('Status result:', result);
        return result;
    }
} 