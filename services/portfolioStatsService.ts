import { createClient } from '@/utils/supabase/client';

export interface PortfolioStats {
  totalProperties: number;
  totalUnits: number;
  completionScore: number;
  sectionsCompleted: {
    basicInfo: number;
    financials: number;
    units: number;
    demographics: number;
    taxHistory: number;
    documents: number;
  };
  averageCompletionBySection: {
    basicInfo: number;
    financials: number;
    units: number;
    demographics: number;
    taxHistory: number;
    documents: number;
  };
}

export interface PropertyStats {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  main_img_url: string | null;
  ai_summary: string | null;
  totalUnits: number;
  documentsCount: number;
  completionScore: number;
  sectionsCompleted: {
    basicInfo: number;
    financials: number;
    units: number;
    demographics: number;
    taxHistory: number;
    documents: number;
  };
}

export class PortfolioStatsService {
  /**
   * Calculate comprehensive portfolio statistics and completion scores
   */
  static async calculatePortfolioStats(portfolioId: string): Promise<PortfolioStats> {
    const supabase = createClient();

    try {
      // 1. Get all properties in the portfolio
      const { data: properties, error: propError } = await supabase
        .from('prop')
        .select('id, address_id, ai_summary, main_img_url, img_urls')
        .eq('portfolio_id', portfolioId)
        .eq('is_deleted', false);

      if (propError) {
        console.error('Error fetching portfolio properties:', propError);
        return this.getEmptyStats();
      }

      if (!properties || properties.length === 0) {
        return this.getEmptyStats();
      }

      const propertyIds = properties.map(p => p.id);
      const totalProperties = properties.length;

      // 2. Get all units for the portfolio
      const { data: units } = await supabase
        .from('prop_units')
        .select('id, prop_id, beds, baths, sqft, rent, price, feature_tags, amenities_tags, img_urls')
        .in('prop_id', propertyIds);

      const totalUnits = units?.length || 0;

      // 3. Calculate completion scores for each section
      const sectionsCompleted = await this.calculateSectionCompletions(supabase, properties, propertyIds);
      
      // 4. Calculate overall completion score (weighted average)
      const weights = {
        basicInfo: 0.2,
        financials: 0.25,
        units: 0.2,
        demographics: 0.15,
        taxHistory: 0.1,
        documents: 0.1
      };

      const overallCompletion = Object.entries(sectionsCompleted).reduce((acc, [key, value]) => {
        const weight = weights[key as keyof typeof weights] || 0;
        return acc + (value * weight);
      }, 0);

      return {
        totalProperties,
        totalUnits,
        completionScore: Math.round(overallCompletion),
        sectionsCompleted,
        averageCompletionBySection: sectionsCompleted // Same as sectionsCompleted for now
      };

    } catch (error) {
      console.error('Error calculating portfolio stats:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * Calculate completion scores for each major section
   */
  private static async calculateSectionCompletions(
    supabase: any, 
    properties: any[], 
    propertyIds: string[]
  ): Promise<PortfolioStats['sectionsCompleted']> {
    
    // Basic Info completion (property details, images, AI summary)
    const basicInfoScore = this.calculateBasicInfoCompletion(properties);

    // Units completion
    const unitsScore = await this.calculateUnitsCompletion(supabase, propertyIds);

    // Demographics completion
    const demographicsScore = await this.calculateDemographicsCompletion(supabase, propertyIds);

    // Tax History completion
    const taxHistoryScore = await this.calculateTaxHistoryCompletion(supabase, propertyIds);

    // Financials completion
    const financialsScore = await this.calculateFinancialsCompletion(supabase, propertyIds);

    // Documents completion
    const documentsScore = await this.calculateDocumentsCompletion(supabase, propertyIds);

    return {
      basicInfo: basicInfoScore,
      financials: financialsScore,
      units: unitsScore,
      demographics: demographicsScore,
      taxHistory: taxHistoryScore,
      documents: documentsScore
    };
  }

  /**
   * Calculate basic info completion (images, AI summary, property details)
   */
  private static calculateBasicInfoCompletion(properties: any[]): number {
    let totalScore = 0;
    
    properties.forEach(property => {
      let propertyScore = 0;
      
      // Has main image (25%)
      if (property.main_img_url) propertyScore += 25;
      
      // Has additional images (25%)
      if (property.img_urls && Array.isArray(property.img_urls) && property.img_urls.length > 0) {
        propertyScore += 25;
      }
      
      // Has AI summary (50%)
      if (property.ai_summary && (
        typeof property.ai_summary === 'string' ? property.ai_summary.length > 0 : 
        property.ai_summary.summary || property.ai_summary.pros_cons || property.ai_summary.poi_summary
      )) propertyScore += 50;
      
      totalScore += propertyScore;
    });

    return Math.round(totalScore / properties.length);
  }

  /**
   * Calculate units completion score
   */
  private static async calculateUnitsCompletion(supabase: any, propertyIds: string[]): Promise<number> {
    const { data: units } = await supabase
      .from('prop_units')
      .select('beds, baths, sqft, rent, price, feature_tags, amenities_tags, img_urls')
      .in('prop_id', propertyIds);

    if (!units || units.length === 0) return 0;

    let totalScore = 0;
    
    units.forEach((unit: any) => {
      let unitScore = 0;
      const maxScore = 100;
      
      // Basic unit info (40%)
      if (unit.beds > 0) unitScore += 10;
      if (unit.baths > 0) unitScore += 10;
      if (unit.sqft > 0) unitScore += 10;
      if (unit.rent > 0 || unit.price > 0) unitScore += 10;
      
      // Tags and features (30%)
      if (unit.feature_tags && unit.feature_tags.length > 0) unitScore += 15;
      if (unit.amenities_tags && unit.amenities_tags.length > 0) unitScore += 15;
      
      // Unit images (30%)
      if (unit.img_urls && Array.isArray(unit.img_urls) && unit.img_urls.length > 0) {
        unitScore += 30;
      }
      
      totalScore += (unitScore / maxScore) * 100;
    });

    return Math.round(totalScore / units.length);
  }

  /**
   * Calculate demographics completion score
   */
  private static async calculateDemographicsCompletion(supabase: any, propertyIds: string[]): Promise<number> {
    const { data: demographics } = await supabase
      .from('demographics')
      .select('population_2020_count, median_household_income, crime_total_risk')
      .in('prop_id', propertyIds);

    if (!demographics || demographics.length === 0) return 0;

    let totalScore = 0;
    
    demographics.forEach((demo: any) => {
      let demoScore = 0;
      
      if (demo.population_2020_count) demoScore += 33;
      if (demo.median_household_income) demoScore += 34;
      if (demo.crime_total_risk) demoScore += 33;
      
      totalScore += demoScore;
    });

    return Math.round(totalScore / demographics.length);
  }

  /**
   * Calculate tax history completion score
   */
  private static async calculateTaxHistoryCompletion(supabase: any, propertyIds: string[]): Promise<number> {
    const { data: taxHistory } = await supabase
      .from('prop_tax_history')
      .select('assessed_tax_year, assessed_value_total, tax_bill_amount')
      .in('prop_id', propertyIds);

    if (!taxHistory || taxHistory.length === 0) return 0;

    // Group by property and check if each property has at least 3 years of data
    const propertiesWithTaxData = new Set(taxHistory.map((tax: any) => tax.prop_id));
    const completionPercentage = (propertiesWithTaxData.size / propertyIds.length) * 100;
    
    return Math.round(completionPercentage);
  }

  /**
   * Calculate financials completion score
   */
  private static async calculateFinancialsCompletion(supabase: any, propertyIds: string[]): Promise<number> {
    const { data: financials } = await supabase
      .from('prop_financials')
      .select('rental_income, property_tax, insurance, net_operating_income')
      .in('prop_id', propertyIds);

    if (!financials || financials.length === 0) return 0;

    let totalScore = 0;
    const requiredFields = ['rental_income', 'property_tax', 'insurance', 'net_operating_income'];
    
    financials.forEach((financial: any) => {
      let financialScore = 0;
      
      requiredFields.forEach(field => {
        if (financial[field] && financial[field] > 0) {
          financialScore += 25; // 25% per required field
        }
      });
      
      totalScore += financialScore;
    });

    return Math.round(totalScore / financials.length);
  }

  /**
   * Calculate documents completion score
   */
  private static async calculateDocumentsCompletion(supabase: any, propertyIds: string[]): Promise<number> {
    const { data: documents } = await supabase
      .from('documents')
      .select('property_id, type')
      .in('property_id', propertyIds)
      .eq('is_deleted', false);

    if (!documents || documents.length === 0) return 0;

    // Check if each property has at least one document
    const propertiesWithDocs = new Set(documents.map((doc: any) => doc.property_id));
    const completionPercentage = (propertiesWithDocs.size / propertyIds.length) * 100;
    
    return Math.round(completionPercentage);
  }

  /**
   * Calculate individual property statistics with completion scores
   */
  static async calculatePropertiesStats(portfolioId: string): Promise<PropertyStats[]> {
    const supabase = createClient();

    try {
      // Get all properties with their address information
      const { data: properties, error: propError } = await supabase
        .from('prop')
        .select(`
          id, 
          address_id, 
          ai_summary, 
          main_img_url, 
          img_urls,
          prop_addresses (
            address,
            city,
            state,
            zip
          )
        `)
        .eq('portfolio_id', portfolioId)
        .eq('is_deleted', false);

      if (propError || !properties) {
        console.error('Error fetching properties:', propError);
        return [];
      }

      // Calculate stats for each property
      const propertyStatsPromises = properties.map(async (property) => {
        const propertyId = property.id;
        const addressInfo = Array.isArray(property.prop_addresses) ? property.prop_addresses[0] : property.prop_addresses;

        // Get unit count for this property
        const { data: units } = await supabase
          .from('prop_units')
          .select('id')
          .eq('prop_id', propertyId);

        const totalUnits = units?.length || 0;

        const { data: documents } = await supabase
          .from('documents')
          .select('id')
          .eq('property_id', propertyId)
          .eq('is_deleted', false);

        const documentsCount = documents?.length || 0;

        // Calculate completion scores for this specific property
        const sectionsCompleted = await this.calculatePropertySectionCompletions(
          supabase, 
          property, 
          propertyId
        );

        // Calculate overall completion score (weighted average)
        const weights = {
          basicInfo: 0.2,
          financials: 0.25,
          units: 0.2,
          demographics: 0.15,
          taxHistory: 0.1,
          documents: 0.1
        };

        const overallCompletion = Object.entries(sectionsCompleted).reduce((acc, [key, value]) => {
          const weight = weights[key as keyof typeof weights] || 0;
          return acc + (value * weight);
        }, 0);

        return {
          id: propertyId,
          address: addressInfo?.address || '',
          city: addressInfo?.city || '',
          state: addressInfo?.state || '',
          zip: addressInfo?.zip || '',
          main_img_url: property.main_img_url,
          ai_summary: property.ai_summary,
          totalUnits,
          documentsCount,
          completionScore: Math.round(overallCompletion),
          sectionsCompleted
        };
      });

      return await Promise.all(propertyStatsPromises);

    } catch (error) {
      console.error('Error calculating properties stats:', error);
      return [];
    }
  }

  /**
   * Calculate completion scores for a single property
   */
  private static async calculatePropertySectionCompletions(
    supabase: any, 
    property: any, 
    propertyId: string
  ): Promise<PropertyStats['sectionsCompleted']> {
    
    // Basic Info completion
    const basicInfoScore = this.calculateSinglePropertyBasicInfo(property);

    // Units completion
    const unitsScore = await this.calculateSinglePropertyUnits(supabase, propertyId);

    // Demographics completion
    const demographicsScore = await this.calculateSinglePropertyDemographics(supabase, propertyId);

    // Tax History completion
    const taxHistoryScore = await this.calculateSinglePropertyTaxHistory(supabase, propertyId);

    // Financials completion
    const financialsScore = await this.calculateSinglePropertyFinancials(supabase, propertyId);

    // Documents completion
    const documentsScore = await this.calculateSinglePropertyDocuments(supabase, propertyId);

    return {
      basicInfo: basicInfoScore,
      financials: financialsScore,
      units: unitsScore,
      demographics: demographicsScore,
      taxHistory: taxHistoryScore,
      documents: documentsScore
    };
  }

  /**
   * Calculate basic info completion for a single property
   */
  private static calculateSinglePropertyBasicInfo(property: any): number {
    let score = 0;
    
    // Has main image (25%)
    if (property.main_img_url) score += 25;
    
    // Has additional images (25%)
    if (property.img_urls && Array.isArray(property.img_urls) && property.img_urls.length > 0) {
      score += 25;
    }
    
    // Has AI summary (50%)
    if (property.ai_summary && (
      typeof property.ai_summary === 'string' ? property.ai_summary.length > 0 : 
      property.ai_summary.summary || property.ai_summary.pros_cons || property.ai_summary.poi_summary
    )) score += 50;
    
    return score;
  }

  /**
   * Calculate units completion for a single property
   */
  private static async calculateSinglePropertyUnits(supabase: any, propertyId: string): Promise<number> {
    const { data: units } = await supabase
      .from('prop_units')
      .select('beds, baths, sqft, rent, price, feature_tags, amenities_tags, img_urls')
      .eq('prop_id', propertyId);

    if (!units || units.length === 0) return 0;

    let totalScore = 0;
    
    units.forEach((unit: any) => {
      let unitScore = 0;
      
      // Basic unit info (40%)
      if (unit.beds > 0) unitScore += 10;
      if (unit.baths > 0) unitScore += 10;
      if (unit.sqft > 0) unitScore += 10;
      if (unit.rent > 0 || unit.price > 0) unitScore += 10;
      
      // Tags and features (30%)
      if (unit.feature_tags && unit.feature_tags.length > 0) unitScore += 15;
      if (unit.amenities_tags && unit.amenities_tags.length > 0) unitScore += 15;
      
      // Unit images (30%)
      if (unit.img_urls && Array.isArray(unit.img_urls) && unit.img_urls.length > 0) {
        unitScore += 30;
      }
      
      totalScore += unitScore;
    });

    return Math.round(totalScore / units.length);
  }

  /**
   * Calculate demographics completion for a single property
   */
  private static async calculateSinglePropertyDemographics(supabase: any, propertyId: string): Promise<number> {
    const { data: demographics } = await supabase
      .from('demographics')
      .select('population_2020_count, median_household_income, crime_total_risk')
      .eq('prop_id', propertyId)
      .single();

    if (!demographics) return 0;

    let score = 0;
    if (demographics.population_2020_count) score += 33;
    if (demographics.median_household_income) score += 34;
    if (demographics.crime_total_risk) score += 33;
    
    return score;
  }

  /**
   * Calculate tax history completion for a single property
   */
  private static async calculateSinglePropertyTaxHistory(supabase: any, propertyId: string): Promise<number> {
    const { data: taxHistory } = await supabase
      .from('prop_tax_history')
      .select('assessed_tax_year, assessed_value_total, tax_bill_amount')
      .eq('prop_id', propertyId);

    if (!taxHistory || taxHistory.length === 0) return 0;

    // Check if we have at least 3 years of data
    return taxHistory.length >= 3 ? 100 : Math.round((taxHistory.length / 3) * 100);
  }

  /**
   * Calculate financials completion for a single property
   */
  private static async calculateSinglePropertyFinancials(supabase: any, propertyId: string): Promise<number> {
    const { data: financials } = await supabase
      .from('prop_financials')
      .select('rental_income, property_tax, insurance, net_operating_income')
      .eq('prop_id', propertyId)
      .single();

    if (!financials) return 0;

    let score = 0;
    const requiredFields = ['rental_income', 'property_tax', 'insurance', 'net_operating_income'];
    
    requiredFields.forEach(field => {
      if (financials[field] && financials[field] > 0) {
        score += 25; // 25% per required field
      }
    });
    
    return score;
  }

  /**
   * Calculate documents completion for a single property
   */
  private static async calculateSinglePropertyDocuments(supabase: any, propertyId: string): Promise<number> {
    const { data: documents } = await supabase
      .from('documents')
      .select('id')
      .eq('property_id', propertyId)
      .eq('is_deleted', false);

    return documents && documents.length > 0 ? 100 : 0;
  }

  /**
   * Return empty stats structure
   */
  private static getEmptyStats(): PortfolioStats {
    return {
      totalProperties: 0,
      totalUnits: 0,
      completionScore: 0,
      sectionsCompleted: {
        basicInfo: 0,
        financials: 0,
        units: 0,
        demographics: 0,
        taxHistory: 0,
        documents: 0
      },
      averageCompletionBySection: {
        basicInfo: 0,
        financials: 0,
        units: 0,
        demographics: 0,
        taxHistory: 0,
        documents: 0
      }
    };
  }
} 