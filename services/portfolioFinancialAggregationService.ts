import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';

export interface PortfolioFinancialAggregation {
  portfolio_id: string;
  year: number;
  rental_income: number;
  long_term_rental: number;
  short_term_rental: number;
  other_income: number;
  vacancy_loss: number;
  credit_loss: number;
  effective_gross_income: number;
  property_tax: number;
  insurance: number;
  repairs: number;
  maintenance: number;
  professional_fees: number;
  management_fees: number;
  leasing_fees: number;
  legal_fees: number;
  accounting_fees: number;
  engineering_fees: number;
  marketing_fees: number;
  consulting_fees: number;
  utilities: number;
  services: number;
  reserves: number;
  total_operating_expenses: number;
  net_operating_income: number;
  annual_debt_service: number;
  dscr: number;
  cash_flow_before_taxes: number;
  cash_flow_after_taxes: number;
  cumulative_cash_flow: number;
  cap_rate: number;
  gross_rent_multiplier: number;
  equity_multiple: number;
  cash_on_cash_return: number;
  total_acquisition_cost: number;
  aggregated_noi: number;
  blended_cap_rate: number;
  portfolio_irr: number;
  property_count: number;
}

export class PortfolioFinancialAggregationService {
  /**
   * Aggregate financial data from all properties in a portfolio for a specific year
   */
  static async aggregatePortfolioFinancials(portfolioId: string, year: number): Promise<PortfolioFinancialAggregation> {
    const supabase = await createClient();

    // 1. Get all properties in the portfolio
    const { data: properties, error: propError } = await supabase
      .from('prop')
      .select('id, address_id')
      .eq('portfolio_id', portfolioId)
      .eq('is_deleted', false);

    if (propError) {
      throw new Error(`Error fetching portfolio properties: ${propError.message}`);
    }

    if (!properties || properties.length === 0) {
      return this.getEmptyAggregation(portfolioId, year, 0);
    }

    // 2. Get financial data for all properties for the specified year
    const propertyIds = properties.map(p => p.id);
    const { data: financials, error: financialsError } = await supabase
      .from('prop_financials')
      .select('*')
      .in('prop_id', propertyIds)
      .eq('year', year);

    if (financialsError) {
      throw new Error(`Error fetching property financials: ${financialsError.message}`);
    }

    // 3. Aggregate the financial data
    return this.calculateAggregation(portfolioId, year, financials || [], properties.length);
  }

  /**
   * Aggregate financial data from all properties in a portfolio for all years (1-5)
   */
  static async aggregateAllYears(portfolioId: string): Promise<PortfolioFinancialAggregation[]> {
    const years = [1, 2, 3, 4, 5];
    const aggregations: PortfolioFinancialAggregation[] = [];

    for (const year of years) {
      try {
        const aggregation = await this.aggregatePortfolioFinancials(portfolioId, year);
        aggregations.push(aggregation);
      } catch (error) {
        console.error(`Error aggregating year ${year}:`, error);
        // Add empty aggregation for this year to maintain consistency
        aggregations.push(this.getEmptyAggregation(portfolioId, year, 0));
      }
    }

    return aggregations;
  }

  /**
   * Update portfolio financial records with aggregated data
   */
  static async updatePortfolioFinancials(portfolioId: string, year?: number): Promise<void> {
    const supabase = await createClient();
    const years = year ? [year] : [1, 2, 3, 4, 5];

    for (const yearValue of years) {
      try {
        const aggregation = await this.aggregatePortfolioFinancials(portfolioId, yearValue);
        
        // Check if a record already exists for this portfolio and year
        const { data: existingRecord, error: fetchError } = await supabase
          .from('portfolio_financials')
          .select('id')
          .eq('portfolio_id', portfolioId)
          .eq('year', yearValue)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') {
          // PGRST116 is "not found" error, which is expected for new records
          throw new Error(`Error checking existing portfolio financials: ${fetchError.message}`);
        }

        // Prepare the data
        const financialData = {
          portfolio_id: portfolioId,
          year: yearValue,
          // Income fields
          rental_income: aggregation.rental_income,
          long_term_rental: aggregation.long_term_rental,
          short_term_rental: aggregation.short_term_rental,
          other_income: aggregation.other_income,
          vacancy_loss: aggregation.vacancy_loss,
          credit_loss: aggregation.credit_loss,
          effective_gross_income: aggregation.effective_gross_income,
          // Expense fields - now including all detailed fees
          property_tax: aggregation.property_tax,
          insurance: aggregation.insurance,
          repairs: aggregation.repairs,
          maintenance: aggregation.maintenance,
          professional_fees: aggregation.professional_fees,
          management_fees: aggregation.management_fees,
          leasing_fees: aggregation.leasing_fees,
          legal_fees: aggregation.legal_fees,
          accounting_fees: aggregation.accounting_fees,
          engineering_fees: aggregation.engineering_fees,
          marketing_fees: aggregation.marketing_fees,
          consulting_fees: aggregation.consulting_fees,
          utilities: aggregation.utilities,
          services: aggregation.services,
          reserves: aggregation.reserves,
          total_operating_expenses: aggregation.total_operating_expenses,
          // Calculated fields
          net_operating_income: aggregation.net_operating_income,
          annual_debt_service: aggregation.annual_debt_service,
          dscr: aggregation.dscr,
          cash_flow_before_taxes: aggregation.cash_flow_before_taxes,
          cash_flow_after_taxes: aggregation.cash_flow_after_taxes,
          cumulative_cash_flow: aggregation.cumulative_cash_flow,
          cap_rate: aggregation.cap_rate,
          gross_rent_multiplier: aggregation.gross_rent_multiplier,
          equity_multiple: aggregation.equity_multiple,
          cash_on_cash_return: aggregation.cash_on_cash_return,
          total_acquisition_cost: aggregation.total_acquisition_cost,
          aggregated_noi: aggregation.aggregated_noi,
          blended_cap_rate: aggregation.blended_cap_rate,
          portfolio_irr: aggregation.portfolio_irr,
          updated_at: new Date().toISOString(),
          metadata: {}
        };

        let error;
        
        if (existingRecord) {
          // Update existing record
          const updateResult = await supabase
            .from('portfolio_financials')
            .update(financialData)
            .eq('portfolio_id', portfolioId)
            .eq('year', yearValue);
          error = updateResult.error;
        } else {
          // Insert new record
          const insertData = {
            id: uuidv4(),
            ...financialData,
            created_at: new Date().toISOString()
          };
          const insertResult = await supabase
            .from('portfolio_financials')
            .insert(insertData);
          error = insertResult.error;
        }

        if (error) {
          throw new Error(`Error ${existingRecord ? 'updating' : 'inserting'} portfolio financials for year ${yearValue}: ${error.message}`);
        }
        
        console.log(`Successfully ${existingRecord ? 'updated' : 'created'} portfolio financials for year ${yearValue}`);
      } catch (error) {
        console.error(`Error updating portfolio financials for year ${yearValue}:`, error);
        throw error;
      }
    }
  }

  /**
   * Calculate aggregated financial metrics from property financial data
   */
  private static calculateAggregation(
    portfolioId: string, 
    year: number, 
    financials: any[], 
    propertyCount: number
  ): PortfolioFinancialAggregation {
    
    const safeSum = (field: string): number => {
      return financials.reduce((sum, f) => {
        const value = parseFloat(f[field]) || 0;
        return sum + value;
      }, 0);
    };

    // Sum all the financial fields
    const rental_income = safeSum('rental_income');
    const long_term_rental = safeSum('long_term_rental');
    const short_term_rental = safeSum('short_term_rental');
    const other_income = safeSum('other_income');
    const vacancy_loss = safeSum('vacancy_loss');
    const credit_loss = safeSum('credit_loss');
    
    // Calculate derived income metrics
    const effective_gross_income = rental_income + other_income - Math.abs(vacancy_loss) - Math.abs(credit_loss);
    
    // Sum all expense fields - now including all detailed fees
    const property_tax = safeSum('property_tax');
    const insurance = safeSum('insurance');
    const repairs = safeSum('repairs');
    const maintenance = safeSum('maintenance');
    const professional_fees = safeSum('professional_fees');
    const management_fees = safeSum('management_fees');
    const leasing_fees = safeSum('leasing_fees');
    const legal_fees = safeSum('legal_fees');
    const accounting_fees = safeSum('accounting_fees');
    const engineering_fees = safeSum('engineering_fees');
    const marketing_fees = safeSum('marketing_fees');
    const consulting_fees = safeSum('consulting_fees');
    const utilities = safeSum('utilities');
    const services = safeSum('services');
    const reserves = safeSum('reserves');
    
    // Calculate total operating expenses - now including all detailed fees
    const total_operating_expenses = property_tax + insurance + repairs + 
      maintenance + professional_fees + management_fees + leasing_fees + 
      legal_fees + accounting_fees + engineering_fees + marketing_fees + 
      consulting_fees + utilities + services + reserves;
    
    // Calculate NOI
    const net_operating_income = effective_gross_income - total_operating_expenses;
    
    // Sum debt service and cash flow metrics
    const annual_debt_service = safeSum('annual_debt_service');
    const cash_flow_before_taxes = net_operating_income - annual_debt_service;
    const cash_flow_after_taxes = safeSum('cash_flow_after_taxes');
    
    // Calculate cumulative cash flow (simplified)
    const cumulative_cash_flow = cash_flow_before_taxes;
    
    // Calculate ratios and metrics
    const total_acquisition_cost = safeSum('total_acquisition_cost') || this.estimateAcquisitionCost(financials);
    const aggregated_noi = net_operating_income;
    
    // Calculate weighted averages for ratios
    const dscr = annual_debt_service > 0 ? net_operating_income / annual_debt_service : 0;
    const cap_rate = total_acquisition_cost > 0 ? (net_operating_income / total_acquisition_cost) * 100 : 0;
    const blended_cap_rate = cap_rate;
    const gross_rent_multiplier = rental_income > 0 ? total_acquisition_cost / rental_income : 0;
    const equity_multiple = safeSum('equity_multiple') / Math.max(propertyCount, 1);
    const cash_on_cash_return = total_acquisition_cost > 0 ? (cash_flow_before_taxes / total_acquisition_cost) * 100 : 0;
    const portfolio_irr = safeSum('portfolio_irr') / Math.max(propertyCount, 1);

    return {
      portfolio_id: portfolioId,
      year,
      rental_income,
      long_term_rental,
      short_term_rental,
      other_income,
      vacancy_loss,
      credit_loss,
      effective_gross_income,
      property_tax,
      insurance,
      repairs,
      maintenance,
      professional_fees,
      management_fees,
      leasing_fees,
      legal_fees,
      accounting_fees,
      engineering_fees,
      marketing_fees,
      consulting_fees,
      utilities,
      services,
      reserves,
      total_operating_expenses,
      net_operating_income,
      annual_debt_service,
      dscr,
      cash_flow_before_taxes,
      cash_flow_after_taxes,
      cumulative_cash_flow,
      cap_rate,
      gross_rent_multiplier,
      equity_multiple,
      cash_on_cash_return,
      total_acquisition_cost,
      aggregated_noi,
      blended_cap_rate,
      portfolio_irr,
      property_count: propertyCount
    };
  }

  /**
   * Get empty aggregation structure for portfolios with no properties or financial data
   */
  private static getEmptyAggregation(portfolioId: string, year: number, propertyCount: number): PortfolioFinancialAggregation {
    return {
      portfolio_id: portfolioId,
      year,
      rental_income: 0,
      long_term_rental: 0,
      short_term_rental: 0,
      other_income: 0,
      vacancy_loss: 0,
      credit_loss: 0,
      effective_gross_income: 0,
      property_tax: 0,
      insurance: 0,
      repairs: 0,
      maintenance: 0,
      professional_fees: 0,
      management_fees: 0,
      leasing_fees: 0,
      legal_fees: 0,
      accounting_fees: 0,
      engineering_fees: 0,
      marketing_fees: 0,
      consulting_fees: 0,
      utilities: 0,
      services: 0,
      reserves: 0,
      total_operating_expenses: 0,
      net_operating_income: 0,
      annual_debt_service: 0,
      dscr: 0,
      cash_flow_before_taxes: 0,
      cash_flow_after_taxes: 0,
      cumulative_cash_flow: 0,
      cap_rate: 0,
      gross_rent_multiplier: 0,
      equity_multiple: 0,
      cash_on_cash_return: 0,
      total_acquisition_cost: 0,
      aggregated_noi: 0,
      blended_cap_rate: 0,
      portfolio_irr: 0,
      property_count: propertyCount
    };
  }

  /**
   * Estimate acquisition cost if not available in the data
   */
  private static estimateAcquisitionCost(financials: any[]): number {
    return financials.reduce((sum, f) => {
      const noi = parseFloat(f.net_operating_income) || 0;
      const capRate = parseFloat(f.cap_rate) || 0;
      if (noi > 0 && capRate > 0) {
        return sum + (noi / (capRate / 100));
      }
      return sum;
    }, 0);
  }
} 