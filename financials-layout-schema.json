{"financialSections": {"property": [{"title": "Income Projections", "items": [{"key": "rental_income", "label": "Gross Scheduled Income", "format": "currency", "edit": {"isCalculated": true, "isAICalculation": false}, "expandable": true, "subItems": [{"key": "long_term_rental", "label": "Long Term Rental", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "short_term_rental", "label": "Short Term Rental", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"key": "other_income", "label": "Other Income", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": true}}, {"key": "vacancy_loss", "label": "Vacancy Loss", "format": "currency", "negative": true, "edit": {"isCalculated": true, "isAICalculation": true}}, {"key": "credit_loss", "label": "Credit Loss", "format": "currency", "negative": true, "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "effective_gross_income", "label": "Effective Gross Income", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Expense Projections", "items": [{"key": "property_tax", "label": "Property Taxes", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "insurance", "label": "Insurance", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "repairs", "label": "Repairs", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "maintenance", "label": "Maintenance", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "professional_fees", "label": "Professional Fees", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}, "expandable": true, "subItems": [{"key": "management_fees", "label": "Management", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "leasing_fees", "label": "Leasing", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "legal_fees", "label": "Legal", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "accounting_fees", "label": "Accounting", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "engineering_fees", "label": "Engineering/Inspections", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "marketing_fees", "label": "Marketing/Advertising", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "consulting_fees", "label": "Consulting", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"key": "utilities", "label": "Utilities", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "services", "label": "Services", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "reserves", "label": "Reserves for Replacements", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_operating_expenses", "label": "Total Operating Expenses", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Net Operating Income (NOI)", "items": [{"key": "net_operating_income", "label": "Net Operating Income (NOI)", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Financing & Debt Service", "items": [{"key": "annual_debt_service", "label": "Annual Debt Service (P&I)", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "dscr", "label": "DSCR (NOI ÷ Debt Service)", "format": "multiplier", "isCalculated": true}]}, {"title": "Cash Flow Analysis", "items": [{"key": "cash_flow_before_taxes", "label": "Cash Flow Before Taxes", "format": "currency", "isSectionTotal": true, "isCalculated": true}, {"key": "cash_flow_after_taxes", "label": "Cash Flow After Taxes", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "cumulative_cash_flow", "label": "Cumulative Cash Flow", "format": "currency", "isCalculated": true}]}, {"title": "Valuation Metrics", "items": [{"key": "cap_rate", "label": "Cap Rate (%)", "format": "percentage", "isCalculated": true}, {"key": "gross_rent_multiplier", "label": "GRM (×)", "format": "multiplier", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "equity_multiple", "label": "Equity Multiple (×)", "format": "multiplier", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "cash_on_cash_return", "label": "Cash-on-Cash Return (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"title": "Portfolio Summary & Roll-Up", "items": [{"key": "total_acquisition_cost", "label": "Total Acquisition Cost", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "aggregated_noi", "label": "Aggregated NOI", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "blended_cap_rate", "label": "Blended Cap Rate (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "portfolio_irr", "label": "Portfolio IRR (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}]}], "portfolio": [{"title": "Income Projections", "items": [{"key": "total_rental_income", "label": "Gross Scheduled Income", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}, "expandable": true, "subItems": [{"key": "total_long_term_rental", "label": "Long Term Rental", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_short_term_rental", "label": "Short Term Rental", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"key": "total_other_income", "label": "Other Income", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_vacancy_loss", "label": "Vacancy Loss", "format": "currency", "negative": true, "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_credit_loss", "label": "Credit Loss", "format": "currency", "negative": true, "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "effective_gross_income", "label": "Effective Gross Income", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Expense Projections", "items": [{"key": "total_property_tax", "label": "Property Taxes", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_insurance", "label": "Insurance", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_repairs", "label": "Repairs", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_maintenance", "label": "Maintenance", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_professional_fees", "label": "Professional Fees", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}, "expandable": true, "subItems": [{"key": "total_management_fees", "label": "Management", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_leasing_fees", "label": "Leasing", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_legal_fees", "label": "Legal", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_accounting_fees", "label": "Accounting", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_engineering_fees", "label": "Engineering/Inspections", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_marketing_fees", "label": "Marketing/Advertising", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_consulting_fees", "label": "Consulting", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"key": "total_utilities", "label": "Utilities", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_services", "label": "Services", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_reserves", "label": "Reserves for Replacements", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "total_operating_expenses", "label": "Total Operating Expenses", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Net Operating Income (NOI)", "items": [{"key": "net_operating_income", "label": "Net Operating Income (NOI)", "format": "currency", "isSectionTotal": true, "isCalculated": true}]}, {"title": "Financing & Debt Service", "items": [{"key": "annual_debt_service", "label": "Annual Debt Service (P&I)", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "dscr", "label": "DSCR (NOI ÷ Debt Service)", "format": "multiplier", "isCalculated": true}]}, {"title": "Cash Flow Analysis", "items": [{"key": "cash_flow_before_taxes", "label": "Cash Flow Before Taxes", "format": "currency", "isSectionTotal": true, "isCalculated": true}, {"key": "cash_flow_after_taxes", "label": "Cash Flow After Taxes", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "cumulative_cash_flow", "label": "Cumulative Cash Flow", "format": "currency", "isCalculated": true}]}, {"title": "Valuation Metrics", "items": [{"key": "cap_rate", "label": "Cap Rate (%)", "format": "percentage", "isCalculated": true}, {"key": "gross_rent_multiplier", "label": "GRM (×)", "format": "multiplier", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "equity_multiple", "label": "Equity Multiple (×)", "format": "multiplier", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "cash_on_cash_return", "label": "Cash-on-Cash Return (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}]}, {"title": "Portfolio Summary & Roll-Up", "items": [{"key": "total_acquisition_cost", "label": "Total Acquisition Cost", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "aggregated_noi", "label": "Aggregated NOI", "format": "currency", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "blended_cap_rate", "label": "Blended Cap Rate (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}, {"key": "portfolio_irr", "label": "Portfolio IRR (%)", "format": "percentage", "edit": {"isCalculated": false, "isAICalculation": false}}]}]}, "schema": {"FinancialSectionItem": {"type": "object", "properties": {"key": {"type": "string", "description": "Unique identifier for the financial item"}, "label": {"type": "string", "description": "Display label for the financial item"}, "format": {"type": "string", "enum": ["currency", "percentage", "multiplier"], "description": "Data format for display and calculation"}, "edit": {"type": "object", "properties": {"isCalculated": {"type": "boolean", "description": "Whether the value is automatically calculated"}, "isAICalculation": {"type": "boolean", "description": "Whether the value uses AI for calculation"}}}, "negative": {"type": "boolean", "description": "Whether this item should be subtracted in calculations"}, "isSectionTotal": {"type": "boolean", "description": "Whether this item represents a section total"}, "isCalculated": {"type": "boolean", "description": "Whether this value is automatically calculated"}, "expandable": {"type": "boolean", "description": "Whether this item can be expanded to show sub-items"}, "subItems": {"type": "array", "items": {"$ref": "#/schema/FinancialSectionItem"}, "description": "Sub-items that appear when expanded"}}, "required": ["key", "label", "format"]}, "FinancialSection": {"type": "object", "properties": {"title": {"type": "string", "description": "Section title"}, "items": {"type": "array", "items": {"$ref": "#/schema/FinancialSectionItem"}, "description": "Financial items in this section"}}, "required": ["title", "items"]}}, "timeframe": {"years": [1, 2, 3, 4, 5], "description": "5-year financial projection timeframe"}, "uiFeatures": {"expandableRows": {"description": "Rows with sub-items can be expanded/collapsed", "indicator": "Chevron icon that rotates on expand"}, "sectionContainers": {"description": "Each section is in its own container", "styling": "15px border radius, shadow, spacing"}, "calculationLogic": {"description": "Automatic calculation of section totals and dependent values", "subItemHandling": "When items have sub-items, sub-items are summed instead of parent value"}}}