{"name": "relm-pro", "version": "0.1.0", "private": true, "scripts": {"dev": "FAST_REFRESH=true next dev --turbopack --port 3001", "dev:polling": "FAST_REFRESH=true CHOKIDAR_USEPOLLING=true next dev --turbopack --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "trigger-news-preload": "node scripts/trigger-news-preload.js", "trigger-news-preload:prod": "node scripts/trigger-news-preload.js production"}, "dependencies": {"@fancyapps/ui": "^5.0.36", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@react-google-maps/api": "^2.20.6", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf/renderer": "^4.3.0", "@sendgrid/mail": "^8.1.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/cheerio": "^0.22.35", "@types/node-cron": "^3.0.11", "@types/redis": "^4.0.10", "axios": "^1.8.4", "canvas": "^3.1.0", "chart.js": "^4.4.9", "cheerio": "^1.0.0", "cookies-next": "^6.0.0", "framer-motion": "^12.14.0", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.487.0", "next": "15.2.4", "node-cron": "^4.0.7", "openai": "^4.104.0", "pg": "^8.16.0", "pnpm": "^10.7.1", "primereact": "^10.9.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-google-autocomplete": "^2.7.5", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "recharts": "^2.15.3", "redis": "^5.1.0", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "styled-components": "^6.1.17", "swiper": "^11.2.6", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.2.4", "supabase": "^2.24.3", "tailwindcss": "^4", "typescript": "^5"}}