const apiUrl = (path: string) => `${process.env.NEXT_PUBLIC_API_DOMAIN}${path}`;

const apiEndpointsServer = {
	chat: {
        create: apiUrl('chat'),
        histtory: apiUrl('chat'),
        active: apiUrl('chat'),
        list: apiUrl('chats'),
        delete: apiUrl('chat'),
        deleteMessages: apiUrl('chat/messages')
    },
    documents: {
        process: apiUrl('documents/process'),
        processStatus: apiUrl('documents/process/status'),
        generateSlides: apiUrl('documents/generate-slides')
    },
    aiSummary: apiUrl('data/prop-ai-summary-short'),
    property: {
        data: apiUrl('property/data/property_data'),
        demographics: apiUrl('property/data/demographics'),
        units: apiUrl('property/data/units'),
        unitData: apiUrl('property/data/unit_data'),
        taxHistory: apiUrl('property/data/tax_history')
    },
    subscription: {
        details: apiUrl('subscription/details'),
        checkout: apiUrl('subscription/checkout'),
        portal: apiUrl('subscription/portal'),
        preview: apiUrl('subscription/preview'),
        confirm: apiUrl('subscription/confirm')
    },
    finance: {
        calculate: apiUrl('finance/calculate'),
        cache: apiUrl('finance/cache'),
        otherIncome: apiUrl('finance/calculate/other_income'),
        vacancyLoss: apiUrl('finance/calculate/vacancy_loss'),
        creditLoss: apiUrl('finance/calculate/credit_loss'),
        longTermRental: apiUrl('finance/calculate/long_term_rental'),
        recalculate: apiUrl('finance/calculate/recalculate'),
        expenseProjections: apiUrl('finance/calculate/overall_expense'),
        expenseProjectionsStatus: (task_token: string)=> apiUrl(`finance/calculate-status/${task_token}`)
    },
    document: {    
        list: apiUrl('documents'),
        delete: (portfolio_id: string, document_id: string) => apiUrl(`documents/${portfolio_id}/${document_id}`)
    }
};

export default apiEndpointsServer;