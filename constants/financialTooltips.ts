interface FinancialTooltip {
    definition: string;
    calculation: string;
    aiAssistance?: string;
}

export const financialTooltips: Record<string, FinancialTooltip> = {
    // Income Projections
    rental_income: {
        definition: "The total gross scheduled rental income from all units before any deductions.",
        calculation: "Sum of all unit rents × 12 months",
        aiAssistance: "AI will analyze market rates and comparable properties to estimate optimal rental income."
    },
    long_term_rental: {
        definition: "Monthly rental income from traditional long-term leases (typically 12+ months).",
        calculation: "Unit rent × number of long-term rental units",
        aiAssistance: "AI will research local market data to suggest competitive long-term rental rates."
    },
    short_term_rental: {
        definition: "Monthly rental income from short-term rentals (Airbnb, VRBO, etc.).",
        calculation: "Daily rate × occupancy rate × days per month",
        aiAssistance: "AI will analyze STR market data, seasonality, and regulations to estimate income potential."
    },
    other_income: {
        definition: "Additional income from parking, laundry, pet fees, storage, or other property-related sources.",
        calculation: "Sum of all non-rental income sources",
        aiAssistance: "AI will identify potential income streams based on property type and local market opportunities."
    },
    vacancy_loss: {
        definition: "Expected loss in rental income due to vacant units between tenants.",
        calculation: "Gross Rental Income × Vacancy Rate %",
        aiAssistance: "AI will analyze local vacancy rates, market conditions, and property type to estimate realistic vacancy losses."
    },
    credit_loss: {
        definition: "Expected loss from tenants who fail to pay rent (bad debt, evictions).",
        calculation: "Gross Rental Income × Credit Loss Rate %",
        aiAssistance: "AI will assess tenant screening processes and local economic conditions to estimate credit risk."
    },
    effective_gross_income: {
        definition: "Total rental income after deducting vacancy and credit losses.",
        calculation: "Gross Scheduled Income - Vacancy Loss - Credit Loss",
        aiAssistance: "AI automatically calculates this based on income and loss projections."
    },

    // Expense Projections
    property_tax: {
        definition: "Annual property taxes owed to local government based on assessed value.",
        calculation: "Assessed Value × Local Tax Rate",
        aiAssistance: "AI will research current tax assessments and rates for accurate projections."
    },
    insurance: {
        definition: "Annual property insurance premiums for fire, liability, and other coverage.",
        calculation: "Property Value × Insurance Rate + Additional Coverage",
        aiAssistance: "AI will analyze property type, location, and risk factors to estimate insurance costs."
    },
    repairs: {
        definition: "Annual costs for fixing broken items and maintaining property condition.",
        calculation: "Historical costs or % of rental income (typically 5-15%)",
        aiAssistance: "AI will assess property age, condition, and maintenance history to project repair needs."
    },
    maintenance: {
        definition: "Ongoing preventive maintenance costs like landscaping, cleaning, and routine upkeep.",
        calculation: "Scheduled maintenance + reactive maintenance costs",
        aiAssistance: "AI will create maintenance schedules and cost estimates based on property type and size."
    },
    professional_fees: {
        definition: "Total fees paid to professionals for property management and services.",
        calculation: "Sum of management, leasing, legal, accounting, and other professional fees",
        aiAssistance: "AI automatically calculates this as the sum of all professional fee categories."
    },
    management_fees: {
        definition: "Fees paid to property management company for day-to-day operations.",
        calculation: "Gross Rental Income × Management Fee % (typically 8-12%)",
        aiAssistance: "AI will research local management fee rates and recommend competitive pricing."
    },
    leasing_fees: {
        definition: "Fees for finding and placing new tenants (marketing, showing, screening).",
        calculation: "Number of turnovers × leasing fee per placement",
        aiAssistance: "AI will estimate turnover rates and local leasing costs based on market data."
    },
    legal_fees: {
        definition: "Annual legal expenses for evictions, contract review, and other legal matters.",
        calculation: "Estimated annual legal expenses based on property type and tenant base",
        aiAssistance: "AI will assess risk factors and typical legal costs for similar properties."
    },
    accounting_fees: {
        definition: "Annual accounting and bookkeeping fees for financial record keeping.",
        calculation: "Monthly accounting fee × 12 months",
        aiAssistance: "AI will estimate appropriate accounting costs based on portfolio complexity."
    },
    engineering_fees: {
        definition: "Annual costs for property inspections, engineering reports, and technical assessments.",
        calculation: "Inspection frequency × cost per inspection",
        aiAssistance: "AI will recommend inspection schedules and estimate costs based on property characteristics."
    },
    marketing_fees: {
        definition: "Annual marketing and advertising costs for attracting tenants.",
        calculation: "Marketing budget + advertising expenses + listing fees",
        aiAssistance: "AI will optimize marketing spend allocation across different channels for maximum ROI."
    },
    consulting_fees: {
        definition: "Annual fees for specialized consulting services (real estate, financial, etc.).",
        calculation: "Estimated annual consulting expenses",
        aiAssistance: "AI will identify areas where consulting might be beneficial and estimate costs."
    },
    utilities: {
        definition: "Property-paid utilities like water, sewer, trash, common area electricity, etc.",
        calculation: "Monthly utility costs × 12 months",
        aiAssistance: "AI will analyze utility usage patterns and local rates to project annual costs."
    },
    services: {
        definition: "Regular service costs like security, pest control, pool maintenance, etc.",
        calculation: "Sum of all contracted services",
        aiAssistance: "AI will identify necessary services and estimate costs based on property amenities and location."
    },
    reserves: {
        definition: "Annual reserves set aside for major repairs and capital expenditures.",
        calculation: "Property Value × Reserve Rate % or fixed annual amount",
        aiAssistance: "AI will assess property age and condition to recommend appropriate reserve levels."
    },
    total_operating_expenses: {
        definition: "Sum of all annual operating expenses for the property.",
        calculation: "Sum of all expense line items above",
        aiAssistance: "AI automatically calculates this as the total of all operating expense categories."
    },

    // Net Operating Income
    net_operating_income: {
        definition: "Property's annual income after operating expenses but before financing costs.",
        calculation: "Effective Gross Income - Total Operating Expenses",
        aiAssistance: "AI automatically calculates NOI, which is the key metric for property valuation and financing."
    },

    // Financing & Debt Service
    annual_debt_service: {
        definition: "Total annual mortgage payments including principal and interest.",
        calculation: "Monthly Payment (P&I) × 12 months",
        aiAssistance: "AI will use current interest rates and loan terms to calculate debt service payments."
    },
    dscr: {
        definition: "Debt Service Coverage Ratio - measures property's ability to cover debt payments.",
        calculation: "Net Operating Income ÷ Annual Debt Service",
        aiAssistance: "AI monitors DSCR to ensure it meets lender requirements (typically 1.2x or higher)."
    },

    // Cash Flow Analysis
    cash_flow_before_taxes: {
        definition: "Annual cash flow available to investor before income taxes.",
        calculation: "Net Operating Income - Annual Debt Service",
        aiAssistance: "AI calculates cash flow and monitors trends to optimize investment performance."
    },
    cash_flow_after_taxes: {
        definition: "Annual cash flow after accounting for income tax implications.",
        calculation: "Cash Flow Before Taxes - Tax Liability + Tax Benefits",
        aiAssistance: "AI will estimate tax impacts including depreciation benefits and local tax rates."
    },
    cumulative_cash_flow: {
        definition: "Running total of cash flows from investment inception.",
        calculation: "Previous Cumulative + Current Year Cash Flow",
        aiAssistance: "AI tracks cumulative performance to identify break-even points and return milestones."
    },

    // Valuation Metrics
    property_value: {
        definition: "Current market value or appraised value of the property used for investment calculations.",
        calculation: "Current Value > Purchase Price > Last Sale Amount",
        aiAssistance: "AI will estimate property value using comparable sales, market trends, and property characteristics."
    },
    cap_rate: {
        definition: "Capitalization rate - measures property's return as a percentage of value.",
        calculation: "Net Operating Income ÷ Property Value × 100",
        aiAssistance: "AI compares cap rates to market benchmarks to assess investment attractiveness."
    },
    gross_rent_multiplier: {
        definition: "Ratio of property price to gross annual rental income.",
        calculation: "Property Purchase Price ÷ Gross Annual Rental Income",
        aiAssistance: "AI benchmarks GRM against comparable properties to evaluate pricing."
    },
    equity_multiple: {
        definition: "Total cash returned to investor divided by initial equity investment.",
        calculation: "(Cumulative Cash Flow + Sale Proceeds) ÷ Initial Equity",
        aiAssistance: "AI projects equity multiples across different holding periods and exit scenarios."
    },
    cash_on_cash_return: {
        definition: "Annual cash return as a percentage of initial cash invested.",
        calculation: "Annual Cash Flow ÷ Initial Cash Investment × 100",
        aiAssistance: "AI calculates and tracks cash-on-cash returns to measure investment efficiency."
    },

    // Portfolio Summary & Roll-Up
    total_acquisition_cost: {
        definition: "Total cost to acquire the property including purchase price and closing costs.",
        calculation: "Purchase Price + Closing Costs + Initial Improvements",
        aiAssistance: "AI tracks all acquisition costs to calculate accurate return metrics."
    },
    aggregated_noi: {
        definition: "Combined Net Operating Income from all properties in the portfolio.",
        calculation: "Sum of NOI from all properties",
        aiAssistance: "AI aggregates NOI across portfolio to provide comprehensive performance view."
    },
    blended_cap_rate: {
        definition: "Weighted average cap rate across all properties in the portfolio.",
        calculation: "Total Portfolio NOI ÷ Total Portfolio Value × 100",
        aiAssistance: "AI calculates blended metrics to assess overall portfolio performance and risk."
    },
    portfolio_irr: {
        definition: "Internal Rate of Return for the entire portfolio over the investment period.",
        calculation: "IRR calculation based on cash flows and portfolio value changes",
        aiAssistance: "AI performs complex IRR calculations considering all cash flows, appreciation, and timing."
    },

    // Portfolio level items (total_ prefixed)
    total_rental_income: {
        definition: "Combined gross scheduled rental income from all portfolio properties.",
        calculation: "Sum of rental income from all properties",
        aiAssistance: "AI aggregates rental income across all properties for portfolio-level analysis."
    },
    total_long_term_rental: {
        definition: "Combined long-term rental income from all portfolio properties.",
        calculation: "Sum of long-term rental income from all properties",
        aiAssistance: "AI tracks long-term rental performance across the entire portfolio."
    },
    total_short_term_rental: {
        definition: "Combined short-term rental income from all portfolio properties.",
        calculation: "Sum of short-term rental income from all properties",
        aiAssistance: "AI monitors STR performance and optimization opportunities across portfolio."
    },
    total_other_income: {
        definition: "Combined other income from all portfolio properties.",
        calculation: "Sum of other income from all properties",
        aiAssistance: "AI identifies and tracks additional income opportunities across portfolio."
    },
    total_vacancy_loss: {
        definition: "Combined vacancy losses from all portfolio properties.",
        calculation: "Sum of vacancy losses from all properties",
        aiAssistance: "AI monitors vacancy trends and suggests strategies to minimize losses."
    },
    total_credit_loss: {
        definition: "Combined credit losses from all portfolio properties.",
        calculation: "Sum of credit losses from all properties",
        aiAssistance: "AI tracks credit performance and suggests tenant screening improvements."
    },
    total_property_tax: {
        definition: "Combined property taxes from all portfolio properties.",
        calculation: "Sum of property taxes from all properties",
        aiAssistance: "AI monitors tax assessments and identifies potential appeal opportunities."
    },
    total_insurance: {
        definition: "Combined insurance costs from all portfolio properties.",
        calculation: "Sum of insurance costs from all properties",
        aiAssistance: "AI analyzes portfolio insurance needs and identifies potential cost savings."
    },
    total_repairs: {
        definition: "Combined repair costs from all portfolio properties.",
        calculation: "Sum of repair costs from all properties",
        aiAssistance: "AI tracks repair patterns and suggests preventive maintenance strategies."
    },
    total_maintenance: {
        definition: "Combined maintenance costs from all portfolio properties.",
        calculation: "Sum of maintenance costs from all properties",
        aiAssistance: "AI optimizes maintenance schedules and identifies cost reduction opportunities."
    },
    total_professional_fees: {
        definition: "Combined professional fees from all portfolio properties.",
        calculation: "Sum of professional fees from all properties",
        aiAssistance: "AI aggregates professional costs and identifies potential economies of scale."
    },
    total_management_fees: {
        definition: "Combined management fees from all portfolio properties.",
        calculation: "Sum of management fees from all properties",
        aiAssistance: "AI analyzes management performance and suggests optimization strategies."
    },
    total_leasing_fees: {
        definition: "Combined leasing fees from all portfolio properties.",
        calculation: "Sum of leasing fees from all properties",
        aiAssistance: "AI tracks leasing efficiency and suggests improvements to reduce costs."
    },
    total_legal_fees: {
        definition: "Combined legal fees from all portfolio properties.",
        calculation: "Sum of legal fees from all properties",
        aiAssistance: "AI monitors legal trends and suggests risk mitigation strategies."
    },
    total_accounting_fees: {
        definition: "Combined accounting fees from all portfolio properties.",
        calculation: "Sum of accounting fees from all properties",
        aiAssistance: "AI optimizes accounting processes and identifies automation opportunities."
    },
    total_engineering_fees: {
        definition: "Combined engineering fees from all portfolio properties.",
        calculation: "Sum of engineering fees from all properties",
        aiAssistance: "AI schedules inspections efficiently and tracks property condition trends."
    },
    total_marketing_fees: {
        definition: "Combined marketing fees from all portfolio properties.",
        calculation: "Sum of marketing fees from all properties",
        aiAssistance: "AI optimizes marketing spend allocation across portfolio properties."
    },
    total_consulting_fees: {
        definition: "Combined consulting fees from all portfolio properties.",
        calculation: "Sum of consulting fees from all properties",
        aiAssistance: "AI identifies consulting needs and manages portfolio-wide advisory relationships."
    },
    total_utilities: {
        definition: "Combined utility costs from all portfolio properties.",
        calculation: "Sum of utility costs from all properties",
        aiAssistance: "AI monitors utility usage and identifies energy efficiency opportunities."
    },
    total_services: {
        definition: "Combined service costs from all portfolio properties.",
        calculation: "Sum of service costs from all properties",
        aiAssistance: "AI manages service contracts and identifies volume discount opportunities."
    },
    total_reserves: {
        definition: "Combined reserves from all portfolio properties.",
        calculation: "Sum of reserves from all properties",
        aiAssistance: "AI optimizes reserve levels based on portfolio age, condition, and risk profile."
    },
    /*total_operating_expenses: {
        definition: "Combined operating expenses from all portfolio properties.",
        calculation: "Sum of operating expenses from all properties",
        aiAssistance: "AI tracks expense trends and identifies cost optimization opportunities across portfolio."
    }*/
}; 